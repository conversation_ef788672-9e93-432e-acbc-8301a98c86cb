# 🐳 DOCKER COMPOSE POSTGRESQL POUR ERP HUB
# Configuration complète pour déploiement production avec PostgreSQL

version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: erp_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: erp_hub
      POSTGRES_USER: erp_admin
      POSTGRES_PASSWORD: erp_secure_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=fr_FR.UTF-8 --lc-ctype=fr_FR.UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - erp_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U erp_admin -d erp_hub"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Backend Flask
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: erp_api
    restart: unless-stopped
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=erp_hub
      - DB_USER=erp_admin
      - DB_PASSWORD=erp_secure_2024
      - JWT_SECRET_KEY=your_super_secret_jwt_key_change_in_production
      - FLASK_ENV=production
      - PYTHONUNBUFFERED=1
    ports:
      - "5000:5000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - erp_network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Nginx
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: erp_frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - api
    networks:
      - erp_network
    volumes:
      - ./frontend/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis pour cache et sessions
  redis:
    image: redis:7-alpine
    container_name: erp_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - erp_network
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass erp_redis_2024

  # Monitoring avec Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: erp_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    networks:
      - erp_network
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Grafana pour visualisation
  grafana:
    image: grafana/grafana:latest
    container_name: erp_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    networks:
      - erp_network
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin_grafana_2024
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro

  # Backup automatique
  backup:
    image: postgres:15-alpine
    container_name: erp_backup
    restart: unless-stopped
    environment:
      - PGPASSWORD=erp_secure_2024
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    networks:
      - erp_network
    depends_on:
      - postgres
    command: >
      sh -c "
        chmod +x /backup.sh &&
        while true; do
          /backup.sh
          sleep 86400
        done
      "

# Réseaux
networks:
  erp_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes persistants
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
