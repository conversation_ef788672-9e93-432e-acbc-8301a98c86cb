-- Script d'initialisation de la base de données ERP HUB
-- Ce script configure la base de données PostgreSQL pour le système ERP modulaire

-- Création des extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Création du schéma pour les agents
CREATE SCHEMA IF NOT EXISTS agents;
CREATE SCHEMA IF NOT EXISTS modules;
CREATE SCHEMA IF NOT EXISTS core;

-- Configuration des permissions
GRANT USAGE ON SCHEMA agents TO erp_user;
GRANT USAGE ON SCHEMA modules TO erp_user;
GRANT USAGE ON SCHEMA core TO erp_user;

GRANT CREATE ON SCHEMA agents TO erp_user;
GRANT CREATE ON SCHEMA modules TO erp_user;
GRANT CREATE ON SCHEMA core TO erp_user;

-- Fonction pour créer des timestamps automatiques
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Fonction pour générer des identifiants uniques
CREATE OR REPLACE FUNCTION generate_unique_id(prefix TEXT DEFAULT 'ERP')
RETURNS TEXT AS $$
BEGIN
    RETURN prefix || '_' || EXTRACT(EPOCH FROM NOW())::BIGINT || '_' || FLOOR(RANDOM() * 1000)::INT;
END;
$$ language 'plpgsql';

-- Table de configuration système
CREATE TABLE IF NOT EXISTS core.system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trigger pour update_modified_column
CREATE TRIGGER update_system_config_modtime
    BEFORE UPDATE ON core.system_config
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_column();

-- Configuration initiale du système
INSERT INTO core.system_config (key, value, description) VALUES
('system_name', 'ERP HUB', 'Nom du système ERP'),
('system_version', '1.0.0', 'Version du système'),
('agent_architecture_enabled', 'true', 'Active l''architecture d''agents'),
('multi_tenant_enabled', 'true', 'Active le mode multi-tenant'),
('default_language', 'fr', 'Langue par défaut du système'),
('default_currency', 'EUR', 'Devise par défaut'),
('backup_enabled', 'true', 'Active les sauvegardes automatiques')
ON CONFLICT (key) DO NOTHING;

-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_system_config_key ON core.system_config(key);
CREATE INDEX IF NOT EXISTS idx_system_config_active ON core.system_config(is_active);

-- Commentaires pour la documentation
COMMENT ON SCHEMA agents IS 'Schéma contenant les tables relatives à l''architecture d''agents';
COMMENT ON SCHEMA modules IS 'Schéma contenant les tables des modules ERP';
COMMENT ON SCHEMA core IS 'Schéma contenant les tables de base du système';
COMMENT ON TABLE core.system_config IS 'Configuration système globale';

-- Message de confirmation
DO $$
BEGIN
    RAISE NOTICE 'Base de données ERP HUB initialisée avec succès';
    RAISE NOTICE 'Schémas créés: core, agents, modules';
    RAISE NOTICE 'Extensions activées: uuid-ossp, pg_trgm, btree_gin';
END $$;
