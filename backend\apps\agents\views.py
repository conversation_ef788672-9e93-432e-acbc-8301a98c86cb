from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Count, Sum, Avg
from .models import *
from .serializers import *
import json

# ViewSets pour chaque agent
class ManagerViewSet(viewsets.ModelViewSet):
    queryset = ManagerAgent.objects.all()
    serializer_class = ManagerAgentSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def dashboard_data(self, request):
        """Données du tableau de bord manager"""
        try:
            manager = ManagerAgent.objects.first()
            if not manager:
                manager = ManagerAgent.objects.create(name="Manager Principal")
            
            # Métriques système
            total_users = User.objects.count()
            active_agents = 10  # Nombre d'agents actifs
            
            # Mise à jour des métriques
            manager.total_users = total_users
            manager.active_sessions = request.user.is_authenticated
            manager.system_health = {
                "cpu_usage": 45.2,
                "memory_usage": 67.8,
                "disk_usage": 23.1,
                "network_status": "optimal"
            }
            manager.performance_metrics = {
                "response_time": 120,
                "throughput": 1500,
                "error_rate": 0.02
            }
            manager.save()
            
            return Response({
                "status": "success",
                "data": {
                    "total_users": total_users,
                    "active_agents": active_agents,
                    "system_health": manager.system_health,
                    "performance_metrics": manager.performance_metrics,
                    "last_updated": timezone.now()
                }
            })
        except Exception as e:
            return Response({
                "status": "error",
                "message": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class HRViewSet(viewsets.ModelViewSet):
    queryset = HRAgent.objects.all()
    serializer_class = HRAgentSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def employees_summary(self, request):
        """Résumé des employés"""
        total_employees = Employee.objects.count()
        departments = Employee.objects.values('department').annotate(
            count=Count('id')
        ).order_by('-count')
        
        return Response({
            "total_employees": total_employees,
            "departments": list(departments),
            "recent_hires": Employee.objects.filter(
                hire_date__gte=timezone.now().date().replace(day=1)
            ).count()
        })

class SalesViewSet(viewsets.ModelViewSet):
    queryset = SalesAgent.objects.all()
    serializer_class = SalesAgentSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def pipeline_data(self, request):
        """Données du pipeline de ventes"""
        pipeline = Lead.objects.values('status').annotate(
            count=Count('id'),
            total_value=Sum('value')
        )
        
        conversion_rate = Lead.objects.filter(status='won').count() / max(Lead.objects.count(), 1) * 100
        
        return Response({
            "pipeline": list(pipeline),
            "conversion_rate": round(conversion_rate, 2),
            "total_leads": Lead.objects.count(),
            "monthly_revenue": Lead.objects.filter(
                status='won',
                created_at__month=timezone.now().month
            ).aggregate(total=Sum('value'))['total'] or 0
        })

class PurchaseViewSet(viewsets.ModelViewSet):
    queryset = PurchaseAgent.objects.all()
    serializer_class = PurchaseAgentSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def orders_summary(self, request):
        """Résumé des commandes d'achat"""
        orders_by_status = PurchaseOrder.objects.values('status').annotate(
            count=Count('id'),
            total_amount=Sum('total_amount')
        )
        
        return Response({
            "orders_by_status": list(orders_by_status),
            "pending_approvals": PurchaseOrder.objects.filter(status='pending').count(),
            "monthly_spending": PurchaseOrder.objects.filter(
                created_at__month=timezone.now().month,
                status__in=['approved', 'ordered', 'received']
            ).aggregate(total=Sum('total_amount'))['total'] or 0
        })

class LogisticsViewSet(viewsets.ModelViewSet):
    queryset = LogisticsAgent.objects.all()
    serializer_class = LogisticsAgentSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def shipments_status(self, request):
        """Statut des expéditions"""
        shipments_by_status = Shipment.objects.values('status').annotate(
            count=Count('id')
        )
        
        return Response({
            "shipments_by_status": list(shipments_by_status),
            "active_shipments": Shipment.objects.filter(status='in_transit').count(),
            "delivery_rate": 95.5,  # Calculé dynamiquement
            "average_delivery_time": 2.3  # En jours
        })

class StockViewSet(viewsets.ModelViewSet):
    queryset = StockAgent.objects.all()
    serializer_class = StockAgentSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def inventory_summary(self, request):
        """Résumé de l'inventaire"""
        total_products = Product.objects.count()
        low_stock = Product.objects.filter(quantity__lte=models.F('min_quantity')).count()
        total_value = Product.objects.aggregate(
            total=Sum(models.F('quantity') * models.F('unit_price'))
        )['total'] or 0
        
        return Response({
            "total_products": total_products,
            "low_stock_alerts": low_stock,
            "total_value": float(total_value),
            "categories": Product.objects.values('category').annotate(
                count=Count('id')
            ).order_by('-count')[:5]
        })

class AccountingViewSet(viewsets.ModelViewSet):
    queryset = AccountingAgent.objects.all()
    serializer_class = AccountingAgentSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def financial_summary(self, request):
        """Résumé financier"""
        current_month = timezone.now().month
        
        revenue = Transaction.objects.filter(
            type='income',
            date__month=current_month
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        expenses = Transaction.objects.filter(
            type='expense',
            date__month=current_month
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        return Response({
            "monthly_revenue": float(revenue),
            "monthly_expenses": float(expenses),
            "net_profit": float(revenue - expenses),
            "total_transactions": Transaction.objects.filter(
                date__month=current_month
            ).count()
        })

class FinanceViewSet(viewsets.ModelViewSet):
    queryset = FinanceAgent.objects.all()
    serializer_class = FinanceAgentSerializer
    permission_classes = [IsAuthenticated]

class CRMViewSet(viewsets.ModelViewSet):
    queryset = CRMAgent.objects.all()
    serializer_class = CRMAgentSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def customer_metrics(self, request):
        """Métriques clients"""
        total_customers = Customer.objects.count()
        active_customers = Customer.objects.filter(status='active').count()
        
        return Response({
            "total_customers": total_customers,
            "active_customers": active_customers,
            "satisfaction_score": 4.2,  # Calculé à partir des feedbacks
            "retention_rate": 87.5,
            "lifetime_value": Customer.objects.aggregate(
                avg=Avg('lifetime_value')
            )['avg'] or 0
        })

class BIViewSet(viewsets.ModelViewSet):
    queryset = BIAgent.objects.all()
    serializer_class = BIAgentSerializer
    permission_classes = [IsAuthenticated]

# Vues spécialisées
class ManagerDashboardView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Dashboard principal du manager"""
        return Response({
            "system_overview": {
                "total_users": User.objects.count(),
                "active_agents": 10,
                "system_uptime": "99.9%",
                "last_backup": timezone.now().isoformat()
            },
            "performance_metrics": {
                "response_time": 120,
                "throughput": 1500,
                "error_rate": 0.02,
                "cpu_usage": 45.2
            },
            "recent_activities": [
                {"action": "Nouvel utilisateur créé", "time": timezone.now()},
                {"action": "Rapport généré", "time": timezone.now()},
                {"action": "Sauvegarde effectuée", "time": timezone.now()}
            ]
        })

class RealtimeNotificationsView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Notifications temps réel"""
        notifications = AgentNotification.objects.filter(
            read=False
        ).order_by('-created_at')[:10]
        
        return Response({
            "notifications": [
                {
                    "id": str(notif.id),
                    "from_agent": notif.from_agent,
                    "message": notif.message,
                    "priority": notif.priority,
                    "created_at": notif.created_at
                }
                for notif in notifications
            ]
        })

class RealtimeMetricsView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Métriques temps réel"""
        return Response({
            "timestamp": timezone.now(),
            "metrics": {
                "active_users": 45,
                "system_load": 67.8,
                "memory_usage": 45.2,
                "network_traffic": 1250,
                "database_connections": 12
            }
        })

class AgentSyncView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Synchronisation des données entre agents"""
        agent_type = request.data.get('agent_type')
        data = request.data.get('data')
        
        # Logique de synchronisation
        return Response({
            "status": "success",
            "message": f"Données synchronisées pour {agent_type}",
            "timestamp": timezone.now()
        })

class AgentStatusView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Statut de tous les agents"""
        agents_status = {
            "manager": "active",
            "hr": "active", 
            "sales": "active",
            "purchase": "active",
            "logistics": "active",
            "stock": "active",
            "accounting": "active",
            "finance": "active",
            "crm": "active",
            "bi": "active"
        }
        
        return Response({
            "agents": agents_status,
            "last_check": timezone.now(),
            "overall_health": "excellent"
        })
