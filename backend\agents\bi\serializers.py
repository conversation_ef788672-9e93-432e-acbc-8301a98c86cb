"""
Serializers pour l'Agent BI
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
from .models import (
    DataSource, Dataset, Report, Dashboard, DashboardReport, KPI,
    Alert, AnalysisJob, BIAnalytics, UserActivity
)

User = get_user_model()


class DataSourceSerializer(serializers.ModelSerializer):
    """Serializer pour les sources de données"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = DataSource
        fields = [
            'id', 'name', 'description', 'source_type', 'connection_config',
            'connection_status', 'last_connection_test', 'schema_info',
            'refresh_frequency', 'last_refresh', 'is_active', 'auto_refresh',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'connection_status', 'last_connection_test', 'last_refresh', 'created_at', 'updated_at']


class DatasetSerializer(serializers.ModelSerializer):
    """Serializer pour les datasets"""
    data_source_name = serializers.CharField(source='data_source.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = Dataset
        fields = [
            'id', 'data_source', 'data_source_name', 'name', 'description', 'dataset_type',
            'query_config', 'columns_config', 'filters_config',
            'row_count', 'column_count', 'data_size_mb',
            'cache_enabled', 'cache_duration_minutes', 'last_cached',
            'is_active', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'row_count', 'column_count', 'data_size_mb', 'last_cached', 'created_at', 'updated_at']


class ReportSerializer(serializers.ModelSerializer):
    """Serializer pour les rapports"""
    dataset_name = serializers.CharField(source='dataset.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = Report
        fields = [
            'id', 'dataset', 'dataset_name', 'name', 'description',
            'report_type', 'chart_type', 'config', 'layout_config', 'style_config',
            'filters', 'parameters', 'is_scheduled', 'schedule_config',
            'last_execution', 'next_execution', 'is_public', 'shared_with',
            'status', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'last_execution', 'next_execution', 'created_at', 'updated_at']


class DashboardSerializer(serializers.ModelSerializer):
    """Serializer pour les tableaux de bord"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    reports_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Dashboard
        fields = [
            'id', 'name', 'description', 'dashboard_type',
            'layout_config', 'theme_config', 'refresh_interval',
            'widgets', 'is_public', 'shared_with',
            'is_active', 'is_default', 'created_by', 'created_by_name',
            'reports_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'reports_count', 'created_at', 'updated_at']
    
    def get_reports_count(self, obj):
        return obj.reports.count()


class DashboardReportSerializer(serializers.ModelSerializer):
    """Serializer pour les rapports de tableau de bord"""
    report_name = serializers.CharField(source='report.name', read_only=True)
    dashboard_name = serializers.CharField(source='dashboard.name', read_only=True)
    
    class Meta:
        model = DashboardReport
        fields = [
            'id', 'dashboard', 'dashboard_name', 'report', 'report_name',
            'position_x', 'position_y', 'width', 'height',
            'widget_config', 'order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class KPISerializer(serializers.ModelSerializer):
    """Serializer pour les KPIs"""
    dataset_name = serializers.CharField(source='dataset.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    performance_vs_target = serializers.ReadOnlyField()
    is_on_target = serializers.ReadOnlyField()
    
    class Meta:
        model = KPI
        fields = [
            'id', 'dataset', 'dataset_name', 'name', 'description', 'kpi_type',
            'calculation_method', 'calculation_config',
            'current_value', 'target_value', 'previous_value',
            'unit', 'format_config', 'warning_threshold', 'critical_threshold',
            'trend_direction', 'trend_percentage', 'last_calculated', 'calculation_frequency',
            'performance_vs_target', 'is_on_target', 'is_active',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'performance_vs_target', 'is_on_target', 'last_calculated',
            'trend_direction', 'trend_percentage', 'created_at', 'updated_at'
        ]


class KPICreateSerializer(serializers.Serializer):
    """Serializer pour la création de KPIs"""
    dataset_id = serializers.UUIDField()
    name = serializers.CharField(max_length=200)
    description = serializers.CharField(required=False, allow_blank=True)
    kpi_type = serializers.ChoiceField(choices=KPI.KPI_TYPES)
    calculation_method = serializers.ChoiceField(choices=KPI.CALCULATION_METHODS)
    calculation_config = serializers.JSONField(default=dict, required=False)
    target_value = serializers.DecimalField(max_digits=15, decimal_places=4, required=False, allow_null=True)
    unit = serializers.CharField(max_length=50, required=False, allow_blank=True)
    format_config = serializers.JSONField(default=dict, required=False)
    warning_threshold = serializers.DecimalField(max_digits=15, decimal_places=4, required=False, allow_null=True)
    critical_threshold = serializers.DecimalField(max_digits=15, decimal_places=4, required=False, allow_null=True)
    calculation_frequency = serializers.IntegerField(default=60)


class AlertSerializer(serializers.ModelSerializer):
    """Serializer pour les alertes"""
    kpi_name = serializers.CharField(source='kpi.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    acknowledged_by_name = serializers.CharField(source='acknowledged_by.get_full_name', read_only=True)
    
    class Meta:
        model = Alert
        fields = [
            'id', 'kpi', 'kpi_name', 'name', 'description', 'alert_type', 'severity',
            'condition_config', 'notification_config', 'status',
            'last_triggered', 'trigger_count', 'acknowledged_by', 'acknowledged_by_name',
            'acknowledged_at', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_triggered', 'trigger_count', 'acknowledged_at',
            'created_at', 'updated_at'
        ]


class AnalysisJobSerializer(serializers.ModelSerializer):
    """Serializer pour les tâches d'analyse"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    duration = serializers.ReadOnlyField()
    
    class Meta:
        model = AnalysisJob
        fields = [
            'id', 'name', 'description', 'job_type', 'priority',
            'config', 'parameters', 'scheduled_at', 'started_at', 'completed_at',
            'status', 'progress_percentage', 'result_data', 'error_message',
            'execution_time_seconds', 'memory_usage_mb', 'duration',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'started_at', 'completed_at', 'progress_percentage',
            'result_data', 'error_message', 'execution_time_seconds',
            'memory_usage_mb', 'duration', 'created_at', 'updated_at'
        ]


class BIAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer pour les analytics BI"""
    calculated_by_name = serializers.CharField(source='calculated_by.get_full_name', read_only=True)
    variance_from_target = serializers.ReadOnlyField()
    variance_from_previous = serializers.ReadOnlyField()
    growth_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = BIAnalytics
        fields = [
            'id', 'metric_type', 'metric_name', 'period_start', 'period_end',
            'metric_value', 'target_value', 'previous_value',
            'variance_from_target', 'variance_from_previous', 'growth_rate',
            'dimensions', 'attributes', 'calculation_method',
            'calculated_by', 'calculated_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'variance_from_target', 'variance_from_previous',
            'growth_rate', 'created_at', 'updated_at'
        ]


class UserActivitySerializer(serializers.ModelSerializer):
    """Serializer pour les activités utilisateur"""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = UserActivity
        fields = [
            'id', 'user', 'user_name', 'activity_type',
            'object_type', 'object_id', 'object_name',
            'session_id', 'ip_address', 'user_agent',
            'metadata', 'duration_seconds', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class BIDashboardSerializer(serializers.Serializer):
    """Serializer pour le dashboard BI"""
    tenant = serializers.CharField()
    timestamp = serializers.CharField()
    data_sources = serializers.DictField()
    datasets = serializers.DictField()
    reports = serializers.DictField()
    dashboards = serializers.DictField()
    kpis = serializers.DictField()
    alerts = serializers.DictField()
    analysis_jobs = serializers.DictField()
    user_activity = serializers.DictField()
    performance = serializers.DictField()
    data_quality = serializers.DictField()
    usage = serializers.DictField()
    active_alerts = serializers.ListField()
    system_health = serializers.DictField()
    recommendations = serializers.ListField()


class BIInsightSerializer(serializers.Serializer):
    """Serializer pour les insights BI"""
    type = serializers.ChoiceField(choices=['critical', 'warning', 'opportunity', 'info', 'error'])
    priority = serializers.ChoiceField(choices=['high', 'medium', 'low'])
    title = serializers.CharField()
    description = serializers.CharField()
    recommendation = serializers.CharField()
    generated_at = serializers.CharField()
    action = serializers.CharField(required=False)


class KPICalculationSerializer(serializers.Serializer):
    """Serializer pour le calcul de KPI"""
    success = serializers.BooleanField()
    kpi = serializers.DictField(required=False)
    error = serializers.CharField(required=False)
