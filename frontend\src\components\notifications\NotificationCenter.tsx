import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'system';
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  source: string;
  actions?: Array<{
    label: string;
    action: () => void;
    style?: 'primary' | 'secondary' | 'danger';
  }>;
}

export const NotificationCenter: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filter, setFilter] = useState<'all' | 'unread' | 'urgent'>('all');

  // Simuler des notifications en temps réel
  useEffect(() => {
    const interval = setInterval(() => {
      // Simuler une nouvelle notification aléatoire
      if (Math.random() > 0.7) {
        addNotification(generateRandomNotification());
      }
    }, 10000);

    // Ajouter quelques notifications initiales
    const initialNotifications = [
      {
        id: '1',
        title: 'Stock Critique',
        message: 'Le stock de "Processeur Intel i7" est en dessous du seuil minimum',
        type: 'warning' as const,
        timestamp: new Date(Date.now() - 300000),
        read: false,
        priority: 'high' as const,
        source: 'Agent Stock',
        actions: [
          { label: 'Commander', action: () => console.log('Commander'), style: 'primary' as const },
          { label: 'Ignorer', action: () => console.log('Ignorer'), style: 'secondary' as const }
        ]
      },
      {
        id: '2',
        title: 'Nouvelle Commande',
        message: 'Commande #CMD-2024-001 reçue de TechCorp SA (125 000€)',
        type: 'success' as const,
        timestamp: new Date(Date.now() - 600000),
        read: false,
        priority: 'medium' as const,
        source: 'Agent Sales'
      },
      {
        id: '3',
        title: 'Maintenance Système',
        message: 'Maintenance programmée ce soir de 22h à 2h',
        type: 'info' as const,
        timestamp: new Date(Date.now() - 1800000),
        read: true,
        priority: 'low' as const,
        source: 'Système'
      }
    ];

    setNotifications(initialNotifications);

    return () => clearInterval(interval);
  }, []);

  const generateRandomNotification = (): Notification => {
    const types: Notification['type'][] = ['info', 'success', 'warning', 'error'];
    const priorities: Notification['priority'][] = ['low', 'medium', 'high'];
    const sources = ['Agent HR', 'Agent Sales', 'Agent Stock', 'Agent Finance', 'Système'];
    
    const messages = [
      'Nouveau employé ajouté au système',
      'Objectif mensuel atteint à 105%',
      'Livraison retardée pour la commande #CMD-001',
      'Rapport financier généré avec succès',
      'Mise à jour système disponible'
    ];

    return {
      id: Date.now().toString(),
      title: 'Notification Automatique',
      message: messages[Math.floor(Math.random() * messages.length)],
      type: types[Math.floor(Math.random() * types.length)],
      timestamp: new Date(),
      read: false,
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      source: sources[Math.floor(Math.random() * sources.length)]
    };
  };

  const addNotification = (notification: Notification) => {
    setNotifications(prev => [notification, ...prev]);
    
    // Notification browser si permission accordée
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico'
      });
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      await Notification.requestPermission();
    }
  };

  const filteredNotifications = notifications.filter(notif => {
    switch (filter) {
      case 'unread': return !notif.read;
      case 'urgent': return notif.priority === 'urgent' || notif.priority === 'high';
      default: return true;
    }
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  const getTypeIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      case 'system': return '⚙️';
      default: return 'ℹ️';
    }
  };

  const getTypeColor = (type: Notification['type']) => {
    switch (type) {
      case 'success': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      case 'system': return 'text-blue-600 bg-blue-100';
      default: return 'text-blue-600 bg-blue-100';
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'urgent': return 'border-l-red-500';
      case 'high': return 'border-l-orange-500';
      case 'medium': return 'border-l-yellow-500';
      default: return 'border-l-gray-300';
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}j`;
    if (hours > 0) return `${hours}h`;
    if (minutes > 0) return `${minutes}min`;
    return 'À l\'instant';
  };

  return (
    <>
      {/* Bouton de notification */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <span className="text-xl">🔔</span>
        {unreadCount > 0 && (
          <motion.span
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
          >
            {unreadCount > 9 ? '9+' : unreadCount}
          </motion.span>
        )}
      </motion.button>

      {/* Panel de notifications */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-50"
              onClick={() => setIsOpen(false)}
            />

            {/* Panel */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed right-0 top-0 h-full w-96 bg-white dark:bg-gray-800 shadow-xl z-50 flex flex-col"
            >
              {/* En-tête */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    🔔 Notifications
                  </h2>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    ✕
                  </button>
                </div>

                {/* Filtres */}
                <div className="flex gap-2 mb-3">
                  {[
                    { key: 'all', label: 'Toutes' },
                    { key: 'unread', label: 'Non lues' },
                    { key: 'urgent', label: 'Urgentes' }
                  ].map(({ key, label }) => (
                    <button
                      key={key}
                      onClick={() => setFilter(key as any)}
                      className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                        filter === key
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      {label}
                    </button>
                  ))}
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <button
                    onClick={markAllAsRead}
                    className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Tout marquer lu
                  </button>
                  <button
                    onClick={clearAll}
                    className="px-3 py-2 text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                  >
                    Effacer
                  </button>
                </div>
              </div>

              {/* Liste des notifications */}
              <div className="flex-1 overflow-y-auto">
                {filteredNotifications.length === 0 ? (
                  <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                    <div className="text-4xl mb-2">📭</div>
                    <div>Aucune notification</div>
                  </div>
                ) : (
                  <div className="p-2">
                    {filteredNotifications.map((notification, index) => (
                      <motion.div
                        key={notification.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className={`
                          p-4 mb-2 rounded-lg border-l-4 cursor-pointer transition-all
                          ${getPriorityColor(notification.priority)}
                          ${notification.read 
                            ? 'bg-gray-50 dark:bg-gray-700' 
                            : 'bg-white dark:bg-gray-600 shadow-md'
                          }
                          hover:shadow-lg
                        `}
                        onClick={() => markAsRead(notification.id)}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className={`p-1 rounded-full ${getTypeColor(notification.type)}`}>
                              {getTypeIcon(notification.type)}
                            </span>
                            <div className="font-semibold text-gray-900 dark:text-white">
                              {notification.title}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-500">
                              {formatTime(notification.timestamp)}
                            </span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteNotification(notification.id);
                              }}
                              className="text-gray-400 hover:text-red-500 transition-colors"
                            >
                              🗑️
                            </button>
                          </div>
                        </div>

                        <div className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                          {notification.message}
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">
                            {notification.source}
                          </span>
                          {!notification.read && (
                            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                          )}
                        </div>

                        {/* Actions */}
                        {notification.actions && (
                          <div className="flex gap-2 mt-3">
                            {notification.actions.map((action, actionIndex) => (
                              <button
                                key={actionIndex}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  action.action();
                                }}
                                className={`px-3 py-1 text-xs rounded transition-colors ${
                                  action.style === 'primary' ? 'bg-blue-600 text-white hover:bg-blue-700' :
                                  action.style === 'danger' ? 'bg-red-600 text-white hover:bg-red-700' :
                                  'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                                }`}
                              >
                                {action.label}
                              </button>
                            ))}
                          </div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={requestNotificationPermission}
                  className="w-full p-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  🔔 Activer les notifications navigateur
                </button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};
