# 🗄️ **LOCALISATION PHYSIQUE DES DONNÉES POSTGRESQL - ERP HUB**

## **📍 1. EMPLACEMENT EXACT DES FICHIERS DE DONNÉES**

### **🐳 Sur votre système Windows :**
```
C:\ProgramData\Docker\volumes\erp_hub_postgres_data\_data\
```

### **🔍 Chemin complet détaillé :**
- **Volume Docker** : `erp_hub_postgres_data`
- **Mountpoint** : `/var/lib/docker/volumes/erp_hub_postgres_data/_data`
- **Dans le conteneur** : `/var/lib/postgresql/data`

### **📂 Structure des dossiers :**
```
C:\ProgramData\Docker\volumes\erp_hub_postgres_data\_data\
├── base/                    # Données des bases de données
│   └── [database_oid]/      # Dossier de votre base erp_hub
│       ├── [table_oid]      # Fichiers des tables (employees, customers, etc.)
│       ├── [table_oid].1    # Segments supplémentaires si tables volumineuses
│       └── pg_internal.init
├── global/                  # Données globales PostgreSQL
├── pg_wal/                  # Write-Ahead Logs (journaux de transactions)
├── pg_xact/                 # Statuts des transactions
├── pg_multixact/            # Données multi-transactions
├── postgresql.conf          # Configuration PostgreSQL
├── pg_hba.conf             # Configuration d'authentification
└── postmaster.pid          # PID du processus principal
```

---

## **🗄️ 2. STOCKAGE DES DONNÉES : CONTENEUR VS DISQUE LOCAL**

### **✅ VOS DONNÉES SONT PERSISTANTES SUR VOTRE DISQUE LOCAL**

| Aspect | Détail |
|--------|--------|
| **Localisation** | Disque local Windows (volume Docker) |
| **Persistance** | ✅ Données conservées même si conteneur supprimé |
| **Sauvegarde** | ✅ Incluses dans les sauvegardes système |
| **Accès direct** | ⚠️ Possible mais non recommandé |
| **Portabilité** | ✅ Volume peut être copié/déplacé |

### **🔄 Cycle de vie des données :**
1. **Création** : Données écrites dans le volume Docker
2. **Modification** : Mises à jour persistées automatiquement
3. **Arrêt conteneur** : Données restent sur le disque
4. **Redémarrage** : Données automatiquement remontées
5. **Suppression conteneur** : Données conservées dans le volume

---

## **🔧 3. ACCÈS AUX FICHIERS DE DONNÉES BRUTS**

### **⚠️ MÉTHODES D'ACCÈS (ATTENTION REQUISE)**

#### **🐳 Via le conteneur Docker (RECOMMANDÉ) :**
```bash
# Accéder au système de fichiers du conteneur
docker exec -it erp_postgres bash

# Naviguer vers les données
cd /var/lib/postgresql/data

# Lister les fichiers
ls -la
```

#### **💻 Accès direct Windows (DÉCONSEILLÉ) :**
```cmd
# Naviguer vers le volume (nécessite droits admin)
cd "C:\ProgramData\Docker\volumes\erp_hub_postgres_data\_data"

# ⚠️ ATTENTION : Ne pas modifier ces fichiers directement !
```

#### **🔍 Inspection sécurisée :**
```bash
# Voir la taille des données
docker exec erp_postgres du -sh /var/lib/postgresql/data

# Lister les bases de données
docker exec erp_postgres psql -U erp_admin -l

# Voir les fichiers de la base
docker exec erp_postgres ls -la /var/lib/postgresql/data/base/
```

---

## **📊 4. STRUCTURE DE STOCKAGE DES 22 TABLES**

### **🗂️ Organisation interne PostgreSQL :**

#### **📋 Tables principales et leur stockage :**
```sql
-- Voir les OID (identifiants internes) des tables
SELECT 
    schemaname,
    tablename,
    pg_relation_filepath(oid) as file_path,
    pg_size_pretty(pg_total_relation_size(oid)) as size
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'public' AND c.relkind = 'r'
ORDER BY pg_total_relation_size(oid) DESC;
```

#### **📂 Fichiers physiques par module :**

| Module | Tables | Fichiers approximatifs |
|--------|--------|----------------------|
| **HR** | employees, leaves, evaluations | 3 fichiers + index |
| **Sales** | customers, opportunities, orders | 3 fichiers + index |
| **Purchase** | suppliers, purchase_orders, purchase_requests | 3 fichiers + index |
| **Stock** | products, inventory, stock_movements | 3 fichiers + index |
| **Logistics** | warehouses, shipments | 2 fichiers + index |
| **Finance** | budgets, chart_of_accounts, journal_entries, journal_entry_lines | 4 fichiers + index |
| **CRM** | contacts, interactions | 2 fichiers + index |
| **BI** | kpis, reports | 2 fichiers + index |

### **🔍 Commandes pour explorer la structure :**
```bash
# Voir la taille de chaque table
docker exec erp_postgres psql -U erp_admin -d erp_hub -c "
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
"

# Voir les index
docker exec erp_postgres psql -U erp_admin -d erp_hub -c "
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexname::regclass)) as index_size
FROM pg_indexes 
WHERE schemaname = 'public'
ORDER BY pg_relation_size(indexname::regclass) DESC;
"
```

---

## **💾 5. OPTIONS DE SAUVEGARDE ET RÉCUPÉRATION**

### **🔄 MÉTHODES DE SAUVEGARDE**

#### **📤 1. Sauvegarde logique (SQL) - RECOMMANDÉE**
```bash
# Sauvegarde complète
docker exec erp_postgres pg_dump -U erp_admin erp_hub > backup_erp_hub_$(date +%Y%m%d).sql

# Sauvegarde compressée
docker exec erp_postgres pg_dump -U erp_admin -Fc erp_hub > backup_erp_hub_$(date +%Y%m%d).dump

# Sauvegarde d'une table spécifique
docker exec erp_postgres pg_dump -U erp_admin -t employees erp_hub > backup_employees.sql
```

#### **📂 2. Sauvegarde physique (Volume Docker)**
```bash
# Arrêter le conteneur
docker stop erp_postgres

# Copier le volume
docker run --rm -v erp_hub_postgres_data:/source -v $(pwd):/backup alpine tar czf /backup/postgres_volume_backup.tar.gz -C /source .

# Redémarrer le conteneur
docker start erp_postgres
```

#### **☁️ 3. Sauvegarde automatique (Configurée)**
```bash
# Le système inclut déjà un conteneur de sauvegarde automatique
# Vérifier les sauvegardes dans le dossier ./backups/
ls -la backups/
```

### **📥 MÉTHODES DE RÉCUPÉRATION**

#### **🔄 1. Restauration logique**
```bash
# Restaurer depuis un fichier SQL
docker exec -i erp_postgres psql -U erp_admin erp_hub < backup_erp_hub.sql

# Restaurer depuis un dump compressé
docker exec erp_postgres pg_restore -U erp_admin -d erp_hub backup_erp_hub.dump

# Restaurer une table spécifique
docker exec -i erp_postgres psql -U erp_admin erp_hub < backup_employees.sql
```

#### **📂 2. Restauration physique**
```bash
# Arrêter le conteneur
docker stop erp_postgres

# Supprimer l'ancien volume
docker volume rm erp_hub_postgres_data

# Créer un nouveau volume
docker volume create erp_hub_postgres_data

# Restaurer les données
docker run --rm -v erp_hub_postgres_data:/target -v $(pwd):/backup alpine tar xzf /backup/postgres_volume_backup.tar.gz -C /target

# Redémarrer le conteneur
docker start erp_postgres
```

### **🛡️ STRATÉGIE DE SAUVEGARDE RECOMMANDÉE**

#### **📅 Planning automatique :**
- **Quotidien** : Sauvegarde logique complète
- **Hebdomadaire** : Sauvegarde physique du volume
- **Mensuel** : Archive des sauvegardes anciennes

#### **📍 Emplacements de sauvegarde :**
- **Local** : `./backups/` (configuré automatiquement)
- **Externe** : Copie sur disque externe ou cloud
- **Test** : Validation régulière des sauvegardes

---

## **🎯 RÉSUMÉ PRATIQUE**

### **📍 VOS DONNÉES SONT ICI :**
```
C:\ProgramData\Docker\volumes\erp_hub_postgres_data\_data\
```

### **✅ POINTS CLÉS :**
1. **Persistance** : Données conservées même si conteneur supprimé
2. **Sécurité** : Accès via PostgreSQL recommandé (pas directement aux fichiers)
3. **Sauvegarde** : Système automatique configuré + sauvegardes manuelles
4. **Portabilité** : Volume peut être copié vers autre machine
5. **Performance** : Stockage optimisé par PostgreSQL

### **🔧 COMMANDES UTILES :**
```bash
# Voir la taille totale des données
docker exec erp_postgres psql -U erp_admin -d erp_hub -c "SELECT pg_size_pretty(pg_database_size('erp_hub'));"

# Sauvegarder maintenant
docker exec erp_postgres pg_dump -U erp_admin erp_hub > backup_$(date +%Y%m%d_%H%M%S).sql

# Voir l'espace disque utilisé
docker system df
```

**🎉 Vos 22 tables ERP avec 53+ enregistrements sont stockées de manière sécurisée et persistante !**
