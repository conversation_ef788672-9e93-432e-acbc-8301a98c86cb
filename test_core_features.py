#!/usr/bin/env python3
"""
Test des fonctionnalités de base du système ERP
Vérifie : Configuration Django, JWT, RBAC, Interface React, API REST, Tests
"""
import os
import sys
from pathlib import Path

def test_django_configuration():
    """Test de la configuration Django complète"""
    print("🔧 TEST CONFIGURATION DJANGO")
    print("-" * 40)
    
    # Vérifier les fichiers de configuration
    config_files = {
        'backend/config/settings.py': 'Configuration principale Django',
        'backend/config/urls.py': 'Configuration des URLs',
        'backend/config/wsgi.py': 'Configuration WSGI',
        'backend/manage.py': 'Script de gestion Django',
        'backend/requirements.txt': 'Dépendances Python'
    }
    
    all_present = True
    for file_path, description in config_files.items():
        if Path(file_path).exists():
            print(f"  ✅ {description}: {file_path}")
        else:
            print(f"  ❌ {description}: {file_path} MANQUANT")
            all_present = False
    
    # Vérifier le contenu de settings.py
    settings_path = Path('backend/config/settings.py')
    if settings_path.exists():
        with open(settings_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Vérifier les éléments clés
        key_elements = [
            'INSTALLED_APPS',
            'MIDDLEWARE',
            'DATABASES',
            'REST_FRAMEWORK',
            'SIMPLE_JWT',
            'CORS_ALLOWED_ORIGINS',
            'AUTH_USER_MODEL'
        ]
        
        for element in key_elements:
            if element in content:
                print(f"  ✅ Configuration {element} présente")
            else:
                print(f"  ❌ Configuration {element} MANQUANTE")
                all_present = False
    
    return all_present

def test_base_models():
    """Test des modèles de base"""
    print("\n📊 TEST MODÈLES DE BASE")
    print("-" * 40)
    
    models_file = Path('backend/core/models.py')
    if not models_file.exists():
        print("  ❌ Fichier models.py manquant")
        return False
    
    with open(models_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Vérifier les modèles essentiels
    essential_models = [
        'class Tenant',
        'class User',
        'class Role',
        'class UserRole',
        'TimeStampedModel',
        'UUIDModel'
    ]
    
    all_present = True
    for model in essential_models:
        if model in content:
            print(f"  ✅ {model} défini")
        else:
            print(f"  ❌ {model} MANQUANT")
            all_present = False
    
    # Vérifier les fonctionnalités multi-tenant
    if 'tenant = models.ForeignKey' in content:
        print("  ✅ Support multi-tenant configuré")
    else:
        print("  ❌ Support multi-tenant MANQUANT")
        all_present = False
    
    return all_present

def test_jwt_authentication():
    """Test du système d'authentification JWT"""
    print("\n🔐 TEST AUTHENTIFICATION JWT")
    print("-" * 40)
    
    # Vérifier la configuration JWT dans settings
    settings_path = Path('backend/config/settings.py')
    if settings_path.exists():
        with open(settings_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        jwt_elements = [
            'rest_framework_simplejwt',
            'SIMPLE_JWT',
            'JWTAuthentication',
            'ACCESS_TOKEN_LIFETIME',
            'REFRESH_TOKEN_LIFETIME'
        ]
        
        all_present = True
        for element in jwt_elements:
            if element in content:
                print(f"  ✅ Configuration JWT {element}")
            else:
                print(f"  ❌ Configuration JWT {element} MANQUANTE")
                all_present = False
    else:
        print("  ❌ Fichier settings.py manquant")
        return False
    
    # Vérifier les vues d'authentification
    views_path = Path('backend/core/views.py')
    if views_path.exists():
        with open(views_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        auth_views = [
            'CustomTokenObtainPairView',
            'UserProfileView',
            'ChangePasswordView',
            'RefreshToken'
        ]
        
        for view in auth_views:
            if view in content:
                print(f"  ✅ Vue d'authentification {view}")
            else:
                print(f"  ❌ Vue d'authentification {view} MANQUANTE")
                all_present = False
    else:
        print("  ❌ Fichier views.py manquant")
        all_present = False
    
    return all_present

def test_rbac_system():
    """Test du système RBAC (Role-Based Access Control)"""
    print("\n👥 TEST SYSTÈME RBAC")
    print("-" * 40)
    
    # Vérifier les modèles RBAC
    models_path = Path('backend/core/models.py')
    if models_path.exists():
        with open(models_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        rbac_elements = [
            'class Role',
            'class UserRole',
            'permissions = models.JSONField',
            'is_tenant_admin',
            'assigned_by'
        ]
        
        all_present = True
        for element in rbac_elements:
            if element in content:
                print(f"  ✅ Modèle RBAC {element}")
            else:
                print(f"  ❌ Modèle RBAC {element} MANQUANT")
                all_present = False
    else:
        print("  ❌ Fichier models.py manquant")
        return False
    
    # Vérifier les permissions personnalisées
    permissions_path = Path('backend/core/permissions.py')
    if permissions_path.exists():
        with open(permissions_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        permission_classes = [
            'IsTenantAdmin',
            'IsSameUserOrTenantAdmin',
            'BasePermission'
        ]
        
        for perm_class in permission_classes:
            if perm_class in content:
                print(f"  ✅ Classe de permission {perm_class}")
            else:
                print(f"  ❌ Classe de permission {perm_class} MANQUANTE")
                all_present = False
    else:
        print("  ❌ Fichier permissions.py manquant")
        all_present = False
    
    # Vérifier les vues de gestion des rôles
    views_path = Path('backend/core/views.py')
    if views_path.exists():
        with open(views_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        rbac_views = [
            'RoleListCreateView',
            'UserRoleManagementView',
            'IsTenantAdmin',
            'IsSameUserOrTenantAdmin'
        ]
        
        for view in rbac_views:
            if view in content:
                print(f"  ✅ Vue RBAC {view}")
            else:
                print(f"  ❌ Vue RBAC {view} MANQUANTE")
                all_present = False
    else:
        all_present = False
    
    return all_present

def test_react_interface():
    """Test de l'interface React de base"""
    print("\n⚛️  TEST INTERFACE REACT")
    print("-" * 40)
    
    # Vérifier la structure React
    react_structure = {
        'frontend/src/App.tsx': 'Application principale',
        'frontend/src/main.tsx': 'Point d\'entrée',
        'frontend/package.json': 'Configuration npm',
        'frontend/vite.config.js': 'Configuration Vite',
        'frontend/tsconfig.json': 'Configuration TypeScript'
    }
    
    all_present = True
    for file_path, description in react_structure.items():
        if Path(file_path).exists():
            print(f"  ✅ {description}: {file_path}")
        else:
            print(f"  ❌ {description}: {file_path} MANQUANT")
            all_present = False
    
    # Vérifier les composants d'authentification
    auth_components = [
        'frontend/src/pages/auth/LoginPage.tsx',
        'frontend/src/store/authStore.ts',
        'frontend/src/types/auth.ts'
    ]
    
    for component in auth_components:
        if Path(component).exists():
            print(f"  ✅ Composant auth: {component}")
        else:
            print(f"  ❌ Composant auth: {component} MANQUANT")
            all_present = False
    
    # Vérifier le routing
    app_path = Path('frontend/src/App.tsx')
    if app_path.exists():
        with open(app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        routing_elements = [
            'react-router-dom',
            'Routes',
            'Route',
            'Navigate',
            'BrowserRouter'
        ]
        
        for element in routing_elements:
            if element in content:
                print(f"  ✅ Routing {element}")
            else:
                print(f"  ❌ Routing {element} MANQUANT")
                all_present = False
    
    return all_present

def test_rest_api():
    """Test de l'API REST"""
    print("\n🔗 TEST API REST")
    print("-" * 40)
    
    # Vérifier les URLs principales
    urls_path = Path('backend/config/urls.py')
    if urls_path.exists():
        with open(urls_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        api_elements = [
            'api/',
            'auth/',
            'agents/',
            'drf_spectacular'
        ]
        
        all_present = True
        for element in api_elements:
            if element in content:
                print(f"  ✅ URL API {element}")
            else:
                print(f"  ❌ URL API {element} MANQUANTE")
                all_present = False
    else:
        print("  ❌ Fichier urls.py principal manquant")
        return False
    
    # Vérifier les URLs core
    core_urls_path = Path('backend/core/urls.py')
    if core_urls_path.exists():
        with open(core_urls_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        core_endpoints = [
            'login/',
            'profile/',
            'users/',
            'roles/',
            'change-password/'
        ]
        
        for endpoint in core_endpoints:
            if endpoint in content:
                print(f"  ✅ Endpoint core {endpoint}")
            else:
                print(f"  ❌ Endpoint core {endpoint} MANQUANT")
                all_present = False
    else:
        print("  ❌ Fichier core/urls.py manquant")
        all_present = False
    
    # Vérifier les serializers
    serializers_path = Path('backend/core/serializers.py')
    if serializers_path.exists():
        with open(serializers_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        serializer_classes = [
            'UserSerializer',
            'LoginSerializer',
            'UserProfileSerializer',
            'TenantSerializer',
            'RoleSerializer'
        ]
        
        for serializer in serializer_classes:
            if serializer in content:
                print(f"  ✅ Serializer {serializer}")
            else:
                print(f"  ❌ Serializer {serializer} MANQUANT")
                all_present = False
    else:
        print("  ❌ Fichier serializers.py manquant")
        all_present = False
    
    return all_present

def test_basic_tests():
    """Test de la présence de tests de base"""
    print("\n🧪 TEST TESTS DE BASE")
    print("-" * 40)
    
    # Vérifier les fichiers de test existants
    test_files = [
        'test_system_complete.py',
        'test_final_integration.py',
        'backend/test_system.py',
        'backend/test_bi.py',
        'backend/test_crm.py'
    ]
    
    existing_tests = 0
    for test_file in test_files:
        if Path(test_file).exists():
            print(f"  ✅ Test présent: {test_file}")
            existing_tests += 1
        else:
            print(f"  ❌ Test manquant: {test_file}")
    
    print(f"  📊 {existing_tests}/{len(test_files)} fichiers de test présents")
    
    # Vérifier la configuration de test Django
    settings_path = Path('backend/config/settings.py')
    if settings_path.exists():
        with open(settings_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'TEST_' in content or 'django.test' in content:
            print("  ✅ Configuration de test Django")
        else:
            print("  ⚠️  Configuration de test Django basique")
    
    return existing_tests >= 3  # Au moins 3 fichiers de test

def main():
    """Test principal des fonctionnalités de base"""
    print("🚀 TEST DES FONCTIONNALITÉS DE BASE")
    print("🏢 SYSTÈME ERP MODULAIRE")
    print("=" * 60)
    
    tests = [
        ("Configuration Django complète", test_django_configuration),
        ("Modèles de base avec multi-tenant", test_base_models),
        ("Système d'authentification JWT", test_jwt_authentication),
        ("Système RBAC", test_rbac_system),
        ("Interface React avec routing", test_react_interface),
        ("API REST pour auth et users", test_rest_api),
        ("Tests de base", test_basic_tests),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur: {e}")
            results.append((test_name, False))
    
    # Résultats finaux
    print("\n" + "=" * 60)
    print("📊 RÉSULTATS DES TESTS:")
    
    passed_tests = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"   {test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / len(results)) * 100
    print(f"\n🎯 Taux de réussite: {passed_tests}/{len(results)} ({success_rate:.1f}%)")
    
    if success_rate >= 85:
        print("\n🎉 TOUTES LES FONCTIONNALITÉS DE BASE SONT PRÉSENTES !")
        print("   ✅ Configuration Django complète")
        print("   ✅ Système d'authentification JWT")
        print("   ✅ RBAC (Role-Based Access Control)")
        print("   ✅ Interface React avec routing")
        print("   ✅ API REST pour auth et gestion users")
        print("   ✅ Tests de base pour validation")
        print("\n🚀 Le système est prêt pour la production !")
    else:
        print("\n⚠️  Certaines fonctionnalités de base manquent.")
        print("   Vérifiez les éléments marqués comme MANQUANTS.")
    
    return success_rate >= 85

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
