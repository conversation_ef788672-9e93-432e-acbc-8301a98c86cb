const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

console.log('🔄 Démarrage du serveur ERP HUB...');

const server = http.createServer((req, res) => {
  console.log(`📥 Requête: ${req.method} ${req.url}`);

  // Headers CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  let filePath;

  // Routes de l'ERP
  switch (req.url) {
    case '/':
    case '/dashboard':
      filePath = path.join(__dirname, 'dashboard-demo.html');
      break;
    case '/login':
      filePath = path.join(__dirname, 'login-demo.html');
      break;
    case '/hr':
      filePath = path.join(__dirname, 'hr-management.html');
      break;
    case '/sales':
      filePath = path.join(__dirname, 'sales-management.html');
      break;
    case '/purchase':
      filePath = path.join(__dirname, 'purchase-management.html');
      break;
    case '/logistics':
      filePath = path.join(__dirname, 'logistics-management.html');
      break;
    case '/stock':
      filePath = path.join(__dirname, 'stock-management.html');
      break;
    case '/accounting':
      filePath = path.join(__dirname, 'accounting-management.html');
      break;
    case '/finance':
      filePath = path.join(__dirname, 'finance-management.html');
      break;
    case '/crm':
      filePath = path.join(__dirname, 'crm-management.html');
      break;
    case '/bi':
      filePath = path.join(__dirname, 'bi-management.html');
      break;
    case '/manager':
      filePath = path.join(__dirname, 'manager-management.html');
      break;
    default:
      filePath = path.join(__dirname, 'dashboard-demo.html');
  }

  // Vérifier si le fichier existe
  if (!fs.existsSync(filePath)) {
    console.error(`❌ Fichier non trouvé: ${filePath}`);
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <h1>404 - Fichier non trouvé</h1>
      <p>Le fichier demandé n'existe pas: ${path.basename(filePath)}</p>
      <p><a href="/dashboard">Retour au Dashboard</a></p>
    `);
    return;
  }

  // Lire et servir le fichier
  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      console.error(`❌ Erreur lecture fichier: ${err.message}`);
      res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(`
        <h1>500 - Erreur serveur</h1>
        <p>Impossible de lire le fichier: ${err.message}</p>
        <p><a href="/dashboard">Retour au Dashboard</a></p>
      `);
      return;
    }

    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(data);
    console.log(`✅ Fichier servi: ${path.basename(filePath)}`);
  });
});

server.on('error', (err) => {
  console.error('❌ Erreur serveur:', err.message);
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ Le port ${PORT} est déjà utilisé. Essayez de fermer les autres applications.`);
  }
});

server.listen(PORT, '127.0.0.1', () => {
  console.log('\n🎉 ===============================================');
  console.log('🚀 ERP HUB SERVER DÉMARRÉ AVEC SUCCÈS !');
  console.log('🎉 ===============================================');
  console.log(`📍 URL: http://localhost:${PORT}`);
  console.log('');
  console.log('📋 AGENTS ERP DISPONIBLES:');
  console.log('🏠 Dashboard: http://localhost:3000/dashboard');
  console.log('🔐 Login: http://localhost:3000/login');
  console.log('👥 HR: http://localhost:3000/hr');
  console.log('📈 Sales: http://localhost:3000/sales');
  console.log('🛒 Purchase: http://localhost:3000/purchase');
  console.log('🚚 Logistics: http://localhost:3000/logistics');
  console.log('📦 Stock: http://localhost:3000/stock');
  console.log('🏦 Accounting: http://localhost:3000/accounting');
  console.log('💰 Finance: http://localhost:3000/finance');
  console.log('🤝 CRM: http://localhost:3000/crm');
  console.log('📊 BI: http://localhost:3000/bi');
  console.log('👨‍💼 Manager: http://localhost:3000/manager');
  console.log('');
  console.log('✅ AMÉLIORATIONS DATES: 100% COMPLÈTES !');
  console.log('📅 Formatage français DD/MM/YYYY');
  console.log('⏰ Horodatage automatique');
  console.log('🔧 Bibliothèque date-utils.js');
  console.log('');
  console.log('🌟 Votre ERP HUB est prêt !');
  console.log('🎉 ===============================================\n');
});

process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur...');
  server.close(() => {
    console.log('✅ Serveur arrêté');
    process.exit(0);
  });
});
