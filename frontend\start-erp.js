const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

const server = http.createServer((req, res) => {
  console.log(`📥 Requête: ${req.method} ${req.url}`);
  
  // Headers CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  let filePath;
  
  // Routes de l'ERP
  switch (req.url) {
    case '/':
    case '/dashboard':
      filePath = path.join(__dirname, 'dashboard-demo.html');
      break;
    case '/login':
      filePath = path.join(__dirname, 'login-demo.html');
      break;
    case '/hr':
      filePath = path.join(__dirname, 'hr-management.html');
      break;
    case '/sales':
      filePath = path.join(__dirname, 'sales-management.html');
      break;
    case '/purchase':
      filePath = path.join(__dirname, 'purchase-management.html');
      break;
    case '/logistics':
      filePath = path.join(__dirname, 'logistics-management.html');
      break;
    case '/stock':
      filePath = path.join(__dirname, 'stock-management.html');
      break;
    case '/accounting':
      filePath = path.join(__dirname, 'accounting-management.html');
      break;
    case '/finance':
      filePath = path.join(__dirname, 'finance-management.html');
      break;
    case '/crm':
      filePath = path.join(__dirname, 'crm-management.html');
      break;
    case '/bi':
      filePath = path.join(__dirname, 'bi-management.html');
      break;
    case '/manager':
      filePath = path.join(__dirname, 'manager-management.html');
      break;
    default:
      filePath = path.join(__dirname, 'dashboard-demo.html');
  }

  // Lire et servir le fichier
  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      console.error(`❌ Erreur lecture fichier: ${err.message}`);
      res.writeHead(404, { 'Content-Type': 'text/html' });
      res.end('<h1>404 - Page non trouvée</h1>');
      return;
    }

    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(data);
    console.log(`✅ Fichier servi: ${path.basename(filePath)}`);
  });
});

server.on('error', (err) => {
  console.error('❌ Erreur serveur:', err.message);
});

server.listen(PORT, '127.0.0.1', () => {
  console.log('\n🎉 ===============================================');
  console.log('🚀 ERP HUB SERVER DÉMARRÉ !');
  console.log('🎉 ===============================================');
  console.log(`📍 URL: http://localhost:${PORT}`);
  console.log('');
  console.log('📋 AGENTS ERP DISPONIBLES:');
  console.log('🏠 Dashboard: http://localhost:3000/dashboard');
  console.log('🔐 Login: http://localhost:3000/login');
  console.log('👥 HR: http://localhost:3000/hr');
  console.log('📈 Sales: http://localhost:3000/sales');
  console.log('🛒 Purchase: http://localhost:3000/purchase');
  console.log('🚚 Logistics: http://localhost:3000/logistics');
  console.log('📦 Stock: http://localhost:3000/stock');
  console.log('🏦 Accounting: http://localhost:3000/accounting');
  console.log('💰 Finance: http://localhost:3000/finance');
  console.log('🤝 CRM: http://localhost:3000/crm');
  console.log('📊 BI: http://localhost:3000/bi');
  console.log('👨‍💼 Manager: http://localhost:3000/manager');
  console.log('');
  console.log('✅ AMÉLIORATIONS DATES: 100% COMPLÈTES !');
  console.log('📅 Formatage français DD/MM/YYYY');
  console.log('⏰ Horodatage automatique');
  console.log('🔧 Bibliothèque date-utils.js');
  console.log('');
  console.log('🌟 Votre ERP HUB est prêt !');
  console.log('🎉 ===============================================\n');
});

process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur...');
  server.close(() => {
    console.log('✅ Serveur arrêté');
    process.exit(0);
  });
});
