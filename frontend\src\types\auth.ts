export interface User {
  id: string
  username: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  avatar?: string
  birth_date?: string
  language: string
  timezone: string
  theme: 'light' | 'dark'
  is_tenant_admin: boolean
  tenant_info: Tenant
  roles: UserRole[]
  permissions: Record<string, string[]>
  last_login?: string
  date_joined: string
}

export interface Tenant {
  id: string
  name: string
  slug: string
  description?: string
  email?: string
  phone?: string
  website?: string
  address_line1?: string
  address_line2?: string
  city?: string
  postal_code?: string
  country?: string
  is_active: boolean
  max_users: number
  enabled_modules: string[]
  created_at: string
  updated_at: string
}

export interface Role {
  id: string
  name: string
  description?: string
  is_system_role: boolean
  permissions: Record<string, string[]>
  created_at: string
  updated_at: string
}

export interface UserRole {
  id: string
  role: string
  role_name: string
  role_description?: string
  is_active: boolean
  assigned_by?: string
  created_at: string
  updated_at: string
}

export interface LoginCredentials {
  username: string
  password: string
  tenant_slug?: string
}

export interface LoginResponse {
  access: string
  refresh: string
  user: User
}

export interface RefreshTokenResponse {
  access: string
  refresh?: string
}

export interface ChangePasswordData {
  old_password: string
  new_password: string
  new_password_confirm: string
}

export interface UserCreateData {
  username: string
  email: string
  password: string
  password_confirm: string
  first_name: string
  last_name: string
  phone?: string
  language?: string
  timezone?: string
}

export interface UserUpdateData {
  first_name?: string
  last_name?: string
  email?: string
  phone?: string
  avatar?: File
  birth_date?: string
  language?: string
  timezone?: string
  theme?: 'light' | 'dark'
}
