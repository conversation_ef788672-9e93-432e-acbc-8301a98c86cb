"""
Modèles pour l'Agent Purchase - Gestion des Achats
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal

from core.models import TimeStampedModel, UUIDModel, Tenant, User


class Supplier(UUIDModel, TimeStampedModel):
    """Modèle pour les fournisseurs"""

    SUPPLIER_TYPES = [
        ('manufacturer', _('Fabricant')),
        ('distributor', _('Distributeur')),
        ('service_provider', _('Prestataire de services')),
        ('consultant', _('Consultant')),
    ]

    PAYMENT_TERMS = [
        (0, _('Comptant')),
        (15, _('15 jours')),
        (30, _('30 jours')),
        (45, _('45 jours')),
        (60, _('60 jours')),
        (90, _('90 jours')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='suppliers',
        verbose_name=_("Tenant")
    )

    # Informations de base
    supplier_code = models.CharField(_("Code fournisseur"), max_length=20, unique=True)
    supplier_type = models.CharField(_("Type de fournisseur"), max_length=20, choices=SUPPLIER_TYPES)

    # Informations contact
    name = models.CharField(_("Nom/Raison sociale"), max_length=200)
    contact_person = models.CharField(_("Personne de contact"), max_length=100, blank=True)
    email = models.EmailField(_("Email"), blank=True)
    phone = models.CharField(_("Téléphone"), max_length=20, blank=True)
    website = models.URLField(_("Site web"), blank=True)

    # Adresse
    address_line1 = models.CharField(_("Adresse ligne 1"), max_length=200, blank=True)
    address_line2 = models.CharField(_("Adresse ligne 2"), max_length=200, blank=True)
    city = models.CharField(_("Ville"), max_length=100, blank=True)
    postal_code = models.CharField(_("Code postal"), max_length=20, blank=True)
    country = models.CharField(_("Pays"), max_length=100, blank=True)

    # Conditions commerciales
    payment_terms = models.PositiveIntegerField(
        _("Délai de paiement (jours)"),
        choices=PAYMENT_TERMS,
        default=30
    )
    currency = models.CharField(_("Devise"), max_length=3, default='EUR')

    # Évaluation fournisseur
    quality_rating = models.FloatField(
        _("Note qualité"),
        validators=[MinValueValidator(0), MaxValueValidator(5)],
        null=True,
        blank=True
    )
    delivery_rating = models.FloatField(
        _("Note livraison"),
        validators=[MinValueValidator(0), MaxValueValidator(5)],
        null=True,
        blank=True
    )
    service_rating = models.FloatField(
        _("Note service"),
        validators=[MinValueValidator(0), MaxValueValidator(5)],
        null=True,
        blank=True
    )

    # Acheteur assigné
    buyer = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_suppliers',
        verbose_name=_("Acheteur")
    )

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)
    is_approved = models.BooleanField(_("Approuvé"), default=False)

    # Informations bancaires
    bank_name = models.CharField(_("Banque"), max_length=100, blank=True)
    bank_account = models.CharField(_("Compte bancaire"), max_length=50, blank=True)

    class Meta:
        verbose_name = _("Fournisseur")
        verbose_name_plural = _("Fournisseurs")
        unique_together = ['tenant', 'supplier_code']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.supplier_code})"

    @property
    def overall_rating(self):
        """Calcule la note globale du fournisseur"""
        ratings = [r for r in [self.quality_rating, self.delivery_rating, self.service_rating] if r is not None]
        return sum(ratings) / len(ratings) if ratings else None


class ProductCategory(UUIDModel, TimeStampedModel):
    """Modèle pour les catégories de produits d'achat"""

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='purchase_categories',
        verbose_name=_("Tenant")
    )

    name = models.CharField(_("Nom"), max_length=100)
    code = models.CharField(_("Code"), max_length=20)
    description = models.TextField(_("Description"), blank=True)

    # Hiérarchie
    parent_category = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='sub_categories',
        verbose_name=_("Catégorie parent")
    )

    # Acheteur responsable
    buyer = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_categories',
        verbose_name=_("Acheteur responsable")
    )

    is_active = models.BooleanField(_("Actif"), default=True)

    class Meta:
        verbose_name = _("Catégorie d'achat")
        verbose_name_plural = _("Catégories d'achat")
        unique_together = ['tenant', 'code']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class PurchaseRequest(UUIDModel, TimeStampedModel):
    """Modèle pour les demandes d'achat"""

    STATUS_CHOICES = [
        ('draft', _('Brouillon')),
        ('submitted', _('Soumise')),
        ('approved', _('Approuvée')),
        ('rejected', _('Rejetée')),
        ('ordered', _('Commandée')),
        ('cancelled', _('Annulée')),
    ]

    PRIORITY_CHOICES = [
        (1, _('Très basse')),
        (2, _('Basse')),
        (3, _('Normale')),
        (4, _('Haute')),
        (5, _('Urgente')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='purchase_requests',
        verbose_name=_("Tenant")
    )

    # Numérotation
    request_number = models.CharField(_("Numéro de demande"), max_length=50, unique=True)

    # Demandeur
    requester = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='purchase_requests',
        verbose_name=_("Demandeur")
    )

    # Détails de la demande
    title = models.CharField(_("Titre"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    justification = models.TextField(_("Justification"), blank=True)

    # Dates
    requested_date = models.DateField(_("Date de demande"))
    required_date = models.DateField(_("Date souhaitée"))

    # Priorité et statut
    priority = models.PositiveIntegerField(_("Priorité"), choices=PRIORITY_CHOICES, default=3)
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='draft')

    # Budget estimé
    estimated_total = models.DecimalField(
        _("Total estimé"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Approbation
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_purchase_requests',
        verbose_name=_("Approuvé par")
    )
    approved_at = models.DateTimeField(_("Approuvé le"), null=True, blank=True)
    rejection_reason = models.TextField(_("Motif de rejet"), blank=True)

    # Acheteur assigné
    buyer = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_purchase_requests',
        verbose_name=_("Acheteur assigné")
    )

    class Meta:
        verbose_name = _("Demande d'achat")
        verbose_name_plural = _("Demandes d'achat")
        ordering = ['-created_at']

    def __str__(self):
        return f"DA {self.request_number} - {self.title}"


class PurchaseRequestItem(UUIDModel, TimeStampedModel):
    """Modèle pour les lignes de demande d'achat"""

    purchase_request = models.ForeignKey(
        PurchaseRequest,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_("Demande d'achat")
    )

    # Produit/Service
    item_name = models.CharField(_("Nom de l'article"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    category = models.ForeignKey(
        ProductCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='request_items',
        verbose_name=_("Catégorie")
    )

    # Quantité et unité
    quantity = models.DecimalField(_("Quantité"), max_digits=10, decimal_places=3)
    unit_of_measure = models.CharField(_("Unité de mesure"), max_length=20, default='pcs')

    # Prix estimé
    estimated_unit_price = models.DecimalField(
        _("Prix unitaire estimé"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    estimated_total = models.DecimalField(
        _("Total estimé"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Fournisseur suggéré
    suggested_supplier = models.ForeignKey(
        Supplier,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='suggested_items',
        verbose_name=_("Fournisseur suggéré")
    )

    # Spécifications techniques
    specifications = models.JSONField(_("Spécifications"), default=dict, blank=True)

    class Meta:
        verbose_name = _("Ligne de demande d'achat")
        verbose_name_plural = _("Lignes de demande d'achat")
        ordering = ['id']

    def save(self, *args, **kwargs):
        """Calcule automatiquement le total estimé"""
        if self.estimated_unit_price:
            self.estimated_total = self.quantity * self.estimated_unit_price
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item_name} x {self.quantity}"


class PurchaseOrder(UUIDModel, TimeStampedModel):
    """Modèle pour les bons de commande"""

    STATUS_CHOICES = [
        ('draft', _('Brouillon')),
        ('sent', _('Envoyé')),
        ('confirmed', _('Confirmé')),
        ('partially_received', _('Partiellement reçu')),
        ('received', _('Reçu')),
        ('invoiced', _('Facturé')),
        ('closed', _('Clôturé')),
        ('cancelled', _('Annulé')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='purchase_orders',
        verbose_name=_("Tenant")
    )

    # Numérotation
    order_number = models.CharField(_("Numéro de commande"), max_length=50, unique=True)

    # Fournisseur
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.PROTECT,
        related_name='purchase_orders',
        verbose_name=_("Fournisseur")
    )

    # Demande d'achat liée
    purchase_request = models.ForeignKey(
        PurchaseRequest,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='purchase_orders',
        verbose_name=_("Demande d'achat")
    )

    # Acheteur
    buyer = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='purchase_orders',
        verbose_name=_("Acheteur")
    )

    # Dates
    order_date = models.DateField(_("Date de commande"))
    expected_delivery_date = models.DateField(_("Date de livraison prévue"))
    actual_delivery_date = models.DateField(_("Date de livraison réelle"), null=True, blank=True)

    # Statut
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='draft')

    # Totaux (calculés automatiquement)
    subtotal = models.DecimalField(
        _("Sous-total"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )
    tax_amount = models.DecimalField(
        _("Montant TVA"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_amount = models.DecimalField(
        _("Montant total"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Conditions
    payment_terms = models.PositiveIntegerField(_("Délai de paiement (jours)"), default=30)
    delivery_address = models.TextField(_("Adresse de livraison"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    terms_conditions = models.TextField(_("Conditions générales"), blank=True)

    class Meta:
        verbose_name = _("Bon de commande")
        verbose_name_plural = _("Bons de commande")
        ordering = ['-order_date']

    def __str__(self):
        return f"BC {self.order_number} - {self.supplier.name}"


class PurchaseOrderItem(UUIDModel, TimeStampedModel):
    """Modèle pour les lignes de bon de commande"""

    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_("Bon de commande")
    )

    # Référence à la ligne de demande d'achat
    request_item = models.ForeignKey(
        PurchaseRequestItem,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='order_items',
        verbose_name=_("Ligne de demande")
    )

    # Produit/Service
    item_name = models.CharField(_("Nom de l'article"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    supplier_reference = models.CharField(_("Référence fournisseur"), max_length=100, blank=True)

    # Quantité et unité
    quantity_ordered = models.DecimalField(_("Quantité commandée"), max_digits=10, decimal_places=3)
    quantity_received = models.DecimalField(
        _("Quantité reçue"),
        max_digits=10,
        decimal_places=3,
        default=Decimal('0.000')
    )
    unit_of_measure = models.CharField(_("Unité de mesure"), max_length=20, default='pcs')

    # Prix
    unit_price = models.DecimalField(_("Prix unitaire"), max_digits=10, decimal_places=2)
    discount_percentage = models.DecimalField(
        _("Remise (%)"),
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00')
    )
    line_total = models.DecimalField(
        _("Total ligne"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Dates
    expected_delivery_date = models.DateField(_("Date de livraison prévue"), null=True, blank=True)

    class Meta:
        verbose_name = _("Ligne de bon de commande")
        verbose_name_plural = _("Lignes de bon de commande")
        ordering = ['id']

    def save(self, *args, **kwargs):
        """Calcule automatiquement le total de la ligne"""
        subtotal = self.quantity_ordered * self.unit_price
        discount_amount = subtotal * (self.discount_percentage / 100)
        self.line_total = subtotal - discount_amount
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item_name} x {self.quantity_ordered}"

    @property
    def quantity_pending(self):
        """Quantité restant à recevoir"""
        return self.quantity_ordered - self.quantity_received

    @property
    def is_fully_received(self):
        """Vérifie si la ligne est complètement reçue"""
        return self.quantity_received >= self.quantity_ordered


class GoodsReceipt(UUIDModel, TimeStampedModel):
    """Modèle pour les réceptions de marchandises"""

    STATUS_CHOICES = [
        ('draft', _('Brouillon')),
        ('confirmed', _('Confirmée')),
        ('quality_check', _('Contrôle qualité')),
        ('accepted', _('Acceptée')),
        ('rejected', _('Rejetée')),
        ('partially_accepted', _('Partiellement acceptée')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='goods_receipts',
        verbose_name=_("Tenant")
    )

    # Numérotation
    receipt_number = models.CharField(_("Numéro de réception"), max_length=50, unique=True)

    # Bon de commande lié
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.PROTECT,
        related_name='goods_receipts',
        verbose_name=_("Bon de commande")
    )

    # Dates
    receipt_date = models.DateField(_("Date de réception"))
    delivery_note_number = models.CharField(_("Numéro bon de livraison"), max_length=50, blank=True)

    # Responsable réception
    received_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='goods_receipts',
        verbose_name=_("Reçu par")
    )

    # Statut et contrôle
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='draft')
    quality_check_passed = models.BooleanField(_("Contrôle qualité OK"), null=True, blank=True)

    # Commentaires
    comments = models.TextField(_("Commentaires"), blank=True)
    quality_notes = models.TextField(_("Notes qualité"), blank=True)

    class Meta:
        verbose_name = _("Réception de marchandises")
        verbose_name_plural = _("Réceptions de marchandises")
        ordering = ['-receipt_date']

    def __str__(self):
        return f"REC {self.receipt_number} - BC {self.purchase_order.order_number}"


class GoodsReceiptItem(UUIDModel, TimeStampedModel):
    """Modèle pour les lignes de réception"""

    goods_receipt = models.ForeignKey(
        GoodsReceipt,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_("Réception")
    )

    # Ligne de commande liée
    order_item = models.ForeignKey(
        PurchaseOrderItem,
        on_delete=models.PROTECT,
        related_name='receipt_items',
        verbose_name=_("Ligne de commande")
    )

    # Quantités
    quantity_expected = models.DecimalField(_("Quantité attendue"), max_digits=10, decimal_places=3)
    quantity_received = models.DecimalField(_("Quantité reçue"), max_digits=10, decimal_places=3)
    quantity_accepted = models.DecimalField(
        _("Quantité acceptée"),
        max_digits=10,
        decimal_places=3,
        default=Decimal('0.000')
    )
    quantity_rejected = models.DecimalField(
        _("Quantité rejetée"),
        max_digits=10,
        decimal_places=3,
        default=Decimal('0.000')
    )

    # Contrôle qualité
    quality_status = models.CharField(
        _("Statut qualité"),
        max_length=20,
        choices=[
            ('pending', _('En attente')),
            ('passed', _('Conforme')),
            ('failed', _('Non conforme')),
            ('partial', _('Partiellement conforme')),
        ],
        default='pending'
    )

    # Commentaires
    comments = models.TextField(_("Commentaires"), blank=True)
    rejection_reason = models.TextField(_("Motif de rejet"), blank=True)

    class Meta:
        verbose_name = _("Ligne de réception")
        verbose_name_plural = _("Lignes de réception")
        ordering = ['id']

    def save(self, *args, **kwargs):
        """Valide les quantités"""
        if self.quantity_accepted + self.quantity_rejected != self.quantity_received:
            # Auto-ajustement si pas de rejet spécifié
            if self.quantity_rejected == 0:
                self.quantity_accepted = self.quantity_received
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.order_item.item_name} - Reçu: {self.quantity_received}"


class SupplierInvoice(UUIDModel, TimeStampedModel):
    """Modèle pour les factures fournisseurs"""

    STATUS_CHOICES = [
        ('received', _('Reçue')),
        ('validated', _('Validée')),
        ('approved', _('Approuvée')),
        ('paid', _('Payée')),
        ('disputed', _('Contestée')),
        ('cancelled', _('Annulée')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='supplier_invoices',
        verbose_name=_("Tenant")
    )

    # Numérotation
    invoice_number = models.CharField(_("Numéro de facture"), max_length=50)
    supplier_invoice_number = models.CharField(_("Numéro facture fournisseur"), max_length=50)

    # Fournisseur et commande
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.PROTECT,
        related_name='invoices',
        verbose_name=_("Fournisseur")
    )
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.PROTECT,
        related_name='invoices',
        verbose_name=_("Bon de commande")
    )

    # Dates
    invoice_date = models.DateField(_("Date de facture"))
    due_date = models.DateField(_("Date d'échéance"))
    received_date = models.DateField(_("Date de réception"))

    # Montants
    subtotal = models.DecimalField(_("Sous-total"), max_digits=12, decimal_places=2)
    tax_amount = models.DecimalField(_("Montant TVA"), max_digits=12, decimal_places=2)
    total_amount = models.DecimalField(_("Montant total"), max_digits=12, decimal_places=2)

    # Statut et validation
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='received')
    validated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='validated_invoices',
        verbose_name=_("Validé par")
    )
    validated_at = models.DateTimeField(_("Validé le"), null=True, blank=True)

    # Paiement
    payment_date = models.DateField(_("Date de paiement"), null=True, blank=True)
    payment_reference = models.CharField(_("Référence paiement"), max_length=100, blank=True)

    # Commentaires
    comments = models.TextField(_("Commentaires"), blank=True)
    dispute_reason = models.TextField(_("Motif de contestation"), blank=True)

    class Meta:
        verbose_name = _("Facture fournisseur")
        verbose_name_plural = _("Factures fournisseurs")
        unique_together = ['tenant', 'invoice_number']
        ordering = ['-invoice_date']

    def __str__(self):
        return f"Facture {self.supplier_invoice_number} - {self.supplier.name}"
