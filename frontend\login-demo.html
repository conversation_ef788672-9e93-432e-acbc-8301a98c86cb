<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .login-container {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            width: 100%;
            max-width: 400px;
            position: relative;
        }

        .back-button {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }

        .back-button:hover {
            background: #f3f4f6;
            color: #667eea;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: #6b7280;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.25rem;
        }

        .login-button {
            width: 100%;
            background: linear-gradient(45deg, #667eea 30%, #764ba2 90%);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 0.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 2rem;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .demo-accounts {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .demo-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .demo-account:last-child {
            border-bottom: none;
        }

        .demo-info {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .demo-credentials {
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.75rem;
            color: #374151;
            font-weight: 500;
        }

        .quick-login {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s;
        }

        .quick-login:hover {
            background: #5a67d8;
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 0.75rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .success-message {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
            padding: 0.75rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .register-link {
            text-align: center;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .register-link a:hover {
            text-decoration: underline;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 2rem;
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <button class="back-button" onclick="goBack()">
            <span class="material-icons">arrow_back</span>
        </button>

        <div class="login-header">
            <div class="login-icon">🔐</div>
            <h1 class="login-title">Connexion</h1>
            <p class="login-subtitle">Accédez à votre espace ERP HUB</p>
        </div>

        <div id="message-container"></div>

        <form id="loginForm">
            <div class="form-group">
                <label class="form-label" for="username">Nom d'utilisateur</label>
                <input
                    type="text"
                    id="username"
                    name="username"
                    class="form-input"
                    required
                    autocomplete="username"
                    placeholder="Entrez votre nom d'utilisateur"
                >
            </div>

            <div class="form-group">
                <label class="form-label" for="password">Mot de passe</label>
                <div class="password-container">
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input"
                        required
                        autocomplete="current-password"
                        placeholder="Entrez votre mot de passe"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        <span class="material-icons" id="passwordIcon">visibility</span>
                    </button>
                </div>
            </div>

            <button type="submit" class="login-button" id="loginButton">
                Se connecter
            </button>
        </form>

        <div class="demo-accounts">
            <div class="demo-title">
                🔑 Comptes de démonstration
            </div>

            <div class="demo-account">
                <div>
                    <div class="demo-info"><strong>Administrateur</strong></div>
                    <div class="demo-credentials">admin / admin123</div>
                </div>
                <button class="quick-login" onclick="quickLogin('admin', 'admin123')">
                    Connexion rapide
                </button>
            </div>

            <div class="demo-account">
                <div>
                    <div class="demo-info"><strong>Manager</strong></div>
                    <div class="demo-credentials">manager / test123</div>
                </div>
                <button class="quick-login" onclick="quickLogin('manager', 'test123')">
                    Connexion rapide
                </button>
            </div>

            <div class="demo-account">
                <div>
                    <div class="demo-info"><strong>RH</strong></div>
                    <div class="demo-credentials">hr_user / test123</div>
                </div>
                <button class="quick-login" onclick="quickLogin('hr_user', 'test123')">
                    Connexion rapide
                </button>
            </div>

            <div class="demo-account">
                <div>
                    <div class="demo-info"><strong>Commercial</strong></div>
                    <div class="demo-credentials">sales_user / test123</div>
                </div>
                <button class="quick-login" onclick="quickLogin('sales_user', 'test123')">
                    Connexion rapide
                </button>
            </div>
        </div>

        <div class="register-link">
            Pas encore de compte ?
            <a href="#" onclick="showRegister()">S'inscrire</a>
        </div>
    </div>

    <script>
        let isLoading = false;

        function showMessage(message, type = 'error') {
            const container = document.getElementById('message-container');
            const className = type === 'success' ? 'success-message' : 'error-message';
            container.innerHTML = `<div class="${className}">${message}</div>`;

            // Auto-hide after 5 seconds
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.textContent = 'visibility_off';
            } else {
                passwordInput.type = 'password';
                passwordIcon.textContent = 'visibility';
            }
        }

        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            handleLogin();
        }

        function setLoading(loading) {
            isLoading = loading;
            const button = document.getElementById('loginButton');
            const form = document.getElementById('loginForm');

            if (loading) {
                button.innerHTML = '<span class="loading"></span> Connexion...';
                button.disabled = true;
                form.style.opacity = '0.7';
            } else {
                button.innerHTML = 'Se connecter';
                button.disabled = false;
                form.style.opacity = '1';
            }
        }

        async function handleLogin() {
            if (isLoading) return;

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showMessage('Veuillez remplir tous les champs');
                return;
            }

            setLoading(true);

            try {
                // Tentative de connexion au backend Django
                const response = await fetch('http://localhost:8000/api/auth/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                if (response.ok) {
                    const data = await response.json();

                    // Stocker les tokens et informations utilisateur
                    localStorage.setItem('access_token', data.access);
                    localStorage.setItem('refresh_token', data.refresh);
                    localStorage.setItem('isAuthenticated', 'true');
                    localStorage.setItem('username', username);
                    localStorage.setItem('user_data', JSON.stringify(data.user || {}));
                    localStorage.setItem('loginTime', new Date().toISOString());

                    showMessage('Connexion réussie ! Redirection...', 'success');

                    // Redirection vers le dashboard
                    setTimeout(() => {
                        window.location.href = 'dashboard-demo.html';
                    }, 1500);

                } else {
                    const errorData = await response.json();
                    showMessage(errorData.detail || 'Nom d\'utilisateur ou mot de passe incorrect');
                }

            } catch (error) {
                console.error('Erreur de connexion:', error);

                // Fallback : connexion locale pour les comptes de démonstration
                const validAccounts = {
                    'admin': 'admin123',
                    'manager': 'test123',
                    'hr_user': 'test123',
                    'sales_user': 'test123'
                };

                if (validAccounts[username] === password) {
                    // Stocker les informations de connexion locale
                    localStorage.setItem('isAuthenticated', 'true');
                    localStorage.setItem('username', username);
                    localStorage.setItem('user_data', JSON.stringify({
                        username: username,
                        first_name: username.charAt(0).toUpperCase() + username.slice(1),
                        role: username === 'admin' ? 'Administrateur' : 'Utilisateur'
                    }));
                    localStorage.setItem('loginTime', new Date().toISOString());

                    showMessage('Connexion réussie ! Redirection...', 'success');

                    setTimeout(() => {
                        window.location.href = 'dashboard-demo.html';
                    }, 1500);
                } else {
                    showMessage('Erreur de connexion. Vérifiez vos identifiants.');
                }
            } finally {
                setLoading(false);
            }
        }

        function goBack() {
            window.history.back() || (window.location.href = 'homepage-demo.html');
        }

        function showRegister() {
            alert('Fonctionnalité d\'inscription en cours de développement');
        }

        // Gestion du formulaire
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            handleLogin();
        });

        // Auto-focus sur le champ username
        document.getElementById('username').focus();

        // Vérifier si déjà connecté
        if (localStorage.getItem('isAuthenticated') === 'true') {
            showMessage('Vous êtes déjà connecté. Redirection...', 'success');
            setTimeout(() => {
                window.location.href = 'http://localhost:8080/dashboard';
            }, 2000);
        }
    </script>
</body>
</html>
