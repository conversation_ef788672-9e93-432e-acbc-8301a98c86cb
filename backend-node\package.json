{"name": "erp-hub-backend", "version": "1.0.0", "description": "Backend Node.js pour ERP HUB - Système Multi-Agents", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "build": "npm install"}, "keywords": ["erp", "nodejs", "agents", "scalable"], "author": "ERP HUB Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "socket.io": "^4.7.4", "redis": "^4.6.10", "mongoose": "^8.0.3", "joi": "^17.11.0", "compression": "^1.7.4", "rate-limiter-flexible": "^3.0.8", "winston": "^3.11.0", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}