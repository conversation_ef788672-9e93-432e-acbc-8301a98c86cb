"""
Services pour l'Agent HR
Logique métier pour la gestion des ressources humaines
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum
from django.db import transaction

from core.models import Tenant, User
from agents.models import Agent
from agents.ai_service import ai_service
from .models import (
    Department, Position, Employee, LeaveType, LeaveRequest,
    PerformanceReview, Training, TrainingEnrollment
)

logger = logging.getLogger('agents.hr')


class HRService:
    """
    Service principal pour l'Agent HR
    Gère toutes les opérations RH
    """
    
    def __init__(self, tenant: Tenant):
        self.tenant = tenant
        self.hr_agent = self._get_or_create_hr_agent()
    
    def _get_or_create_hr_agent(self) -> Agent:
        """Récupère ou crée l'agent HR pour le tenant"""
        agent, created = Agent.objects.get_or_create(
            tenant=self.tenant,
            agent_type='hr',
            defaults={
                'name': 'Agent RH',
                'description': 'Gestion des ressources humaines',
                'ai_enabled': True,
                'ai_model': 'gpt-4',
                'capabilities': [
                    'employee_management',
                    'leave_management',
                    'performance_evaluation',
                    'training_coordination',
                    'recruitment_support',
                    'hr_analytics'
                ]
            }
        )
        if created:
            logger.info(f"Agent HR créé pour le tenant {self.tenant.name}")
        return agent
    
    def get_hr_dashboard(self) -> Dict[str, Any]:
        """Retourne les données du dashboard RH"""
        
        # Statistiques des employés
        employees = Employee.objects.filter(user__tenant=self.tenant)
        active_employees = employees.filter(employment_status='active')
        
        # Demandes de congés en attente
        pending_leaves = LeaveRequest.objects.filter(
            employee__user__tenant=self.tenant,
            status='pending'
        )
        
        # Formations en cours
        ongoing_trainings = Training.objects.filter(
            tenant=self.tenant,
            status='ongoing'
        )
        
        # Évaluations à faire
        pending_reviews = PerformanceReview.objects.filter(
            employee__user__tenant=self.tenant,
            status__in=['draft', 'in_progress']
        )
        
        # Départements
        departments = Department.objects.filter(tenant=self.tenant, is_active=True)
        
        # Calculs de métriques
        total_employees = employees.count()
        active_count = active_employees.count()
        turnover_rate = self._calculate_turnover_rate()
        avg_performance = active_employees.aggregate(
            avg_rating=Avg('performance_rating')
        )['avg_rating'] or 0
        
        return {
            'tenant': self.tenant.name,
            'timestamp': timezone.now().isoformat(),
            'employees': {
                'total': total_employees,
                'active': active_count,
                'inactive': total_employees - active_count,
                'turnover_rate': turnover_rate,
                'avg_performance': round(avg_performance, 2)
            },
            'departments': {
                'total': departments.count(),
                'list': [{'id': str(d.id), 'name': d.name, 'employee_count': d.positions.aggregate(
                    count=Count('employees', filter=Q(employees__employment_status='active'))
                )['count']} for d in departments]
            },
            'pending_actions': {
                'leave_requests': pending_leaves.count(),
                'performance_reviews': pending_reviews.count(),
                'ongoing_trainings': ongoing_trainings.count()
            },
            'recent_activities': self._get_recent_activities()
        }
    
    def _calculate_turnover_rate(self) -> float:
        """Calcule le taux de rotation des employés"""
        current_year = timezone.now().year
        start_of_year = timezone.datetime(current_year, 1, 1).date()
        
        # Employés actifs en début d'année
        employees_start = Employee.objects.filter(
            user__tenant=self.tenant,
            hire_date__lt=start_of_year
        ).exclude(
            termination_date__lt=start_of_year
        ).count()
        
        # Employés qui ont quitté cette année
        employees_left = Employee.objects.filter(
            user__tenant=self.tenant,
            termination_date__gte=start_of_year,
            termination_date__year=current_year
        ).count()
        
        if employees_start == 0:
            return 0.0
        
        return (employees_left / employees_start) * 100
    
    def _get_recent_activities(self) -> List[Dict[str, Any]]:
        """Récupère les activités récentes"""
        activities = []
        
        # Demandes de congés récentes
        recent_leaves = LeaveRequest.objects.filter(
            employee__user__tenant=self.tenant,
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:5]
        
        for leave in recent_leaves:
            activities.append({
                'type': 'leave_request',
                'description': f"Demande de congé: {leave.employee.user.get_full_name()}",
                'date': leave.created_at.isoformat(),
                'status': leave.status
            })
        
        # Nouvelles embauches
        new_hires = Employee.objects.filter(
            user__tenant=self.tenant,
            hire_date__gte=timezone.now().date() - timedelta(days=30)
        ).order_by('-hire_date')[:3]
        
        for hire in new_hires:
            activities.append({
                'type': 'new_hire',
                'description': f"Nouvelle embauche: {hire.user.get_full_name()}",
                'date': hire.created_at.isoformat(),
                'status': 'active'
            })
        
        return sorted(activities, key=lambda x: x['date'], reverse=True)[:10]
    
    def process_leave_request(self, leave_request_id: str, action: str, 
                            approved_by: User, comments: str = "") -> Dict[str, Any]:
        """Traite une demande de congé"""
        try:
            leave_request = LeaveRequest.objects.get(
                id=leave_request_id,
                employee__user__tenant=self.tenant
            )
            
            if action == 'approve':
                leave_request.status = 'approved'
                leave_request.approved_by = approved_by
                leave_request.approved_at = timezone.now()
                leave_request.comments = comments
                
                # Vérifier les conflits avec d'autres congés
                conflicts = self._check_leave_conflicts(leave_request)
                if conflicts:
                    return {
                        'success': False,
                        'error': 'Conflits détectés avec d\'autres congés',
                        'conflicts': conflicts
                    }
                
            elif action == 'reject':
                leave_request.status = 'rejected'
                leave_request.rejection_reason = comments
                
            leave_request.save()
            
            # Notification à l'employé (simulation)
            self._notify_employee_leave_decision(leave_request, action)
            
            return {
                'success': True,
                'leave_request': {
                    'id': str(leave_request.id),
                    'status': leave_request.status,
                    'employee': leave_request.employee.user.get_full_name()
                }
            }
            
        except LeaveRequest.DoesNotExist:
            return {
                'success': False,
                'error': 'Demande de congé non trouvée'
            }
    
    def _check_leave_conflicts(self, leave_request: LeaveRequest) -> List[Dict[str, Any]]:
        """Vérifie les conflits de congés dans le même département"""
        conflicts = []
        
        # Autres congés approuvés dans le même département et période
        overlapping_leaves = LeaveRequest.objects.filter(
            employee__position__department=leave_request.employee.position.department,
            status='approved',
            start_date__lte=leave_request.end_date,
            end_date__gte=leave_request.start_date
        ).exclude(id=leave_request.id)
        
        for leave in overlapping_leaves:
            conflicts.append({
                'employee': leave.employee.user.get_full_name(),
                'start_date': leave.start_date.isoformat(),
                'end_date': leave.end_date.isoformat(),
                'type': leave.leave_type.name
            })
        
        return conflicts
    
    def _notify_employee_leave_decision(self, leave_request: LeaveRequest, action: str):
        """Notifie l'employé de la décision sur sa demande de congé"""
        # Dans un vrai système, cela enverrait un email ou une notification
        logger.info(f"Notification envoyée à {leave_request.employee.user.email}: "
                   f"Demande de congé {action}")
    
    def create_performance_review(self, employee_id: str, reviewer: User, 
                                review_data: Dict[str, Any]) -> Dict[str, Any]:
        """Crée une nouvelle évaluation de performance"""
        try:
            employee = Employee.objects.get(
                id=employee_id,
                user__tenant=self.tenant
            )
            
            review = PerformanceReview.objects.create(
                employee=employee,
                reviewer=reviewer,
                review_period_start=review_data['review_period_start'],
                review_period_end=review_data['review_period_end'],
                review_type=review_data.get('review_type', 'annual'),
                overall_rating=review_data.get('overall_rating'),
                criteria_scores=review_data.get('criteria_scores', {}),
                strengths=review_data.get('strengths', ''),
                areas_for_improvement=review_data.get('areas_for_improvement', ''),
                goals_next_period=review_data.get('goals_next_period', ''),
                status='draft'
            )
            
            # Générer des suggestions IA si disponible
            if ai_service.is_available():
                ai_suggestions = self._generate_review_suggestions(employee, review_data)
                if ai_suggestions:
                    review.criteria_scores.update(ai_suggestions.get('suggested_scores', {}))
                    review.save()
            
            return {
                'success': True,
                'review': {
                    'id': str(review.id),
                    'employee': employee.user.get_full_name(),
                    'status': review.status
                }
            }
            
        except Employee.DoesNotExist:
            return {
                'success': False,
                'error': 'Employé non trouvé'
            }
    
    def _generate_review_suggestions(self, employee: Employee, review_data: Dict[str, Any]) -> Dict[str, Any]:
        """Génère des suggestions IA pour l'évaluation"""
        try:
            context = {
                'employee_info': {
                    'name': employee.user.get_full_name(),
                    'position': employee.position.title,
                    'department': employee.position.department.name,
                    'hire_date': employee.hire_date.isoformat(),
                    'current_performance': employee.performance_rating,
                    'skills': employee.skills
                },
                'review_period': {
                    'start': review_data['review_period_start'],
                    'end': review_data['review_period_end']
                }
            }
            
            prompt = f"""
            En tant qu'expert RH, analyse le profil de cet employé et suggère des points d'évaluation:
            
            Contexte: {context}
            
            Fournis des suggestions pour:
            1. Points forts potentiels
            2. Axes d'amélioration
            3. Objectifs pour la prochaine période
            4. Score suggéré (1-5) pour chaque critère: communication, travail_equipe, initiative, qualite_travail, respect_delais
            
            Réponds au format JSON.
            """
            
            ai_response = ai_service.generate_response(prompt, "hr", temperature=0.6)
            
            if ai_response.success:
                import json
                return json.loads(ai_response.content)
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération de suggestions IA: {str(e)}")
        
        return {}
    
    def schedule_training(self, training_data: Dict[str, Any]) -> Dict[str, Any]:
        """Programme une nouvelle formation"""
        try:
            training = Training.objects.create(
                tenant=self.tenant,
                title=training_data['title'],
                description=training_data.get('description', ''),
                trainer=training_data.get('trainer', ''),
                location=training_data.get('location', ''),
                is_online=training_data.get('is_online', False),
                start_date=training_data['start_date'],
                end_date=training_data['end_date'],
                duration_hours=training_data['duration_hours'],
                max_participants=training_data.get('max_participants', 20),
                cost_per_participant=training_data.get('cost_per_participant'),
                skills_developed=training_data.get('skills_developed', [])
            )
            
            return {
                'success': True,
                'training': {
                    'id': str(training.id),
                    'title': training.title,
                    'start_date': training.start_date.isoformat()
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def enroll_employee_in_training(self, training_id: str, employee_id: str) -> Dict[str, Any]:
        """Inscrit un employé à une formation"""
        try:
            training = Training.objects.get(id=training_id, tenant=self.tenant)
            employee = Employee.objects.get(id=employee_id, user__tenant=self.tenant)
            
            # Vérifier la capacité
            current_enrollments = training.enrollments.filter(
                status__in=['enrolled', 'attended']
            ).count()
            
            if current_enrollments >= training.max_participants:
                return {
                    'success': False,
                    'error': 'Formation complète'
                }
            
            # Créer l'inscription
            enrollment, created = TrainingEnrollment.objects.get_or_create(
                training=training,
                employee=employee,
                defaults={'status': 'enrolled'}
            )
            
            if not created:
                return {
                    'success': False,
                    'error': 'Employé déjà inscrit'
                }
            
            return {
                'success': True,
                'enrollment': {
                    'id': str(enrollment.id),
                    'employee': employee.user.get_full_name(),
                    'training': training.title
                }
            }
            
        except (Training.DoesNotExist, Employee.DoesNotExist):
            return {
                'success': False,
                'error': 'Formation ou employé non trouvé'
            }
    
    def generate_hr_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights RH basés sur les données"""
        insights = []
        
        # Analyse du turnover
        turnover_rate = self._calculate_turnover_rate()
        if turnover_rate > 15:  # Seuil d'alerte
            insights.append({
                'type': 'turnover_alert',
                'priority': 'high',
                'title': 'Taux de rotation élevé',
                'description': f'Le taux de rotation est de {turnover_rate:.1f}%, ce qui est préoccupant.',
                'recommendation': 'Analyser les causes de départ et améliorer la rétention.'
            })
        
        # Analyse des performances
        low_performers = Employee.objects.filter(
            user__tenant=self.tenant,
            employment_status='active',
            performance_rating__lt=2.5
        ).count()
        
        if low_performers > 0:
            insights.append({
                'type': 'performance_alert',
                'priority': 'medium',
                'title': 'Employés sous-performants',
                'description': f'{low_performers} employé(s) ont une performance inférieure à 2.5/5.',
                'recommendation': 'Mettre en place des plans d\'amélioration et formations.'
            })
        
        # Analyse des formations
        employees_without_training = Employee.objects.filter(
            user__tenant=self.tenant,
            employment_status='active',
            training_enrollments__isnull=True
        ).count()
        
        if employees_without_training > 0:
            insights.append({
                'type': 'training_opportunity',
                'priority': 'low',
                'title': 'Opportunités de formation',
                'description': f'{employees_without_training} employé(s) n\'ont suivi aucune formation.',
                'recommendation': 'Proposer des formations pour développer les compétences.'
            })
        
        return insights
