import React from 'react';
import { motion } from 'framer-motion';
import { Box, Typography, Chip, CircularProgress } from '@mui/material';
import { CheckCircle, Warning, Error, Info, Pending } from '@mui/icons-material';

interface StatusIndicatorProps {
  status: 'active' | 'warning' | 'error' | 'info' | 'pending' | 'loading';
  label?: string;
  size?: 'small' | 'medium' | 'large';
  showIcon?: boolean;
  animated?: boolean;
  onClick?: () => void;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  label,
  size = 'medium',
  showIcon = true,
  animated = true,
  onClick
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'active':
        return {
          color: 'success' as const,
          icon: <CheckCircle />,
          bgColor: '#e8f5e8',
          textColor: '#2e7d32',
          borderColor: '#4caf50'
        };
      case 'warning':
        return {
          color: 'warning' as const,
          icon: <Warning />,
          bgColor: '#fff3e0',
          textColor: '#f57c00',
          borderColor: '#ff9800'
        };
      case 'error':
        return {
          color: 'error' as const,
          icon: <Error />,
          bgColor: '#ffebee',
          textColor: '#d32f2f',
          borderColor: '#f44336'
        };
      case 'info':
        return {
          color: 'info' as const,
          icon: <Info />,
          bgColor: '#e3f2fd',
          textColor: '#1976d2',
          borderColor: '#2196f3'
        };
      case 'pending':
        return {
          color: 'default' as const,
          icon: <Pending />,
          bgColor: '#f5f5f5',
          textColor: '#757575',
          borderColor: '#9e9e9e'
        };
      case 'loading':
        return {
          color: 'primary' as const,
          icon: <CircularProgress size={16} />,
          bgColor: '#e3f2fd',
          textColor: '#1976d2',
          borderColor: '#2196f3'
        };
      default:
        return {
          color: 'default' as const,
          icon: <Info />,
          bgColor: '#f5f5f5',
          textColor: '#757575',
          borderColor: '#9e9e9e'
        };
    }
  };

  const config = getStatusConfig();

  const pulseAnimation = {
    scale: [1, 1.05, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  const Component = motion.div;

  return (
    <Component
      animate={animated && status === 'active' ? pulseAnimation : {}}
      whileHover={{ scale: onClick ? 1.05 : 1 }}
      onClick={onClick}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      {label ? (
        <Chip
          icon={showIcon ? config.icon : undefined}
          label={label}
          color={config.color}
          size={size}
          sx={{
            backgroundColor: config.bgColor,
            color: config.textColor,
            border: `1px solid ${config.borderColor}`,
            '& .MuiChip-icon': {
              color: config.textColor
            }
          }}
        />
      ) : (
        <Box
          sx={{
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: size === 'small' ? 24 : size === 'large' ? 40 : 32,
            height: size === 'small' ? 24 : size === 'large' ? 40 : 32,
            borderRadius: '50%',
            backgroundColor: config.bgColor,
            border: `2px solid ${config.borderColor}`,
            color: config.textColor
          }}
        >
          {showIcon && React.cloneElement(config.icon, {
            fontSize: size === 'small' ? 'small' : size === 'large' ? 'large' : 'medium'
          })}
        </Box>
      )}
    </Component>
  );
};

// Composant pour afficher plusieurs statuts
interface StatusGroupProps {
  statuses: Array<{
    id: string;
    status: StatusIndicatorProps['status'];
    label: string;
    count?: number;
  }>;
  orientation?: 'horizontal' | 'vertical';
  spacing?: number;
}

export const StatusGroup: React.FC<StatusGroupProps> = ({
  statuses,
  orientation = 'horizontal',
  spacing = 1
}) => {
  return (
    <Box
      display="flex"
      flexDirection={orientation === 'horizontal' ? 'row' : 'column'}
      gap={spacing}
      flexWrap="wrap"
    >
      {statuses.map((item) => (
        <Box key={item.id} display="flex" alignItems="center" gap={1}>
          <StatusIndicator status={item.status} />
          <Typography variant="body2">
            {item.label}
            {item.count !== undefined && (
              <Typography component="span" variant="body2" fontWeight={600} ml={0.5}>
                ({item.count})
              </Typography>
            )}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};
