"""
Serializers pour l'Agent Logistics
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
from .models import (
    Carrier, CarrierRate, Shipment, ShipmentItem, ShipmentPackage,
    ShipmentTracking, Route, RouteStop, DeliveryAttempt
)

User = get_user_model()


class CarrierSerializer(serializers.ModelSerializer):
    """Serializer pour les transporteurs"""
    account_manager_name = serializers.CharField(source='account_manager.get_full_name', read_only=True)
    overall_rating = serializers.ReadOnlyField()
    
    class Meta:
        model = Carrier
        fields = [
            'id', 'carrier_code', 'name', 'carrier_type', 'service_level',
            'contact_person', 'email', 'phone', 'website',
            'address_line1', 'address_line2', 'city', 'postal_code', 'country',
            'max_weight', 'max_volume', 'service_zones', 'international_service',
            'reliability_rating', 'speed_rating', 'cost_rating', 'overall_rating',
            'account_manager', 'account_manager_name', 'payment_terms', 'currency',
            'is_active', 'is_preferred', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'overall_rating', 'created_at', 'updated_at']


class CarrierRateSerializer(serializers.ModelSerializer):
    """Serializer pour les tarifs des transporteurs"""
    carrier_name = serializers.CharField(source='carrier.name', read_only=True)
    
    class Meta:
        model = CarrierRate
        fields = [
            'id', 'carrier', 'carrier_name', 'rate_name', 'rate_type', 'service_level',
            'origin_zone', 'destination_zone', 'min_weight', 'max_weight',
            'min_volume', 'max_volume', 'min_distance', 'max_distance',
            'base_rate', 'rate_per_unit', 'fuel_surcharge_percentage',
            'transit_time_days', 'valid_from', 'valid_to', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ShipmentItemSerializer(serializers.ModelSerializer):
    """Serializer pour les articles d'expédition"""
    
    class Meta:
        model = ShipmentItem
        fields = [
            'id', 'item_name', 'description', 'sku', 'quantity', 'unit_of_measure',
            'unit_weight', 'unit_volume', 'unit_value', 'total_value',
            'hs_code', 'country_of_origin', 'fragile', 'hazardous',
            'temperature_controlled', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'total_value', 'created_at', 'updated_at']


class ShipmentPackageSerializer(serializers.ModelSerializer):
    """Serializer pour les colis d'expédition"""
    volume = serializers.ReadOnlyField()
    
    class Meta:
        model = ShipmentPackage
        fields = [
            'id', 'package_number', 'tracking_number', 'package_type',
            'length', 'width', 'height', 'weight', 'volume',
            'description', 'barcode', 'label_printed',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'volume', 'created_at', 'updated_at']


class ShipmentTrackingSerializer(serializers.ModelSerializer):
    """Serializer pour le suivi des expéditions"""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = ShipmentTracking
        fields = [
            'id', 'event_type', 'event_date', 'event_description',
            'location', 'city', 'country', 'carrier_event_code',
            'next_expected_event', 'estimated_delivery', 'source',
            'user', 'user_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ShipmentSerializer(serializers.ModelSerializer):
    """Serializer pour les expéditions"""
    carrier_name = serializers.CharField(source='carrier.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    items = ShipmentItemSerializer(many=True, read_only=True)
    packages = ShipmentPackageSerializer(many=True, read_only=True)
    tracking_events = ShipmentTrackingSerializer(many=True, read_only=True)
    is_delayed = serializers.ReadOnlyField()
    delivery_delay_days = serializers.ReadOnlyField()
    
    class Meta:
        model = Shipment
        fields = [
            'id', 'shipment_number', 'tracking_number', 'shipment_type', 'status', 'priority',
            'carrier', 'carrier_name', 'service_level',
            'sender_name', 'sender_address', 'sender_city', 'sender_postal_code',
            'sender_country', 'sender_contact', 'sender_phone', 'sender_email',
            'recipient_name', 'recipient_address', 'recipient_city', 'recipient_postal_code',
            'recipient_country', 'recipient_contact', 'recipient_phone', 'recipient_email',
            'total_weight', 'total_volume', 'package_count',
            'planned_pickup_date', 'actual_pickup_date', 'planned_delivery_date', 'actual_delivery_date',
            'estimated_cost', 'actual_cost', 'currency',
            'special_instructions', 'delivery_instructions', 'customer_reference', 'order_reference',
            'created_by', 'created_by_name', 'declared_value', 'insurance_required', 'insurance_cost',
            'signature_required', 'proof_of_delivery', 'delivery_photo_url',
            'items', 'packages', 'tracking_events', 'is_delayed', 'delivery_delay_days',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'shipment_number', 'is_delayed', 'delivery_delay_days', 'created_at', 'updated_at']


class ShipmentCreateSerializer(serializers.Serializer):
    """Serializer pour la création d'expéditions"""
    shipment_type = serializers.ChoiceField(choices=Shipment.SHIPMENT_TYPES)
    carrier_id = serializers.UUIDField()
    service_level = serializers.ChoiceField(choices=Carrier.SERVICE_LEVELS)
    priority = serializers.ChoiceField(choices=Shipment.PRIORITY_LEVELS, default=3)
    
    # Expéditeur
    sender_name = serializers.CharField(max_length=200)
    sender_address = serializers.CharField()
    sender_city = serializers.CharField(max_length=100)
    sender_postal_code = serializers.CharField(max_length=20)
    sender_country = serializers.CharField(max_length=100)
    sender_contact = serializers.CharField(max_length=200, required=False, allow_blank=True)
    sender_phone = serializers.CharField(max_length=50, required=False, allow_blank=True)
    sender_email = serializers.EmailField(required=False, allow_blank=True)
    
    # Destinataire
    recipient_name = serializers.CharField(max_length=200)
    recipient_address = serializers.CharField()
    recipient_city = serializers.CharField(max_length=100)
    recipient_postal_code = serializers.CharField(max_length=20)
    recipient_country = serializers.CharField(max_length=100)
    recipient_contact = serializers.CharField(max_length=200, required=False, allow_blank=True)
    recipient_phone = serializers.CharField(max_length=50, required=False, allow_blank=True)
    recipient_email = serializers.EmailField(required=False, allow_blank=True)
    
    # Dates
    planned_pickup_date = serializers.DateTimeField(required=False, allow_null=True)
    planned_delivery_date = serializers.DateTimeField(required=False, allow_null=True)
    
    # Instructions et références
    special_instructions = serializers.CharField(required=False, allow_blank=True)
    delivery_instructions = serializers.CharField(required=False, allow_blank=True)
    customer_reference = serializers.CharField(max_length=100, required=False, allow_blank=True)
    order_reference = serializers.CharField(max_length=100, required=False, allow_blank=True)
    
    # Valeur et assurance
    declared_value = serializers.DecimalField(max_digits=12, decimal_places=2, required=False, allow_null=True)
    insurance_required = serializers.BooleanField(default=False)
    signature_required = serializers.BooleanField(default=False)
    
    # Articles
    items = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True
    )


class RouteStopSerializer(serializers.ModelSerializer):
    """Serializer pour les arrêts d'itinéraire"""
    shipments_count = serializers.SerializerMethodField()
    
    class Meta:
        model = RouteStop
        fields = [
            'id', 'stop_sequence', 'stop_type', 'status', 'stop_name',
            'address', 'city', 'postal_code', 'country',
            'contact_name', 'contact_phone', 'contact_email',
            'planned_arrival_time', 'planned_departure_time',
            'actual_arrival_time', 'actual_departure_time',
            'estimated_service_time', 'actual_service_time',
            'special_instructions', 'latitude', 'longitude',
            'delivery_success', 'failure_reason', 'signature_obtained',
            'proof_of_delivery', 'shipments_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'shipments_count', 'created_at', 'updated_at']
    
    def get_shipments_count(self, obj):
        return obj.shipments.count()


class RouteSerializer(serializers.ModelSerializer):
    """Serializer pour les itinéraires"""
    carrier_name = serializers.CharField(source='carrier.name', read_only=True)
    planner_name = serializers.CharField(source='planner.get_full_name', read_only=True)
    stops = RouteStopSerializer(many=True, read_only=True)
    
    class Meta:
        model = Route
        fields = [
            'id', 'route_number', 'route_name', 'status', 'planned_date',
            'start_time', 'end_time', 'actual_start_time', 'actual_end_time',
            'carrier', 'carrier_name', 'vehicle_info', 'driver_name', 'driver_phone',
            'max_weight', 'max_volume', 'total_distance', 'estimated_duration',
            'actual_duration', 'estimated_cost', 'actual_cost',
            'planner', 'planner_name', 'notes', 'stops',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'route_number', 'created_at', 'updated_at']


class RouteCreateSerializer(serializers.Serializer):
    """Serializer pour la création d'itinéraires"""
    route_name = serializers.CharField(max_length=200)
    planned_date = serializers.DateField()
    start_time = serializers.TimeField(required=False, allow_null=True)
    end_time = serializers.TimeField(required=False, allow_null=True)
    carrier_id = serializers.UUIDField()
    vehicle_info = serializers.CharField(max_length=200, required=False, allow_blank=True)
    driver_name = serializers.CharField(max_length=200, required=False, allow_blank=True)
    driver_phone = serializers.CharField(max_length=50, required=False, allow_blank=True)
    max_weight = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True)
    max_volume = serializers.DecimalField(max_digits=10, decimal_places=3, required=False, allow_null=True)
    estimated_cost = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True)
    notes = serializers.CharField(required=False, allow_blank=True)
    optimize = serializers.BooleanField(default=False)
    
    # Arrêts
    stops = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True
    )


class DeliveryAttemptSerializer(serializers.ModelSerializer):
    """Serializer pour les tentatives de livraison"""
    shipment_number = serializers.CharField(source='shipment.shipment_number', read_only=True)
    
    class Meta:
        model = DeliveryAttempt
        fields = [
            'id', 'shipment', 'shipment_number', 'route_stop',
            'attempt_number', 'attempt_date', 'result',
            'delivered_to', 'signature_name', 'failure_reason', 'notes',
            'next_attempt_date', 'photo_url', 'signature_url', 'driver_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class LogisticsDashboardSerializer(serializers.Serializer):
    """Serializer pour le dashboard Logistics"""
    tenant = serializers.CharField()
    timestamp = serializers.CharField()
    shipments = serializers.DictField()
    carriers = serializers.DictField()
    routes = serializers.DictField()
    performance = serializers.DictField()
    costs = serializers.DictField()
    alerts = serializers.DictField()
    recent_activities = serializers.ListField()


class CarrierPerformanceSerializer(serializers.Serializer):
    """Serializer pour l'analyse des performances transporteurs"""
    analysis_period = serializers.DictField()
    carriers = serializers.ListField()
    summary = serializers.DictField()


class LogisticsInsightSerializer(serializers.Serializer):
    """Serializer pour les insights logistiques"""
    type = serializers.ChoiceField(choices=['critical', 'warning', 'opportunity', 'info'])
    priority = serializers.ChoiceField(choices=['high', 'medium', 'low'])
    title = serializers.CharField()
    description = serializers.CharField()
    recommendation = serializers.CharField()
    generated_at = serializers.CharField()


class ShipmentTrackingUpdateSerializer(serializers.Serializer):
    """Serializer pour la mise à jour du suivi"""
    event_type = serializers.ChoiceField(choices=ShipmentTracking.EVENT_TYPES)
    event_description = serializers.CharField()
    event_date = serializers.DateTimeField(required=False)
    location = serializers.CharField(max_length=200, required=False, allow_blank=True)
    city = serializers.CharField(max_length=100, required=False, allow_blank=True)
    country = serializers.CharField(max_length=100, required=False, allow_blank=True)
    carrier_event_code = serializers.CharField(max_length=50, required=False, allow_blank=True)
    next_expected_event = serializers.CharField(max_length=200, required=False, allow_blank=True)
    estimated_delivery = serializers.DateTimeField(required=False, allow_null=True)
    source = serializers.ChoiceField(
        choices=[
            ('manual', 'Manuel'),
            ('carrier_api', 'API transporteur'),
            ('webhook', 'Webhook'),
            ('email', 'Email'),
            ('scan', 'Scan'),
        ],
        default='manual'
    )


class ShipmentCostEstimateSerializer(serializers.Serializer):
    """Serializer pour l'estimation des coûts d'expédition"""
    carrier_id = serializers.UUIDField()
    service_level = serializers.ChoiceField(choices=Carrier.SERVICE_LEVELS)
    origin_postal_code = serializers.CharField(max_length=20)
    destination_postal_code = serializers.CharField(max_length=20)
    weight = serializers.DecimalField(max_digits=10, decimal_places=2)
    volume = serializers.DecimalField(max_digits=10, decimal_places=3, required=False)
    declared_value = serializers.DecimalField(max_digits=12, decimal_places=2, required=False)
    insurance_required = serializers.BooleanField(default=False)


class CarrierComparisonSerializer(serializers.Serializer):
    """Serializer pour la comparaison de transporteurs"""
    origin_postal_code = serializers.CharField(max_length=20)
    destination_postal_code = serializers.CharField(max_length=20)
    weight = serializers.DecimalField(max_digits=10, decimal_places=2)
    volume = serializers.DecimalField(max_digits=10, decimal_places=3, required=False)
    declared_value = serializers.DecimalField(max_digits=12, decimal_places=2, required=False)
    service_level = serializers.ChoiceField(choices=Carrier.SERVICE_LEVELS, required=False)
    max_transit_days = serializers.IntegerField(required=False)


class RouteOptimizationSerializer(serializers.Serializer):
    """Serializer pour l'optimisation d'itinéraires"""
    route_id = serializers.UUIDField()
    optimization_type = serializers.ChoiceField(
        choices=[
            ('distance', 'Distance minimale'),
            ('time', 'Temps minimal'),
            ('cost', 'Coût minimal'),
            ('balanced', 'Équilibré'),
        ],
        default='balanced'
    )
    constraints = serializers.DictField(required=False)


class ShipmentLabelSerializer(serializers.Serializer):
    """Serializer pour la génération d'étiquettes"""
    shipment_id = serializers.UUIDField()
    label_format = serializers.ChoiceField(
        choices=[
            ('pdf', 'PDF'),
            ('png', 'PNG'),
            ('zpl', 'ZPL'),
            ('epl', 'EPL'),
        ],
        default='pdf'
    )
    label_size = serializers.ChoiceField(
        choices=[
            ('4x6', '4x6 inches'),
            ('4x8', '4x8 inches'),
            ('a4', 'A4'),
            ('letter', 'Letter'),
        ],
        default='4x6'
    )
