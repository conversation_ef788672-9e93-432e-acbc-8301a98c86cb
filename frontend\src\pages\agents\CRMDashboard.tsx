import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { SimpleMetricCard } from '../../components/ui/SimpleMetricCard';
import { SimpleChart } from '../../components/ui/SimpleChart';

interface CRMMetrics {
  totalCustomers: number;
  activeCustomers: number;
  newCustomers: number;
  customerSatisfaction: number;
  averageOrderValue: number;
  customerLifetimeValue: number;
}

interface Customer {
  id: string;
  name: string;
  company: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'prospect' | 'vip';
  lastContact: string;
  totalOrders: number;
  totalValue: number;
  satisfactionScore: number;
}

interface Interaction {
  id: string;
  customerId: string;
  customerName: string;
  type: 'call' | 'email' | 'meeting' | 'support';
  subject: string;
  date: string;
  duration?: number;
  outcome: 'positive' | 'neutral' | 'negative';
  nextAction?: string;
}

export const CRMDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<CRMMetrics>({
    totalCustomers: 1247,
    activeCustomers: 892,
    newCustomers: 45,
    customerSatisfaction: 4.3,
    averageOrderValue: 15750,
    customerLifetimeValue: 125000
  });

  const [topCustomers] = useState<Customer[]>([
    { id: 'CUST-001', name: 'Jean Dupont', company: 'TechCorp SA', email: '<EMAIL>', phone: '+33 1 23 45 67 89', status: 'vip', lastContact: '2024-01-27', totalOrders: 28, totalValue: 450000, satisfactionScore: 4.8 },
    { id: 'CUST-002', name: 'Marie Martin', company: 'InnovateLtd', email: '<EMAIL>', phone: '+33 1 98 76 54 32', status: 'active', lastContact: '2024-01-26', totalOrders: 22, totalValue: 320000, satisfactionScore: 4.5 },
    { id: 'CUST-003', name: 'Pierre Durand', company: 'GlobalSystems', email: '<EMAIL>', phone: '+33 1 11 22 33 44', status: 'active', lastContact: '2024-01-25', totalOrders: 18, totalValue: 280000, satisfactionScore: 4.2 },
    { id: 'CUST-004', name: 'Sophie Laurent', company: 'StartupXYZ', email: '<EMAIL>', phone: '+33 1 55 66 77 88', status: 'prospect', lastContact: '2024-01-24', totalOrders: 5, totalValue: 75000, satisfactionScore: 4.0 },
    { id: 'CUST-005', name: 'Thomas Bernard', company: 'Enterprise Co', email: '<EMAIL>', phone: '+33 1 99 88 77 66', status: 'active', lastContact: '2024-01-23', totalOrders: 15, totalValue: 180000, satisfactionScore: 4.6 }
  ];

  const [recentInteractions] = useState<Interaction[]>([
    { id: 'INT-001', customerId: 'CUST-001', customerName: 'Jean Dupont', type: 'meeting', subject: 'Négociation contrat annuel', date: '2024-01-27', duration: 90, outcome: 'positive', nextAction: 'Envoyer proposition' },
    { id: 'INT-002', customerId: 'CUST-002', customerName: 'Marie Martin', type: 'call', subject: 'Support technique produit', date: '2024-01-26', duration: 30, outcome: 'neutral', nextAction: 'Planifier formation' },
    { id: 'INT-003', customerId: 'CUST-003', customerName: 'Pierre Durand', type: 'email', subject: 'Demande de devis', date: '2024-01-25', outcome: 'positive', nextAction: 'Préparer présentation' },
    { id: 'INT-004', customerId: 'CUST-004', customerName: 'Sophie Laurent', type: 'support', subject: 'Problème facturation', date: '2024-01-24', duration: 45, outcome: 'negative', nextAction: 'Escalader au manager' },
    { id: 'INT-005', customerId: 'CUST-005', customerName: 'Thomas Bernard', type: 'meeting', subject: 'Revue trimestrielle', date: '2024-01-23', duration: 60, outcome: 'positive', nextAction: 'Programmer prochain RDV' }
  ];

  const [customerData] = useState([
    { name: 'Jan', value: 1180, value2: 45 },
    { name: 'Fév', value: 1205, value2: 52 },
    { name: 'Mar', value: 1190, value2: 38 },
    { name: 'Avr', value: 1220, value2: 61 },
    { name: 'Mai', value: 1235, value2: 48 },
    { name: 'Jun', value: 1260, value2: 55 },
    { name: 'Jul', value: 1247, value2: 45 }
  ]);

  const [segmentData] = useState([
    { name: 'VIP', value: 125 },
    { name: 'Actifs', value: 892 },
    { name: 'Prospects', value: 180 },
    { name: 'Inactifs', value: 50 }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const getCustomerStatusColor = (status: string) => {
    switch (status) {
      case 'vip': return 'bg-purple-100 text-purple-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'prospect': return 'bg-blue-100 text-blue-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getInteractionTypeIcon = (type: string) => {
    switch (type) {
      case 'call': return '📞';
      case 'email': return '📧';
      case 'meeting': return '🤝';
      case 'support': return '🛠️';
      default: return '💬';
    }
  };

  const getOutcomeColor = (outcome: string) => {
    switch (outcome) {
      case 'positive': return 'text-green-600';
      case 'neutral': return 'text-yellow-600';
      case 'negative': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getOutcomeIcon = (outcome: string) => {
    switch (outcome) {
      case 'positive': return '✅';
      case 'neutral': return '⚪';
      case 'negative': return '❌';
      default: return '⚪';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getSatisfactionStars = (score: number) => {
    const fullStars = Math.floor(score);
    const hasHalfStar = score % 1 >= 0.5;
    return '⭐'.repeat(fullStars) + (hasHalfStar ? '⭐' : '');
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center text-3xl">
              🤝
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Agent CRM</h1>
              <p className="text-gray-600 text-lg">Gestion de la relation client</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              🔄 Actualiser
            </button>
            <button className="px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors">
              📊 Rapport
            </button>
          </div>
        </div>
      </motion.div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <SimpleMetricCard
          title="Clients Total"
          value={metrics.totalCustomers}
          icon="👥"
          color="blue"
          trend="up"
          trendValue={5.2}
        />
        <SimpleMetricCard
          title="Clients Actifs"
          value={metrics.activeCustomers}
          icon="✅"
          color="green"
          trend="up"
          trendValue={3.8}
        />
        <SimpleMetricCard
          title="Nouveaux Clients"
          value={metrics.newCustomers}
          icon="🆕"
          color="purple"
          trend="up"
          trendValue={18.5}
        />
        <SimpleMetricCard
          title="Satisfaction"
          value={metrics.customerSatisfaction}
          unit="/5"
          icon="😊"
          color="yellow"
          trend="up"
          trendValue={2.1}
        />
        <SimpleMetricCard
          title="Panier Moyen"
          value={`${(metrics.averageOrderValue / 1000).toFixed(0)}k€`}
          icon="🛒"
          color="green"
          trend="up"
          trendValue={8.7}
        />
        <SimpleMetricCard
          title="LTV Client"
          value={`${(metrics.customerLifetimeValue / 1000).toFixed(0)}k€`}
          icon="💎"
          color="purple"
          trend="up"
          trendValue={12.3}
        />
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <SimpleChart
            title="Évolution Clients (Total vs Nouveaux)"
            data={customerData}
            type="line"
            height={350}
            color="#EC4899"
          />
        </div>
        <div>
          <SimpleChart
            title="Segmentation Clients"
            data={segmentData}
            type="bar"
            height={350}
            color="#F472B6"
          />
        </div>
      </div>

      {/* Top clients et interactions récentes */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top clients */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold mb-4">Clients Principaux</h3>
          <div className="space-y-4">
            {topCustomers.map((customer, index) => (
              <motion.div
                key={customer.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center text-pink-600 font-semibold">
                    {customer.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <div className="font-semibold">{customer.name}</div>
                    <div className="text-sm text-gray-600">{customer.company}</div>
                    <div className="text-xs text-gray-500">
                      {customer.totalOrders} commandes • {getSatisfactionStars(customer.satisfactionScore)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCustomerStatusColor(customer.status)}`}>
                    {customer.status}
                  </span>
                  <div className="font-semibold mt-1">{formatCurrency(customer.totalValue)}</div>
                  <div className="text-xs text-gray-500">
                    Dernier contact: {new Date(customer.lastContact).toLocaleDateString('fr-FR')}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Interactions récentes */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold mb-4">Interactions Récentes</h3>
          <div className="space-y-4">
            {recentInteractions.map((interaction, index) => (
              <motion.div
                key={interaction.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{getInteractionTypeIcon(interaction.type)}</span>
                    <span className="font-semibold text-pink-600">{interaction.customerName}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className={getOutcomeColor(interaction.outcome)}>
                      {getOutcomeIcon(interaction.outcome)}
                    </span>
                  </div>
                </div>
                <div className="text-sm text-gray-600 mb-2">{interaction.subject}</div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500">
                    {new Date(interaction.date).toLocaleDateString('fr-FR')}
                    {interaction.duration && ` • ${interaction.duration}min`}
                  </span>
                </div>
                {interaction.nextAction && (
                  <div className="mt-2 text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded">
                    À faire: {interaction.nextAction}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
