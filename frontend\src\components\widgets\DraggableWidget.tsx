import React, { useState, useRef } from 'react';
import { motion, useDragControls, PanInfo } from 'framer-motion';

interface DraggableWidgetProps {
  id: string;
  title: string;
  children: React.ReactNode;
  initialPosition?: { x: number; y: number };
  size?: 'small' | 'medium' | 'large';
  onPositionChange?: (id: string, position: { x: number; y: number }) => void;
  onResize?: (id: string, size: 'small' | 'medium' | 'large') => void;
  onRemove?: (id: string) => void;
  isDraggable?: boolean;
  isResizable?: boolean;
  isRemovable?: boolean;
}

export const DraggableWidget: React.FC<DraggableWidgetProps> = ({
  id,
  title,
  children,
  initialPosition = { x: 0, y: 0 },
  size = 'medium',
  onPositionChange,
  onResize,
  onRemove,
  isDraggable = true,
  isResizable = true,
  isRemovable = true
}) => {
  const [position, setPosition] = useState(initialPosition);
  const [currentSize, setCurrentSize] = useState(size);
  const [isDragging, setIsDragging] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const dragControls = useDragControls();
  const constraintsRef = useRef(null);

  const getSizeClasses = () => {
    switch (currentSize) {
      case 'small': return 'w-64 h-48';
      case 'large': return 'w-96 h-80';
      default: return 'w-80 h-64';
    }
  };

  const handleDragEnd = (event: any, info: PanInfo) => {
    const newPosition = {
      x: position.x + info.offset.x,
      y: position.y + info.offset.y
    };
    setPosition(newPosition);
    setIsDragging(false);
    onPositionChange?.(id, newPosition);
  };

  const handleSizeChange = (newSize: 'small' | 'medium' | 'large') => {
    setCurrentSize(newSize);
    onResize?.(id, newSize);
  };

  const handleRemove = () => {
    onRemove?.(id);
  };

  return (
    <motion.div
      ref={constraintsRef}
      className="absolute"
      style={{ x: position.x, y: position.y }}
    >
      <motion.div
        drag={isDraggable}
        dragControls={dragControls}
        dragMomentum={false}
        dragElastic={0.1}
        onDragStart={() => setIsDragging(true)}
        onDragEnd={handleDragEnd}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className={`
          ${getSizeClasses()}
          bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700
          ${isDragging ? 'shadow-2xl scale-105 z-50' : 'hover:shadow-xl'}
          transition-all duration-200 cursor-move
        `}
        whileHover={{ scale: isDragging ? 1.05 : 1.02 }}
        layout
      >
        {/* Header avec contrôles */}
        <div 
          className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-t-xl"
          onPointerDown={(e) => isDraggable && dragControls.start(e)}
        >
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <h3 className="font-semibold text-gray-900 dark:text-white ml-2">
              {title}
            </h3>
          </div>

          {/* Contrôles (visibles au survol) */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            className="flex items-center gap-1"
          >
            {/* Contrôles de taille */}
            {isResizable && (
              <div className="flex gap-1">
                {(['small', 'medium', 'large'] as const).map((sizeOption) => (
                  <button
                    key={sizeOption}
                    onClick={() => handleSizeChange(sizeOption)}
                    className={`
                      w-6 h-6 rounded border-2 transition-colors
                      ${currentSize === sizeOption 
                        ? 'bg-blue-500 border-blue-500' 
                        : 'bg-gray-200 dark:bg-gray-600 border-gray-300 dark:border-gray-500 hover:border-blue-400'
                      }
                    `}
                    title={`Taille ${sizeOption}`}
                  >
                    <div className={`
                      w-full h-full rounded-sm
                      ${sizeOption === 'small' ? 'scale-50' : sizeOption === 'large' ? 'scale-100' : 'scale-75'}
                      ${currentSize === sizeOption ? 'bg-white' : 'bg-gray-400 dark:bg-gray-300'}
                    `} />
                  </button>
                ))}
              </div>
            )}

            {/* Bouton de suppression */}
            {isRemovable && (
              <button
                onClick={handleRemove}
                className="w-6 h-6 text-red-500 hover:bg-red-100 dark:hover:bg-red-900 rounded transition-colors"
                title="Supprimer le widget"
              >
                ✕
              </button>
            )}
          </motion.div>
        </div>

        {/* Contenu du widget */}
        <div className="p-4 h-full overflow-hidden">
          {children}
        </div>

        {/* Indicateur de redimensionnement */}
        {isResizable && (
          <div className="absolute bottom-2 right-2 w-4 h-4 opacity-30 hover:opacity-60 transition-opacity">
            <svg viewBox="0 0 16 16" className="w-full h-full text-gray-400">
              <path
                d="M16 16V10h-2v4h-4v2h6zM10 0v2h4v4h2V0h-6z"
                fill="currentColor"
              />
            </svg>
          </div>
        )}

        {/* Overlay de drag */}
        {isDragging && (
          <div className="absolute inset-0 bg-blue-500 bg-opacity-10 rounded-xl pointer-events-none" />
        )}
      </motion.div>
    </motion.div>
  );
};

// Composant de grille pour organiser les widgets
interface WidgetGridProps {
  children: React.ReactNode;
  snapToGrid?: boolean;
  gridSize?: number;
}

export const WidgetGrid: React.FC<WidgetGridProps> = ({
  children,
  snapToGrid = true,
  gridSize = 20
}) => {
  return (
    <div className="relative w-full h-full min-h-screen overflow-hidden">
      {/* Grille de fond (optionnelle) */}
      {snapToGrid && (
        <div 
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `
              linear-gradient(to right, #000 1px, transparent 1px),
              linear-gradient(to bottom, #000 1px, transparent 1px)
            `,
            backgroundSize: `${gridSize}px ${gridSize}px`
          }}
        />
      )}
      
      {children}
    </div>
  );
};

// Hook pour gérer l'état des widgets
export const useWidgetManager = () => {
  const [widgets, setWidgets] = useState<Array<{
    id: string;
    title: string;
    component: React.ReactNode;
    position: { x: number; y: number };
    size: 'small' | 'medium' | 'large';
  }>>([]);

  const addWidget = (widget: {
    id: string;
    title: string;
    component: React.ReactNode;
    position?: { x: number; y: number };
    size?: 'small' | 'medium' | 'large';
  }) => {
    setWidgets(prev => [...prev, {
      position: { x: 0, y: 0 },
      size: 'medium',
      ...widget
    }]);
  };

  const removeWidget = (id: string) => {
    setWidgets(prev => prev.filter(w => w.id !== id));
  };

  const updateWidgetPosition = (id: string, position: { x: number; y: number }) => {
    setWidgets(prev => prev.map(w => 
      w.id === id ? { ...w, position } : w
    ));
  };

  const updateWidgetSize = (id: string, size: 'small' | 'medium' | 'large') => {
    setWidgets(prev => prev.map(w => 
      w.id === id ? { ...w, size } : w
    ));
  };

  const saveLayout = () => {
    const layout = widgets.map(w => ({
      id: w.id,
      position: w.position,
      size: w.size
    }));
    localStorage.setItem('widget-layout', JSON.stringify(layout));
  };

  const loadLayout = () => {
    const saved = localStorage.getItem('widget-layout');
    if (saved) {
      const layout = JSON.parse(saved);
      setWidgets(prev => prev.map(widget => {
        const savedWidget = layout.find((l: any) => l.id === widget.id);
        return savedWidget ? { ...widget, ...savedWidget } : widget;
      }));
    }
  };

  return {
    widgets,
    addWidget,
    removeWidget,
    updateWidgetPosition,
    updateWidgetSize,
    saveLayout,
    loadLayout
  };
};
