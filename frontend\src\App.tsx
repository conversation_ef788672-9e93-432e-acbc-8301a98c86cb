import React, { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { ThemeProvider, CssBaseline } from '@mui/material'
import { useAuth } from './contexts/AuthContext'
import { erpTheme } from './theme/erpTheme'
import { useBackground } from './hooks/useBackground'

// Pages
import { HomePage } from './pages/HomePage'
import { LoginPage } from './pages/LoginPage'
import LoginPageAuth from './pages/auth/LoginPage'
import DashboardPage from './pages/DashboardPage'
import ProfilePage from './pages/ProfilePage'
import ManagerPage from './pages/agents/ManagerPage'
import HRPage from './pages/agents/HRPage'
import SalesPage from './pages/agents/SalesPage'
import PurchasePage from './pages/agents/PurchasePage'
import StockPage from './pages/agents/StockPage'
import AccountingPage from './pages/agents/AccountingPage'
import FinancePage from './pages/agents/FinancePage'
import CRMPage from './pages/CRMPage'
import BIPage from './pages/BIPage'
import { BackgroundShowcase } from './pages/BackgroundShowcase'

// Layouts
import AuthLayout from './components/layouts/AuthLayout'
import DashboardLayout from './components/layouts/DashboardLayout'

// Components
import LoadingSpinner from './components/ui/LoadingSpinner'
import ProtectedRoute from './components/auth/ProtectedRoute'

function App() {
  const { isAuthenticated, isLoading, user } = useAuth()
  const { currentBackground } = useBackground()

  // Appliquer l'arrière-plan au chargement
  useEffect(() => {
    // L'arrière-plan est automatiquement appliqué par le hook useBackground
  }, [currentBackground])

  // Afficher un spinner pendant le chargement initial
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <ThemeProvider theme={erpTheme}>
      <CssBaseline />
      <div className="min-h-screen bg-gray-50">
        <Routes>
        {/* Page d'accueil publique */}
        <Route path="/" element={<HomePage />} />
        <Route path="/home" element={<HomePage />} />
        <Route path="/login" element={<LoginPage />} />

        {/* Routes d'authentification */}
        <Route
          path="/auth/*"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <AuthLayout>
                <Routes>
                  <Route path="login" element={<LoginPage />} />
                  <Route path="*" element={<Navigate to="/auth/login" replace />} />
                </Routes>
              </AuthLayout>
            )
          }
        />

        {/* Routes protégées */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <DashboardLayout>
                <Routes>
                  <Route path="dashboard" element={<DashboardPage />} />
                  <Route path="profile" element={<ProfilePage />} />

                  {/* Routes des agents */}
                  <Route path="agents/manager" element={<ManagerPage />} />
                  <Route path="agents/hr" element={<HRPage />} />
                  <Route path="agents/sales" element={<SalesPage />} />
                  <Route path="agents/purchase" element={<PurchasePage />} />
                  <Route path="agents/stock" element={<StockPage />} />
                  <Route path="agents/accounting" element={<AccountingPage />} />
                  <Route path="agents/finance" element={<FinancePage />} />
                  <Route path="agents/logistics/*" element={<div>Agent Logistique</div>} />
                  <Route path="agents/crm/*" element={<CRMPage />} />
                  <Route path="agents/bi/*" element={<BIPage />} />

                  {/* Pages spéciales */}
                  <Route path="backgrounds" element={<BackgroundShowcase />} />

                  {/* Redirection par défaut pour les routes protégées */}
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Redirection par défaut pour les routes non trouvées */}
        <Route
          path="*"
          element={
            <Navigate
              to={isAuthenticated ? "/dashboard" : "/"}
              replace
            />
          }
        />
        </Routes>
      </div>
    </ThemeProvider>
  )
}

export default App
