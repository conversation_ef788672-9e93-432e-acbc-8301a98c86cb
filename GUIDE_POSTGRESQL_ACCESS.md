
# 🗄️ GUIDE DE CONNEXION POSTGRESQL

## 📊 INFORMATIONS DE CONNEXION

### 🐳 Via Docker (Ligne de commande)
```bash
# Accès direct à PostgreSQL
docker exec -it erp_postgres psql -U erp_admin -d erp_hub

# Commandes utiles une fois connecté :
\dt                    # Lister toutes les tables
\d table_name          # Décrire une table
\q                     # Quitter
```

### 🌐 Via pgAdmin (Interface graphique)
1. **Ouvrir** : http://localhost:8080
2. **Email** : <EMAIL>
3. **Mot de passe** : admin123
4. **Ajouter serveur** :
   - Nom : ERP HUB PostgreSQL
   - Host : erp_postgres
   - Port : 5432
   - Database : erp_hub
   - Username : erp_admin
   - Password : erp_password

### 💻 Via Client Local
- **Host** : localhost
- **Port** : 5432
- **Database** : erp_hub
- **Username** : erp_admin
- **Password** : erp_password

## 📋 TABLES DISPONIBLES (22 tables)

### 👥 MODULE HR
- **employees** : Employés (8 enregistrements)
- **leaves** : Congés (3 enregistrements)
- **evaluations** : Évaluations

### 💼 MODULE SALES
- **customers** : Clients (3 enregistrements)
- **opportunities** : Opportunités (3 enregistrements)
- **orders** : Commandes (2 enregistrements)

### 🛒 MODULE PURCHASE
- **suppliers** : Fournisseurs (3 enregistrements)
- **purchase_orders** : Bons de commande (2 enregistrements)
- **purchase_requests** : Demandes d'achat

### 📦 MODULE STOCK
- **products** : Produits (4 enregistrements)
- **inventory** : Inventaire (4 enregistrements)
- **stock_movements** : Mouvements de stock

### 🚚 MODULE LOGISTICS
- **warehouses** : Entrepôts (3 enregistrements)
- **shipments** : Expéditions (2 enregistrements)

### 💰 MODULE FINANCE
- **budgets** : Budgets (3 enregistrements)
- **chart_of_accounts** : Plan comptable
- **journal_entries** : Écritures comptables
- **journal_entry_lines** : Lignes d'écriture

### 💼 MODULE CRM
- **contacts** : Contacts (4 enregistrements)
- **interactions** : Interactions (4 enregistrements)

### 📊 MODULE BI
- **kpis** : Indicateurs (5 enregistrements)
- **reports** : Rapports

## 🔧 COMMANDES SQL UTILES

### 📊 Consultation des données
```sql
-- Voir tous les employés
SELECT * FROM employees;

-- Voir tous les clients
SELECT * FROM customers;

-- Voir tous les produits
SELECT * FROM products;

-- Statistiques par table
SELECT 
    schemaname,
    tablename,
    n_tup_ins as insertions,
    n_tup_upd as updates,
    n_tup_del as deletions
FROM pg_stat_user_tables;
```

### ✏️ Modification des données
```sql
-- Ajouter un employé
INSERT INTO employees (id, employee_number, first_name, last_name, email, position, department, salary, status)
VALUES ('EMP009', 'E009', 'Jean', 'Dupont', '<EMAIL>', 'Développeur', 'IT', 45000, 'Actif');

-- Modifier un employé
UPDATE employees 
SET salary = 50000 
WHERE employee_number = 'E009';

-- Supprimer un employé
DELETE FROM employees 
WHERE employee_number = 'E009';
```

### 🗄️ Structure des tables
```sql
-- Voir la structure d'une table
\d employees

-- Voir les contraintes
SELECT conname, contype, conrelid::regclass 
FROM pg_constraint 
WHERE conrelid = 'employees'::regclass;
```

## ⚠️ SAUVEGARDES

### 💾 Créer une sauvegarde
```bash
# Sauvegarde complète
docker exec erp_postgres pg_dump -U erp_admin erp_hub > backup_$(date +%Y%m%d).sql

# Sauvegarde d'une table
docker exec erp_postgres pg_dump -U erp_admin -t employees erp_hub > employees_backup.sql
```

### 🔄 Restaurer une sauvegarde
```bash
# Restaurer depuis un fichier
docker exec -i erp_postgres psql -U erp_admin erp_hub < backup.sql
```
