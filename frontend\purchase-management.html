<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Purchase - Gestion Achats | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #10b981 30%, #059669 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #10b981;
            color: white;
        }
        
        .btn-primary:hover {
            background: #059669;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #10b981;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">🛒 Agent Purchase - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="openModal('orderModal')">
                <span class="material-icons" style="font-size: 1rem;">add</span>
                Nouvelle Commande
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion des Achats</h1>
            <p class="page-subtitle">Commandes fournisseurs et approvisionnement</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalOrders">0</div>
                <div class="stat-label">Commandes Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="pendingOrders">0</div>
                <div class="stat-label">En Attente</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="deliveredOrders">0</div>
                <div class="stat-label">Livrées</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalAmount">0€</div>
                <div class="stat-label">Montant Total</div>
            </div>
        </div>

        <!-- Liste des commandes -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Commandes d'Achat</h2>
                <button class="btn btn-primary" onclick="refreshOrders()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div id="alertContainer"></div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>N° Commande</th>
                                <th>Fournisseur</th>
                                <th>Date</th>
                                <th>Produits</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="ordersTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Commande -->
    <div id="orderModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Nouvelle Commande</h3>
                <button class="close-btn" onclick="closeModal('orderModal')">&times;</button>
            </div>
            <form id="orderForm">
                <div id="modalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="supplier">Fournisseur *</label>
                        <select id="supplier" name="supplier" class="form-select" required>
                            <option value="">Sélectionner un fournisseur</option>
                            <option value="TechSupply Co">TechSupply Co</option>
                            <option value="Office Solutions">Office Solutions</option>
                            <option value="Industrial Parts">Industrial Parts</option>
                            <option value="Global Materials">Global Materials</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="orderDate">Date de commande *</label>
                        <input type="date" id="orderDate" name="orderDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="expectedDate">Date de livraison prévue</label>
                        <input type="date" id="expectedDate" name="expectedDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="priority">Priorité</label>
                        <select id="priority" name="priority" class="form-select">
                            <option value="low">Basse</option>
                            <option value="medium" selected>Moyenne</option>
                            <option value="high">Haute</option>
                            <option value="urgent">Urgente</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="products">Produits *</label>
                        <input type="text" id="products" name="products" class="form-input" required placeholder="Ex: Ordinateurs portables, Imprimantes...">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="quantity">Quantité</label>
                        <input type="number" id="quantity" name="quantity" class="form-input" min="1" step="1">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="unitPrice">Prix unitaire (€)</label>
                        <input type="number" id="unitPrice" name="unitPrice" class="form-input" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="totalAmount">Montant total (€)</label>
                        <input type="number" id="totalAmount" name="totalAmount" class="form-input" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="status">Statut</label>
                        <select id="status" name="status" class="form-select">
                            <option value="draft">Brouillon</option>
                            <option value="pending" selected>En attente</option>
                            <option value="approved">Approuvée</option>
                            <option value="ordered">Commandée</option>
                            <option value="delivered">Livrée</option>
                            <option value="cancelled">Annulée</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="notes">Notes</label>
                    <textarea id="notes" name="notes" class="form-textarea" placeholder="Notes sur la commande..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('orderModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveOrderBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let orders = [];
        let editingOrderId = null;
        let isLoading = false;

        // Données de démonstration réalistes
        const demoOrders = [
            {
                id: 'PO-2024-001',
                supplier: 'TechSupply Co',
                orderDate: '2024-01-15',
                expectedDate: '2024-01-25',
                products: 'Ordinateurs portables Dell Latitude 5540',
                quantity: 10,
                unitPrice: 800,
                totalAmount: 8000,
                status: 'delivered',
                priority: 'medium',
                notes: 'Commande pour le nouveau département IT, livraison conforme'
            },
            {
                id: 'PO-2024-002',
                supplier: 'Office Solutions',
                orderDate: '2024-01-20',
                expectedDate: '2024-01-30',
                products: 'Fournitures de bureau (papier, stylos, classeurs)',
                quantity: 50,
                unitPrice: 25,
                totalAmount: 1250,
                status: 'pending',
                priority: 'low',
                notes: 'Réapprovisionnement mensuel standard'
            },
            {
                id: 'PO-2024-003',
                supplier: 'Industrial Parts',
                orderDate: '2024-01-22',
                expectedDate: '2024-02-05',
                products: 'Pièces de rechange machines industrielles',
                quantity: 5,
                unitPrice: 450,
                totalAmount: 2250,
                status: 'approved',
                priority: 'high',
                notes: 'Maintenance préventive urgente, fournisseur confirmé'
            },
            {
                id: 'PO-2024-004',
                supplier: 'Global Materials',
                orderDate: '2024-01-18',
                expectedDate: '2024-02-01',
                products: 'Matières premières aluminium',
                quantity: 100,
                unitPrice: 35,
                totalAmount: 3500,
                status: 'ordered',
                priority: 'medium',
                notes: 'Production Q1 2024, qualité certifiée ISO'
            },
            {
                id: 'PO-2024-005',
                supplier: 'TechSupply Co',
                orderDate: '2024-01-25',
                expectedDate: '2024-02-08',
                products: 'Écrans Dell 24 pouces',
                quantity: 15,
                unitPrice: 220,
                totalAmount: 3300,
                status: 'pending',
                priority: 'low',
                notes: 'Équipement bureaux, attente validation budget'
            },
            {
                id: 'PO-2024-006',
                supplier: 'Office Solutions',
                orderDate: '2024-01-12',
                expectedDate: '2024-01-20',
                products: 'Mobilier de bureau (chaises ergonomiques)',
                quantity: 8,
                unitPrice: 180,
                totalAmount: 1440,
                status: 'delivered',
                priority: 'medium',
                notes: 'Installation terminée, employés satisfaits'
            },
            {
                id: 'PO-2024-007',
                supplier: 'Industrial Parts',
                orderDate: '2024-01-28',
                expectedDate: '2024-02-15',
                products: 'Outillage spécialisé',
                quantity: 12,
                unitPrice: 95,
                totalAmount: 1140,
                status: 'draft',
                priority: 'low',
                notes: 'En attente de validation technique'
            },
            {
                id: 'PO-2024-008',
                supplier: 'Global Materials',
                orderDate: '2024-01-10',
                expectedDate: '2024-01-18',
                products: 'Emballages carton personnalisés',
                quantity: 500,
                unitPrice: 2.5,
                totalAmount: 1250,
                status: 'cancelled',
                priority: 'low',
                notes: 'Annulé - changement de design packaging'
            },
            {
                id: 'PO-2024-009',
                supplier: 'TechSupply Co',
                orderDate: '2024-01-30',
                expectedDate: '2024-02-12',
                products: 'Serveur Dell PowerEdge R750',
                quantity: 1,
                unitPrice: 4500,
                totalAmount: 4500,
                status: 'approved',
                priority: 'high',
                notes: 'Upgrade infrastructure, installation prévue'
            },
            {
                id: 'PO-2024-010',
                supplier: 'Office Solutions',
                orderDate: '2024-01-26',
                expectedDate: '2024-02-05',
                products: 'Licences logicielles Microsoft Office',
                quantity: 25,
                unitPrice: 120,
                totalAmount: 3000,
                status: 'ordered',
                priority: 'medium',
                notes: 'Renouvellement annuel, clés en attente'
            }
        ];

        function showAlert(message, type = 'error', container = 'alertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
            
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            if (modalId === 'orderModal') {
                document.getElementById('modalTitle').textContent = editingOrderId ? 'Modifier Commande' : 'Nouvelle Commande';
                // Définir la date d'aujourd'hui par défaut
                if (!editingOrderId) {
                    document.getElementById('orderDate').value = new Date().toISOString().split('T')[0];
                }
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            if (modalId === 'orderModal') {
                document.getElementById('orderForm').reset();
                document.getElementById('modalAlertContainer').innerHTML = '';
                editingOrderId = null;
            }
        }

        function updateStats() {
            const total = orders.length;
            const pending = orders.filter(order => ['pending', 'approved', 'ordered'].includes(order.status)).length;
            const delivered = orders.filter(order => order.status === 'delivered').length;
            const totalAmount = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);

            document.getElementById('totalOrders').textContent = total;
            document.getElementById('pendingOrders').textContent = pending;
            document.getElementById('deliveredOrders').textContent = delivered;
            document.getElementById('totalAmount').textContent = totalAmount.toLocaleString() + '€';
        }

        function renderOrdersTable() {
            const tbody = document.getElementById('ordersTableBody');
            
            if (orders.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune commande trouvée. Cliquez sur "Nouvelle Commande" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = orders.map(order => {
                const statusBadge = getStatusBadge(order.status);
                return `
                    <tr>
                        <td>${order.id}</td>
                        <td>${order.supplier}</td>
                        <td>${new Date(order.orderDate).toLocaleDateString()}</td>
                        <td>${order.products}</td>
                        <td>${order.totalAmount ? order.totalAmount.toLocaleString() + '€' : '-'}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editOrder('${order.id}')" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteOrder('${order.id}')" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getStatusBadge(status) {
            const badges = {
                'draft': '<span class="badge badge-info">Brouillon</span>',
                'pending': '<span class="badge badge-warning">En attente</span>',
                'approved': '<span class="badge badge-info">Approuvée</span>',
                'ordered': '<span class="badge badge-warning">Commandée</span>',
                'delivered': '<span class="badge badge-success">Livrée</span>',
                'cancelled': '<span class="badge badge-danger">Annulée</span>'
            };
            return badges[status] || '<span class="badge badge-danger">Inconnu</span>';
        }

        async function loadOrders() {
            try {
                // Tentative de chargement depuis l'API
                const token = localStorage.getItem('access_token');
                if (token) {
                    const response = await fetch('http://localhost:8000/api/agents/purchase/orders/', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (response.ok) {
                        orders = await response.json();
                    } else {
                        throw new Error('Erreur API');
                    }
                } else {
                    throw new Error('Pas de token');
                }
            } catch (error) {
                console.log('Chargement des données de démonstration');
                orders = [...demoOrders];
            }
            
            renderOrdersTable();
            updateStats();
        }

        function refreshOrders() {
            loadOrders();
            showAlert('Données actualisées avec succès', 'success');
        }

        function editOrder(id) {
            const order = orders.find(o => o.id === id);
            if (!order) return;

            editingOrderId = id;
            
            // Remplir le formulaire
            document.getElementById('supplier').value = order.supplier;
            document.getElementById('orderDate').value = order.orderDate;
            document.getElementById('expectedDate').value = order.expectedDate || '';
            document.getElementById('products').value = order.products;
            document.getElementById('quantity').value = order.quantity || '';
            document.getElementById('unitPrice').value = order.unitPrice || '';
            document.getElementById('totalAmount').value = order.totalAmount || '';
            document.getElementById('status').value = order.status;
            document.getElementById('priority').value = order.priority || 'medium';
            document.getElementById('notes').value = order.notes || '';

            openModal('orderModal');
        }

        function deleteOrder(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette commande ?')) {
                orders = orders.filter(o => o.id !== id);
                renderOrdersTable();
                updateStats();
                showAlert('Commande supprimée avec succès', 'success');
            }
        }

        // Gestion du formulaire
        document.getElementById('orderForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const orderData = {
                supplier: formData.get('supplier'),
                orderDate: formData.get('orderDate'),
                expectedDate: formData.get('expectedDate'),
                products: formData.get('products'),
                quantity: formData.get('quantity') ? parseInt(formData.get('quantity')) : null,
                unitPrice: formData.get('unitPrice') ? parseFloat(formData.get('unitPrice')) : null,
                totalAmount: formData.get('totalAmount') ? parseFloat(formData.get('totalAmount')) : null,
                status: formData.get('status'),
                priority: formData.get('priority'),
                notes: formData.get('notes')
            };

            try {
                if (editingOrderId) {
                    // Modification
                    const index = orders.findIndex(o => o.id === editingOrderId);
                    if (index !== -1) {
                        orders[index] = { ...orders[index], ...orderData };
                        showAlert('Commande modifiée avec succès', 'success');
                    }
                } else {
                    // Création
                    const newOrder = {
                        id: `PO-2024-${String(orders.length + 1).padStart(3, '0')}`,
                        ...orderData
                    };
                    orders.push(newOrder);
                    showAlert('Commande créée avec succès', 'success');
                }

                renderOrdersTable();
                updateStats();
                closeModal('orderModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'modalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Calcul automatique du montant total
        document.getElementById('quantity').addEventListener('input', calculateTotal);
        document.getElementById('unitPrice').addEventListener('input', calculateTotal);

        function calculateTotal() {
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;
            const unitPrice = parseFloat(document.getElementById('unitPrice').value) || 0;
            const total = quantity * unitPrice;
            document.getElementById('totalAmount').value = total > 0 ? total.toFixed(2) : '';
        }

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadOrders();
            }
        });
    </script>
</body>
</html>
