"""
URLs pour l'agent Logistics
"""
from django.urls import path
from . import views

app_name = 'logistics'

urlpatterns = [
    # Statut et dashboard
    path('status/', views.logistics_status, name='status'),
    path('dashboard/', views.logistics_dashboard, name='dashboard'),

    # Gestion des transporteurs
    path('carriers/', views.CarrierListCreateView.as_view(), name='carrier-list'),
    path('carriers/<uuid:pk>/', views.CarrierDetailView.as_view(), name='carrier-detail'),

    # Gestion des tarifs transporteurs
    path('carrier-rates/', views.CarrierRateListCreateView.as_view(), name='carrier-rate-list'),
    path('carrier-rates/<uuid:pk>/', views.CarrierRateDetailView.as_view(), name='carrier-rate-detail'),

    # Gestion des expéditions
    path('shipments/', views.ShipmentListCreateView.as_view(), name='shipment-list'),
    path('shipments/create/', views.ShipmentCreateView.as_view(), name='shipment-create'),
    path('shipments/<uuid:pk>/', views.ShipmentDetailView.as_view(), name='shipment-detail'),
    path('shipments/<uuid:shipment_id>/tracking/', views.ShipmentTrackingUpdateView.as_view(), name='shipment-tracking'),

    # Gestion des itinéraires
    path('routes/', views.RouteListCreateView.as_view(), name='route-list'),
    path('routes/create/', views.RouteCreateView.as_view(), name='route-create'),
    path('routes/<uuid:pk>/', views.RouteDetailView.as_view(), name='route-detail'),

    # Analyses et rapports
    path('performance/carriers/', views.carrier_performance_analysis, name='carrier-performance'),
    path('insights/', views.logistics_insights, name='insights'),
    path('delayed-shipments/', views.delayed_shipments, name='delayed-shipments'),
    path('delivery-performance/', views.delivery_performance, name='delivery-performance'),
    path('transport-costs/', views.transport_costs, name='transport-costs'),
]
