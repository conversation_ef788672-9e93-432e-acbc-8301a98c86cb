"""
Modèles pour l'Agent Finance - Trésorerie et Analyses Financières
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid

from core.models import TimeStampedModel, UUIDModel, Tenant, User


class Bank(UUIDModel, TimeStampedModel):
    """Modèle pour les banques"""

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='banks',
        verbose_name=_("Tenant")
    )

    # Informations de base
    bank_code = models.Char<PERSON>ield(_("Code banque"), max_length=20)
    name = models.CharField(_("Nom de la banque"), max_length=200)
    swift_code = models.CharField(_("Code SWIFT/BIC"), max_length=11, blank=True)

    # Contact
    address_line1 = models.CharField(_("Adresse ligne 1"), max_length=200, blank=True)
    address_line2 = models.CharField(_("Adresse ligne 2"), max_length=200, blank=True)
    city = models.CharField(_("Ville"), max_length=100, blank=True)
    postal_code = models.CharField(_("Code postal"), max_length=20, blank=True)
    country = models.CharField(_("Pays"), max_length=100, blank=True)
    phone = models.CharField(_("Téléphone"), max_length=50, blank=True)
    email = models.EmailField(_("Email"), blank=True)
    website = models.URLField(_("Site web"), blank=True)

    # Contact commercial
    contact_person = models.CharField(_("Personne de contact"), max_length=200, blank=True)
    contact_phone = models.CharField(_("Téléphone contact"), max_length=50, blank=True)
    contact_email = models.EmailField(_("Email contact"), blank=True)

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)
    is_primary = models.BooleanField(_("Banque principale"), default=False)

    class Meta:
        verbose_name = _("Banque")
        verbose_name_plural = _("Banques")
        unique_together = ['tenant', 'bank_code']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.bank_code})"


class BankAccount(UUIDModel, TimeStampedModel):
    """Modèle pour les comptes bancaires"""

    ACCOUNT_TYPES = [
        ('checking', _('Compte courant')),
        ('savings', _('Compte d\'épargne')),
        ('credit_line', _('Ligne de crédit')),
        ('term_deposit', _('Dépôt à terme')),
        ('foreign_currency', _('Compte devise')),
        ('escrow', _('Compte séquestre')),
        ('investment', _('Compte d\'investissement')),
    ]

    ACCOUNT_STATUS = [
        ('active', _('Actif')),
        ('inactive', _('Inactif')),
        ('closed', _('Fermé')),
        ('frozen', _('Gelé')),
        ('restricted', _('Restreint')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='bank_accounts',
        verbose_name=_("Tenant")
    )

    bank = models.ForeignKey(
        Bank,
        on_delete=models.PROTECT,
        related_name='accounts',
        verbose_name=_("Banque")
    )

    # Identification
    account_number = models.CharField(_("Numéro de compte"), max_length=50)
    iban = models.CharField(_("IBAN"), max_length=34, blank=True)
    account_name = models.CharField(_("Nom du compte"), max_length=200)
    account_type = models.CharField(_("Type de compte"), max_length=20, choices=ACCOUNT_TYPES)

    # Devise et limites
    currency = models.CharField(_("Devise"), max_length=3, default='EUR')
    overdraft_limit = models.DecimalField(
        _("Limite de découvert"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    credit_limit = models.DecimalField(
        _("Limite de crédit"),
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Soldes
    opening_balance = models.DecimalField(
        _("Solde d'ouverture"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    current_balance = models.DecimalField(
        _("Solde actuel"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    available_balance = models.DecimalField(
        _("Solde disponible"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Dates importantes
    opening_date = models.DateField(_("Date d'ouverture"))
    closing_date = models.DateField(_("Date de fermeture"), null=True, blank=True)
    last_reconciliation_date = models.DateField(_("Dernière réconciliation"), null=True, blank=True)

    # Statut et propriétés
    status = models.CharField(_("Statut"), max_length=15, choices=ACCOUNT_STATUS, default='active')
    is_main_account = models.BooleanField(_("Compte principal"), default=False)
    auto_reconciliation = models.BooleanField(_("Réconciliation automatique"), default=False)

    # Responsable
    account_manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_bank_accounts',
        verbose_name=_("Responsable compte")
    )

    # Informations complémentaires
    description = models.TextField(_("Description"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Compte bancaire")
        verbose_name_plural = _("Comptes bancaires")
        unique_together = ['tenant', 'account_number']
        ordering = ['bank__name', 'account_name']

    def __str__(self):
        return f"{self.account_name} ({self.account_number})"

    @property
    def balance_with_overdraft(self):
        """Solde incluant le découvert autorisé"""
        return self.current_balance + self.overdraft_limit

    @property
    def is_overdrawn(self):
        """Indique si le compte est à découvert"""
        return self.current_balance < 0

    def update_balance(self, amount: Decimal, operation_type: str = 'add'):
        """Met à jour le solde du compte"""
        if operation_type == 'add':
            self.current_balance += amount
        elif operation_type == 'subtract':
            self.current_balance -= amount
        else:
            self.current_balance = amount

        # Calculer le solde disponible
        self.available_balance = self.current_balance + self.overdraft_limit
        self.save(update_fields=['current_balance', 'available_balance'])


class BankTransaction(UUIDModel, TimeStampedModel):
    """Modèle pour les transactions bancaires"""

    TRANSACTION_TYPES = [
        ('debit', _('Débit')),
        ('credit', _('Crédit')),
        ('transfer_in', _('Virement entrant')),
        ('transfer_out', _('Virement sortant')),
        ('fee', _('Frais bancaires')),
        ('interest', _('Intérêts')),
        ('check', _('Chèque')),
        ('card_payment', _('Paiement carte')),
        ('direct_debit', _('Prélèvement')),
        ('standing_order', _('Virement permanent')),
    ]

    TRANSACTION_STATUS = [
        ('pending', _('En attente')),
        ('processed', _('Traité')),
        ('cancelled', _('Annulé')),
        ('rejected', _('Rejeté')),
        ('reconciled', _('Rapproché')),
    ]

    bank_account = models.ForeignKey(
        BankAccount,
        on_delete=models.CASCADE,
        related_name='transactions',
        verbose_name=_("Compte bancaire")
    )

    # Identification
    transaction_id = models.CharField(_("ID transaction"), max_length=100, unique=True)
    reference = models.CharField(_("Référence"), max_length=100, blank=True)

    # Type et statut
    transaction_type = models.CharField(_("Type de transaction"), max_length=20, choices=TRANSACTION_TYPES)
    status = models.CharField(_("Statut"), max_length=15, choices=TRANSACTION_STATUS, default='pending')

    # Montant et devise
    amount = models.DecimalField(
        _("Montant"),
        max_digits=15,
        decimal_places=2
    )
    currency = models.CharField(_("Devise"), max_length=3, default='EUR')

    # Dates
    transaction_date = models.DateField(_("Date de transaction"))
    value_date = models.DateField(_("Date de valeur"))
    booking_date = models.DateField(_("Date de comptabilisation"), null=True, blank=True)

    # Description et détails
    description = models.TextField(_("Description"))
    counterpart_name = models.CharField(_("Nom de la contrepartie"), max_length=200, blank=True)
    counterpart_account = models.CharField(_("Compte contrepartie"), max_length=50, blank=True)
    counterpart_bank = models.CharField(_("Banque contrepartie"), max_length=200, blank=True)

    # Catégorisation
    category = models.CharField(_("Catégorie"), max_length=100, blank=True)
    subcategory = models.CharField(_("Sous-catégorie"), max_length=100, blank=True)

    # Soldes
    balance_before = models.DecimalField(
        _("Solde avant"),
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )
    balance_after = models.DecimalField(
        _("Solde après"),
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Réconciliation
    is_reconciled = models.BooleanField(_("Rapproché"), default=False)
    reconciliation_date = models.DateField(_("Date de rapprochement"), null=True, blank=True)
    reconciled_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reconciled_transactions',
        verbose_name=_("Rapproché par")
    )

    # Références comptables
    accounting_entry_id = models.CharField(_("ID écriture comptable"), max_length=100, blank=True)

    # Import et source
    import_batch = models.CharField(_("Lot d'import"), max_length=100, blank=True)
    source = models.CharField(
        _("Source"),
        max_length=20,
        choices=[
            ('manual', _('Manuel')),
            ('import', _('Import')),
            ('api', _('API bancaire')),
            ('file', _('Fichier')),
        ],
        default='manual'
    )

    class Meta:
        verbose_name = _("Transaction bancaire")
        verbose_name_plural = _("Transactions bancaires")
        ordering = ['-transaction_date', '-created_at']

    def __str__(self):
        return f"{self.transaction_id} - {self.description} ({self.amount} {self.currency})"

    def save(self, *args, **kwargs):
        # Générer l'ID de transaction si nécessaire
        if not self.transaction_id:
            self.transaction_id = self._generate_transaction_id()

        super().save(*args, **kwargs)

        # Mettre à jour le solde du compte si la transaction est traitée
        if self.status == 'processed' and not self.is_reconciled:
            self._update_account_balance()

    def _generate_transaction_id(self):
        """Génère un ID de transaction unique"""
        from django.utils import timezone
        current_date = timezone.now()
        sequence = BankTransaction.objects.filter(
            bank_account=self.bank_account,
            created_at__date=current_date.date()
        ).count() + 1

        return f"TXN{current_date.strftime('%Y%m%d')}{self.bank_account.id.hex[:8].upper()}{sequence:04d}"

    def _update_account_balance(self):
        """Met à jour le solde du compte bancaire"""
        if self.transaction_type in ['credit', 'transfer_in', 'interest']:
            self.bank_account.update_balance(self.amount, 'add')
        elif self.transaction_type in ['debit', 'transfer_out', 'fee', 'check', 'card_payment', 'direct_debit']:
            self.bank_account.update_balance(self.amount, 'subtract')


class CashFlowForecast(UUIDModel, TimeStampedModel):
    """Modèle pour les prévisions de trésorerie"""

    FORECAST_TYPES = [
        ('daily', _('Quotidien')),
        ('weekly', _('Hebdomadaire')),
        ('monthly', _('Mensuel')),
        ('quarterly', _('Trimestriel')),
        ('annual', _('Annuel')),
    ]

    FORECAST_STATUS = [
        ('draft', _('Brouillon')),
        ('active', _('Actif')),
        ('archived', _('Archivé')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='cash_flow_forecasts',
        verbose_name=_("Tenant")
    )

    # Identification
    name = models.CharField(_("Nom de la prévision"), max_length=200)
    forecast_type = models.CharField(_("Type de prévision"), max_length=15, choices=FORECAST_TYPES)

    # Période
    start_date = models.DateField(_("Date de début"))
    end_date = models.DateField(_("Date de fin"))

    # Statut
    status = models.CharField(_("Statut"), max_length=10, choices=FORECAST_STATUS, default='draft')

    # Montants prévisionnels
    opening_balance = models.DecimalField(
        _("Solde d'ouverture"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_inflows = models.DecimalField(
        _("Total des entrées"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_outflows = models.DecimalField(
        _("Total des sorties"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    closing_balance = models.DecimalField(
        _("Solde de clôture"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Responsable
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_forecasts',
        verbose_name=_("Créé par")
    )

    # Métadonnées
    description = models.TextField(_("Description"), blank=True)
    assumptions = models.TextField(_("Hypothèses"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Prévision de trésorerie")
        verbose_name_plural = _("Prévisions de trésorerie")
        ordering = ['-start_date', 'name']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

    @property
    def net_cash_flow(self):
        """Flux de trésorerie net"""
        return self.total_inflows - self.total_outflows

    def calculate_totals(self):
        """Calcule les totaux de la prévision"""
        lines = self.lines.all()

        self.total_inflows = sum(
            line.amount for line in lines
            if line.flow_type == 'inflow'
        )
        self.total_outflows = sum(
            line.amount for line in lines
            if line.flow_type == 'outflow'
        )
        self.closing_balance = self.opening_balance + self.net_cash_flow

        self.save(update_fields=['total_inflows', 'total_outflows', 'closing_balance'])


class CashFlowForecastLine(UUIDModel, TimeStampedModel):
    """Modèle pour les lignes de prévision de trésorerie"""

    FLOW_TYPES = [
        ('inflow', _('Entrée')),
        ('outflow', _('Sortie')),
    ]

    FLOW_CATEGORIES = [
        # Entrées
        ('sales_revenue', _('Chiffre d\'affaires')),
        ('customer_payments', _('Encaissements clients')),
        ('loan_proceeds', _('Produits d\'emprunts')),
        ('investment_income', _('Revenus d\'investissement')),
        ('asset_sales', _('Cessions d\'actifs')),
        ('other_income', _('Autres revenus')),

        # Sorties
        ('supplier_payments', _('Paiements fournisseurs')),
        ('payroll', _('Salaires et charges')),
        ('loan_payments', _('Remboursements d\'emprunts')),
        ('tax_payments', _('Paiements d\'impôts')),
        ('capex', _('Investissements')),
        ('operating_expenses', _('Charges d\'exploitation')),
        ('other_expenses', _('Autres charges')),
    ]

    forecast = models.ForeignKey(
        CashFlowForecast,
        on_delete=models.CASCADE,
        related_name='lines',
        verbose_name=_("Prévision")
    )

    # Identification
    line_number = models.PositiveIntegerField(_("Numéro de ligne"))
    description = models.CharField(_("Description"), max_length=200)

    # Type et catégorie
    flow_type = models.CharField(_("Type de flux"), max_length=10, choices=FLOW_TYPES)
    category = models.CharField(_("Catégorie"), max_length=30, choices=FLOW_CATEGORIES)

    # Montant et date
    amount = models.DecimalField(
        _("Montant"),
        max_digits=15,
        decimal_places=2
    )
    expected_date = models.DateField(_("Date prévue"))

    # Probabilité et confiance
    probability = models.DecimalField(
        _("Probabilité (%)"),
        max_digits=5,
        decimal_places=2,
        default=Decimal('100.00'),
        validators=[MinValueValidator(Decimal('0.00')), MaxValueValidator(Decimal('100.00'))]
    )
    confidence_level = models.CharField(
        _("Niveau de confiance"),
        max_length=10,
        choices=[
            ('high', _('Élevé')),
            ('medium', _('Moyen')),
            ('low', _('Faible')),
        ],
        default='medium'
    )

    # Références
    reference_document = models.CharField(_("Document de référence"), max_length=100, blank=True)
    customer_supplier = models.CharField(_("Client/Fournisseur"), max_length=200, blank=True)

    # Récurrence
    is_recurring = models.BooleanField(_("Récurrent"), default=False)
    recurrence_pattern = models.CharField(
        _("Modèle de récurrence"),
        max_length=20,
        choices=[
            ('daily', _('Quotidien')),
            ('weekly', _('Hebdomadaire')),
            ('monthly', _('Mensuel')),
            ('quarterly', _('Trimestriel')),
            ('annual', _('Annuel')),
        ],
        blank=True
    )

    # Métadonnées
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Ligne de prévision")
        verbose_name_plural = _("Lignes de prévision")
        unique_together = ['forecast', 'line_number']
        ordering = ['forecast', 'line_number']

    def __str__(self):
        return f"{self.forecast.name} - Ligne {self.line_number}: {self.description}"

    @property
    def weighted_amount(self):
        """Montant pondéré par la probabilité"""
        return self.amount * (self.probability / 100)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Mettre à jour les totaux de la prévision
        if self.forecast_id:
            self.forecast.calculate_totals()


class Investment(UUIDModel, TimeStampedModel):
    """Modèle pour les investissements"""

    INVESTMENT_TYPES = [
        ('stocks', _('Actions')),
        ('bonds', _('Obligations')),
        ('mutual_funds', _('Fonds communs')),
        ('etf', _('ETF')),
        ('real_estate', _('Immobilier')),
        ('commodities', _('Matières premières')),
        ('crypto', _('Cryptomonnaies')),
        ('term_deposit', _('Dépôt à terme')),
        ('money_market', _('Marché monétaire')),
        ('private_equity', _('Capital-investissement')),
        ('other', _('Autre')),
    ]

    INVESTMENT_STATUS = [
        ('active', _('Actif')),
        ('sold', _('Vendu')),
        ('matured', _('Arrivé à échéance')),
        ('suspended', _('Suspendu')),
    ]

    RISK_LEVELS = [
        (1, _('Très faible')),
        (2, _('Faible')),
        (3, _('Modéré')),
        (4, _('Élevé')),
        (5, _('Très élevé')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='investments',
        verbose_name=_("Tenant")
    )

    # Identification
    investment_code = models.CharField(_("Code investissement"), max_length=50)
    name = models.CharField(_("Nom de l'investissement"), max_length=200)
    investment_type = models.CharField(_("Type d'investissement"), max_length=20, choices=INVESTMENT_TYPES)

    # Détails financiers
    initial_amount = models.DecimalField(
        _("Montant initial"),
        max_digits=15,
        decimal_places=2
    )
    current_value = models.DecimalField(
        _("Valeur actuelle"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    currency = models.CharField(_("Devise"), max_length=3, default='EUR')

    # Dates
    purchase_date = models.DateField(_("Date d'achat"))
    maturity_date = models.DateField(_("Date d'échéance"), null=True, blank=True)
    sale_date = models.DateField(_("Date de vente"), null=True, blank=True)

    # Rendement
    expected_return_rate = models.DecimalField(
        _("Taux de rendement attendu (%)"),
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True
    )
    actual_return_rate = models.DecimalField(
        _("Taux de rendement réel (%)"),
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Risque
    risk_level = models.PositiveIntegerField(_("Niveau de risque"), choices=RISK_LEVELS, default=3)

    # Statut
    status = models.CharField(_("Statut"), max_length=15, choices=INVESTMENT_STATUS, default='active')

    # Responsable
    portfolio_manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_investments',
        verbose_name=_("Gestionnaire de portefeuille")
    )

    # Informations complémentaires
    description = models.TextField(_("Description"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Investissement")
        verbose_name_plural = _("Investissements")
        unique_together = ['tenant', 'investment_code']
        ordering = ['-purchase_date']

    def __str__(self):
        return f"{self.name} ({self.investment_code})"

    @property
    def gain_loss(self):
        """Gain ou perte sur l'investissement"""
        return self.current_value - self.initial_amount

    @property
    def gain_loss_percentage(self):
        """Pourcentage de gain ou perte"""
        if self.initial_amount > 0:
            return (self.gain_loss / self.initial_amount) * 100
        return Decimal('0.00')

    @property
    def is_profitable(self):
        """Indique si l'investissement est rentable"""
        return self.gain_loss > 0


class Loan(UUIDModel, TimeStampedModel):
    """Modèle pour les emprunts et crédits"""

    LOAN_TYPES = [
        ('term_loan', _('Prêt à terme')),
        ('credit_line', _('Ligne de crédit')),
        ('mortgage', _('Prêt hypothécaire')),
        ('equipment_loan', _('Crédit équipement')),
        ('working_capital', _('Crédit de trésorerie')),
        ('overdraft', _('Découvert autorisé')),
        ('bond_issue', _('Émission obligataire')),
        ('leasing', _('Crédit-bail')),
        ('other', _('Autre')),
    ]

    LOAN_STATUS = [
        ('active', _('Actif')),
        ('fully_paid', _('Remboursé')),
        ('defaulted', _('En défaut')),
        ('restructured', _('Restructuré')),
        ('cancelled', _('Annulé')),
    ]

    INTEREST_TYPES = [
        ('fixed', _('Taux fixe')),
        ('variable', _('Taux variable')),
        ('mixed', _('Taux mixte')),
    ]

    REPAYMENT_FREQUENCIES = [
        ('monthly', _('Mensuel')),
        ('quarterly', _('Trimestriel')),
        ('semi_annual', _('Semestriel')),
        ('annual', _('Annuel')),
        ('bullet', _('In fine')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='loans',
        verbose_name=_("Tenant")
    )

    # Identification
    loan_number = models.CharField(_("Numéro de prêt"), max_length=50)
    loan_name = models.CharField(_("Nom du prêt"), max_length=200)
    loan_type = models.CharField(_("Type de prêt"), max_length=20, choices=LOAN_TYPES)

    # Prêteur
    lender_name = models.CharField(_("Nom du prêteur"), max_length=200)
    lender_contact = models.CharField(_("Contact prêteur"), max_length=200, blank=True)

    # Montants
    principal_amount = models.DecimalField(
        _("Montant principal"),
        max_digits=15,
        decimal_places=2
    )
    outstanding_balance = models.DecimalField(
        _("Solde restant dû"),
        max_digits=15,
        decimal_places=2
    )
    currency = models.CharField(_("Devise"), max_length=3, default='EUR')

    # Taux d'intérêt
    interest_rate = models.DecimalField(
        _("Taux d'intérêt (%)"),
        max_digits=5,
        decimal_places=2
    )
    interest_type = models.CharField(_("Type de taux"), max_length=10, choices=INTEREST_TYPES, default='fixed')

    # Dates
    disbursement_date = models.DateField(_("Date de déblocage"))
    maturity_date = models.DateField(_("Date d'échéance"))
    first_payment_date = models.DateField(_("Date du premier remboursement"))

    # Remboursement
    repayment_frequency = models.CharField(_("Fréquence de remboursement"), max_length=15, choices=REPAYMENT_FREQUENCIES)
    monthly_payment = models.DecimalField(
        _("Mensualité"),
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Garanties
    collateral_description = models.TextField(_("Description des garanties"), blank=True)
    collateral_value = models.DecimalField(
        _("Valeur des garanties"),
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Covenants
    debt_to_equity_covenant = models.DecimalField(
        _("Covenant ratio d'endettement"),
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True
    )
    interest_coverage_covenant = models.DecimalField(
        _("Covenant couverture des intérêts"),
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Statut
    status = models.CharField(_("Statut"), max_length=15, choices=LOAN_STATUS, default='active')

    # Responsable
    loan_officer = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_loans',
        verbose_name=_("Chargé de crédit")
    )

    # Informations complémentaires
    purpose = models.TextField(_("Objet du prêt"), blank=True)
    terms_conditions = models.TextField(_("Conditions particulières"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Emprunt")
        verbose_name_plural = _("Emprunts")
        unique_together = ['tenant', 'loan_number']
        ordering = ['-disbursement_date']

    def __str__(self):
        return f"{self.loan_name} ({self.loan_number})"

    @property
    def total_paid(self):
        """Montant total remboursé"""
        return self.principal_amount - self.outstanding_balance

    @property
    def payment_progress_percentage(self):
        """Pourcentage de remboursement"""
        if self.principal_amount > 0:
            return (self.total_paid / self.principal_amount) * 100
        return Decimal('0.00')

    @property
    def remaining_payments(self):
        """Nombre de paiements restants (estimation)"""
        if self.monthly_payment and self.monthly_payment > 0:
            return int(self.outstanding_balance / self.monthly_payment)
        return 0


class FinancialRatio(UUIDModel, TimeStampedModel):
    """Modèle pour les ratios financiers"""

    RATIO_CATEGORIES = [
        ('liquidity', _('Liquidité')),
        ('profitability', _('Rentabilité')),
        ('leverage', _('Endettement')),
        ('efficiency', _('Efficacité')),
        ('market', _('Marché')),
        ('coverage', _('Couverture')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='financial_ratios',
        verbose_name=_("Tenant")
    )

    # Identification
    ratio_name = models.CharField(_("Nom du ratio"), max_length=200)
    ratio_code = models.CharField(_("Code ratio"), max_length=50)
    category = models.CharField(_("Catégorie"), max_length=15, choices=RATIO_CATEGORIES)

    # Période
    calculation_date = models.DateField(_("Date de calcul"))
    period_start = models.DateField(_("Début de période"))
    period_end = models.DateField(_("Fin de période"))

    # Valeurs
    ratio_value = models.DecimalField(
        _("Valeur du ratio"),
        max_digits=10,
        decimal_places=4
    )
    benchmark_value = models.DecimalField(
        _("Valeur de référence"),
        max_digits=10,
        decimal_places=4,
        null=True,
        blank=True
    )
    target_value = models.DecimalField(
        _("Valeur cible"),
        max_digits=10,
        decimal_places=4,
        null=True,
        blank=True
    )

    # Calcul
    numerator = models.DecimalField(
        _("Numérateur"),
        max_digits=15,
        decimal_places=2
    )
    denominator = models.DecimalField(
        _("Dénominateur"),
        max_digits=15,
        decimal_places=2
    )

    # Métadonnées
    calculation_method = models.TextField(_("Méthode de calcul"), blank=True)
    interpretation = models.TextField(_("Interprétation"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    # Responsable
    calculated_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='calculated_ratios',
        verbose_name=_("Calculé par")
    )

    class Meta:
        verbose_name = _("Ratio financier")
        verbose_name_plural = _("Ratios financiers")
        unique_together = ['tenant', 'ratio_code', 'calculation_date']
        ordering = ['-calculation_date', 'category', 'ratio_name']

    def __str__(self):
        return f"{self.ratio_name} ({self.calculation_date}): {self.ratio_value}"

    @property
    def variance_from_benchmark(self):
        """Écart par rapport à la référence"""
        if self.benchmark_value:
            return self.ratio_value - self.benchmark_value
        return None

    @property
    def variance_from_target(self):
        """Écart par rapport à la cible"""
        if self.target_value:
            return self.ratio_value - self.target_value
        return None

    @property
    def performance_vs_benchmark(self):
        """Performance par rapport à la référence (%)"""
        if self.benchmark_value and self.benchmark_value != 0:
            return ((self.ratio_value - self.benchmark_value) / self.benchmark_value) * 100
        return None


class TreasuryReport(UUIDModel, TimeStampedModel):
    """Modèle pour les rapports de trésorerie"""

    REPORT_TYPES = [
        ('daily_cash', _('Position de trésorerie quotidienne')),
        ('cash_flow', _('Tableau de flux de trésorerie')),
        ('forecast_vs_actual', _('Prévisionnel vs Réalisé')),
        ('bank_reconciliation', _('Rapprochement bancaire')),
        ('investment_portfolio', _('Portefeuille d\'investissements')),
        ('loan_portfolio', _('Portefeuille de crédits')),
        ('financial_ratios', _('Ratios financiers')),
        ('liquidity_analysis', _('Analyse de liquidité')),
    ]

    REPORT_STATUS = [
        ('generating', _('En cours de génération')),
        ('completed', _('Terminé')),
        ('error', _('Erreur')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='treasury_reports',
        verbose_name=_("Tenant")
    )

    # Identification
    report_name = models.CharField(_("Nom du rapport"), max_length=200)
    report_type = models.CharField(_("Type de rapport"), max_length=25, choices=REPORT_TYPES)

    # Période
    report_date = models.DateField(_("Date du rapport"))
    period_start = models.DateField(_("Début de période"))
    period_end = models.DateField(_("Fin de période"))

    # Statut
    status = models.CharField(_("Statut"), max_length=15, choices=REPORT_STATUS, default='generating')

    # Données du rapport (JSON)
    report_data = models.JSONField(_("Données du rapport"), default=dict, blank=True)

    # Paramètres
    include_forecasts = models.BooleanField(_("Inclure les prévisions"), default=False)
    include_investments = models.BooleanField(_("Inclure les investissements"), default=True)
    include_loans = models.BooleanField(_("Inclure les emprunts"), default=True)
    currency_filter = models.CharField(_("Filtre devise"), max_length=3, blank=True)

    # Utilisateur
    generated_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='generated_treasury_reports',
        verbose_name=_("Généré par")
    )

    # Métadonnées
    generation_time = models.DurationField(_("Temps de génération"), null=True, blank=True)
    error_message = models.TextField(_("Message d'erreur"), blank=True)

    class Meta:
        verbose_name = _("Rapport de trésorerie")
        verbose_name_plural = _("Rapports de trésorerie")
        ordering = ['-report_date', '-created_at']

    def __str__(self):
        return f"{self.report_name} ({self.report_date})"
