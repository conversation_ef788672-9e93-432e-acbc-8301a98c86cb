from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import uuid

# Modèle de base pour tous les agents
class BaseAgent(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    status = models.CharField(max_length=20, choices=[
        ('active', 'Actif'),
        ('inactive', 'Inactif'),
        ('maintenance', 'Maintenance'),
        ('error', 'Erreur')
    ], default='active')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(default=timezone.now)

    class Meta:
        abstract = True

# Agent Manager - Supervision générale
class ManagerAgent(BaseAgent):
    total_users = models.IntegerField(default=0)
    active_sessions = models.IntegerField(default=0)
    system_health = models.JSONField(default=dict)
    performance_metrics = models.JSONField(default=dict)

    class Meta:
        db_table = 'agent_manager'

# Agent HR - Ressources Humaines
class HRAgent(BaseAgent):
    total_employees = models.IntegerField(default=0)
    departments = models.JSONField(default=list)
    pending_requests = models.IntegerField(default=0)

    class Meta:
        db_table = 'agent_hr'

class Employee(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    employee_id = models.CharField(max_length=20, unique=True)
    department = models.CharField(max_length=100)
    position = models.CharField(max_length=100)
    hire_date = models.DateField()
    salary = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, default='active')

    class Meta:
        db_table = 'hr_employees'

# Agent Sales - Gestion commerciale
class SalesAgent(BaseAgent):
    total_leads = models.IntegerField(default=0)
    conversion_rate = models.FloatField(default=0.0)
    monthly_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    class Meta:
        db_table = 'agent_sales'

class Lead(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    company = models.CharField(max_length=200)
    status = models.CharField(max_length=20, choices=[
        ('new', 'Nouveau'),
        ('contacted', 'Contacté'),
        ('qualified', 'Qualifié'),
        ('proposal', 'Proposition'),
        ('won', 'Gagné'),
        ('lost', 'Perdu')
    ], default='new')
    value = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'sales_leads'

# Agent Purchase - Gestion des achats
class PurchaseAgent(BaseAgent):
    total_orders = models.IntegerField(default=0)
    pending_approvals = models.IntegerField(default=0)
    monthly_spending = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    class Meta:
        db_table = 'agent_purchase'

class PurchaseOrder(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order_number = models.CharField(max_length=50, unique=True)
    supplier = models.CharField(max_length=200)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(max_length=20, choices=[
        ('draft', 'Brouillon'),
        ('pending', 'En attente'),
        ('approved', 'Approuvé'),
        ('ordered', 'Commandé'),
        ('received', 'Reçu'),
        ('cancelled', 'Annulé')
    ], default='draft')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'purchase_orders'

# Agent Logistics - Transport et livraisons
class LogisticsAgent(BaseAgent):
    active_shipments = models.IntegerField(default=0)
    delivery_rate = models.FloatField(default=0.0)
    average_delivery_time = models.FloatField(default=0.0)

    class Meta:
        db_table = 'agent_logistics'

class Shipment(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tracking_number = models.CharField(max_length=100, unique=True)
    origin = models.CharField(max_length=200)
    destination = models.CharField(max_length=200)
    status = models.CharField(max_length=20, choices=[
        ('pending', 'En attente'),
        ('in_transit', 'En transit'),
        ('delivered', 'Livré'),
        ('delayed', 'Retardé'),
        ('cancelled', 'Annulé')
    ], default='pending')
    estimated_delivery = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'logistics_shipments'

# Agent Stock - Gestion d'inventaire
class StockAgent(BaseAgent):
    total_products = models.IntegerField(default=0)
    low_stock_alerts = models.IntegerField(default=0)
    total_value = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    class Meta:
        db_table = 'agent_stock'

class Product(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    sku = models.CharField(max_length=50, unique=True)
    name = models.CharField(max_length=200)
    category = models.CharField(max_length=100)
    quantity = models.IntegerField(default=0)
    min_quantity = models.IntegerField(default=10)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'stock_products'

# Agent Accounting - Comptabilité
class AccountingAgent(BaseAgent):
    total_transactions = models.IntegerField(default=0)
    monthly_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    monthly_expenses = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    class Meta:
        db_table = 'agent_accounting'

class Transaction(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    reference = models.CharField(max_length=100, unique=True)
    type = models.CharField(max_length=20, choices=[
        ('income', 'Recette'),
        ('expense', 'Dépense'),
        ('transfer', 'Virement')
    ])
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    description = models.TextField()
    date = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'accounting_transactions'

# Agent Finance - Gestion financière
class FinanceAgent(BaseAgent):
    cash_flow = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    profit_margin = models.FloatField(default=0.0)
    budget_variance = models.FloatField(default=0.0)

    class Meta:
        db_table = 'agent_finance'

# Agent CRM - Relation client
class CRMAgent(BaseAgent):
    total_customers = models.IntegerField(default=0)
    satisfaction_score = models.FloatField(default=0.0)
    retention_rate = models.FloatField(default=0.0)

    class Meta:
        db_table = 'agent_crm'

class Customer(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    company = models.CharField(max_length=200, blank=True)
    status = models.CharField(max_length=20, choices=[
        ('prospect', 'Prospect'),
        ('active', 'Actif'),
        ('inactive', 'Inactif'),
        ('churned', 'Perdu')
    ], default='prospect')
    lifetime_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'crm_customers'

# Agent BI - Business Intelligence
class BIAgent(BaseAgent):
    reports_generated = models.IntegerField(default=0)
    data_sources = models.IntegerField(default=0)
    last_analysis = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'agent_bi'

# Modèle pour les métriques temps réel
class RealtimeMetric(models.Model):
    agent_type = models.CharField(max_length=50)
    metric_name = models.CharField(max_length=100)
    value = models.JSONField()
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'realtime_metrics'
        indexes = [
            models.Index(fields=['agent_type', 'timestamp']),
            models.Index(fields=['metric_name', 'timestamp']),
        ]

# Modèle pour les notifications inter-agents
class AgentNotification(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    from_agent = models.CharField(max_length=50)
    to_agent = models.CharField(max_length=50)
    message = models.TextField()
    data = models.JSONField(default=dict)
    priority = models.CharField(max_length=20, choices=[
        ('low', 'Faible'),
        ('medium', 'Moyenne'),
        ('high', 'Haute'),
        ('critical', 'Critique')
    ], default='medium')
    read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'agent_notifications'
