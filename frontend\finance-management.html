<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Finance - Gestion Financière | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #f97316 30%, #ea580c 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #f97316;
            color: white;
        }
        
        .btn-primary:hover {
            background: #ea580c;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .btn-info {
            background: #3b82f6;
            color: white;
        }
        
        .btn-info:hover {
            background: #2563eb;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #f97316;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #f97316;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .amount-positive {
            color: #10b981;
            font-weight: 600;
        }
        
        .amount-negative {
            color: #ef4444;
            font-weight: 600;
        }
        
        .amount-neutral {
            color: #6b7280;
            font-weight: 600;
        }
        
        .balance-critical {
            color: #ef4444;
            font-weight: 700;
        }
        
        .balance-warning {
            color: #f59e0b;
            font-weight: 600;
        }
        
        .balance-good {
            color: #10b981;
            font-weight: 600;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #f97316;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">💰 Agent Finance - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="openModal('movementModal')">
                <span class="material-icons" style="font-size: 1rem;">add</span>
                Nouveau Mouvement
            </button>
            <button class="btn btn-info" onclick="openModal('budgetModal')">
                <span class="material-icons" style="font-size: 1rem;">account_balance_wallet</span>
                Gérer Budgets
            </button>
            <button class="btn btn-success" onclick="generateFinanceReport()">
                <span class="material-icons" style="font-size: 1rem;">assessment</span>
                Générer Rapport
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion Financière</h1>
            <p class="page-subtitle">Trésorerie, budgets et analyses financières</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalTreasury">0€</div>
                <div class="stat-label">Trésorerie Totale</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="monthlyVariation">0€</div>
                <div class="stat-label">Variation Mensuelle</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="availableBudget">0€</div>
                <div class="stat-label">Budget Disponible</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="monthlyROI">0%</div>
                <div class="stat-label">ROI Mensuel</div>
            </div>
        </div>

        <!-- Comptes bancaires -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Comptes Bancaires</h2>
                <button class="btn btn-primary" onclick="refreshAccounts()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div id="alertContainer"></div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Compte</th>
                                <th>Banque</th>
                                <th>Type</th>
                                <th>Solde</th>
                                <th>Seuil Alerte</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="accountsTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Mouvements de trésorerie -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Mouvements de Trésorerie</h2>
                <button class="btn btn-primary" onclick="refreshMovements()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date Transaction</th>
                                <th>Date Valeur</th>
                                <th>Date Échéance</th>
                                <th>Libellé</th>
                                <th>Compte</th>
                                <th>Type</th>
                                <th>Montant</th>
                                <th>Référence</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="movementsTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Mouvement Bancaire -->
    <div id="movementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="movementModalTitle">Nouveau Mouvement Bancaire</h3>
                <button class="close-btn" onclick="closeModal('movementModal')">&times;</button>
            </div>
            <form id="movementForm">
                <div id="movementModalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="movementDate">Date *</label>
                        <input type="date" id="movementDate" name="movementDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementAccount">Compte *</label>
                        <select id="movementAccount" name="movementAccount" class="form-select" required>
                            <option value="">Sélectionner un compte</option>
                            <!-- Options chargées dynamiquement -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementType">Type *</label>
                        <select id="movementType" name="movementType" class="form-select" required>
                            <option value="">Sélectionner un type</option>
                            <option value="income">Recette (+)</option>
                            <option value="expense">Dépense (-)</option>
                            <option value="transfer">Virement</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementAmount">Montant (€) *</label>
                        <input type="number" id="movementAmount" name="movementAmount" class="form-input" required min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementLabel">Libellé *</label>
                        <input type="text" id="movementLabel" name="movementLabel" class="form-input" required placeholder="Description du mouvement">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementReference">Référence</label>
                        <input type="text" id="movementReference" name="movementReference" class="form-input" placeholder="N° facture, chèque, etc.">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementCategory">Catégorie</label>
                        <select id="movementCategory" name="movementCategory" class="form-select">
                            <option value="">Sélectionner une catégorie</option>
                            <option value="sales">Ventes</option>
                            <option value="purchases">Achats</option>
                            <option value="salaries">Salaires</option>
                            <option value="taxes">Taxes et charges</option>
                            <option value="investments">Investissements</option>
                            <option value="loans">Emprunts</option>
                            <option value="other">Autres</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="movementNotes">Notes</label>
                    <textarea id="movementNotes" name="movementNotes" class="form-textarea" placeholder="Notes complémentaires..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('movementModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveMovementBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal Budget -->
    <div id="budgetModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="budgetModalTitle">Gestion des Budgets</h3>
                <button class="close-btn" onclick="closeModal('budgetModal')">&times;</button>
            </div>
            <form id="budgetForm">
                <div id="budgetModalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="budgetDepartment">Département *</label>
                        <select id="budgetDepartment" name="budgetDepartment" class="form-select" required>
                            <option value="">Sélectionner un département</option>
                            <option value="IT">Informatique</option>
                            <option value="HR">Ressources Humaines</option>
                            <option value="Sales">Commercial</option>
                            <option value="Marketing">Marketing</option>
                            <option value="Operations">Opérations</option>
                            <option value="Finance">Finance</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="budgetPeriod">Période *</label>
                        <select id="budgetPeriod" name="budgetPeriod" class="form-select" required>
                            <option value="">Sélectionner une période</option>
                            <option value="2024-Q1">Q1 2024</option>
                            <option value="2024-Q2">Q2 2024</option>
                            <option value="2024-Q3">Q3 2024</option>
                            <option value="2024-Q4">Q4 2024</option>
                            <option value="2024">Année 2024</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="budgetScenario">Scénario *</label>
                        <select id="budgetScenario" name="budgetScenario" class="form-select" required>
                            <option value="realistic">Réaliste (base)</option>
                            <option value="pessimistic">Pessimiste (-20%)</option>
                            <option value="optimistic">Optimiste (+20%)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="budgetAmount">Montant Budget (€) *</label>
                        <input type="number" id="budgetAmount" name="budgetAmount" class="form-input" required min="0" step="100">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="budgetSpent">Montant Dépensé (€)</label>
                        <input type="number" id="budgetSpent" name="budgetSpent" class="form-input" min="0" step="0.01" value="0">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="budgetNotes">Notes</label>
                    <textarea id="budgetNotes" name="budgetNotes" class="form-textarea" placeholder="Objectifs et notes sur le budget..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('budgetModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveBudgetBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let accounts = [];
        let movements = [];
        let budgets = [];
        let editingMovementId = null;
        let editingBudgetId = null;
        let isLoading = false;

        // Fonction utilitaire pour formater les dates
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        }

        // Données de démonstration réalistes - Comptes bancaires
        const demoAccounts = [
            {
                id: 'ACC-001',
                name: 'Compte Principal',
                bank: 'BNP Paribas',
                type: 'Courant',
                balance: 125000.00,
                alertThreshold: 10000.00,
                iban: 'FR76 3000 6000 0112 3456 7890 189'
            },
            {
                id: 'ACC-002',
                name: 'Compte Épargne',
                bank: 'Crédit Agricole',
                type: 'Épargne',
                balance: 85000.00,
                alertThreshold: 50000.00,
                iban: 'FR76 1751 2000 0003 0123 4567 890'
            },
            {
                id: 'ACC-003',
                name: 'Ligne de Crédit',
                bank: 'Société Générale',
                type: 'Crédit',
                balance: -15000.00,
                alertThreshold: -50000.00,
                iban: 'FR76 3000 3000 4000 0123 4567 890'
            },
            {
                id: 'ACC-004',
                name: 'Compte Devises USD',
                bank: 'HSBC',
                type: 'Devises',
                balance: 45000.00,
                alertThreshold: 5000.00,
                iban: 'FR76 3005 6000 0112 3456 7890 189'
            }
        ];

        // Données de démonstration réalistes - Mouvements bancaires
        const demoMovements = [
            {
                id: 'MOV-001',
                date: '2024-01-25',
                label: 'Virement client Global Manufacturing',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'income',
                amount: 54000.00,
                reference: 'VIR-CLI-001',
                category: 'sales',
                notes: 'Règlement facture FACT-CLI-001',
                valueDate: '2024-01-26',
                dueDate: null,
                createdDate: '2024-01-25T14:30:00'
            },
            {
                id: 'MOV-002',
                date: '2024-01-20',
                label: 'Paiement salaires janvier',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'expense',
                amount: 25000.00,
                reference: 'PAIE-2024-01',
                category: 'salaries',
                notes: 'Salaires nets équipe - 10 employés'
            },
            {
                id: 'MOV-003',
                date: '2024-01-18',
                label: 'Paiement fournisseur TechSupply Co',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'expense',
                amount: 9600.00,
                reference: 'FACT-2024-001',
                category: 'purchases',
                notes: 'Ordinateurs portables Dell'
            },
            {
                id: 'MOV-004',
                date: '2024-01-15',
                label: 'Charges sociales URSSAF',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'expense',
                amount: 15500.00,
                reference: 'URSSAF-2024-01',
                category: 'taxes',
                notes: 'Charges sociales janvier 2024'
            },
            {
                id: 'MOV-005',
                date: '2024-01-30',
                label: 'Virement client StartupTech',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'income',
                amount: 9600.00,
                reference: 'VIR-CLI-002',
                category: 'sales',
                notes: 'Règlement facture services'
            },
            {
                id: 'MOV-006',
                date: '2024-01-12',
                label: 'Placement épargne entreprise',
                accountId: 'ACC-002',
                accountName: 'Compte Épargne',
                type: 'income',
                amount: 50000.00,
                reference: 'VIR-INT-001',
                category: 'investments',
                notes: 'Transfert excédent de trésorerie'
            },
            {
                id: 'MOV-007',
                date: '2024-01-28',
                label: 'Remboursement emprunt équipement',
                accountId: 'ACC-003',
                accountName: 'Ligne de Crédit',
                type: 'expense',
                amount: 5000.00,
                reference: 'PRET-2024-01',
                category: 'loans',
                notes: 'Mensualité janvier - matériel informatique'
            },
            {
                id: 'MOV-008',
                date: '2024-01-22',
                label: 'Frais bancaires',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'expense',
                amount: 125.00,
                reference: 'FRAIS-2024-01',
                category: 'other',
                notes: 'Commission de tenue de compte'
            }
        ];

        // Données de démonstration réalistes - Budgets
        const demoBudgets = [
            {
                id: 'BUD-001',
                department: 'IT',
                period: '2024-Q1',
                scenario: 'realistic',
                budgetAmount: 50000.00,
                spentAmount: 12000.00,
                notes: 'Renouvellement matériel informatique'
            },
            {
                id: 'BUD-002',
                department: 'HR',
                period: '2024-Q1',
                scenario: 'realistic',
                budgetAmount: 80000.00,
                spentAmount: 25000.00,
                notes: 'Salaires et charges sociales'
            },
            {
                id: 'BUD-003',
                department: 'Sales',
                period: '2024-Q1',
                scenario: 'optimistic',
                budgetAmount: 30000.00,
                spentAmount: 8500.00,
                notes: 'Marketing et prospection commerciale'
            },
            {
                id: 'BUD-004',
                department: 'Marketing',
                period: '2024-Q1',
                scenario: 'realistic',
                budgetAmount: 25000.00,
                spentAmount: 6200.00,
                notes: 'Campagnes publicitaires et événements'
            }
        ];

        // Fonctions utilitaires
        function showAlert(message, type = 'error', container = 'alertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            if (modalId === 'movementModal') {
                document.getElementById('movementModalTitle').textContent = editingMovementId ? 'Modifier Mouvement' : 'Nouveau Mouvement Bancaire';
                loadAccountsInSelect();
                if (!editingMovementId) {
                    document.getElementById('movementDate').value = new Date().toISOString().split('T')[0];
                }
            } else if (modalId === 'budgetModal') {
                document.getElementById('budgetModalTitle').textContent = editingBudgetId ? 'Modifier Budget' : 'Gestion des Budgets';
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            if (modalId === 'movementModal') {
                document.getElementById('movementForm').reset();
                document.getElementById('movementModalAlertContainer').innerHTML = '';
                editingMovementId = null;
            } else if (modalId === 'budgetModal') {
                document.getElementById('budgetForm').reset();
                document.getElementById('budgetModalAlertContainer').innerHTML = '';
                editingBudgetId = null;
            }
        }

        function loadAccountsInSelect() {
            const select = document.getElementById('movementAccount');
            select.innerHTML = '<option value="">Sélectionner un compte</option>';

            accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.id;
                option.textContent = `${account.name} - ${account.bank}`;
                select.appendChild(option);
            });
        }

        // Calcul des statistiques financières
        function updateStats() {
            // Trésorerie totale
            const totalTreasury = accounts.reduce((sum, acc) => sum + acc.balance, 0);

            // Variation mensuelle (mouvements du mois en cours)
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();
            const monthlyMovements = movements.filter(m => {
                const moveDate = new Date(m.date);
                return moveDate.getMonth() === currentMonth && moveDate.getFullYear() === currentYear;
            });

            const monthlyIncome = monthlyMovements
                .filter(m => m.type === 'income')
                .reduce((sum, m) => sum + m.amount, 0);
            const monthlyExpenses = monthlyMovements
                .filter(m => m.type === 'expense')
                .reduce((sum, m) => sum + m.amount, 0);
            const monthlyVariation = monthlyIncome - monthlyExpenses;

            // Budget disponible
            const totalBudget = budgets.reduce((sum, b) => sum + b.budgetAmount, 0);
            const totalSpent = budgets.reduce((sum, b) => sum + b.spentAmount, 0);
            const availableBudget = totalBudget - totalSpent;

            // ROI mensuel (simplifié)
            const monthlyROI = totalTreasury > 0 ? ((monthlyVariation / totalTreasury) * 100) : 0;

            // Mise à jour de l'affichage
            document.getElementById('totalTreasury').textContent = totalTreasury.toLocaleString() + '€';
            document.getElementById('monthlyVariation').textContent = monthlyVariation.toLocaleString() + '€';
            document.getElementById('availableBudget').textContent = availableBudget.toLocaleString() + '€';
            document.getElementById('monthlyROI').textContent = monthlyROI.toFixed(1) + '%';

            // Coloration des valeurs
            const variationElement = document.getElementById('monthlyVariation');
            variationElement.className = 'stat-value ' + (monthlyVariation >= 0 ? 'amount-positive' : 'amount-negative');

            const roiElement = document.getElementById('monthlyROI');
            roiElement.className = 'stat-value ' + (monthlyROI >= 0 ? 'amount-positive' : 'amount-negative');
        }

        // Déterminer le statut d'un compte selon son solde
        function getAccountStatus(account) {
            if (account.type === 'Crédit') {
                // Pour les comptes de crédit, la logique est inversée
                if (account.balance <= account.alertThreshold) {
                    return { class: 'balance-good', text: 'Normal', badge: 'badge-success' };
                } else {
                    return { class: 'balance-warning', text: 'Attention', badge: 'badge-warning' };
                }
            } else {
                // Pour les autres comptes
                if (account.balance <= account.alertThreshold) {
                    return { class: 'balance-critical', text: 'Critique', badge: 'badge-danger' };
                } else if (account.balance <= account.alertThreshold * 2) {
                    return { class: 'balance-warning', text: 'Attention', badge: 'badge-warning' };
                } else {
                    return { class: 'balance-good', text: 'Normal', badge: 'badge-success' };
                }
            }
        }

        // Rendu du tableau des comptes
        function renderAccountsTable() {
            const tbody = document.getElementById('accountsTableBody');

            if (accounts.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun compte trouvé. Contactez l'administrateur.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = accounts.map(account => {
                const status = getAccountStatus(account);
                return `
                    <tr>
                        <td>${account.name}<br><small style="color: #6b7280;">${account.iban}</small></td>
                        <td>${account.bank}</td>
                        <td>${account.type}</td>
                        <td><span class="${status.class}">${account.balance.toLocaleString()}€</span></td>
                        <td>${account.alertThreshold.toLocaleString()}€</td>
                        <td><span class="badge ${status.badge}">${status.text}</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="viewAccountDetails('${account.id}')" title="Détails">
                                <span class="material-icons" style="font-size: 1rem;">visibility</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Rendu du tableau des mouvements
        function renderMovementsTable() {
            const tbody = document.getElementById('movementsTableBody');

            if (movements.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun mouvement trouvé. Cliquez sur "Nouveau Mouvement" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            // Trier par date décroissante
            const sortedMovements = [...movements].sort((a, b) => new Date(b.date) - new Date(a.date));

            tbody.innerHTML = sortedMovements.map(movement => {
                const typeDisplay = getMovementTypeDisplay(movement.type);
                const amountClass = movement.type === 'income' ? 'amount-positive' : 'amount-negative';
                const amountSign = movement.type === 'income' ? '+' : '-';

                return `
                    <tr>
                        <td>${new Date(movement.date).toLocaleDateString()}</td>
                        <td>${movement.label}</td>
                        <td>${movement.accountName}</td>
                        <td>${typeDisplay}</td>
                        <td><span class="${amountClass}">${amountSign}${movement.amount.toLocaleString()}€</span></td>
                        <td>${movement.reference || '-'}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editMovement('${movement.id}')" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteMovement('${movement.id}')" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getMovementTypeDisplay(type) {
            const types = {
                'income': '<span class="badge badge-success">Recette</span>',
                'expense': '<span class="badge badge-danger">Dépense</span>',
                'transfer': '<span class="badge badge-info">Virement</span>'
            };
            return types[type] || '<span class="badge badge-warning">Inconnu</span>';
        }

        // Chargement des données
        async function loadFinanceData() {
            try {
                // Tentative de chargement depuis l'API
                const token = localStorage.getItem('access_token');
                if (token) {
                    const response = await fetch('http://localhost:8000/api/agents/finance/accounts/', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        accounts = data.accounts || [];
                        movements = data.movements || [];
                        budgets = data.budgets || [];
                    } else {
                        throw new Error('Erreur API');
                    }
                } else {
                    throw new Error('Pas de token');
                }
            } catch (error) {
                console.log('Chargement des données de démonstration');
                accounts = [...demoAccounts];
                movements = [...demoMovements];
                budgets = [...demoBudgets];
            }

            renderAccountsTable();
            renderMovementsTable();
            updateStats();
        }

        function refreshAccounts() {
            loadFinanceData();
            showAlert('Données actualisées avec succès', 'success');
        }

        function refreshMovements() {
            loadFinanceData();
            showAlert('Données actualisées avec succès', 'success');
        }

        function viewAccountDetails(accountId) {
            const account = accounts.find(a => a.id === accountId);
            if (!account) return;

            const accountMovements = movements.filter(m => m.accountId === accountId);
            const totalIncome = accountMovements.filter(m => m.type === 'income').reduce((sum, m) => sum + m.amount, 0);
            const totalExpenses = accountMovements.filter(m => m.type === 'expense').reduce((sum, m) => sum + m.amount, 0);

            alert(`Détails du compte ${account.name}:\n\n` +
                  `Banque: ${account.bank}\n` +
                  `Type: ${account.type}\n` +
                  `Solde actuel: ${account.balance.toLocaleString()}€\n` +
                  `Seuil d'alerte: ${account.alertThreshold.toLocaleString()}€\n\n` +
                  `Mouvements:\n` +
                  `- Total recettes: ${totalIncome.toLocaleString()}€\n` +
                  `- Total dépenses: ${totalExpenses.toLocaleString()}€\n` +
                  `- Nombre de mouvements: ${accountMovements.length}`);
        }

        function editMovement(id) {
            const movement = movements.find(m => m.id === id);
            if (!movement) return;

            editingMovementId = id;

            // Remplir le formulaire
            document.getElementById('movementDate').value = movement.date;
            document.getElementById('movementAccount').value = movement.accountId;
            document.getElementById('movementType').value = movement.type;
            document.getElementById('movementAmount').value = movement.amount;
            document.getElementById('movementLabel').value = movement.label;
            document.getElementById('movementReference').value = movement.reference || '';
            document.getElementById('movementCategory').value = movement.category || '';
            document.getElementById('movementNotes').value = movement.notes || '';

            openModal('movementModal');
        }

        function deleteMovement(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce mouvement ?')) {
                movements = movements.filter(m => m.id !== id);
                renderMovementsTable();
                updateStats();
                showAlert('Mouvement supprimé avec succès', 'success');
            }
        }

        // Génération du rapport financier
        function generateFinanceReport() {
            const totalTreasury = accounts.reduce((sum, acc) => sum + acc.balance, 0);
            const totalBudget = budgets.reduce((sum, b) => sum + b.budgetAmount, 0);
            const totalSpent = budgets.reduce((sum, b) => sum + b.spentAmount, 0);

            // Calculs par catégorie
            const categoryTotals = {};
            movements.forEach(movement => {
                const category = movement.category || 'other';
                if (!categoryTotals[category]) {
                    categoryTotals[category] = { income: 0, expense: 0 };
                }
                if (movement.type === 'income') {
                    categoryTotals[category].income += movement.amount;
                } else if (movement.type === 'expense') {
                    categoryTotals[category].expense += movement.amount;
                }
            });

            let reportContent = `
                <h2>Rapport Financier - ${new Date().toLocaleDateString()}</h2>

                <h3>Synthèse Trésorerie</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Compte</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Solde</th>
                    </tr>
            `;

            accounts.forEach(account => {
                const balanceColor = account.balance >= 0 ? '#10b981' : '#ef4444';
                reportContent += `
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">${account.name} (${account.bank})</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: ${balanceColor};">${account.balance.toLocaleString()}€</td>
                    </tr>
                `;
            });

            reportContent += `
                    <tr style="background: #f9fafb; font-weight: bold;">
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">TOTAL TRÉSORERIE</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${totalTreasury.toLocaleString()}€</td>
                    </tr>
                </table>

                <h3>Analyse par Catégorie</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Catégorie</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Recettes</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Dépenses</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Solde</th>
                    </tr>
            `;

            Object.keys(categoryTotals).forEach(category => {
                const cat = categoryTotals[category];
                const balance = cat.income - cat.expense;
                const balanceColor = balance >= 0 ? '#10b981' : '#ef4444';
                const categoryName = {
                    'sales': 'Ventes',
                    'purchases': 'Achats',
                    'salaries': 'Salaires',
                    'taxes': 'Taxes et charges',
                    'investments': 'Investissements',
                    'loans': 'Emprunts',
                    'other': 'Autres'
                }[category] || category;

                reportContent += `
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">${categoryName}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">${cat.income.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #ef4444;">${cat.expense.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: ${balanceColor};">${balance.toLocaleString()}€</td>
                    </tr>
                `;
            });

            reportContent += `
                </table>

                <h3>Suivi Budgétaire</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Département</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Budget</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Dépensé</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Disponible</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">% Utilisé</th>
                    </tr>
            `;

            budgets.forEach(budget => {
                const available = budget.budgetAmount - budget.spentAmount;
                const percentUsed = (budget.spentAmount / budget.budgetAmount * 100).toFixed(1);
                const percentColor = percentUsed > 80 ? '#ef4444' : percentUsed > 60 ? '#f59e0b' : '#10b981';

                reportContent += `
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">${budget.department}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${budget.budgetAmount.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${budget.spentAmount.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${available.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: ${percentColor};">${percentUsed}%</td>
                    </tr>
                `;
            });

            reportContent += `
                </table>
            `;

            // Ouvrir le rapport dans une nouvelle fenêtre
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Rapport Financier - ERP HUB</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 2rem; }
                        h2 { color: #f97316; margin-bottom: 1rem; }
                        h3 { color: #374151; margin: 1.5rem 0 0.5rem 0; }
                        table { border-collapse: collapse; width: 100%; }
                        th, td { border: 1px solid #e5e7eb; padding: 0.5rem; }
                        th { background: #f9fafb; font-weight: 600; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${reportContent}
                    <div style="margin-top: 2rem; text-align: center;">
                        <button onclick="window.print()" style="padding: 0.5rem 1rem; background: #f97316; color: white; border: none; border-radius: 0.25rem; cursor: pointer;">
                            Imprimer
                        </button>
                    </div>
                </body>
                </html>
            `);
            reportWindow.document.close();
        }

        // Gestion du formulaire mouvement
        document.getElementById('movementForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const accountId = formData.get('movementAccount');
            const account = accounts.find(a => a.id === accountId);

            if (!account) {
                showAlert('Compte non trouvé', 'error', 'movementModalAlertContainer');
                isLoading = false;
                return;
            }

            const movementData = {
                date: formData.get('movementDate'),
                accountId: accountId,
                accountName: account.name,
                type: formData.get('movementType'),
                amount: parseFloat(formData.get('movementAmount')),
                label: formData.get('movementLabel'),
                reference: formData.get('movementReference'),
                category: formData.get('movementCategory'),
                notes: formData.get('movementNotes')
            };

            try {
                if (editingMovementId) {
                    // Modification
                    const index = movements.findIndex(m => m.id === editingMovementId);
                    if (index !== -1) {
                        movements[index] = { ...movements[index], ...movementData };
                        showAlert('Mouvement modifié avec succès', 'success');
                    }
                } else {
                    // Création
                    const newMovement = {
                        id: `MOV-${String(movements.length + 1).padStart(3, '0')}`,
                        ...movementData
                    };
                    movements.push(newMovement);
                    showAlert('Mouvement créé avec succès', 'success');
                }

                renderMovementsTable();
                updateStats();
                closeModal('movementModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'movementModalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Gestion du formulaire budget
        document.getElementById('budgetForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const budgetData = {
                department: formData.get('budgetDepartment'),
                period: formData.get('budgetPeriod'),
                scenario: formData.get('budgetScenario'),
                budgetAmount: parseFloat(formData.get('budgetAmount')),
                spentAmount: parseFloat(formData.get('budgetSpent')) || 0,
                notes: formData.get('budgetNotes')
            };

            try {
                if (editingBudgetId) {
                    // Modification
                    const index = budgets.findIndex(b => b.id === editingBudgetId);
                    if (index !== -1) {
                        budgets[index] = { ...budgets[index], ...budgetData };
                        showAlert('Budget modifié avec succès', 'success');
                    }
                } else {
                    // Création
                    const newBudget = {
                        id: `BUD-${String(budgets.length + 1).padStart(3, '0')}`,
                        ...budgetData
                    };
                    budgets.push(newBudget);
                    showAlert('Budget créé avec succès', 'success');
                }

                updateStats();
                closeModal('budgetModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'budgetModalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadFinanceData();
            }
        });
    </script>
</body>
</html>
