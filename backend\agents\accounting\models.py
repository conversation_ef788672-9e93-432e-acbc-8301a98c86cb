"""
Modèles pour l'Agent Accounting - Comptabilité générale et analytique
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid

from core.models import TimeStampedModel, UUIDModel, Tenant, User


class ChartOfAccounts(UUIDModel, TimeStampedModel):
    """Plan comptable"""

    ACCOUNT_TYPES = [
        ('asset', _('Actif')),
        ('liability', _('Passif')),
        ('equity', _('Capitaux propres')),
        ('revenue', _('Produits')),
        ('expense', _('Charges')),
    ]

    ACCOUNT_CATEGORIES = [
        # Actifs
        ('current_assets', _('Actifs circulants')),
        ('fixed_assets', _('Immobilisations')),
        ('intangible_assets', _('Immobilisations incorporelles')),

        # Passifs
        ('current_liabilities', _('Dettes à court terme')),
        ('long_term_liabilities', _('Dettes à long terme')),

        # Capitaux propres
        ('share_capital', _('Capital social')),
        ('retained_earnings', _('Résultats reportés')),
        ('reserves', _('Réserves')),

        # Produits
        ('operating_revenue', _('Chiffre d\'affaires')),
        ('other_revenue', _('Autres produits')),
        ('financial_revenue', _('Produits financiers')),

        # Charges
        ('cost_of_sales', _('Coût des ventes')),
        ('operating_expenses', _('Charges d\'exploitation')),
        ('financial_expenses', _('Charges financières')),
        ('exceptional_expenses', _('Charges exceptionnelles')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='chart_of_accounts',
        verbose_name=_("Tenant")
    )

    # Identification
    account_code = models.CharField(_("Code compte"), max_length=20)
    account_name = models.CharField(_("Nom du compte"), max_length=200)
    account_type = models.CharField(_("Type de compte"), max_length=20, choices=ACCOUNT_TYPES)
    account_category = models.CharField(_("Catégorie"), max_length=30, choices=ACCOUNT_CATEGORIES)

    # Hiérarchie
    parent_account = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='sub_accounts',
        verbose_name=_("Compte parent")
    )
    level = models.PositiveIntegerField(_("Niveau"), default=1)

    # Propriétés
    is_active = models.BooleanField(_("Actif"), default=True)
    is_reconcilable = models.BooleanField(_("Lettrable"), default=False)
    allow_manual_entries = models.BooleanField(_("Saisie manuelle autorisée"), default=True)

    # Soldes
    opening_balance = models.DecimalField(
        _("Solde d'ouverture"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    current_balance = models.DecimalField(
        _("Solde actuel"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Métadonnées
    description = models.TextField(_("Description"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Compte comptable")
        verbose_name_plural = _("Plan comptable")
        unique_together = ['tenant', 'account_code']
        ordering = ['account_code']

    def __str__(self):
        return f"{self.account_code} - {self.account_name}"

    @property
    def full_code(self):
        """Code complet avec hiérarchie"""
        if self.parent_account:
            return f"{self.parent_account.full_code}.{self.account_code}"
        return self.account_code

    def get_balance(self, date=None):
        """Calcule le solde à une date donnée"""
        from django.utils import timezone
        if date is None:
            date = timezone.now().date()

        # Calculer le solde basé sur les écritures
        entries = self.journal_entries.filter(entry_date__lte=date)
        debit_total = entries.aggregate(total=models.Sum('debit_amount'))['total'] or Decimal('0.00')
        credit_total = entries.aggregate(total=models.Sum('credit_amount'))['total'] or Decimal('0.00')

        if self.account_type in ['asset', 'expense']:
            return self.opening_balance + debit_total - credit_total
        else:  # liability, equity, revenue
            return self.opening_balance + credit_total - debit_total


class FiscalYear(UUIDModel, TimeStampedModel):
    """Exercice comptable"""

    FISCAL_YEAR_STATUS = [
        ('open', _('Ouvert')),
        ('closed', _('Clôturé')),
        ('locked', _('Verrouillé')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='fiscal_years',
        verbose_name=_("Tenant")
    )

    # Période
    name = models.CharField(_("Nom de l'exercice"), max_length=100)
    start_date = models.DateField(_("Date de début"))
    end_date = models.DateField(_("Date de fin"))

    # Statut
    status = models.CharField(_("Statut"), max_length=10, choices=FISCAL_YEAR_STATUS, default='open')
    is_current = models.BooleanField(_("Exercice en cours"), default=False)

    # Dates importantes
    closed_date = models.DateTimeField(_("Date de clôture"), null=True, blank=True)
    locked_date = models.DateTimeField(_("Date de verrouillage"), null=True, blank=True)

    # Responsable
    closed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='closed_fiscal_years',
        verbose_name=_("Clôturé par")
    )

    class Meta:
        verbose_name = _("Exercice comptable")
        verbose_name_plural = _("Exercices comptables")
        unique_together = ['tenant', 'name']
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

    def save(self, *args, **kwargs):
        # S'assurer qu'un seul exercice est marqué comme courant
        if self.is_current:
            FiscalYear.objects.filter(tenant=self.tenant, is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class Journal(UUIDModel, TimeStampedModel):
    """Journal comptable"""

    JOURNAL_TYPES = [
        ('sales', _('Ventes')),
        ('purchases', _('Achats')),
        ('cash', _('Caisse')),
        ('bank', _('Banque')),
        ('general', _('Opérations diverses')),
        ('opening', _('À-nouveaux')),
        ('closing', _('Clôture')),
        ('inventory', _('Inventaire')),
        ('payroll', _('Paie')),
        ('fixed_assets', _('Immobilisations')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='journals',
        verbose_name=_("Tenant")
    )

    # Identification
    code = models.CharField(_("Code journal"), max_length=10)
    name = models.CharField(_("Nom du journal"), max_length=100)
    journal_type = models.CharField(_("Type de journal"), max_length=20, choices=JOURNAL_TYPES)

    # Configuration
    default_debit_account = models.ForeignKey(
        ChartOfAccounts,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='default_debit_journals',
        verbose_name=_("Compte de débit par défaut")
    )
    default_credit_account = models.ForeignKey(
        ChartOfAccounts,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='default_credit_journals',
        verbose_name=_("Compte de crédit par défaut")
    )

    # Numérotation
    sequence_prefix = models.CharField(_("Préfixe de numérotation"), max_length=10, blank=True)
    sequence_number = models.PositiveIntegerField(_("Numéro de séquence"), default=1)

    # Propriétés
    is_active = models.BooleanField(_("Actif"), default=True)
    allow_cancellation = models.BooleanField(_("Autoriser l'annulation"), default=True)
    require_validation = models.BooleanField(_("Validation requise"), default=False)

    # Responsable
    responsible_user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_journals',
        verbose_name=_("Responsable")
    )

    class Meta:
        verbose_name = _("Journal")
        verbose_name_plural = _("Journaux")
        unique_together = ['tenant', 'code']
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

    def get_next_sequence_number(self):
        """Obtient le prochain numéro de séquence"""
        current = self.sequence_number
        self.sequence_number += 1
        self.save(update_fields=['sequence_number'])
        return current


class AccountingEntry(UUIDModel, TimeStampedModel):
    """Écriture comptable"""

    ENTRY_STATUS = [
        ('draft', _('Brouillon')),
        ('posted', _('Comptabilisée')),
        ('validated', _('Validée')),
        ('cancelled', _('Annulée')),
    ]

    ENTRY_TYPES = [
        ('manual', _('Saisie manuelle')),
        ('automatic', _('Automatique')),
        ('import', _('Importée')),
        ('adjustment', _('Écriture de régularisation')),
        ('closing', _('Écriture de clôture')),
        ('opening', _('À-nouveau')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='accounting_entries',
        verbose_name=_("Tenant")
    )

    # Identification
    entry_number = models.CharField(_("Numéro d'écriture"), max_length=50, unique=True)
    reference = models.CharField(_("Référence"), max_length=100, blank=True)

    # Journal et exercice
    journal = models.ForeignKey(
        Journal,
        on_delete=models.PROTECT,
        related_name='entries',
        verbose_name=_("Journal")
    )
    fiscal_year = models.ForeignKey(
        FiscalYear,
        on_delete=models.PROTECT,
        related_name='entries',
        verbose_name=_("Exercice comptable")
    )

    # Dates
    entry_date = models.DateField(_("Date d'écriture"))
    value_date = models.DateField(_("Date de valeur"), null=True, blank=True)
    due_date = models.DateField(_("Date d'échéance"), null=True, blank=True)

    # Description
    description = models.TextField(_("Libellé"))
    notes = models.TextField(_("Notes"), blank=True)

    # Statut et type
    status = models.CharField(_("Statut"), max_length=10, choices=ENTRY_STATUS, default='draft')
    entry_type = models.CharField(_("Type d'écriture"), max_length=15, choices=ENTRY_TYPES, default='manual')

    # Montants
    total_debit = models.DecimalField(
        _("Total débit"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_credit = models.DecimalField(
        _("Total crédit"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Utilisateurs
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_entries',
        verbose_name=_("Créé par")
    )
    validated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='validated_entries',
        verbose_name=_("Validé par")
    )
    validated_date = models.DateTimeField(_("Date de validation"), null=True, blank=True)

    # Références externes
    source_document_type = models.CharField(_("Type de document source"), max_length=50, blank=True)
    source_document_id = models.CharField(_("ID document source"), max_length=100, blank=True)

    # Lettrage
    reconciliation_ref = models.CharField(_("Référence de lettrage"), max_length=50, blank=True)
    is_reconciled = models.BooleanField(_("Lettré"), default=False)

    class Meta:
        verbose_name = _("Écriture comptable")
        verbose_name_plural = _("Écritures comptables")
        ordering = ['-entry_date', '-entry_number']

    def __str__(self):
        return f"{self.entry_number} - {self.description}"

    def save(self, *args, **kwargs):
        # Générer le numéro d'écriture si nécessaire
        if not self.entry_number:
            self.entry_number = self._generate_entry_number()

        # Calculer les totaux
        if self.pk:
            self._update_totals()

        super().save(*args, **kwargs)

    def _generate_entry_number(self):
        """Génère un numéro d'écriture unique"""
        from django.utils import timezone
        current_year = timezone.now().year
        sequence = self.journal.get_next_sequence_number()

        if self.journal.sequence_prefix:
            return f"{self.journal.sequence_prefix}{current_year}{sequence:06d}"
        else:
            return f"{self.journal.code}{current_year}{sequence:06d}"

    def _update_totals(self):
        """Met à jour les totaux débit et crédit"""
        lines = self.lines.all()
        self.total_debit = sum(line.debit_amount for line in lines)
        self.total_credit = sum(line.credit_amount for line in lines)

    @property
    def is_balanced(self):
        """Vérifie si l'écriture est équilibrée"""
        return self.total_debit == self.total_credit

    def post(self, user):
        """Comptabilise l'écriture"""
        if not self.is_balanced:
            raise ValueError("L'écriture n'est pas équilibrée")

        if self.status != 'draft':
            raise ValueError("Seules les écritures en brouillon peuvent être comptabilisées")

        self.status = 'posted'
        self.save()

        # Mettre à jour les soldes des comptes
        for line in self.lines.all():
            account = line.account
            if account.account_type in ['asset', 'expense']:
                account.current_balance += line.debit_amount - line.credit_amount
            else:  # liability, equity, revenue
                account.current_balance += line.credit_amount - line.debit_amount
            account.save(update_fields=['current_balance'])


class AccountingEntryLine(UUIDModel, TimeStampedModel):
    """Ligne d'écriture comptable"""

    entry = models.ForeignKey(
        AccountingEntry,
        on_delete=models.CASCADE,
        related_name='lines',
        verbose_name=_("Écriture")
    )

    account = models.ForeignKey(
        ChartOfAccounts,
        on_delete=models.PROTECT,
        related_name='journal_entries',
        verbose_name=_("Compte")
    )

    # Montants
    debit_amount = models.DecimalField(
        _("Montant débit"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    credit_amount = models.DecimalField(
        _("Montant crédit"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Description
    description = models.CharField(_("Libellé"), max_length=200)

    # Références
    partner_type = models.CharField(_("Type de tiers"), max_length=20, blank=True)
    partner_id = models.CharField(_("ID tiers"), max_length=100, blank=True)
    partner_name = models.CharField(_("Nom du tiers"), max_length=200, blank=True)

    # Analytique
    analytic_account = models.CharField(_("Compte analytique"), max_length=50, blank=True)
    cost_center = models.CharField(_("Centre de coût"), max_length=50, blank=True)
    project_code = models.CharField(_("Code projet"), max_length=50, blank=True)

    # Lettrage
    reconciliation_ref = models.CharField(_("Référence de lettrage"), max_length=50, blank=True)
    is_reconciled = models.BooleanField(_("Lettré"), default=False)
    reconciled_date = models.DateField(_("Date de lettrage"), null=True, blank=True)

    # Taxes
    tax_code = models.CharField(_("Code taxe"), max_length=20, blank=True)
    tax_amount = models.DecimalField(
        _("Montant de taxe"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    class Meta:
        verbose_name = _("Ligne d'écriture")
        verbose_name_plural = _("Lignes d'écriture")
        ordering = ['entry', 'id']

    def __str__(self):
        return f"{self.entry.entry_number} - {self.account.account_code}"

    def save(self, *args, **kwargs):
        # Validation : un seul montant (débit ou crédit) doit être renseigné
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValueError("Une ligne ne peut avoir à la fois un montant débit et crédit")

        super().save(*args, **kwargs)

        # Mettre à jour les totaux de l'écriture
        if self.entry_id:
            self.entry._update_totals()
            self.entry.save(update_fields=['total_debit', 'total_credit'])

    @property
    def amount(self):
        """Retourne le montant de la ligne (débit ou crédit)"""
        return self.debit_amount if self.debit_amount > 0 else self.credit_amount

    @property
    def is_debit(self):
        """Indique si la ligne est au débit"""
        return self.debit_amount > 0


class TaxCode(UUIDModel, TimeStampedModel):
    """Code de taxe (TVA, etc.)"""

    TAX_TYPES = [
        ('vat', _('TVA')),
        ('sales_tax', _('Taxe sur les ventes')),
        ('withholding', _('Retenue à la source')),
        ('excise', _('Accise')),
        ('other', _('Autre')),
    ]

    TAX_SCOPE = [
        ('sale', _('Vente')),
        ('purchase', _('Achat')),
        ('both', _('Vente et Achat')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='tax_codes',
        verbose_name=_("Tenant")
    )

    # Identification
    code = models.CharField(_("Code taxe"), max_length=20)
    name = models.CharField(_("Nom de la taxe"), max_length=100)
    tax_type = models.CharField(_("Type de taxe"), max_length=15, choices=TAX_TYPES)
    scope = models.CharField(_("Domaine d'application"), max_length=10, choices=TAX_SCOPE)

    # Taux
    rate = models.DecimalField(
        _("Taux (%)"),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00')), MaxValueValidator(Decimal('100.00'))]
    )

    # Comptes comptables
    tax_account = models.ForeignKey(
        ChartOfAccounts,
        on_delete=models.PROTECT,
        related_name='tax_codes',
        verbose_name=_("Compte de taxe")
    )

    # Propriétés
    is_active = models.BooleanField(_("Actif"), default=True)
    is_default = models.BooleanField(_("Par défaut"), default=False)
    include_in_price = models.BooleanField(_("Inclus dans le prix"), default=False)

    # Dates de validité
    valid_from = models.DateField(_("Valide à partir du"))
    valid_to = models.DateField(_("Valide jusqu'au"), null=True, blank=True)

    class Meta:
        verbose_name = _("Code de taxe")
        verbose_name_plural = _("Codes de taxe")
        unique_together = ['tenant', 'code']
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name} ({self.rate}%)"


class Budget(UUIDModel, TimeStampedModel):
    """Budget"""

    BUDGET_TYPES = [
        ('annual', _('Budget annuel')),
        ('quarterly', _('Budget trimestriel')),
        ('monthly', _('Budget mensuel')),
        ('project', _('Budget de projet')),
        ('department', _('Budget de département')),
    ]

    BUDGET_STATUS = [
        ('draft', _('Brouillon')),
        ('submitted', _('Soumis')),
        ('approved', _('Approuvé')),
        ('active', _('Actif')),
        ('closed', _('Clôturé')),
        ('cancelled', _('Annulé')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='budgets',
        verbose_name=_("Tenant")
    )

    # Identification
    name = models.CharField(_("Nom du budget"), max_length=200)
    code = models.CharField(_("Code budget"), max_length=50)
    budget_type = models.CharField(_("Type de budget"), max_length=15, choices=BUDGET_TYPES)

    # Période
    fiscal_year = models.ForeignKey(
        FiscalYear,
        on_delete=models.PROTECT,
        related_name='budgets',
        verbose_name=_("Exercice comptable")
    )
    start_date = models.DateField(_("Date de début"))
    end_date = models.DateField(_("Date de fin"))

    # Statut
    status = models.CharField(_("Statut"), max_length=10, choices=BUDGET_STATUS, default='draft')

    # Montants
    total_revenue_budget = models.DecimalField(
        _("Budget total produits"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_expense_budget = models.DecimalField(
        _("Budget total charges"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Responsables
    budget_manager = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='managed_budgets',
        verbose_name=_("Responsable budget")
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_budgets',
        verbose_name=_("Approuvé par")
    )
    approved_date = models.DateTimeField(_("Date d'approbation"), null=True, blank=True)

    # Métadonnées
    description = models.TextField(_("Description"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Budget")
        verbose_name_plural = _("Budgets")
        unique_together = ['tenant', 'code', 'fiscal_year']
        ordering = ['-start_date', 'name']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

    @property
    def net_budget(self):
        """Budget net (produits - charges)"""
        return self.total_revenue_budget - self.total_expense_budget

    def calculate_totals(self):
        """Calcule les totaux du budget"""
        revenue_total = self.lines.filter(
            account__account_type='revenue'
        ).aggregate(total=models.Sum('budgeted_amount'))['total'] or Decimal('0.00')

        expense_total = self.lines.filter(
            account__account_type='expense'
        ).aggregate(total=models.Sum('budgeted_amount'))['total'] or Decimal('0.00')

        self.total_revenue_budget = revenue_total
        self.total_expense_budget = expense_total
        self.save(update_fields=['total_revenue_budget', 'total_expense_budget'])


class BudgetLine(UUIDModel, TimeStampedModel):
    """Ligne de budget"""

    budget = models.ForeignKey(
        Budget,
        on_delete=models.CASCADE,
        related_name='lines',
        verbose_name=_("Budget")
    )

    account = models.ForeignKey(
        ChartOfAccounts,
        on_delete=models.PROTECT,
        related_name='budget_lines',
        verbose_name=_("Compte")
    )

    # Montants
    budgeted_amount = models.DecimalField(
        _("Montant budgété"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Répartition mensuelle (optionnelle)
    january = models.DecimalField(_("Janvier"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    february = models.DecimalField(_("Février"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    march = models.DecimalField(_("Mars"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    april = models.DecimalField(_("Avril"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    may = models.DecimalField(_("Mai"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    june = models.DecimalField(_("Juin"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    july = models.DecimalField(_("Juillet"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    august = models.DecimalField(_("Août"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    september = models.DecimalField(_("Septembre"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    october = models.DecimalField(_("Octobre"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    november = models.DecimalField(_("Novembre"), max_digits=15, decimal_places=2, default=Decimal('0.00'))
    december = models.DecimalField(_("Décembre"), max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Analytique
    cost_center = models.CharField(_("Centre de coût"), max_length=50, blank=True)
    project_code = models.CharField(_("Code projet"), max_length=50, blank=True)

    # Métadonnées
    description = models.CharField(_("Description"), max_length=200, blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Ligne de budget")
        verbose_name_plural = _("Lignes de budget")
        unique_together = ['budget', 'account', 'cost_center', 'project_code']
        ordering = ['budget', 'account__account_code']

    def __str__(self):
        return f"{self.budget.name} - {self.account.account_code}"

    def save(self, *args, **kwargs):
        # Calculer le montant budgété total si répartition mensuelle
        monthly_total = (
            self.january + self.february + self.march + self.april +
            self.may + self.june + self.july + self.august +
            self.september + self.october + self.november + self.december
        )

        if monthly_total > 0 and self.budgeted_amount == 0:
            self.budgeted_amount = monthly_total

        super().save(*args, **kwargs)

        # Mettre à jour les totaux du budget
        if self.budget_id:
            self.budget.calculate_totals()

    def get_monthly_amount(self, month):
        """Retourne le montant budgété pour un mois donné (1-12)"""
        month_fields = [
            'january', 'february', 'march', 'april', 'may', 'june',
            'july', 'august', 'september', 'october', 'november', 'december'
        ]

        if 1 <= month <= 12:
            return getattr(self, month_fields[month - 1])
        return Decimal('0.00')


class FinancialReport(UUIDModel, TimeStampedModel):
    """Rapport financier"""

    REPORT_TYPES = [
        ('balance_sheet', _('Bilan')),
        ('income_statement', _('Compte de résultat')),
        ('cash_flow', _('Tableau de flux de trésorerie')),
        ('trial_balance', _('Balance générale')),
        ('aged_receivables', _('Balance âgée clients')),
        ('aged_payables', _('Balance âgée fournisseurs')),
        ('budget_vs_actual', _('Budget vs Réalisé')),
        ('custom', _('Rapport personnalisé')),
    ]

    REPORT_STATUS = [
        ('generating', _('En cours de génération')),
        ('completed', _('Terminé')),
        ('error', _('Erreur')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='financial_reports',
        verbose_name=_("Tenant")
    )

    # Identification
    name = models.CharField(_("Nom du rapport"), max_length=200)
    report_type = models.CharField(_("Type de rapport"), max_length=20, choices=REPORT_TYPES)

    # Période
    start_date = models.DateField(_("Date de début"))
    end_date = models.DateField(_("Date de fin"))
    fiscal_year = models.ForeignKey(
        FiscalYear,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='reports',
        verbose_name=_("Exercice comptable")
    )

    # Statut
    status = models.CharField(_("Statut"), max_length=15, choices=REPORT_STATUS, default='generating')

    # Données du rapport (JSON)
    report_data = models.JSONField(_("Données du rapport"), default=dict, blank=True)

    # Paramètres
    include_zero_balances = models.BooleanField(_("Inclure les soldes nuls"), default=False)
    group_by_category = models.BooleanField(_("Grouper par catégorie"), default=True)
    show_comparatives = models.BooleanField(_("Afficher les comparatifs"), default=False)

    # Utilisateur
    generated_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='generated_reports',
        verbose_name=_("Généré par")
    )

    # Métadonnées
    generation_time = models.DurationField(_("Temps de génération"), null=True, blank=True)
    error_message = models.TextField(_("Message d'erreur"), blank=True)

    class Meta:
        verbose_name = _("Rapport financier")
        verbose_name_plural = _("Rapports financiers")
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"


class ReconciliationRule(UUIDModel, TimeStampedModel):
    """Règle de lettrage automatique"""

    RULE_TYPES = [
        ('amount_match', _('Correspondance de montant')),
        ('reference_match', _('Correspondance de référence')),
        ('date_range', _('Plage de dates')),
        ('partner_match', _('Correspondance de tiers')),
        ('custom', _('Règle personnalisée')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='reconciliation_rules',
        verbose_name=_("Tenant")
    )

    # Identification
    name = models.CharField(_("Nom de la règle"), max_length=200)
    rule_type = models.CharField(_("Type de règle"), max_length=20, choices=RULE_TYPES)

    # Comptes concernés
    accounts = models.ManyToManyField(
        ChartOfAccounts,
        related_name='reconciliation_rules',
        verbose_name=_("Comptes concernés")
    )

    # Conditions
    amount_tolerance = models.DecimalField(
        _("Tolérance de montant"),
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    date_tolerance_days = models.PositiveIntegerField(_("Tolérance de date (jours)"), default=0)

    # Configuration
    is_active = models.BooleanField(_("Actif"), default=True)
    auto_apply = models.BooleanField(_("Application automatique"), default=False)
    priority = models.PositiveIntegerField(_("Priorité"), default=1)

    # Conditions personnalisées (JSON)
    custom_conditions = models.JSONField(_("Conditions personnalisées"), default=dict, blank=True)

    class Meta:
        verbose_name = _("Règle de lettrage")
        verbose_name_plural = _("Règles de lettrage")
        ordering = ['priority', 'name']

    def __str__(self):
        return self.name
