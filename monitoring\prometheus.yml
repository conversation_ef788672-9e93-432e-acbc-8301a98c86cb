# Configuration Prometheus pour ERP HUB
global:
  scrape_interval: 15s
  evaluation_interval: 15s

# Règles d'alertes
rule_files:
  - "alert_rules.yml"

# Configuration des alertes
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Targets à surveiller
scrape_configs:
  # Prometheus lui-même
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Backend Django
  - job_name: 'django-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/api/metrics/'
    scrape_interval: 30s

  # Frontend Nginx
  - job_name: 'nginx-frontend'
    static_configs:
      - targets: ['frontend:80']
    scrape_interval: 30s

  # Base de données PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Nginx reverse proxy
  - job_name: 'nginx-proxy'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s

  # Node Exporter (métriques système)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # cAdvisor (métriques containers)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
