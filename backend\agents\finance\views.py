"""
Vues pour l'agent Finance
"""
import logging
from django.db import models
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema

from core.permissions import FinanceReadPermission, FinanceWritePermission
from .services import FinanceService
from .models import (
    Bank, BankAccount, BankTransaction, CashFlowForecast,
    Investment, Loan, FinancialRatio, TreasuryReport
)
from .serializers import (
    BankSerializer, BankAccountSerializer, BankTransactionSerializer,
    BankTransactionCreateSerializer, CashFlowForecastSerializer,
    CashFlowForecastCreateSerializer, InvestmentSerializer, InvestmentCreateSerializer,
    LoanSerializer, LoanCreateSerializer, FinancialRatioSerializer,
    TreasuryReportSerializer, TreasuryReportGenerateSerializer,
    FinancialRatiosCalculateSerializer, FinanceDashboardSerializer,
    FinanceInsightSerializer
)

logger = logging.getLogger('agents.finance')


@extend_schema(
    summary="Statut de l'agent Finance",
    description="Retourne le statut de l'agent Finance"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def finance_status(request):
    """Retourne le statut de l'agent Finance"""
    try:
        finance_service = FinanceService(request.user.tenant)

        return Response({
            'status': 'active',
            'agent': 'finance',
            'message': 'Agent Finance opérationnel',
            'capabilities': [
                'treasury_management',
                'cash_flow_forecasting',
                'investment_portfolio',
                'loan_management',
                'financial_ratios',
                'risk_analysis',
                'liquidity_management',
                'financial_reporting'
            ]
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut Finance: {str(e)}")
        return Response({
            'status': 'error',
            'agent': 'finance',
            'message': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Dashboard de l'agent Finance",
    description="Retourne les données complètes du dashboard Finance"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, FinanceReadPermission])
def finance_dashboard(request):
    """Retourne les données du dashboard Finance"""
    try:
        finance_service = FinanceService(request.user.tenant)
        dashboard_data = finance_service.get_finance_dashboard()

        return Response(dashboard_data)
    except Exception as e:
        logger.error(f"Erreur lors de la génération du dashboard Finance: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Gestion des banques
class BankListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des banques"""
    serializer_class = BankSerializer
    permission_classes = [IsAuthenticated, FinanceReadPermission]

    def get_queryset(self):
        queryset = Bank.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        is_active = self.request.query_params.get('active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        is_primary = self.request.query_params.get('primary')
        if is_primary is not None:
            queryset = queryset.filter(is_primary=is_primary.lower() == 'true')

        # Recherche par nom ou code
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(bank_code__icontains=search)
            )

        return queryset.order_by('name')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class BankDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une banque"""
    serializer_class = BankSerializer
    permission_classes = [IsAuthenticated, FinanceWritePermission]

    def get_queryset(self):
        return Bank.objects.filter(tenant=self.request.user.tenant)


# Gestion des comptes bancaires
class BankAccountListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des comptes bancaires"""
    serializer_class = BankAccountSerializer
    permission_classes = [IsAuthenticated, FinanceReadPermission]

    def get_queryset(self):
        queryset = BankAccount.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        bank_id = self.request.query_params.get('bank')
        if bank_id:
            queryset = queryset.filter(bank_id=bank_id)

        account_type = self.request.query_params.get('type')
        if account_type:
            queryset = queryset.filter(account_type=account_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        currency = self.request.query_params.get('currency')
        if currency:
            queryset = queryset.filter(currency=currency)

        is_main = self.request.query_params.get('main')
        if is_main is not None:
            queryset = queryset.filter(is_main_account=is_main.lower() == 'true')

        # Recherche par nom ou numéro
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(account_name__icontains=search) |
                models.Q(account_number__icontains=search) |
                models.Q(iban__icontains=search)
            )

        return queryset.order_by('bank__name', 'account_name')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class BankAccountDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un compte bancaire"""
    serializer_class = BankAccountSerializer
    permission_classes = [IsAuthenticated, FinanceWritePermission]

    def get_queryset(self):
        return BankAccount.objects.filter(tenant=self.request.user.tenant)


# Gestion des transactions bancaires
class BankTransactionCreateView(APIView):
    """Vue pour créer des transactions bancaires avec logique métier"""
    permission_classes = [IsAuthenticated, FinanceWritePermission]

    @extend_schema(
        summary="Créer une transaction bancaire",
        description="Crée une nouvelle transaction bancaire avec mise à jour automatique des soldes",
        request=BankTransactionCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle transaction bancaire"""
        try:
            serializer = BankTransactionCreateSerializer(data=request.data)
            if serializer.is_valid():
                finance_service = FinanceService(request.user.tenant)

                result = finance_service.create_bank_transaction(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de la transaction: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BankTransactionListView(generics.ListAPIView):
    """Vue pour lister les transactions bancaires"""
    serializer_class = BankTransactionSerializer
    permission_classes = [IsAuthenticated, FinanceReadPermission]

    def get_queryset(self):
        queryset = BankTransaction.objects.filter(bank_account__tenant=self.request.user.tenant)

        # Filtres
        bank_account_id = self.request.query_params.get('account')
        if bank_account_id:
            queryset = queryset.filter(bank_account_id=bank_account_id)

        transaction_type = self.request.query_params.get('type')
        if transaction_type:
            queryset = queryset.filter(transaction_type=transaction_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)

        # Filtres par date
        date_from = self.request.query_params.get('date_from')
        if date_from:
            queryset = queryset.filter(transaction_date__gte=date_from)

        date_to = self.request.query_params.get('date_to')
        if date_to:
            queryset = queryset.filter(transaction_date__lte=date_to)

        # Recherche
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(transaction_id__icontains=search) |
                models.Q(description__icontains=search) |
                models.Q(counterpart_name__icontains=search) |
                models.Q(reference__icontains=search)
            )

        return queryset.order_by('-transaction_date', '-created_at')


# Gestion des prévisions de trésorerie
class CashFlowForecastCreateView(APIView):
    """Vue pour créer des prévisions de trésorerie avec logique métier"""
    permission_classes = [IsAuthenticated, FinanceWritePermission]

    @extend_schema(
        summary="Créer une prévision de trésorerie",
        description="Crée une nouvelle prévision de trésorerie avec calcul automatique des totaux",
        request=CashFlowForecastCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle prévision de trésorerie"""
        try:
            serializer = CashFlowForecastCreateSerializer(data=request.data)
            if serializer.is_valid():
                finance_service = FinanceService(request.user.tenant)

                result = finance_service.create_cash_flow_forecast(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de la prévision: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CashFlowForecastListView(generics.ListAPIView):
    """Vue pour lister les prévisions de trésorerie"""
    serializer_class = CashFlowForecastSerializer
    permission_classes = [IsAuthenticated, FinanceReadPermission]

    def get_queryset(self):
        queryset = CashFlowForecast.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        forecast_type = self.request.query_params.get('type')
        if forecast_type:
            queryset = queryset.filter(forecast_type=forecast_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-start_date', 'name')


# Gestion des investissements
class InvestmentCreateView(APIView):
    """Vue pour créer des investissements avec logique métier"""
    permission_classes = [IsAuthenticated, FinanceWritePermission]

    @extend_schema(
        summary="Créer un investissement",
        description="Crée un nouvel investissement avec génération automatique du code",
        request=InvestmentCreateSerializer
    )
    def post(self, request):
        """Crée un nouvel investissement"""
        try:
            serializer = InvestmentCreateSerializer(data=request.data)
            if serializer.is_valid():
                finance_service = FinanceService(request.user.tenant)

                result = finance_service.create_investment(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'investissement: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class InvestmentListView(generics.ListAPIView):
    """Vue pour lister les investissements"""
    serializer_class = InvestmentSerializer
    permission_classes = [IsAuthenticated, FinanceReadPermission]

    def get_queryset(self):
        queryset = Investment.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        investment_type = self.request.query_params.get('type')
        if investment_type:
            queryset = queryset.filter(investment_type=investment_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        risk_level = self.request.query_params.get('risk_level')
        if risk_level:
            queryset = queryset.filter(risk_level=risk_level)

        currency = self.request.query_params.get('currency')
        if currency:
            queryset = queryset.filter(currency=currency)

        # Recherche par nom ou code
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(investment_code__icontains=search)
            )

        return queryset.order_by('-purchase_date')


# Gestion des emprunts
class LoanCreateView(APIView):
    """Vue pour créer des emprunts avec logique métier"""
    permission_classes = [IsAuthenticated, FinanceWritePermission]

    @extend_schema(
        summary="Créer un emprunt",
        description="Crée un nouvel emprunt avec génération automatique du numéro",
        request=LoanCreateSerializer
    )
    def post(self, request):
        """Crée un nouvel emprunt"""
        try:
            serializer = LoanCreateSerializer(data=request.data)
            if serializer.is_valid():
                finance_service = FinanceService(request.user.tenant)

                result = finance_service.create_loan(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'emprunt: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class LoanListView(generics.ListAPIView):
    """Vue pour lister les emprunts"""
    serializer_class = LoanSerializer
    permission_classes = [IsAuthenticated, FinanceReadPermission]

    def get_queryset(self):
        queryset = Loan.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        loan_type = self.request.query_params.get('type')
        if loan_type:
            queryset = queryset.filter(loan_type=loan_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        interest_type = self.request.query_params.get('interest_type')
        if interest_type:
            queryset = queryset.filter(interest_type=interest_type)

        currency = self.request.query_params.get('currency')
        if currency:
            queryset = queryset.filter(currency=currency)

        # Recherche par nom ou numéro
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(loan_name__icontains=search) |
                models.Q(loan_number__icontains=search) |
                models.Q(lender_name__icontains=search)
            )

        return queryset.order_by('-disbursement_date')


# Gestion des rapports de trésorerie
class TreasuryReportGenerateView(APIView):
    """Vue pour générer des rapports de trésorerie"""
    permission_classes = [IsAuthenticated, FinanceReadPermission]

    @extend_schema(
        summary="Générer un rapport de trésorerie",
        description="Génère un rapport de trésorerie selon le type spécifié",
        request=TreasuryReportGenerateSerializer
    )
    def post(self, request):
        """Génère un rapport de trésorerie"""
        try:
            serializer = TreasuryReportGenerateSerializer(data=request.data)
            if serializer.is_valid():
                finance_service = FinanceService(request.user.tenant)

                result = finance_service.generate_treasury_report(
                    serializer.validated_data['report_type'],
                    serializer.validated_data['period_start'].isoformat(),
                    serializer.validated_data['period_end'].isoformat(),
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la génération du rapport: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TreasuryReportListView(generics.ListAPIView):
    """Vue pour lister les rapports de trésorerie"""
    serializer_class = TreasuryReportSerializer
    permission_classes = [IsAuthenticated, FinanceReadPermission]

    def get_queryset(self):
        queryset = TreasuryReport.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        report_type = self.request.query_params.get('type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-report_date', '-created_at')


# Calcul des ratios financiers
@extend_schema(
    summary="Calculer les ratios financiers",
    description="Calcule les ratios financiers pour une période donnée",
    request=FinancialRatiosCalculateSerializer
)
@api_view(['POST'])
@permission_classes([IsAuthenticated, FinanceWritePermission])
def calculate_financial_ratios(request):
    """Calcule les ratios financiers"""
    try:
        serializer = FinancialRatiosCalculateSerializer(data=request.data)
        if serializer.is_valid():
            finance_service = FinanceService(request.user.tenant)

            result = finance_service.calculate_financial_ratios(
                serializer.validated_data['period_start'].isoformat(),
                serializer.validated_data['period_end'].isoformat(),
                request.user
            )

            if result['success']:
                return Response(result, status=status.HTTP_201_CREATED)
            else:
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Erreur lors du calcul des ratios: {str(e)}")
        return Response({
            'error': f'Erreur interne: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Insights financiers
@extend_schema(
    summary="Insights financiers IA",
    description="Génère des insights et recommandations financières basés sur l'IA"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, FinanceReadPermission])
def finance_insights(request):
    """Génère des insights financiers avec IA"""
    try:
        finance_service = FinanceService(request.user.tenant)
        insights = finance_service.generate_finance_insights()

        return Response({'insights': insights})
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'insights: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération d\'insights: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Position de trésorerie
@extend_schema(
    summary="Position de trésorerie",
    description="Retourne la position de trésorerie actuelle"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, FinanceReadPermission])
def treasury_position(request):
    """Retourne la position de trésorerie"""
    try:
        finance_service = FinanceService(request.user.tenant)
        position = finance_service._calculate_treasury_position()

        return Response(position)
    except Exception as e:
        logger.error(f"Erreur lors du calcul de la position: {str(e)}")
        return Response({
            'error': f'Erreur lors du calcul de la position: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Analyse de liquidité
@extend_schema(
    summary="Analyse de liquidité",
    description="Retourne l'analyse de liquidité de l'entreprise"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, FinanceReadPermission])
def liquidity_analysis(request):
    """Retourne l'analyse de liquidité"""
    try:
        finance_service = FinanceService(request.user.tenant)
        analysis = finance_service._analyze_liquidity()

        return Response(analysis)
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse de liquidité: {str(e)}")
        return Response({
            'error': f'Erreur lors de l\'analyse de liquidité: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)