import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, Typography, Box, ToggleButtonGroup, ToggleButton } from '@mui/material';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

interface ChartData {
  name: string;
  value: number;
  value2?: number;
  [key: string]: any;
}

interface AnimatedChartProps {
  title: string;
  data: ChartData[];
  type?: 'line' | 'area' | 'bar' | 'pie';
  height?: number;
  color?: string;
  secondaryColor?: string;
  showToggle?: boolean;
  isLoading?: boolean;
  subtitle?: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export const AnimatedChart: React.FC<AnimatedChartProps> = ({
  title,
  data,
  type = 'line',
  height = 300,
  color = '#1976d2',
  secondaryColor = '#42a5f5',
  showToggle = true,
  isLoading = false,
  subtitle
}) => {
  const [chartType, setChartType] = useState(type);
  const [animatedData, setAnimatedData] = useState<ChartData[]>([]);

  useEffect(() => {
    // Animation des données
    const timer = setTimeout(() => {
      setAnimatedData(data);
    }, 300);

    return () => clearTimeout(timer);
  }, [data]);

  const handleTypeChange = (event: React.MouseEvent<HTMLElement>, newType: string | null) => {
    if (newType !== null) {
      setChartType(newType as any);
    }
  };

  const renderChart = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height={height}>
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            <Typography variant="h4">⏳</Typography>
          </motion.div>
        </Box>
      );
    }

    const commonProps = {
      data: animatedData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 }
    };

    switch (chartType) {
      case 'area':
        return (
          <AreaChart {...commonProps}>
            <defs>
              <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={color} stopOpacity={0.8}/>
                <stop offset="95%" stopColor={color} stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="name" stroke="#666" />
            <YAxis stroke="#666" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                border: 'none', 
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
              }} 
            />
            <Area 
              type="monotone" 
              dataKey="value" 
              stroke={color} 
              fillOpacity={1} 
              fill="url(#colorGradient)"
              strokeWidth={3}
            />
          </AreaChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="name" stroke="#666" />
            <YAxis stroke="#666" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                border: 'none', 
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
              }} 
            />
            <Bar dataKey="value" fill={color} radius={[4, 4, 0, 0]} />
            {animatedData[0]?.value2 && (
              <Bar dataKey="value2" fill={secondaryColor} radius={[4, 4, 0, 0]} />
            )}
          </BarChart>
        );

      case 'pie':
        return (
          <PieChart>
            <Pie
              data={animatedData}
              cx="50%"
              cy="50%"
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {animatedData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        );

      default: // line
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="name" stroke="#666" />
            <YAxis stroke="#666" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                border: 'none', 
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
              }} 
            />
            <Line 
              type="monotone" 
              dataKey="value" 
              stroke={color} 
              strokeWidth={3}
              dot={{ fill: color, strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: color, strokeWidth: 2 }}
            />
            {animatedData[0]?.value2 && (
              <Line 
                type="monotone" 
                dataKey="value2" 
                stroke={secondaryColor} 
                strokeWidth={3}
                dot={{ fill: secondaryColor, strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: secondaryColor, strokeWidth: 2 }}
              />
            )}
          </LineChart>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Card sx={{ height: '100%' }}>
        <CardHeader
          title={
            <Typography variant="h6" fontWeight={600}>
              {title}
            </Typography>
          }
          subheader={subtitle}
          action={
            showToggle && (
              <ToggleButtonGroup
                value={chartType}
                exclusive
                onChange={handleTypeChange}
                size="small"
              >
                <ToggleButton value="line">📈</ToggleButton>
                <ToggleButton value="area">📊</ToggleButton>
                <ToggleButton value="bar">📊</ToggleButton>
                <ToggleButton value="pie">🥧</ToggleButton>
              </ToggleButtonGroup>
            )
          }
        />
        <CardContent>
          <ResponsiveContainer width="100%" height={height}>
            {renderChart()}
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </motion.div>
  );
};
