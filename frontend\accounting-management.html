<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Accounting - Gestion Comptable | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #ef4444 30%, #dc2626 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #ef4444;
            color: white;
        }
        
        .btn-primary:hover {
            background: #dc2626;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .btn-info {
            background: #3b82f6;
            color: white;
        }
        
        .btn-info:hover {
            background: #2563eb;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #ef4444;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .amount-debit {
            color: #ef4444;
            font-weight: 600;
        }
        
        .amount-credit {
            color: #10b981;
            font-weight: 600;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #ef4444;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">🏦 Agent Accounting - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="openModal('entryModal')">
                <span class="material-icons" style="font-size: 1rem;">add</span>
                Nouvelle Écriture
            </button>
            <button class="btn btn-info" onclick="generateReport()">
                <span class="material-icons" style="font-size: 1rem;">assessment</span>
                Rapport
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion Comptable</h1>
            <p class="page-subtitle">Écritures comptables et suivi financier</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalEntries">0</div>
                <div class="stat-label">Écritures Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalDebit">0€</div>
                <div class="stat-label">Total Débit</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalCredit">0€</div>
                <div class="stat-label">Total Crédit</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="balance">0€</div>
                <div class="stat-label">Solde</div>
            </div>
        </div>

        <!-- Liste des écritures -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Journal Comptable</h2>
                <button class="btn btn-primary" onclick="refreshEntries()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div id="alertContainer"></div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>N° Écriture</th>
                                <th>Date</th>
                                <th>Compte</th>
                                <th>Libellé</th>
                                <th>Débit</th>
                                <th>Crédit</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="entriesTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Écriture Comptable -->
    <div id="entryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="entryModalTitle">Nouvelle Écriture Comptable</h3>
                <button class="close-btn" onclick="closeModal('entryModal')">&times;</button>
            </div>
            <form id="entryForm">
                <div id="entryModalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="entryDate">Date *</label>
                        <input type="date" id="entryDate" name="entryDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="accountNumber">N° Compte *</label>
                        <select id="accountNumber" name="accountNumber" class="form-select" required>
                            <option value="">Sélectionner un compte</option>
                            <option value="101000">101000 - Capital social</option>
                            <option value="120000">120000 - Résultat de l'exercice</option>
                            <option value="164000">164000 - Emprunts auprès des établissements de crédit</option>
                            <option value="211000">211000 - Terrains</option>
                            <option value="213000">213000 - Constructions</option>
                            <option value="218300">218300 - Matériel informatique</option>
                            <option value="218400">218400 - Mobilier</option>
                            <option value="401000">401000 - Fournisseurs</option>
                            <option value="411000">411000 - Clients</option>
                            <option value="421000">421000 - Personnel - Rémunérations dues</option>
                            <option value="431000">431000 - Sécurité sociale</option>
                            <option value="445710">445710 - TVA collectée</option>
                            <option value="445660">445660 - TVA déductible</option>
                            <option value="512000">512000 - Banque</option>
                            <option value="530000">530000 - Caisse</option>
                            <option value="601000">601000 - Achats de matières premières</option>
                            <option value="606000">606000 - Achats non stockés de matières et fournitures</option>
                            <option value="641000">641000 - Rémunérations du personnel</option>
                            <option value="645000">645000 - Charges de sécurité sociale</option>
                            <option value="661000">661000 - Charges d'intérêts</option>
                            <option value="701000">701000 - Ventes de produits finis</option>
                            <option value="706000">706000 - Prestations de services</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="description">Libellé *</label>
                        <input type="text" id="description" name="description" class="form-input" required placeholder="Description de l'opération">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="reference">Référence</label>
                        <input type="text" id="reference" name="reference" class="form-input" placeholder="N° facture, chèque, etc.">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="debitAmount">Montant Débit (€)</label>
                        <input type="number" id="debitAmount" name="debitAmount" class="form-input" min="0" step="0.01" placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="creditAmount">Montant Crédit (€)</label>
                        <input type="number" id="creditAmount" name="creditAmount" class="form-input" min="0" step="0.01" placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="entryType">Type d'écriture</label>
                        <select id="entryType" name="entryType" class="form-select">
                            <option value="standard">Standard</option>
                            <option value="opening">Ouverture</option>
                            <option value="closing">Clôture</option>
                            <option value="adjustment">Régularisation</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="status">Statut</label>
                        <select id="status" name="status" class="form-select">
                            <option value="draft">Brouillon</option>
                            <option value="validated" selected>Validée</option>
                            <option value="posted">Comptabilisée</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="notes">Notes</label>
                    <textarea id="notes" name="notes" class="form-textarea" placeholder="Notes complémentaires..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('entryModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveEntryBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let entries = [];
        let editingEntryId = null;
        let isLoading = false;

        // Données de démonstration réalistes
        const demoEntries = [
            {
                id: 'ECR-2024-001',
                date: '2024-01-15',
                accountNumber: '606000',
                accountName: 'Achats non stockés de matières et fournitures',
                description: 'Achat ordinateurs portables Dell',
                reference: 'FACT-2024-001',
                debitAmount: 8000.00,
                creditAmount: 0.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'Commande PO-2024-001 - 10 ordinateurs'
            },
            {
                id: 'ECR-2024-002',
                date: '2024-01-15',
                accountNumber: '445660',
                accountName: 'TVA déductible',
                description: 'TVA sur achat ordinateurs',
                reference: 'FACT-2024-001',
                debitAmount: 1600.00,
                creditAmount: 0.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'TVA 20% sur 8000€'
            },
            {
                id: 'ECR-2024-003',
                date: '2024-01-15',
                accountNumber: '401000',
                accountName: 'Fournisseurs',
                description: 'Facture TechSupply Co',
                reference: 'FACT-2024-001',
                debitAmount: 0.00,
                creditAmount: 9600.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'Facture fournisseur TTC'
            },
            {
                id: 'ECR-2024-004',
                date: '2024-01-20',
                accountNumber: '641000',
                accountName: 'Rémunérations du personnel',
                description: 'Salaires janvier 2024',
                reference: 'PAIE-2024-01',
                debitAmount: 25000.00,
                creditAmount: 0.00,
                entryType: 'standard',
                status: 'validated',
                notes: 'Paie mensuelle - 10 employés'
            },
            {
                id: 'ECR-2024-005',
                date: '2024-01-20',
                accountNumber: '645000',
                accountName: 'Charges de sécurité sociale',
                description: 'Charges sociales janvier',
                reference: 'PAIE-2024-01',
                debitAmount: 10000.00,
                creditAmount: 0.00,
                entryType: 'standard',
                status: 'validated',
                notes: 'Charges patronales'
            },
            {
                id: 'ECR-2024-006',
                date: '2024-01-20',
                accountNumber: '421000',
                accountName: 'Personnel - Rémunérations dues',
                description: 'Salaires nets à payer',
                reference: 'PAIE-2024-01',
                debitAmount: 0.00,
                creditAmount: 19500.00,
                entryType: 'standard',
                status: 'validated',
                notes: 'Salaires nets après charges'
            },
            {
                id: 'ECR-2024-007',
                date: '2024-01-20',
                accountNumber: '431000',
                accountName: 'Sécurité sociale',
                description: 'Charges sociales à payer',
                reference: 'PAIE-2024-01',
                debitAmount: 0.00,
                creditAmount: 15500.00,
                entryType: 'standard',
                status: 'validated',
                notes: 'Charges salariales + patronales'
            },
            {
                id: 'ECR-2024-008',
                date: '2024-01-25',
                accountNumber: '512000',
                accountName: 'Banque',
                description: 'Virement client Global Manufacturing',
                reference: 'VIR-2024-003',
                debitAmount: 54000.00,
                creditAmount: 0.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'Règlement facture FACT-CLI-001'
            },
            {
                id: 'ECR-2024-009',
                date: '2024-01-25',
                accountNumber: '411000',
                accountName: 'Clients',
                description: 'Règlement Global Manufacturing',
                reference: 'VIR-2024-003',
                debitAmount: 0.00,
                creditAmount: 45000.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'Encaissement facture client'
            },
            {
                id: 'ECR-2024-010',
                date: '2024-01-25',
                accountNumber: '445710',
                accountName: 'TVA collectée',
                description: 'TVA sur vente Global Manufacturing',
                reference: 'VIR-2024-003',
                debitAmount: 0.00,
                creditAmount: 9000.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'TVA 20% sur 45000€'
            }
        ];
    </script>
</body>
</html>
