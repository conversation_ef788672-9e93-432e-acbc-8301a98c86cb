<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Accounting - Gestion Comptable | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #ef4444 30%, #dc2626 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #ef4444;
            color: white;
        }
        
        .btn-primary:hover {
            background: #dc2626;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .btn-info {
            background: #3b82f6;
            color: white;
        }
        
        .btn-info:hover {
            background: #2563eb;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #ef4444;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .amount-debit {
            color: #ef4444;
            font-weight: 600;
        }
        
        .amount-credit {
            color: #10b981;
            font-weight: 600;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #ef4444;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">🏦 Agent Accounting - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="openModal('entryModal')">
                <span class="material-icons" style="font-size: 1rem;">add</span>
                Nouvelle Écriture
            </button>
            <button class="btn btn-info" onclick="generateReport()">
                <span class="material-icons" style="font-size: 1rem;">assessment</span>
                Rapport
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion Comptable</h1>
            <p class="page-subtitle">Écritures comptables et suivi financier</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalEntries">0</div>
                <div class="stat-label">Écritures Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalDebit">0€</div>
                <div class="stat-label">Total Débit</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalCredit">0€</div>
                <div class="stat-label">Total Crédit</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="balance">0€</div>
                <div class="stat-label">Solde</div>
            </div>
        </div>

        <!-- Liste des écritures -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Journal Comptable</h2>
                <button class="btn btn-primary" onclick="refreshEntries()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div id="alertContainer"></div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>N° Écriture</th>
                                <th>Date</th>
                                <th>Compte</th>
                                <th>Libellé</th>
                                <th>Débit</th>
                                <th>Crédit</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="entriesTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Écriture Comptable -->
    <div id="entryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="entryModalTitle">Nouvelle Écriture Comptable</h3>
                <button class="close-btn" onclick="closeModal('entryModal')">&times;</button>
            </div>
            <form id="entryForm">
                <div id="entryModalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="entryDate">Date *</label>
                        <input type="date" id="entryDate" name="entryDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="accountNumber">N° Compte *</label>
                        <select id="accountNumber" name="accountNumber" class="form-select" required>
                            <option value="">Sélectionner un compte</option>
                            <option value="101000">101000 - Capital social</option>
                            <option value="120000">120000 - Résultat de l'exercice</option>
                            <option value="164000">164000 - Emprunts auprès des établissements de crédit</option>
                            <option value="211000">211000 - Terrains</option>
                            <option value="213000">213000 - Constructions</option>
                            <option value="218300">218300 - Matériel informatique</option>
                            <option value="218400">218400 - Mobilier</option>
                            <option value="401000">401000 - Fournisseurs</option>
                            <option value="411000">411000 - Clients</option>
                            <option value="421000">421000 - Personnel - Rémunérations dues</option>
                            <option value="431000">431000 - Sécurité sociale</option>
                            <option value="445710">445710 - TVA collectée</option>
                            <option value="445660">445660 - TVA déductible</option>
                            <option value="512000">512000 - Banque</option>
                            <option value="530000">530000 - Caisse</option>
                            <option value="601000">601000 - Achats de matières premières</option>
                            <option value="606000">606000 - Achats non stockés de matières et fournitures</option>
                            <option value="641000">641000 - Rémunérations du personnel</option>
                            <option value="645000">645000 - Charges de sécurité sociale</option>
                            <option value="661000">661000 - Charges d'intérêts</option>
                            <option value="701000">701000 - Ventes de produits finis</option>
                            <option value="706000">706000 - Prestations de services</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="description">Libellé *</label>
                        <input type="text" id="description" name="description" class="form-input" required placeholder="Description de l'opération">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="reference">Référence</label>
                        <input type="text" id="reference" name="reference" class="form-input" placeholder="N° facture, chèque, etc.">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="debitAmount">Montant Débit (€)</label>
                        <input type="number" id="debitAmount" name="debitAmount" class="form-input" min="0" step="0.01" placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="creditAmount">Montant Crédit (€)</label>
                        <input type="number" id="creditAmount" name="creditAmount" class="form-input" min="0" step="0.01" placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="entryType">Type d'écriture</label>
                        <select id="entryType" name="entryType" class="form-select">
                            <option value="standard">Standard</option>
                            <option value="opening">Ouverture</option>
                            <option value="closing">Clôture</option>
                            <option value="adjustment">Régularisation</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="status">Statut</label>
                        <select id="status" name="status" class="form-select">
                            <option value="draft">Brouillon</option>
                            <option value="validated" selected>Validée</option>
                            <option value="posted">Comptabilisée</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="notes">Notes</label>
                    <textarea id="notes" name="notes" class="form-textarea" placeholder="Notes complémentaires..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('entryModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveEntryBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let entries = [];
        let editingEntryId = null;
        let isLoading = false;

        // Données de démonstration réalistes
        const demoEntries = [
            {
                id: 'ECR-2024-001',
                date: '2024-01-15',
                accountNumber: '606000',
                accountName: 'Achats non stockés de matières et fournitures',
                description: 'Achat ordinateurs portables Dell',
                reference: 'FACT-2024-001',
                debitAmount: 8000.00,
                creditAmount: 0.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'Commande PO-2024-001 - 10 ordinateurs'
            },
            {
                id: 'ECR-2024-002',
                date: '2024-01-15',
                accountNumber: '445660',
                accountName: 'TVA déductible',
                description: 'TVA sur achat ordinateurs',
                reference: 'FACT-2024-001',
                debitAmount: 1600.00,
                creditAmount: 0.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'TVA 20% sur 8000€'
            },
            {
                id: 'ECR-2024-003',
                date: '2024-01-15',
                accountNumber: '401000',
                accountName: 'Fournisseurs',
                description: 'Facture TechSupply Co',
                reference: 'FACT-2024-001',
                debitAmount: 0.00,
                creditAmount: 9600.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'Facture fournisseur TTC'
            },
            {
                id: 'ECR-2024-004',
                date: '2024-01-20',
                accountNumber: '641000',
                accountName: 'Rémunérations du personnel',
                description: 'Salaires janvier 2024',
                reference: 'PAIE-2024-01',
                debitAmount: 25000.00,
                creditAmount: 0.00,
                entryType: 'standard',
                status: 'validated',
                notes: 'Paie mensuelle - 10 employés'
            },
            {
                id: 'ECR-2024-005',
                date: '2024-01-20',
                accountNumber: '645000',
                accountName: 'Charges de sécurité sociale',
                description: 'Charges sociales janvier',
                reference: 'PAIE-2024-01',
                debitAmount: 10000.00,
                creditAmount: 0.00,
                entryType: 'standard',
                status: 'validated',
                notes: 'Charges patronales'
            },
            {
                id: 'ECR-2024-006',
                date: '2024-01-20',
                accountNumber: '421000',
                accountName: 'Personnel - Rémunérations dues',
                description: 'Salaires nets à payer',
                reference: 'PAIE-2024-01',
                debitAmount: 0.00,
                creditAmount: 19500.00,
                entryType: 'standard',
                status: 'validated',
                notes: 'Salaires nets après charges'
            },
            {
                id: 'ECR-2024-007',
                date: '2024-01-20',
                accountNumber: '431000',
                accountName: 'Sécurité sociale',
                description: 'Charges sociales à payer',
                reference: 'PAIE-2024-01',
                debitAmount: 0.00,
                creditAmount: 15500.00,
                entryType: 'standard',
                status: 'validated',
                notes: 'Charges salariales + patronales'
            },
            {
                id: 'ECR-2024-008',
                date: '2024-01-25',
                accountNumber: '512000',
                accountName: 'Banque',
                description: 'Virement client Global Manufacturing',
                reference: 'VIR-2024-003',
                debitAmount: 54000.00,
                creditAmount: 0.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'Règlement facture FACT-CLI-001'
            },
            {
                id: 'ECR-2024-009',
                date: '2024-01-25',
                accountNumber: '411000',
                accountName: 'Clients',
                description: 'Règlement Global Manufacturing',
                reference: 'VIR-2024-003',
                debitAmount: 0.00,
                creditAmount: 45000.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'Encaissement facture client'
            },
            {
                id: 'ECR-2024-010',
                date: '2024-01-25',
                accountNumber: '445710',
                accountName: 'TVA collectée',
                description: 'TVA sur vente Global Manufacturing',
                reference: 'VIR-2024-003',
                debitAmount: 0.00,
                creditAmount: 9000.00,
                entryType: 'standard',
                status: 'posted',
                notes: 'TVA 20% sur 45000€'
            }
        ];

        function showAlert(message, type = 'error', container = 'alertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            if (modalId === 'entryModal') {
                document.getElementById('entryModalTitle').textContent = editingEntryId ? 'Modifier Écriture' : 'Nouvelle Écriture Comptable';
                // Définir la date d'aujourd'hui par défaut
                if (!editingEntryId) {
                    document.getElementById('entryDate').value = new Date().toISOString().split('T')[0];
                }
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            if (modalId === 'entryModal') {
                document.getElementById('entryForm').reset();
                document.getElementById('entryModalAlertContainer').innerHTML = '';
                editingEntryId = null;
            }
        }

        function updateStats() {
            const total = entries.length;
            const totalDebit = entries.reduce((sum, entry) => sum + (entry.debitAmount || 0), 0);
            const totalCredit = entries.reduce((sum, entry) => sum + (entry.creditAmount || 0), 0);
            const balance = totalDebit - totalCredit;

            document.getElementById('totalEntries').textContent = total;
            document.getElementById('totalDebit').textContent = totalDebit.toLocaleString() + '€';
            document.getElementById('totalCredit').textContent = totalCredit.toLocaleString() + '€';
            document.getElementById('balance').textContent = balance.toLocaleString() + '€';

            // Colorer le solde selon le signe
            const balanceElement = document.getElementById('balance');
            if (balance > 0) {
                balanceElement.style.color = '#ef4444'; // Rouge pour débit
            } else if (balance < 0) {
                balanceElement.style.color = '#10b981'; // Vert pour crédit
            } else {
                balanceElement.style.color = '#6b7280'; // Gris pour équilibré
            }
        }

        function getAccountName(accountNumber) {
            const accountNames = {
                '101000': 'Capital social',
                '120000': 'Résultat de l\'exercice',
                '164000': 'Emprunts auprès des établissements de crédit',
                '211000': 'Terrains',
                '213000': 'Constructions',
                '218300': 'Matériel informatique',
                '218400': 'Mobilier',
                '401000': 'Fournisseurs',
                '411000': 'Clients',
                '421000': 'Personnel - Rémunérations dues',
                '431000': 'Sécurité sociale',
                '445710': 'TVA collectée',
                '445660': 'TVA déductible',
                '512000': 'Banque',
                '530000': 'Caisse',
                '601000': 'Achats de matières premières',
                '606000': 'Achats non stockés de matières et fournitures',
                '641000': 'Rémunérations du personnel',
                '645000': 'Charges de sécurité sociale',
                '661000': 'Charges d\'intérêts',
                '701000': 'Ventes de produits finis',
                '706000': 'Prestations de services'
            };
            return accountNames[accountNumber] || 'Compte inconnu';
        }

        function renderEntriesTable() {
            const tbody = document.getElementById('entriesTableBody');

            if (entries.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune écriture trouvée. Cliquez sur "Nouvelle Écriture" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = entries.map(entry => {
                const statusBadge = getStatusBadge(entry.status);
                const debitDisplay = entry.debitAmount > 0 ? `<span class="amount-debit">${entry.debitAmount.toFixed(2)}€</span>` : '-';
                const creditDisplay = entry.creditAmount > 0 ? `<span class="amount-credit">${entry.creditAmount.toFixed(2)}€</span>` : '-';

                return `
                    <tr>
                        <td>${entry.id}</td>
                        <td>${new Date(entry.date).toLocaleDateString()}</td>
                        <td>${entry.accountNumber}<br><small style="color: #6b7280;">${entry.accountName || getAccountName(entry.accountNumber)}</small></td>
                        <td>${entry.description}</td>
                        <td>${debitDisplay}</td>
                        <td>${creditDisplay}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editEntry('${entry.id}')" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteEntry('${entry.id}')" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getStatusBadge(status) {
            const badges = {
                'draft': '<span class="badge badge-info">Brouillon</span>',
                'validated': '<span class="badge badge-warning">Validée</span>',
                'posted': '<span class="badge badge-success">Comptabilisée</span>'
            };
            return badges[status] || '<span class="badge badge-danger">Inconnu</span>';
        }

        async function loadEntries() {
            try {
                // Tentative de chargement depuis l'API
                const token = localStorage.getItem('access_token');
                if (token) {
                    const response = await fetch('http://localhost:8000/api/agents/accounting/entries/', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        entries = await response.json();
                    } else {
                        throw new Error('Erreur API');
                    }
                } else {
                    throw new Error('Pas de token');
                }
            } catch (error) {
                console.log('Chargement des données de démonstration');
                entries = [...demoEntries];
            }

            renderEntriesTable();
            updateStats();
        }

        function refreshEntries() {
            loadEntries();
            showAlert('Données actualisées avec succès', 'success');
        }

        function editEntry(id) {
            const entry = entries.find(e => e.id === id);
            if (!entry) return;

            editingEntryId = id;

            // Remplir le formulaire
            document.getElementById('entryDate').value = entry.date;
            document.getElementById('accountNumber').value = entry.accountNumber;
            document.getElementById('description').value = entry.description;
            document.getElementById('reference').value = entry.reference || '';
            document.getElementById('debitAmount').value = entry.debitAmount || '';
            document.getElementById('creditAmount').value = entry.creditAmount || '';
            document.getElementById('entryType').value = entry.entryType || 'standard';
            document.getElementById('status').value = entry.status;
            document.getElementById('notes').value = entry.notes || '';

            openModal('entryModal');
        }

        function deleteEntry(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette écriture ?')) {
                entries = entries.filter(e => e.id !== id);
                renderEntriesTable();
                updateStats();
                showAlert('Écriture supprimée avec succès', 'success');
            }
        }

        function generateReport() {
            // Calculer les totaux par type de compte
            const accountSummary = {};
            entries.forEach(entry => {
                const accountNumber = entry.accountNumber;
                if (!accountSummary[accountNumber]) {
                    accountSummary[accountNumber] = {
                        name: entry.accountName || getAccountName(accountNumber),
                        debit: 0,
                        credit: 0
                    };
                }
                accountSummary[accountNumber].debit += entry.debitAmount || 0;
                accountSummary[accountNumber].credit += entry.creditAmount || 0;
            });

            // Créer le rapport
            let reportContent = `
                <h3>Rapport Comptable - ${new Date().toLocaleDateString()}</h3>
                <table style="width: 100%; border-collapse: collapse; margin-top: 1rem;">
                    <thead>
                        <tr style="background: #f9fafb;">
                            <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Compte</th>
                            <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Débit</th>
                            <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Crédit</th>
                            <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Solde</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            Object.keys(accountSummary).sort().forEach(accountNumber => {
                const account = accountSummary[accountNumber];
                const balance = account.debit - account.credit;
                const balanceColor = balance > 0 ? '#ef4444' : balance < 0 ? '#10b981' : '#6b7280';

                reportContent += `
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">${accountNumber} - ${account.name}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${account.debit.toFixed(2)}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${account.credit.toFixed(2)}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: ${balanceColor};">${balance.toFixed(2)}€</td>
                    </tr>
                `;
            });

            reportContent += `
                    </tbody>
                </table>
            `;

            // Ouvrir le rapport dans une nouvelle fenêtre
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Rapport Comptable - ERP HUB</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 2rem; }
                        h3 { color: #ef4444; margin-bottom: 1rem; }
                        table { border-collapse: collapse; }
                        th, td { border: 1px solid #e5e7eb; padding: 0.5rem; }
                        th { background: #f9fafb; font-weight: 600; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${reportContent}
                    <div style="margin-top: 2rem; text-align: center;">
                        <button onclick="window.print()" style="padding: 0.5rem 1rem; background: #ef4444; color: white; border: none; border-radius: 0.25rem; cursor: pointer;">
                            Imprimer
                        </button>
                    </div>
                </body>
                </html>
            `);
            reportWindow.document.close();
        }

        // Validation des montants débit/crédit
        function validateAmounts() {
            const debitAmount = parseFloat(document.getElementById('debitAmount').value) || 0;
            const creditAmount = parseFloat(document.getElementById('creditAmount').value) || 0;

            if (debitAmount > 0 && creditAmount > 0) {
                showAlert('Une écriture ne peut pas avoir à la fois un montant débit et crédit', 'error', 'entryModalAlertContainer');
                return false;
            }

            if (debitAmount === 0 && creditAmount === 0) {
                showAlert('Veuillez saisir un montant débit ou crédit', 'error', 'entryModalAlertContainer');
                return false;
            }

            return true;
        }

        // Gestion du formulaire
        document.getElementById('entryForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;

            if (!validateAmounts()) return;

            isLoading = true;

            const formData = new FormData(e.target);
            const entryData = {
                date: formData.get('entryDate'),
                accountNumber: formData.get('accountNumber'),
                accountName: getAccountName(formData.get('accountNumber')),
                description: formData.get('description'),
                reference: formData.get('reference'),
                debitAmount: parseFloat(formData.get('debitAmount')) || 0,
                creditAmount: parseFloat(formData.get('creditAmount')) || 0,
                entryType: formData.get('entryType'),
                status: formData.get('status'),
                notes: formData.get('notes')
            };

            try {
                if (editingEntryId) {
                    // Modification
                    const index = entries.findIndex(e => e.id === editingEntryId);
                    if (index !== -1) {
                        entries[index] = { ...entries[index], ...entryData };
                        showAlert('Écriture modifiée avec succès', 'success');
                    }
                } else {
                    // Création
                    const newEntry = {
                        id: `ECR-2024-${String(entries.length + 1).padStart(3, '0')}`,
                        ...entryData
                    };
                    entries.push(newEntry);
                    showAlert('Écriture créée avec succès', 'success');
                }

                renderEntriesTable();
                updateStats();
                closeModal('entryModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'entryModalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadEntries();
            }
        });
    </script>
</body>
</html>
