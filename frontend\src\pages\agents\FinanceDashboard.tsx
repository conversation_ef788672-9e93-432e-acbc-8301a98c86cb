import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  Divider,
  Button,
  IconButton,
  Tooltip,
  Paper
} from '@mui/material';
import {
  AccountBalance,
  TrendingUp,
  TrendingDown,
  AttachMoney,
  Assessment,
  PieChart,
  BarChart,
  Refresh,
  Download,
  FilterList
} from '@mui/icons-material';
import { MetricCard } from '../../components/ui/MetricCard';
import { AnimatedChart } from '../../components/ui/AnimatedChart';
import { StatusIndicator } from '../../components/ui/StatusIndicator';

interface FinancialMetrics {
  totalAccountsReceivable: number;
  totalAccountsPayable: number;
  equityRatio: number;
  debtEquity: number;
  currentRatio: number;
  netWorkingCapital: number;
  grossWorkingCapital: number;
}

interface DSO {
  type: string;
  days: number;
  color: string;
  status: 'good' | 'warning' | 'critical';
}

export const FinanceDashboard: React.FC = () => {
  const [financialMetrics, setFinancialMetrics] = useState<FinancialMetrics>({
    totalAccountsReceivable: 6621280,
    totalAccountsPayable: 1630270,
    equityRatio: 75.38,
    debtEquity: 1.10,
    currentRatio: 1.85,
    netWorkingCapital: 4991010,
    grossWorkingCapital: 8251550
  });

  const [dsoData] = useState<DSO[]>([
    { type: 'DSI (Days Sales Inventory)', days: 31, color: '#2196f3', status: 'good' },
    { type: 'DSO (Days Sales Outstanding)', days: 10, color: '#ff9800', status: 'warning' },
    { type: 'DPO (Days Payable Outstanding)', days: 7, color: '#f44336', status: 'critical' },
    { type: 'CCC (Cash Conversion Cycle)', days: 28, color: '#4caf50', status: 'good' }
  ]);

  const [workingCapitalData] = useState([
    { name: 'Jan', netWorking: 4200000, grossWorking: 7800000 },
    { name: 'Fév', netWorking: 4350000, grossWorking: 7950000 },
    { name: 'Mar', netWorking: 4500000, grossWorking: 8100000 },
    { name: 'Avr', netWorking: 4650000, grossWorking: 8250000 },
    { name: 'Mai', netWorking: 4800000, grossWorking: 8400000 },
    { name: 'Jun', netWorking: 4950000, grossWorking: 8550000 },
    { name: 'Jul', netWorking: 4991010, grossWorking: 8251550 }
  ]);

  const [profitLossData] = useState([
    { name: 'Jan', profit: 1200000, loss: -200000, revenue: 2800000 },
    { name: 'Fév', profit: 1350000, loss: -180000, revenue: 3100000 },
    { name: 'Mar', profit: 1500000, loss: -220000, revenue: 3400000 },
    { name: 'Avr', profit: 1650000, loss: -190000, revenue: 3700000 },
    { name: 'Mai', profit: 1800000, loss: -210000, revenue: 4000000 },
    { name: 'Jun', profit: 1950000, loss: -170000, revenue: 4300000 },
    { name: 'Jul', profit: 2100000, loss: -160000, revenue: 4600000 }
  ]);

  const [accountsData] = useState([
    { name: 'Comptes Clients', receivable: 2800000, payable: 0 },
    { name: 'Fournisseurs', receivable: 0, payable: 1200000 },
    { name: 'Stocks', receivable: 1900000, payable: 0 },
    { name: 'Immobilisations', receivable: 1500000, payable: 0 },
    { name: 'Dettes Courantes', receivable: 0, payable: 430270 },
    { name: 'Autres', receivable: 421280, payable: 0 }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getDSOStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'success';
      case 'warning': return 'warning';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar sx={{ bgcolor: 'success.main', width: 56, height: 56 }}>
              <AccountBalance fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={700} color="success.main">
                Agent Finance
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Gestion financière et analyse de trésorerie
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={1}>
            <Tooltip title="Actualiser les données">
              <IconButton onClick={handleRefresh} disabled={isLoading}>
                <Refresh />
              </IconButton>
            </Tooltip>
            <Button variant="outlined" startIcon={<FilterList />}>
              Filtres
            </Button>
            <Button variant="contained" startIcon={<Download />} color="success">
              Exporter
            </Button>
          </Box>
        </Box>
      </motion.div>

      {/* Métriques principales */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Comptes Clients"
            value={financialMetrics.totalAccountsReceivable}
            icon={<TrendingUp />}
            color="primary"
            trend="up"
            trendValue={8.5}
            format="currency"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Comptes Fournisseurs"
            value={financialMetrics.totalAccountsPayable}
            icon={<TrendingDown />}
            color="secondary"
            trend="down"
            trendValue={-3.2}
            format="currency"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Ratio de Solvabilité"
            value={financialMetrics.equityRatio}
            unit="%"
            icon={<PieChart />}
            color="success"
            trend="up"
            trendValue={2.1}
            format="percentage"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Ratio Dette/Capitaux"
            value={financialMetrics.debtEquity}
            unit="%"
            icon={<BarChart />}
            color="warning"
            trend="neutral"
            trendValue={0.5}
            format="percentage"
            isLoading={isLoading}
          />
        </Grid>
      </Grid>

      {/* Indicateurs DSO */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" fontWeight={600} mb={3}>
              Indicateurs de Performance Financière
            </Typography>
            
            <Grid container spacing={3}>
              {dsoData.map((dso, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Paper
                      elevation={2}
                      sx={{
                        p: 3,
                        textAlign: 'center',
                        background: `linear-gradient(135deg, ${dso.color}15 0%, ${dso.color}05 100%)`,
                        border: `2px solid ${dso.color}30`,
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      {/* Cercle de progression */}
                      <Box
                        sx={{
                          width: 80,
                          height: 80,
                          borderRadius: '50%',
                          border: `6px solid ${dso.color}`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          margin: '0 auto 16px',
                          position: 'relative',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: -3,
                            left: -3,
                            right: -3,
                            bottom: -3,
                            borderRadius: '50%',
                            border: `3px solid ${dso.color}20`,
                          }
                        }}
                      >
                        <Typography variant="h4" fontWeight={700} color={dso.color}>
                          {dso.days}
                        </Typography>
                      </Box>
                      
                      <Typography variant="caption" color="text.secondary" display="block" mb={1}>
                        {dso.type}
                      </Typography>
                      
                      <Chip
                        label={dso.status === 'good' ? 'Excellent' : dso.status === 'warning' ? 'Attention' : 'Critique'}
                        color={getDSOStatusColor(dso.status) as any}
                        size="small"
                      />
                    </Paper>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </motion.div>

      {/* Graphiques financiers */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} lg={6}>
          <AnimatedChart
            title="Évolution du Fonds de Roulement"
            subtitle="Net vs Brut (en millions €)"
            data={workingCapitalData.map(item => ({
              name: item.name,
              value: item.netWorking / 1000000,
              value2: item.grossWorking / 1000000
            }))}
            type="line"
            height={350}
            color="#1976d2"
            secondaryColor="#42a5f5"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} lg={6}>
          <AnimatedChart
            title="Analyse Profit & Loss"
            subtitle="Évolution mensuelle (en millions €)"
            data={profitLossData.map(item => ({
              name: item.name,
              value: item.profit / 1000000,
              value2: Math.abs(item.loss) / 1000000,
              value3: item.revenue / 1000000
            }))}
            type="bar"
            height={350}
            color="#4caf50"
            secondaryColor="#f44336"
            isLoading={isLoading}
          />
        </Grid>
      </Grid>

      {/* Répartition des comptes */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <AnimatedChart
            title="Répartition Comptes Clients vs Fournisseurs"
            subtitle="Analyse comparative par catégorie"
            data={accountsData.map(item => ({
              name: item.name,
              value: item.receivable / 1000000,
              value2: item.payable / 1000000
            }))}
            type="bar"
            height={350}
            color="#2196f3"
            secondaryColor="#ff9800"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight={600} mb={3}>
                Ratios Financiers Clés
              </Typography>
              
              <Box mb={3}>
                <Typography variant="body2" color="text.secondary" mb={1}>
                  Ratio de Liquidité Générale
                </Typography>
                <Typography variant="h5" color="success.main" fontWeight={600}>
                  {financialMetrics.currentRatio.toFixed(2)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Objectif: > 1.5
                </Typography>
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Box mb={3}>
                <Typography variant="body2" color="text.secondary" mb={1}>
                  Fonds de Roulement Net
                </Typography>
                <Typography variant="h5" color="primary.main" fontWeight={600}>
                  {formatCurrency(financialMetrics.netWorkingCapital)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Évolution: +12.5% vs N-1
                </Typography>
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Box>
                <Typography variant="body2" color="text.secondary" mb={1}>
                  Statut Trésorerie
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  <StatusIndicator status="active" size="small" />
                  <Typography variant="body2" fontWeight={600}>
                    Situation saine
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};
