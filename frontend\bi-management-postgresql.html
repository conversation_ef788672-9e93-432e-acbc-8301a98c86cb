<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent BI - Business Intelligence | ERP HUB PostgreSQL</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #7c3aed 30%, #5b21b6 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #7c3aed;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5b21b6;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #7c3aed;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #7c3aed;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .stat-trend {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
            margin-top: 0.5rem;
            font-size: 0.75rem;
        }
        
        .trend-up {
            color: #10b981;
        }
        
        .trend-down {
            color: #ef4444;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .chart-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .chart-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-size: 0.875rem; /* Réduction de la taille de police */
        }
        
        .data-table th,
        .data-table td {
            padding: 0.5rem; /* Réduction du padding de 1rem à 0.5rem */
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            line-height: 1.25; /* Réduction de la hauteur de ligne */
            vertical-align: middle;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 0.8125rem; /* Police encore plus petite pour les en-têtes */
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .data-table tbody tr {
            height: 2.5rem; /* Hauteur fixe pour les lignes */
        }
        
        .badge {
            display: inline-block;
            padding: 0.125rem 0.5rem; /* Réduction du padding des badges */
            border-radius: 0.75rem;
            font-size: 0.6875rem; /* Police plus petite pour les badges */
            font-weight: 600;
            line-height: 1.2;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .kpi-progress {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }
        
        .kpi-progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s;
        }
        
        .progress-excellent {
            background: #10b981;
        }
        
        .progress-good {
            background: #3b82f6;
        }
        
        .progress-warning {
            background: #f59e0b;
        }
        
        .progress-danger {
            background: #ef4444;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #7c3aed;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #fde68a;
            color: #92400e;
        }
        
        .alert-info {
            background: #dbeafe;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-connected {
            background: #10b981;
        }
        
        .status-disconnected {
            background: #ef4444;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">📊 Agent BI - ERP HUB</div>
        <div class="nav-buttons">
            <div class="connection-status">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Connexion...</span>
            </div>
            <button class="btn btn-primary" onclick="refreshData()">
                <span class="material-icons" style="font-size: 1rem;">refresh</span>
                Actualiser
            </button>
            <a href="dashboard-global-postgresql.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Business Intelligence</h1>
            <p class="page-subtitle">KPIs et analyses - Connecté à PostgreSQL</p>
        </div>

        <!-- Statistiques KPI -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalKpis">0</div>
                <div class="stat-label">Total KPIs</div>
                <div class="stat-trend trend-up">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>+5 ce mois</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeKpis">0</div>
                <div class="stat-label">KPIs Actifs</div>
                <div class="stat-trend trend-up">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>98% opérationnels</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="kpisOnTarget">0</div>
                <div class="stat-label">Objectifs Atteints</div>
                <div class="stat-trend trend-up">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>85% réussite</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgPerformance">0%</div>
                <div class="stat-label">Performance Moyenne</div>
                <div class="stat-trend trend-up">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>+12% vs mois dernier</span>
                </div>
            </div>
        </div>

        <!-- Alertes -->
        <div id="alertContainer"></div>

        <!-- Graphiques -->
        <div class="charts-grid">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">Performance des KPIs</h3>
                    <button class="btn btn-secondary" onclick="updateKpiChart()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    </button>
                </div>
                <div class="chart-container">
                    <canvas id="kpiChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">Répartition par Type</h3>
                </div>
                <div class="chart-container">
                    <canvas id="typeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Tableau des KPIs -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">📈 Indicateurs de Performance (KPIs)</h2>
                <button class="btn btn-primary" onclick="loadKpis()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Recharger
                </button>
            </div>
            <div class="section-content">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Nom du KPI</th>
                                <th>Type</th>
                                <th>Valeur Cible</th>
                                <th>Valeur Actuelle</th>
                                <th>Unité</th>
                                <th>Fréquence</th>
                                <th>Performance</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody id="kpisTableBody">
                            <tr>
                                <td colspan="8" style="text-align: center; padding: 2rem;">
                                    <div class="loading"></div>
                                    Chargement des KPIs...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <script>
        let kpis = [];
        let kpiChart = null;
        let typeChart = null;

        // Configuration API
        const API_BASE_URL = 'http://localhost:5000/api';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
        });

        // Charger toutes les données
        async function loadAllData() {
            await checkConnection();
            await loadKpis();
            initializeCharts();
            updateStats();
        }

        // Vérifier la connexion à l'API
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    updateConnectionStatus(true, data.database_status === 'connected');
                } else {
                    updateConnectionStatus(false, false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateConnectionStatus(false, false);
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(apiConnected, dbConnected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (apiConnected && dbConnected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'PostgreSQL connecté';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Déconnecté';
            }
        }

        // Charger les KPIs depuis PostgreSQL
        async function loadKpis() {
            try {
                showAlert('Chargement des KPIs depuis PostgreSQL...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/kpis`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        kpis = data.data || [];
                        renderKpisTable();
                        showAlert(`${kpis.length} KPIs chargés depuis PostgreSQL`, 'success');
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement KPIs:', error);
                showAlert('Erreur lors du chargement des KPIs: ' + error.message, 'error');
                kpis = [];
                renderKpisTable();
            }
        }

        // Afficher le tableau des KPIs
        function renderKpisTable() {
            const tbody = document.getElementById('kpisTableBody');
            
            if (kpis.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun KPI trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = kpis.map(kpi => {
                const performance = calculatePerformance(kpi.currentValue, kpi.targetValue);
                return `
                    <tr>
                        <td><strong>${kpi.kpiName}</strong></td>
                        <td>${getKpiTypeBadge(kpi.kpiType)}</td>
                        <td>${kpi.targetValue ? kpi.targetValue.toLocaleString() : 'N/A'}</td>
                        <td>${kpi.currentValue ? kpi.currentValue.toLocaleString() : 'N/A'}</td>
                        <td>${kpi.unit || 'N/A'}</td>
                        <td>${getFrequencyBadge(kpi.frequency)}</td>
                        <td>${getPerformanceDisplay(performance)}</td>
                        <td>${getStatusBadge(kpi.status)}</td>
                    </tr>
                `;
            }).join('');
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const totalKpis = kpis.length;
            const activeKpis = kpis.filter(kpi => kpi.status === 'active').length;
            const kpisOnTarget = kpis.filter(kpi => {
                const performance = calculatePerformance(kpi.currentValue, kpi.targetValue);
                return performance >= 90;
            }).length;
            
            const avgPerformance = kpis.length > 0 
                ? kpis.reduce((sum, kpi) => sum + calculatePerformance(kpi.currentValue, kpi.targetValue), 0) / kpis.length 
                : 0;

            document.getElementById('totalKpis').textContent = totalKpis;
            document.getElementById('activeKpis').textContent = activeKpis;
            document.getElementById('kpisOnTarget').textContent = kpisOnTarget;
            document.getElementById('avgPerformance').textContent = avgPerformance.toFixed(1) + '%';
        }

        // Initialiser les graphiques
        function initializeCharts() {
            // Graphique de performance des KPIs
            const kpiCtx = document.getElementById('kpiChart').getContext('2d');
            kpiChart = new Chart(kpiCtx, {
                type: 'bar',
                data: {
                    labels: kpis.slice(0, 5).map(kpi => kpi.kpiName),
                    datasets: [{
                        label: 'Performance (%)',
                        data: kpis.slice(0, 5).map(kpi => calculatePerformance(kpi.currentValue, kpi.targetValue)),
                        backgroundColor: '#7c3aed',
                        borderColor: '#5b21b6',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 120
                        }
                    }
                }
            });

            // Graphique de répartition par type
            const typeCtx = document.getElementById('typeChart').getContext('2d');
            const typeData = getKpiTypeDistribution();
            typeChart = new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(typeData),
                    datasets: [{
                        data: Object.values(typeData),
                        backgroundColor: ['#7c3aed', '#3b82f6', '#10b981', '#f59e0b', '#ef4444']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Fonctions utilitaires
        function calculatePerformance(current, target) {
            if (!current || !target) return 0;
            return Math.min((current / target) * 100, 120);
        }

        function getKpiTypeDistribution() {
            const distribution = {};
            kpis.forEach(kpi => {
                const type = kpi.kpiType || 'Autre';
                distribution[type] = (distribution[type] || 0) + 1;
            });
            return distribution;
        }

        function getKpiTypeBadge(type) {
            const badges = {
                'financial': '<span class="badge badge-success">Financier</span>',
                'operational': '<span class="badge badge-info">Opérationnel</span>',
                'customer': '<span class="badge badge-warning">Client</span>',
                'quality': '<span class="badge badge-danger">Qualité</span>'
            };
            return badges[type] || `<span class="badge badge-info">${type}</span>`;
        }

        function getFrequencyBadge(frequency) {
            const badges = {
                'daily': '<span class="badge badge-success">Quotidien</span>',
                'weekly': '<span class="badge badge-info">Hebdomadaire</span>',
                'monthly': '<span class="badge badge-warning">Mensuel</span>',
                'quarterly': '<span class="badge badge-danger">Trimestriel</span>'
            };
            return badges[frequency] || `<span class="badge badge-info">${frequency}</span>`;
        }

        function getStatusBadge(status) {
            const badges = {
                'active': '<span class="badge badge-success">Actif</span>',
                'inactive': '<span class="badge badge-warning">Inactif</span>',
                'archived': '<span class="badge badge-danger">Archivé</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getPerformanceDisplay(performance) {
            let progressClass, badgeClass;
            
            if (performance >= 100) {
                progressClass = 'progress-excellent';
                badgeClass = 'badge-success';
            } else if (performance >= 80) {
                progressClass = 'progress-good';
                badgeClass = 'badge-info';
            } else if (performance >= 60) {
                progressClass = 'progress-warning';
                badgeClass = 'badge-warning';
            } else {
                progressClass = 'progress-danger';
                badgeClass = 'badge-danger';
            }

            return `
                <div>
                    <span class="badge ${badgeClass}">${performance.toFixed(1)}%</span>
                    <div class="kpi-progress">
                        <div class="kpi-progress-fill ${progressClass}" style="width: ${Math.min(performance, 100)}%"></div>
                    </div>
                </div>
            `;
        }

        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            container.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
            
            // Masquer l'alerte après 5 secondes
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function refreshData() {
            loadAllData();
        }

        function updateKpiChart() {
            if (kpiChart) {
                kpiChart.data.labels = kpis.slice(0, 5).map(kpi => kpi.kpiName);
                kpiChart.data.datasets[0].data = kpis.slice(0, 5).map(kpi => calculatePerformance(kpi.currentValue, kpi.targetValue));
                kpiChart.update();
            }
            showAlert('Graphique des KPIs mis à jour', 'success');
        }
    </script>
</body>
</html>
