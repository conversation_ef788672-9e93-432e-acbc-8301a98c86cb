"""
Moteur de workflow pour l'architecture d'agents ERP HUB
Gère l'exécution automatisée des workflows métier
"""
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from django.utils import timezone
from django.db import transaction

from core.models import Tenant, User
from agents.models import Agent, AgentTask, Workflow, WorkflowExecution
from agents.ai_service import ai_service

logger = logging.getLogger('agents.workflow')


class WorkflowEngine:
    """
    Moteur d'exécution des workflows
    """
    
    def __init__(self, tenant: Tenant):
        self.tenant = tenant
    
    def execute_workflow(self, workflow: Workflow, input_data: Dict[str, Any], 
                        triggered_by: Optional[User] = None) -> WorkflowExecution:
        """
        Exécute un workflow complet
        """
        execution = WorkflowExecution.objects.create(
            workflow=workflow,
            input_data=input_data,
            triggered_by=triggered_by
        )
        
        try:
            logger.info(f"Démarrage du workflow {workflow.name} (ID: {execution.id})")
            
            # Exécution des étapes
            for step_index, step in enumerate(workflow.steps):
                execution.current_step = step_index
                execution.save()
                
                result = self._execute_step(execution, step, step_index)
                
                # Ajouter au journal d'exécution
                execution.execution_log.append({
                    'step': step_index,
                    'step_name': step.get('name', f'Étape {step_index + 1}'),
                    'timestamp': timezone.now().isoformat(),
                    'result': result,
                    'status': 'completed' if result.get('success') else 'failed'
                })
                execution.save()
                
                # Arrêter en cas d'échec si pas de gestion d'erreur
                if not result.get('success') and not step.get('continue_on_error', False):
                    raise WorkflowExecutionError(f"Échec à l'étape {step_index}: {result.get('error')}")
            
            # Workflow terminé avec succès
            execution.status = 'completed'
            execution.completed_at = timezone.now()
            execution.save()
            
            # Mettre à jour les statistiques du workflow
            workflow.execution_count += 1
            workflow.success_count += 1
            workflow.save()
            
            logger.info(f"Workflow {workflow.name} terminé avec succès")
            
        except Exception as e:
            # Workflow échoué
            execution.status = 'failed'
            execution.error_message = str(e)
            execution.completed_at = timezone.now()
            execution.save()
            
            # Mettre à jour les statistiques du workflow
            workflow.execution_count += 1
            workflow.failure_count += 1
            workflow.save()
            
            logger.error(f"Échec du workflow {workflow.name}: {str(e)}")
        
        return execution
    
    def _execute_step(self, execution: WorkflowExecution, step: Dict[str, Any], 
                     step_index: int) -> Dict[str, Any]:
        """
        Exécute une étape individuelle du workflow
        """
        step_type = step.get('type')
        step_config = step.get('config', {})
        
        try:
            if step_type == 'agent_task':
                return self._execute_agent_task_step(execution, step_config)
            elif step_type == 'condition':
                return self._execute_condition_step(execution, step_config)
            elif step_type == 'parallel':
                return self._execute_parallel_step(execution, step_config)
            elif step_type == 'delay':
                return self._execute_delay_step(execution, step_config)
            elif step_type == 'notification':
                return self._execute_notification_step(execution, step_config)
            elif step_type == 'data_transformation':
                return self._execute_data_transformation_step(execution, step_config)
            elif step_type == 'ai_decision':
                return self._execute_ai_decision_step(execution, step_config)
            else:
                return {
                    'success': False,
                    'error': f'Type d\'étape non supporté: {step_type}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _execute_agent_task_step(self, execution: WorkflowExecution, 
                                config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Exécute une étape de tâche d'agent
        """
        agent_type = config.get('agent_type')
        task_type = config.get('task_type')
        task_data = config.get('task_data', {})
        
        # Récupérer l'agent
        agent = Agent.objects.filter(
            tenant=self.tenant,
            agent_type=agent_type,
            status='active',
            is_enabled=True
        ).first()
        
        if not agent:
            return {
                'success': False,
                'error': f'Agent {agent_type} non disponible'
            }
        
        # Créer la tâche
        task = AgentTask.objects.create(
            agent=agent,
            title=config.get('title', f'Tâche workflow {execution.workflow.name}'),
            description=config.get('description', ''),
            task_type=task_type,
            priority=config.get('priority', 3),
            input_data={
                **task_data,
                'workflow_execution_id': str(execution.id),
                'workflow_data': execution.input_data
            }
        )
        
        # Simuler l'exécution de la tâche (dans un vrai système, cela serait asynchrone)
        task.status = 'completed'
        task.started_at = timezone.now()
        task.completed_at = timezone.now()
        task.output_data = {
            'result': 'Task completed successfully',
            'processed_at': timezone.now().isoformat()
        }
        task.save()
        
        # Mettre à jour les métriques de l'agent
        agent.update_metrics(success=True)
        
        return {
            'success': True,
            'task_id': str(task.id),
            'output_data': task.output_data
        }
    
    def _execute_condition_step(self, execution: WorkflowExecution, 
                               config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Exécute une étape conditionnelle
        """
        condition = config.get('condition')
        condition_type = config.get('condition_type', 'simple')
        
        if condition_type == 'simple':
            # Évaluation simple basée sur les données d'entrée
            field = condition.get('field')
            operator = condition.get('operator')
            value = condition.get('value')
            
            actual_value = self._get_nested_value(execution.input_data, field)
            result = self._evaluate_condition(actual_value, operator, value)
            
        elif condition_type == 'ai':
            # Évaluation par IA
            if ai_service.is_available():
                prompt = f"""
                Évalue cette condition basée sur les données suivantes:
                Condition: {condition}
                Données: {json.dumps(execution.input_data, indent=2)}
                
                Réponds uniquement par 'true' ou 'false'.
                """
                
                ai_response = ai_service.generate_response(prompt, "manager", temperature=0.1)
                result = ai_response.success and ai_response.content.strip().lower() == 'true'
            else:
                result = False
        else:
            result = False
        
        return {
            'success': True,
            'condition_result': result,
            'condition_met': result
        }
    
    def _execute_parallel_step(self, execution: WorkflowExecution, 
                              config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Exécute des étapes en parallèle
        """
        parallel_steps = config.get('steps', [])
        results = []
        
        # Dans un vrai système, cela serait exécuté en parallèle
        for i, step in enumerate(parallel_steps):
            result = self._execute_step(execution, step, f"parallel_{i}")
            results.append(result)
        
        # Vérifier si toutes les étapes ont réussi
        all_success = all(result.get('success', False) for result in results)
        
        return {
            'success': all_success,
            'parallel_results': results,
            'completed_steps': len([r for r in results if r.get('success')])
        }
    
    def _execute_delay_step(self, execution: WorkflowExecution, 
                           config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Exécute une étape de délai
        """
        delay_seconds = config.get('delay_seconds', 0)
        delay_minutes = config.get('delay_minutes', 0)
        
        total_delay = delay_seconds + (delay_minutes * 60)
        
        # Dans un vrai système, cela programmerait la suite du workflow
        # Ici, on simule juste le délai
        
        return {
            'success': True,
            'delay_applied': total_delay,
            'message': f'Délai de {total_delay} secondes appliqué'
        }
    
    def _execute_notification_step(self, execution: WorkflowExecution, 
                                  config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Exécute une étape de notification
        """
        notification_type = config.get('type', 'info')
        message = config.get('message', '')
        recipients = config.get('recipients', [])
        
        # Remplacer les variables dans le message
        formatted_message = self._format_message(message, execution.input_data)
        
        # Dans un vrai système, cela enverrait des notifications réelles
        logger.info(f"Notification workflow: {formatted_message}")
        
        return {
            'success': True,
            'notification_sent': True,
            'message': formatted_message,
            'recipients_count': len(recipients)
        }
    
    def _execute_data_transformation_step(self, execution: WorkflowExecution, 
                                         config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Exécute une étape de transformation de données
        """
        transformations = config.get('transformations', [])
        transformed_data = execution.input_data.copy()
        
        for transformation in transformations:
            transform_type = transformation.get('type')
            
            if transform_type == 'map_field':
                source_field = transformation.get('source_field')
                target_field = transformation.get('target_field')
                source_value = self._get_nested_value(transformed_data, source_field)
                self._set_nested_value(transformed_data, target_field, source_value)
                
            elif transform_type == 'calculate':
                formula = transformation.get('formula')
                target_field = transformation.get('target_field')
                # Évaluation sécurisée de formules simples
                try:
                    result = eval(formula, {"__builtins__": {}}, transformed_data)
                    self._set_nested_value(transformed_data, target_field, result)
                except:
                    pass
        
        # Mettre à jour les données d'exécution
        execution.output_data.update(transformed_data)
        execution.save()
        
        return {
            'success': True,
            'transformed_data': transformed_data
        }
    
    def _execute_ai_decision_step(self, execution: WorkflowExecution, 
                                 config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Exécute une étape de décision IA
        """
        if not ai_service.is_available():
            return {
                'success': False,
                'error': 'Service IA non disponible'
            }
        
        decision_prompt = config.get('prompt', '')
        context_data = {
            'workflow_data': execution.input_data,
            'execution_log': execution.execution_log
        }
        
        formatted_prompt = f"""
        {decision_prompt}
        
        Contexte:
        {json.dumps(context_data, indent=2)}
        
        Réponds au format JSON avec les clés: decision, reasoning, confidence
        """
        
        ai_response = ai_service.generate_response(formatted_prompt, "manager", temperature=0.4)
        
        if ai_response.success:
            try:
                decision_data = json.loads(ai_response.content)
                return {
                    'success': True,
                    'ai_decision': decision_data,
                    'tokens_used': ai_response.tokens_used
                }
            except json.JSONDecodeError:
                return {
                    'success': False,
                    'error': 'Réponse IA invalide'
                }
        else:
            return {
                'success': False,
                'error': ai_response.error
            }
    
    def _get_nested_value(self, data: Dict, field_path: str) -> Any:
        """Récupère une valeur dans un dictionnaire imbriqué"""
        keys = field_path.split('.')
        value = data
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        return value
    
    def _set_nested_value(self, data: Dict, field_path: str, value: Any):
        """Définit une valeur dans un dictionnaire imbriqué"""
        keys = field_path.split('.')
        current = data
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[keys[-1]] = value
    
    def _evaluate_condition(self, actual_value: Any, operator: str, expected_value: Any) -> bool:
        """Évalue une condition simple"""
        if operator == 'equals':
            return actual_value == expected_value
        elif operator == 'not_equals':
            return actual_value != expected_value
        elif operator == 'greater_than':
            return actual_value > expected_value
        elif operator == 'less_than':
            return actual_value < expected_value
        elif operator == 'contains':
            return expected_value in str(actual_value)
        elif operator == 'exists':
            return actual_value is not None
        else:
            return False
    
    def _format_message(self, message: str, data: Dict[str, Any]) -> str:
        """Formate un message avec les données du workflow"""
        try:
            return message.format(**data)
        except:
            return message


class WorkflowExecutionError(Exception):
    """Exception pour les erreurs d'exécution de workflow"""
    pass


# Instance globale du moteur de workflow
def get_workflow_engine(tenant: Tenant) -> WorkflowEngine:
    """Factory pour créer une instance du moteur de workflow"""
    return WorkflowEngine(tenant)
