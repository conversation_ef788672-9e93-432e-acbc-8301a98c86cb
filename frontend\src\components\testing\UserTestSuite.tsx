import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface TestResult {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  duration?: number;
  error?: string;
}

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  threshold: number;
  status: 'good' | 'warning' | 'critical';
}

export const UserTestSuite: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([
    { id: 'ui-load', name: 'Chargement Interface', description: 'Temps de chargement initial des composants', status: 'pending' },
    { id: 'navigation', name: 'Navigation Agents', description: 'Navigation entre les différents agents', status: 'pending' },
    { id: 'animations', name: 'Animations Fluides', description: 'Performance des animations Framer Motion', status: 'pending' },
    { id: 'charts', name: 'Rendu Graphiques', description: 'Affichage des graphiques interactifs', status: 'pending' },
    { id: 'responsive', name: 'Design Responsive', description: 'Adaptation mobile et desktop', status: 'pending' },
    { id: 'interactions', name: 'Interactions Utilisateur', description: 'Réactivité des boutons et formulaires', status: 'pending' },
    { id: 'data-loading', name: 'Chargement Données', description: 'Simulation de chargement des données API', status: 'pending' },
    { id: 'error-handling', name: 'Gestion Erreurs', description: 'Affichage des erreurs et états de chargement', status: 'pending' }
  ]);

  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetric[]>([
    { name: 'Temps de chargement initial', value: 0, unit: 'ms', threshold: 3000, status: 'good' },
    { name: 'Temps de navigation', value: 0, unit: 'ms', threshold: 500, status: 'good' },
    { name: 'FPS animations', value: 0, unit: 'fps', threshold: 30, status: 'good' },
    { name: 'Utilisation mémoire', value: 0, unit: 'MB', threshold: 100, status: 'good' },
    { name: 'Temps de rendu graphiques', value: 0, unit: 'ms', threshold: 1000, status: 'good' }
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string | null>(null);
  const [overallScore, setOverallScore] = useState(0);

  const runTest = async (testId: string): Promise<boolean> => {
    const startTime = performance.now();
    
    // Simuler différents tests
    switch (testId) {
      case 'ui-load':
        await new Promise(resolve => setTimeout(resolve, 1000));
        updatePerformanceMetric('Temps de chargement initial', performance.now() - startTime);
        return true;
        
      case 'navigation':
        await new Promise(resolve => setTimeout(resolve, 300));
        updatePerformanceMetric('Temps de navigation', performance.now() - startTime);
        return true;
        
      case 'animations':
        await new Promise(resolve => setTimeout(resolve, 500));
        updatePerformanceMetric('FPS animations', 60);
        return true;
        
      case 'charts':
        await new Promise(resolve => setTimeout(resolve, 800));
        updatePerformanceMetric('Temps de rendu graphiques', performance.now() - startTime);
        return true;
        
      case 'responsive':
        await new Promise(resolve => setTimeout(resolve, 200));
        return window.innerWidth > 0; // Simple check
        
      case 'interactions':
        await new Promise(resolve => setTimeout(resolve, 100));
        return true;
        
      case 'data-loading':
        await new Promise(resolve => setTimeout(resolve, 1500));
        return Math.random() > 0.1; // 90% success rate
        
      case 'error-handling':
        await new Promise(resolve => setTimeout(resolve, 300));
        return true;
        
      default:
        return false;
    }
  };

  const updatePerformanceMetric = (name: string, value: number) => {
    setPerformanceMetrics(prev => prev.map(metric => {
      if (metric.name === name) {
        const status = value <= metric.threshold ? 'good' : 
                      value <= metric.threshold * 1.5 ? 'warning' : 'critical';
        return { ...metric, value, status };
      }
      return metric;
    }));
  };

  const runAllTests = async () => {
    setIsRunning(true);
    let passedTests = 0;

    for (const test of tests) {
      setCurrentTest(test.id);
      
      // Marquer le test comme en cours
      setTests(prev => prev.map(t => 
        t.id === test.id ? { ...t, status: 'running' } : t
      ));

      try {
        const startTime = performance.now();
        const result = await runTest(test.id);
        const duration = performance.now() - startTime;

        if (result) {
          passedTests++;
          setTests(prev => prev.map(t => 
            t.id === test.id ? { ...t, status: 'passed', duration } : t
          ));
        } else {
          setTests(prev => prev.map(t => 
            t.id === test.id ? { ...t, status: 'failed', duration, error: 'Test échoué' } : t
          ));
        }
      } catch (error) {
        setTests(prev => prev.map(t => 
          t.id === test.id ? { ...t, status: 'failed', error: error instanceof Error ? error.message : 'Erreur inconnue' } : t
        ));
      }

      // Pause entre les tests
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    setCurrentTest(null);
    setIsRunning(false);
    setOverallScore(Math.round((passedTests / tests.length) * 100));
  };

  const resetTests = () => {
    setTests(prev => prev.map(test => ({ ...test, status: 'pending', duration: undefined, error: undefined })));
    setOverallScore(0);
    setCurrentTest(null);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed': return '✅';
      case 'failed': return '❌';
      case 'running': return '⏳';
      default: return '⚪';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'running': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getPerformanceColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const passedCount = tests.filter(t => t.status === 'passed').length;
  const failedCount = tests.filter(t => t.status === 'failed').length;

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-3xl">
              🧪
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Suite de Tests Utilisateur</h1>
              <p className="text-gray-600 text-lg">Validation de l'expérience utilisateur ERP HUB</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={resetTests}
              disabled={isRunning}
              className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              🔄 Reset
            </button>
            <button
              onClick={runAllTests}
              disabled={isRunning}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {isRunning ? '⏳ Tests en cours...' : '🚀 Lancer les Tests'}
            </button>
          </div>
        </div>
      </motion.div>

      {/* Score global */}
      {overallScore > 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className={`p-6 rounded-xl text-center ${
            overallScore >= 90 ? 'bg-green-100 border-green-500' :
            overallScore >= 70 ? 'bg-yellow-100 border-yellow-500' :
            'bg-red-100 border-red-500'
          } border-2`}>
            <div className="text-4xl font-bold mb-2">{overallScore}%</div>
            <div className="text-lg">
              {overallScore >= 90 ? '🎉 Excellent!' :
               overallScore >= 70 ? '👍 Bon' :
               '⚠️ À améliorer'}
            </div>
            <div className="text-sm mt-2">
              {passedCount} tests réussis • {failedCount} tests échoués
            </div>
          </div>
        </motion.div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tests */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Tests Fonctionnels</h3>
          <div className="space-y-3">
            {tests.map((test, index) => (
              <motion.div
                key={test.id}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`p-4 rounded-lg border-2 transition-all ${
                  currentTest === test.id ? 'border-blue-500 bg-blue-50' :
                  test.status === 'passed' ? 'border-green-500 bg-green-50' :
                  test.status === 'failed' ? 'border-red-500 bg-red-50' :
                  'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-xl">{getStatusIcon(test.status)}</span>
                    <div>
                      <div className="font-semibold">{test.name}</div>
                      <div className="text-sm text-gray-600">{test.description}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`font-semibold ${getStatusColor(test.status)}`}>
                      {test.status}
                    </div>
                    {test.duration && (
                      <div className="text-xs text-gray-500">
                        {test.duration.toFixed(0)}ms
                      </div>
                    )}
                  </div>
                </div>
                {test.error && (
                  <div className="mt-2 text-sm text-red-600 bg-red-100 p-2 rounded">
                    {test.error}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>

        {/* Métriques de performance */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Métriques de Performance</h3>
          <div className="space-y-4">
            {performanceMetrics.map((metric, index) => (
              <motion.div
                key={metric.name}
                initial={{ opacity: 0, x: 10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="p-4 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="font-semibold">{metric.name}</div>
                  <div className={`font-bold ${getPerformanceColor(metric.status)}`}>
                    {metric.value > 0 ? `${metric.value.toFixed(metric.unit === 'ms' ? 0 : 1)}${metric.unit}` : '-'}
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-500 ${
                      metric.status === 'good' ? 'bg-green-500' :
                      metric.status === 'warning' ? 'bg-yellow-500' :
                      'bg-red-500'
                    }`}
                    style={{ 
                      width: metric.value > 0 ? 
                        `${Math.min((metric.value / (metric.threshold * 2)) * 100, 100)}%` : 
                        '0%' 
                    }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Seuil: {metric.threshold}{metric.unit}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
