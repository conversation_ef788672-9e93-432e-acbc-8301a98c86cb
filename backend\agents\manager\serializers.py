"""
Serializers pour l'Agent Manager
"""
from rest_framework import serializers
from agents.models import Agent, AgentTask, AgentCommunication, Workflow, WorkflowExecution
from .models import ManagerDecision, SystemMetrics


class AgentSerializer(serializers.ModelSerializer):
    """Serializer pour les agents"""
    success_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = Agent
        fields = [
            'id', 'name', 'agent_type', 'description', 'status', 'is_enabled',
            'priority', 'ai_enabled', 'ai_model', 'capabilities', 'dependencies',
            'last_activity', 'total_tasks', 'successful_tasks', 'failed_tasks',
            'success_rate', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'success_rate']


class AgentTaskSerializer(serializers.ModelSerializer):
    """Serializer pour les tâches d'agents"""
    agent_name = serializers.CharField(source='agent.name', read_only=True)
    agent_type = serializers.CharField(source='agent.agent_type', read_only=True)
    assigned_by_name = serializers.CharField(source='assigned_by.username', read_only=True)
    
    class Meta:
        model = AgentTask
        fields = [
            'id', 'title', 'description', 'task_type', 'status', 'priority',
            'input_data', 'output_data', 'error_message', 'scheduled_at',
            'started_at', 'completed_at', 'agent', 'agent_name', 'agent_type',
            'parent_task', 'assigned_by', 'assigned_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class TaskAssignmentSerializer(serializers.Serializer):
    """Serializer pour l'assignation de tâches"""
    title = serializers.CharField(max_length=200)
    description = serializers.CharField(required=False, allow_blank=True)
    task_type = serializers.CharField(max_length=100)
    priority = serializers.IntegerField(min_value=1, max_value=5, default=3)
    target_agent_type = serializers.CharField(max_length=20, required=False)
    input_data = serializers.JSONField(default=dict)
    scheduled_at = serializers.DateTimeField(required=False)
    
    def validate_target_agent_type(self, value):
        """Valide le type d'agent cible"""
        if value:
            valid_types = [choice[0] for choice in Agent.AGENT_TYPES]
            if value not in valid_types:
                raise serializers.ValidationError(f"Type d'agent invalide. Choix: {valid_types}")
        return value


class AgentCommunicationSerializer(serializers.ModelSerializer):
    """Serializer pour la communication entre agents"""
    from_agent_name = serializers.CharField(source='from_agent.name', read_only=True)
    to_agent_name = serializers.CharField(source='to_agent.name', read_only=True)
    
    class Meta:
        model = AgentCommunication
        fields = [
            'id', 'message_type', 'subject', 'content', 'correlation_id',
            'is_read', 'read_at', 'from_agent', 'from_agent_name',
            'to_agent', 'to_agent_name', 'related_task', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ManagerDecisionSerializer(serializers.ModelSerializer):
    """Serializer pour les décisions du manager"""
    involved_agents_names = serializers.SerializerMethodField()
    
    class Meta:
        model = ManagerDecision
        fields = [
            'id', 'decision_type', 'context', 'decision_data', 'reasoning',
            'implemented', 'success', 'impact_score', 'involved_agents',
            'involved_agents_names', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_involved_agents_names(self, obj):
        """Retourne les noms des agents impliqués"""
        return [agent.name for agent in obj.involved_agents.all()]


class SystemMetricsSerializer(serializers.ModelSerializer):
    """Serializer pour les métriques système"""
    
    class Meta:
        model = SystemMetrics
        fields = [
            'id', 'total_agents', 'active_agents', 'total_tasks', 'pending_tasks',
            'running_tasks', 'completed_tasks', 'failed_tasks', 'average_task_duration',
            'system_load', 'success_rate', 'agent_metrics', 'alerts',
            'recommendations', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class WorkflowSerializer(serializers.ModelSerializer):
    """Serializer pour les workflows"""
    execution_count = serializers.ReadOnlyField()
    success_count = serializers.ReadOnlyField()
    failure_count = serializers.ReadOnlyField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = Workflow
        fields = [
            'id', 'name', 'description', 'trigger_type', 'trigger_config',
            'steps', 'status', 'is_enabled', 'execution_count', 'success_count',
            'failure_count', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class WorkflowExecutionSerializer(serializers.ModelSerializer):
    """Serializer pour les exécutions de workflows"""
    workflow_name = serializers.CharField(source='workflow.name', read_only=True)
    triggered_by_name = serializers.CharField(source='triggered_by.username', read_only=True)
    
    class Meta:
        model = WorkflowExecution
        fields = [
            'id', 'workflow', 'workflow_name', 'status', 'input_data',
            'output_data', 'execution_log', 'error_message', 'started_at',
            'completed_at', 'current_step', 'triggered_by', 'triggered_by_name'
        ]
        read_only_fields = ['id', 'started_at']


class AIInsightSerializer(serializers.Serializer):
    """Serializer pour les insights IA"""
    observation = serializers.CharField()
    impact = serializers.CharField()
    recommendation = serializers.CharField()
    priority = serializers.ChoiceField(choices=['haute', 'moyenne', 'basse'])
    confidence = serializers.FloatField(min_value=0.0, max_value=1.0, default=0.8)


class WorkflowOptimizationSerializer(serializers.Serializer):
    """Serializer pour les optimisations de workflow"""
    workflow_id = serializers.UUIDField()
    workflow_name = serializers.CharField()
    issue = serializers.CharField()
    current_rate = serializers.FloatField(required=False)
    current_duration = serializers.FloatField(required=False)
    recommendation = serializers.CharField()
    priority = serializers.ChoiceField(choices=['haute', 'moyenne', 'basse'], default='moyenne')


class MessageBroadcastSerializer(serializers.Serializer):
    """Serializer pour la diffusion de messages"""
    message_type = serializers.ChoiceField(choices=AgentCommunication.MESSAGE_TYPES)
    subject = serializers.CharField(max_length=200)
    content = serializers.JSONField()
    target_agents = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text="Liste des types d'agents cibles. Si vide, diffuse à tous."
    )


class SystemHealthSerializer(serializers.Serializer):
    """Serializer pour la santé du système"""
    overall_status = serializers.ChoiceField(choices=['excellent', 'good', 'warning', 'critical'])
    success_rate = serializers.FloatField()
    system_load = serializers.FloatField()
    active_agents = serializers.IntegerField()
    total_agents = serializers.IntegerField()
    pending_tasks = serializers.IntegerField()
    running_tasks = serializers.IntegerField()
    alerts = serializers.ListField(child=serializers.DictField())
    recommendations = serializers.ListField(child=serializers.DictField())
    timestamp = serializers.DateTimeField()
