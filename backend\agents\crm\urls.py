"""
URLs pour l'agent CRM
"""
from django.urls import path
from . import views

app_name = 'crm'

urlpatterns = [
    # Statut et dashboard
    path('status/', views.crm_status, name='status'),
    path('dashboard/', views.crm_dashboard, name='dashboard'),

    # Contacts
    path('contacts/', views.ContactListCreateView.as_view(), name='contact-list'),
    path('contacts/create/', views.ContactCreateView.as_view(), name='contact-create'),
    path('contacts/<uuid:pk>/', views.ContactDetailView.as_view(), name='contact-detail'),

    # Opportunités
    path('opportunities/', views.OpportunityListCreateView.as_view(), name='opportunity-list'),
    path('opportunities/create/', views.OpportunityCreateView.as_view(), name='opportunity-create'),
    path('opportunities/<uuid:pk>/', views.OpportunityDetailView.as_view(), name='opportunity-detail'),

    # Campagnes
    path('campaigns/', views.CampaignListCreateView.as_view(), name='campaign-list'),
    path('campaigns/create/', views.CampaignCreateView.as_view(), name='campaign-create'),
    path('campaigns/<uuid:pk>/', views.CampaignDetailView.as_view(), name='campaign-detail'),

    # Tickets de support
    path('tickets/', views.SupportTicketListCreateView.as_view(), name='ticket-list'),
    path('tickets/create/', views.SupportTicketCreateView.as_view(), name='ticket-create'),
    path('tickets/<uuid:pk>/', views.SupportTicketDetailView.as_view(), name='ticket-detail'),

    # Interactions
    path('interactions/', views.InteractionListCreateView.as_view(), name='interaction-list'),
    path('interactions/create/', views.InteractionCreateView.as_view(), name='interaction-create'),
    path('interactions/<uuid:pk>/', views.InteractionDetailView.as_view(), name='interaction-detail'),

    # Analytics et insights
    path('insights/', views.crm_insights, name='insights'),
    path('lead-scores/update/', views.update_lead_scores, name='update-lead-scores'),
]
