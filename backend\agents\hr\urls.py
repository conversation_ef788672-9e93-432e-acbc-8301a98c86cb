"""
URLs pour l'agent HR
"""
from django.urls import path
from . import views

app_name = 'hr'

urlpatterns = [
    # Statut et dashboard
    path('status/', views.hr_status, name='status'),
    path('dashboard/', views.hr_dashboard, name='dashboard'),
    path('insights/', views.hr_insights, name='insights'),

    # Gestion des départements
    path('departments/', views.DepartmentListCreateView.as_view(), name='department_list_create'),
    path('departments/<uuid:pk>/', views.DepartmentDetailView.as_view(), name='department_detail'),

    # Gestion des postes
    path('positions/', views.PositionListCreateView.as_view(), name='position_list_create'),
    path('positions/<uuid:pk>/', views.PositionDetailView.as_view(), name='position_detail'),

    # Gestion des employés
    path('employees/', views.EmployeeListCreateView.as_view(), name='employee_list_create'),
    path('employees/<uuid:pk>/', views.EmployeeDetailView.as_view(), name='employee_detail'),

    # Types de congés
    path('leave-types/', views.LeaveTypeListCreateView.as_view(), name='leave_type_list_create'),

    # Demandes de congés
    path('leave-requests/', views.LeaveRequestListCreateView.as_view(), name='leave_request_list_create'),
    path('leave-requests/<uuid:pk>/', views.LeaveRequestDetailView.as_view(), name='leave_request_detail'),
    path('leave-requests/<uuid:leave_request_id>/action/', views.LeaveRequestActionView.as_view(), name='leave_request_action'),

    # Évaluations de performance
    path('performance-reviews/', views.PerformanceReviewListCreateView.as_view(), name='performance_review_list'),
    path('performance-reviews/create/', views.PerformanceReviewCreateView.as_view(), name='performance_review_create'),

    # Formations
    path('trainings/', views.TrainingListCreateView.as_view(), name='training_list_create'),
    path('trainings/create/', views.TrainingCreateView.as_view(), name='training_create'),
    path('trainings/<uuid:training_id>/enroll/<uuid:employee_id>/', views.TrainingEnrollmentView.as_view(), name='training_enrollment'),
]
