"""
Vues pour l'agent Sales
"""
import logging
from django.utils import timezone
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema

from core.permissions import SalesReadPermission, SalesWritePermission
from .services import SalesService
from .models import Customer, Product, Opportunity, Quote, QuoteItem
from .serializers import (
    CustomerSerializer, CustomerCreateSerializer, ProductSerializer, ProductCreateSerializer,
    OpportunitySerializer, OpportunityCreateSerializer, QuoteSerializer, QuoteCreateSerializer,
    QuoteItemSerializer, SalesDashboardSerializer, OpportunityStageUpdateSerializer,
    SalesPerformanceSerializer, SalesInsightSerializer, QuoteStatusUpdateSerializer
)

logger = logging.getLogger('agents.sales')


@extend_schema(
    summary="Statut de l'agent Sales",
    description="Retourne le statut de l'agent Sales"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def sales_status(request):
    """Retourne le statut de l'agent Sales"""
    try:
        sales_service = SalesService(request.user.tenant)

        return Response({
            'status': 'active',
            'agent': 'sales',
            'message': 'Agent Ventes opérationnel',
            'capabilities': [
                'opportunity_management',
                'quote_generation',
                'customer_relationship',
                'sales_forecasting',
                'pipeline_analysis',
                'pricing_optimization'
            ]
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut Sales: {str(e)}")
        return Response({
            'status': 'error',
            'agent': 'sales',
            'message': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Dashboard de l'agent Sales",
    description="Retourne les données complètes du dashboard Sales"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, SalesReadPermission])
def sales_dashboard(request):
    """Retourne les données du dashboard Sales"""
    try:
        sales_service = SalesService(request.user.tenant)
        dashboard_data = sales_service.get_sales_dashboard()

        return Response(dashboard_data)
    except Exception as e:
        logger.error(f"Erreur lors de la génération du dashboard Sales: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Gestion des clients
class CustomerListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des clients"""
    permission_classes = [IsAuthenticated, SalesReadPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CustomerCreateSerializer
        return CustomerSerializer

    def get_queryset(self):
        queryset = Customer.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        customer_type = self.request.query_params.get('type')
        if customer_type:
            queryset = queryset.filter(customer_type=customer_type)

        is_active = self.request.query_params.get('active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        sales_rep = self.request.query_params.get('sales_rep')
        if sales_rep:
            queryset = queryset.filter(sales_rep_id=sales_rep)

        return queryset.order_by('name')

    def perform_create(self, serializer):
        # Générer le code client automatiquement
        customer_code = self._generate_customer_code()
        serializer.save(
            tenant=self.request.user.tenant,
            customer_code=customer_code
        )

    def _generate_customer_code(self):
        """Génère un code client unique"""
        last_customer = Customer.objects.filter(
            tenant=self.request.user.tenant
        ).order_by('-customer_code').first()

        if last_customer and last_customer.customer_code.startswith('CLI'):
            try:
                last_number = int(last_customer.customer_code[3:])
                new_number = last_number + 1
            except ValueError:
                new_number = 1
        else:
            new_number = 1

        return f"CLI{new_number:06d}"


class CustomerDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un client"""
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated, SalesWritePermission]

    def get_queryset(self):
        return Customer.objects.filter(tenant=self.request.user.tenant)


# Gestion des produits
class ProductListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des produits"""
    permission_classes = [IsAuthenticated, SalesReadPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ProductCreateSerializer
        return ProductSerializer

    def get_queryset(self):
        queryset = Product.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        product_type = self.request.query_params.get('type')
        if product_type:
            queryset = queryset.filter(product_type=product_type)

        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)

        is_sellable = self.request.query_params.get('sellable')
        if is_sellable is not None:
            queryset = queryset.filter(is_sellable=is_sellable.lower() == 'true')

        return queryset.order_by('name')

    def perform_create(self, serializer):
        # Générer le code produit automatiquement
        product_code = self._generate_product_code()
        serializer.save(
            tenant=self.request.user.tenant,
            product_code=product_code
        )

    def _generate_product_code(self):
        """Génère un code produit unique"""
        last_product = Product.objects.filter(
            tenant=self.request.user.tenant
        ).order_by('-product_code').first()

        if last_product and last_product.product_code.startswith('PRD'):
            try:
                last_number = int(last_product.product_code[3:])
                new_number = last_number + 1
            except ValueError:
                new_number = 1
        else:
            new_number = 1

        return f"PRD{new_number:06d}"


class ProductDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un produit"""
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated, SalesWritePermission]

    def get_queryset(self):
        return Product.objects.filter(tenant=self.request.user.tenant)


# Gestion des opportunités
class OpportunityListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des opportunités"""
    permission_classes = [IsAuthenticated, SalesReadPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return OpportunityCreateSerializer
        return OpportunitySerializer

    def get_queryset(self):
        queryset = Opportunity.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        stage = self.request.query_params.get('stage')
        if stage:
            queryset = queryset.filter(stage=stage)

        customer_id = self.request.query_params.get('customer')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)

        sales_rep = self.request.query_params.get('sales_rep')
        if sales_rep:
            queryset = queryset.filter(sales_rep_id=sales_rep)

        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(
            tenant=self.request.user.tenant,
            sales_rep=self.request.user
        )


class OpportunityDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une opportunité"""
    serializer_class = OpportunitySerializer
    permission_classes = [IsAuthenticated, SalesWritePermission]

    def get_queryset(self):
        return Opportunity.objects.filter(tenant=self.request.user.tenant)


class OpportunityCreateView(APIView):
    """Vue pour créer des opportunités avec l'aide de l'IA"""
    permission_classes = [IsAuthenticated, SalesWritePermission]

    @extend_schema(
        summary="Créer une opportunité",
        description="Crée une nouvelle opportunité avec recommandations IA",
        request=OpportunityCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle opportunité"""
        try:
            serializer = OpportunityCreateSerializer(data=request.data)
            if serializer.is_valid():
                sales_service = SalesService(request.user.tenant)

                result = sales_service.create_opportunity(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'opportunité: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class OpportunityStageUpdateView(APIView):
    """Vue pour mettre à jour l'étape d'une opportunité"""
    permission_classes = [IsAuthenticated, SalesWritePermission]

    @extend_schema(
        summary="Mettre à jour l'étape d'une opportunité",
        description="Change l'étape d'une opportunité dans le pipeline",
        request=OpportunityStageUpdateSerializer
    )
    def patch(self, request, opportunity_id):
        """Met à jour l'étape d'une opportunité"""
        try:
            serializer = OpportunityStageUpdateSerializer(data=request.data)
            if serializer.is_valid():
                sales_service = SalesService(request.user.tenant)

                result = sales_service.update_opportunity_stage(
                    opportunity_id,
                    serializer.validated_data['stage'],
                    serializer.validated_data.get('notes', '')
                )

                if result['success']:
                    return Response(result, status=status.HTTP_200_OK)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour de l'opportunité: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Gestion des devis
class QuoteListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des devis"""
    serializer_class = QuoteSerializer
    permission_classes = [IsAuthenticated, SalesReadPermission]

    def get_queryset(self):
        queryset = Quote.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        customer_id = self.request.query_params.get('customer')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)

        sales_rep = self.request.query_params.get('sales_rep')
        if sales_rep:
            queryset = queryset.filter(sales_rep_id=sales_rep)

        return queryset.order_by('-quote_date')


class QuoteCreateView(APIView):
    """Vue pour créer des devis"""
    permission_classes = [IsAuthenticated, SalesWritePermission]

    @extend_schema(
        summary="Créer un devis",
        description="Génère un nouveau devis avec calculs automatiques",
        request=QuoteCreateSerializer
    )
    def post(self, request):
        """Crée un nouveau devis"""
        try:
            serializer = QuoteCreateSerializer(data=request.data)
            if serializer.is_valid():
                sales_service = SalesService(request.user.tenant)

                result = sales_service.generate_quote(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création du devis: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class QuoteDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un devis"""
    serializer_class = QuoteSerializer
    permission_classes = [IsAuthenticated, SalesWritePermission]

    def get_queryset(self):
        return Quote.objects.filter(tenant=self.request.user.tenant)


class QuoteStatusUpdateView(APIView):
    """Vue pour mettre à jour le statut d'un devis"""
    permission_classes = [IsAuthenticated, SalesWritePermission]

    @extend_schema(
        summary="Mettre à jour le statut d'un devis",
        description="Change le statut d'un devis (envoyé, accepté, rejeté, etc.)",
        request=QuoteStatusUpdateSerializer
    )
    def patch(self, request, quote_id):
        """Met à jour le statut d'un devis"""
        try:
            quote = Quote.objects.get(id=quote_id, tenant=request.user.tenant)
            serializer = QuoteStatusUpdateSerializer(data=request.data)

            if serializer.is_valid():
                quote.status = serializer.validated_data['status']
                quote.save()

                return Response({
                    'success': True,
                    'quote': {
                        'id': str(quote.id),
                        'quote_number': quote.quote_number,
                        'status': quote.status
                    }
                })
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Quote.DoesNotExist:
            return Response(
                {'error': 'Devis non trouvé'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour du devis: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Analytics et insights
@extend_schema(
    summary="Performance de vente",
    description="Retourne l'analyse des performances de vente"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, SalesReadPermission])
def sales_performance(request):
    """Retourne l'analyse des performances de vente"""
    try:
        sales_service = SalesService(request.user.tenant)
        performance_data = sales_service.analyze_sales_performance()

        return Response(performance_data)
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse des performances: {str(e)}")
        return Response(
            {'error': f'Erreur lors de l\'analyse: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Insights de vente",
    description="Retourne les insights et recommandations de vente basés sur l'IA"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, SalesReadPermission])
def sales_insights(request):
    """Retourne les insights de vente"""
    try:
        sales_service = SalesService(request.user.tenant)
        insights = sales_service.generate_sales_insights()

        return Response({
            'insights': insights,
            'count': len(insights),
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'insights: {str(e)}")
        return Response(
            {'error': f'Erreur lors de la génération d\'insights: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )