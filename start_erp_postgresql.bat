@echo off
REM 🚀 SCRIPT DE DÉMARRAGE ERP HUB POSTGRESQL POUR WINDOWS
REM Lancement automatique de tous les services

echo 🚀 Démarrage ERP HUB avec PostgreSQL...
echo ================================================

REM Vérifier que Docker est installé
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker n'est pas installé. Veuillez l'installer d'abord.
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose n'est pas installé. Veuillez l'installer d'abord.
    pause
    exit /b 1
)

REM Vérifier que les fichiers nécessaires existent
if not exist "docker-compose-postgresql.yml" (
    echo ❌ Fichier docker-compose-postgresql.yml non trouvé.
    pause
    exit /b 1
)

REM Créer les dossiers nécessaires
echo 📁 Création des dossiers...
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
if not exist "ssl" mkdir ssl
if not exist "monitoring" mkdir monitoring
if not exist "monitoring\grafana" mkdir monitoring\grafana
if not exist "monitoring\grafana\dashboards" mkdir monitoring\grafana\dashboards
if not exist "monitoring\grafana\datasources" mkdir monitoring\grafana\datasources

REM Arrêter les services existants
echo 🛑 Arrêt des services existants...
docker-compose -f docker-compose-postgresql.yml down

REM Nettoyer les containers orphelins
echo 🧹 Nettoyage des containers orphelins...
docker system prune -f

REM Construire les images
echo 🔨 Construction des images Docker...
docker-compose -f docker-compose-postgresql.yml build

REM Démarrer les services
echo 🚀 Démarrage des services...
docker-compose -f docker-compose-postgresql.yml up -d

REM Attendre que PostgreSQL soit prêt
echo ⏳ Attente de PostgreSQL...
timeout /t 10 /nobreak >nul

REM Vérifier l'état des services
echo 📊 Vérification de l'état des services...
docker-compose -f docker-compose-postgresql.yml ps

REM Initialiser la base de données si nécessaire
echo 🗄️ Initialisation de la base de données...
python --version >nul 2>&1
if not errorlevel 1 (
    python backend\postgresql_setup.py
) else (
    echo ⚠️ Python non trouvé. Initialisation manuelle nécessaire.
)

REM Afficher les informations de connexion
echo.
echo ✅ ERP HUB PostgreSQL démarré avec succès !
echo ================================================
echo 🌐 Frontend : http://localhost
echo 🔌 API : http://localhost:5000
echo 🐘 PostgreSQL : localhost:5432
echo 📊 Grafana : http://localhost:3000
echo 🔍 Prometheus : http://localhost:9090
echo 💾 Redis : localhost:6379
echo.
echo 🔒 Comptes par défaut :
echo    Admin : admin / Admin123!
echo    Grafana : admin / admin_grafana_2024
echo.
echo 📋 Commandes utiles :
echo    Arrêter : docker-compose -f docker-compose-postgresql.yml down
echo    Logs : docker-compose -f docker-compose-postgresql.yml logs -f
echo    Redémarrer : docker-compose -f docker-compose-postgresql.yml restart
echo.
echo 🎉 Votre ERP HUB est prêt à l'emploi !
echo.
echo Appuyez sur une touche pour ouvrir le navigateur...
pause >nul

REM Ouvrir le navigateur
start http://localhost

echo Appuyez sur une touche pour fermer...
pause >nul
