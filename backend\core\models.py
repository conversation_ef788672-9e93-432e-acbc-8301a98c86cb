"""
Modèles de base pour ERP HUB
Inclut le système d'authentification, multi-tenant et RBAC
"""
import uuid
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator


class TimeStampedModel(models.Model):
    """
    Modèle abstrait qui ajoute des timestamps automatiques
    """
    created_at = models.DateTimeField(_("Créé le"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Modifié le"), auto_now=True)
    
    class Meta:
        abstract = True


class UUIDModel(models.Model):
    """
    Modèle abstrait qui utilise UUID comme clé primaire
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    class Meta:
        abstract = True


class Tenant(UUIDModel, TimeStampedModel):
    """
    Modèle pour le multi-tenant (entreprises/organisations)
    """
    name = models.CharField(_("Nom"), max_length=255)
    slug = models.SlugField(_("Slug"), unique=True, max_length=100)
    description = models.TextField(_("Description"), blank=True)
    
    # Informations de contact
    email = models.EmailField(_("Email"), blank=True)
    phone = models.CharField(_("Téléphone"), max_length=20, blank=True)
    website = models.URLField(_("Site web"), blank=True)
    
    # Adresse
    address_line1 = models.CharField(_("Adresse ligne 1"), max_length=255, blank=True)
    address_line2 = models.CharField(_("Adresse ligne 2"), max_length=255, blank=True)
    city = models.CharField(_("Ville"), max_length=100, blank=True)
    postal_code = models.CharField(_("Code postal"), max_length=20, blank=True)
    country = models.CharField(_("Pays"), max_length=100, blank=True)
    
    # Configuration
    is_active = models.BooleanField(_("Actif"), default=True)
    max_users = models.PositiveIntegerField(_("Nombre max d'utilisateurs"), default=10)
    
    # Modules activés (pour l'architecture modulaire)
    enabled_modules = models.JSONField(_("Modules activés"), default=list, blank=True)
    
    class Meta:
        verbose_name = _("Tenant")
        verbose_name_plural = _("Tenants")
        ordering = ['name']
    
    def __str__(self):
        return self.name


class User(AbstractUser, UUIDModel, TimeStampedModel):
    """
    Modèle utilisateur personnalisé avec support multi-tenant
    """
    # Relation avec le tenant
    tenant = models.ForeignKey(
        Tenant, 
        on_delete=models.CASCADE, 
        related_name='users',
        verbose_name=_("Tenant")
    )
    
    # Informations personnelles étendues
    phone = models.CharField(
        _("Téléphone"), 
        max_length=20, 
        blank=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', _("Format de téléphone invalide"))]
    )
    avatar = models.ImageField(_("Avatar"), upload_to='avatars/', blank=True, null=True)
    birth_date = models.DateField(_("Date de naissance"), blank=True, null=True)
    
    # Préférences utilisateur
    language = models.CharField(_("Langue"), max_length=10, default='fr')
    timezone = models.CharField(_("Fuseau horaire"), max_length=50, default='Europe/Paris')
    theme = models.CharField(
        _("Thème"), 
        max_length=20, 
        choices=[('light', _('Clair')), ('dark', _('Sombre'))],
        default='light'
    )
    
    # Statut et métadonnées
    is_tenant_admin = models.BooleanField(_("Administrateur du tenant"), default=False)
    last_login_ip = models.GenericIPAddressField(_("Dernière IP de connexion"), blank=True, null=True)
    failed_login_attempts = models.PositiveIntegerField(_("Tentatives de connexion échouées"), default=0)
    account_locked_until = models.DateTimeField(_("Compte verrouillé jusqu'à"), blank=True, null=True)
    
    class Meta:
        verbose_name = _("Utilisateur")
        verbose_name_plural = _("Utilisateurs")
        unique_together = ['tenant', 'username']
        ordering = ['last_name', 'first_name']
    
    def __str__(self):
        return f"{self.get_full_name()} ({self.username})"
    
    def get_full_name(self):
        """Retourne le nom complet de l'utilisateur"""
        return f"{self.first_name} {self.last_name}".strip() or self.username


class Role(UUIDModel, TimeStampedModel):
    """
    Modèle pour les rôles dans le système RBAC
    """
    tenant = models.ForeignKey(
        Tenant, 
        on_delete=models.CASCADE, 
        related_name='roles',
        verbose_name=_("Tenant")
    )
    name = models.CharField(_("Nom"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    is_system_role = models.BooleanField(_("Rôle système"), default=False)
    
    # Permissions par module
    permissions = models.JSONField(_("Permissions"), default=dict, blank=True)
    
    class Meta:
        verbose_name = _("Rôle")
        verbose_name_plural = _("Rôles")
        unique_together = ['tenant', 'name']
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.tenant.name})"


class UserRole(UUIDModel, TimeStampedModel):
    """
    Modèle pour associer les utilisateurs aux rôles
    """
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='user_roles',
        verbose_name=_("Utilisateur")
    )
    role = models.ForeignKey(
        Role, 
        on_delete=models.CASCADE, 
        related_name='user_roles',
        verbose_name=_("Rôle")
    )
    assigned_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='assigned_roles',
        verbose_name=_("Assigné par")
    )
    is_active = models.BooleanField(_("Actif"), default=True)
    
    class Meta:
        verbose_name = _("Attribution de rôle")
        verbose_name_plural = _("Attributions de rôles")
        unique_together = ['user', 'role']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user} - {self.role}"
