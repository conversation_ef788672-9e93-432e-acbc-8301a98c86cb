"""
URLs pour l'agent manager
"""
from django.urls import path
from . import views

app_name = 'manager'

urlpatterns = [
    # Statut et dashboard
    path('status/', views.manager_status, name='status'),
    path('dashboard/', views.manager_dashboard, name='dashboard'),

    # Gestion des tâches
    path('tasks/assign/', views.TaskAssignmentView.as_view(), name='assign_task'),

    # Communication entre agents
    path('communication/', views.AgentCommunicationView.as_view(), name='communication'),

    # Métriques et monitoring
    path('metrics/', views.system_metrics, name='system_metrics'),
    path('optimizations/', views.workflow_optimizations, name='workflow_optimizations'),
]
