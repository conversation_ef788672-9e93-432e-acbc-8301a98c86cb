@echo off
echo 🌐 ERP HUB - Serveur Réseau
echo ===========================
echo.

REM Obtenir l'adresse IP locale
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    set IP=%%a
    goto :found
)
:found
set IP=%IP: =%

echo 📡 Configuration du serveur réseau...
echo 🖥️  Adresse IP du serveur : %IP%
echo 🌐 URL d'accès réseau : http://%IP%:8080
echo.
echo 📱 Accès depuis autres appareils :
echo    - Ordinateurs : http://%IP%:8080
echo    - Smartphones : http://%IP%:8080
echo    - Tablettes : http://%IP%:8080
echo.
echo ⚠️  Assurez-vous que :
echo    1. Tous les appareils sont sur le même réseau WiFi
echo    2. Le firewall autorise le port 8080
echo    3. Ce serveur reste allumé pour l'accès réseau
echo.

REM Vérifier si Python est disponible
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python détecté - Démarrage du serveur réseau...
    echo.
    echo 🔥 Serveur démarré ! Accès réseau disponible.
    echo 🛑 Pour arrêter : Ctrl+C dans cette fenêtre
    echo.
    
    cd frontend
    python -m http.server 8080 --bind 0.0.0.0
) else (
    echo ❌ Python non trouvé
    echo.
    echo 💡 Solutions :
    echo 1. Installer Python depuis https://python.org
    echo 2. Utiliser Node.js : npm install -g http-server
    echo.
    pause
)
