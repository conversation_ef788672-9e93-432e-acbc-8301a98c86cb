<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }

        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea 30%, #764ba2 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea 30%, #764ba2 90%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .logout-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .logout-btn:hover {
            background: #dc2626;
        }

        .main-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .welcome-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            transition: all 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .agents-section {
            margin-top: 3rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #1f2937;
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .agent-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 1rem;
            padding: 1.5rem;
            transition: all 0.3s;
            cursor: pointer;
        }

        .agent-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .agent-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .agent-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .agent-info h3 {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            background: #10b981;
            color: white;
        }

        .agent-description {
            color: #6b7280;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .agent-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-outline {
            border: 1px solid #d1d5db;
            color: #6b7280;
            background: white;
        }

        .btn-outline:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }

            .main-content {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .agents-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">🏢 ERP HUB</div>
        <div class="user-info">
            <div class="user-avatar" id="userAvatar">A</div>
            <span id="userName">Utilisateur</span>
            <button class="logout-btn" onclick="logout()">
                <span class="material-icons" style="font-size: 1rem; vertical-align: middle;">logout</span>
                Déconnexion
            </button>
        </div>
    </header>

    <main class="main-content">
        <div class="welcome-section">
            <h1 class="welcome-title">Bienvenue dans ERP HUB</h1>
            <p class="welcome-subtitle">Tableau de bord principal - Système multi-agents</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="background: #667eea;">
                        <span class="material-icons">people</span>
                    </div>
                </div>
                <div class="stat-value">45</div>
                <div class="stat-label">Utilisateurs actifs</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="background: #10b981;">
                        <span class="material-icons">trending_up</span>
                    </div>
                </div>
                <div class="stat-value">2,340</div>
                <div class="stat-label">Transactions aujourd'hui</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="background: #f59e0b;">
                        <span class="material-icons">speed</span>
                    </div>
                </div>
                <div class="stat-value">99.9%</div>
                <div class="stat-label">Disponibilité système</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="background: #ef4444;">
                        <span class="material-icons">schedule</span>
                    </div>
                </div>
                <div class="stat-value">120ms</div>
                <div class="stat-label">Temps de réponse</div>
            </div>
        </div>

        <div class="agents-section">
            <h2 class="section-title">🤖 Agents ERP Disponibles</h2>
            <div class="agents-grid" id="agentsGrid">
                <!-- Les agents seront chargés dynamiquement -->
            </div>
        </div>
    </main>

    <script>
        // Données des agents
        const agents = [
            {
                id: 'manager',
                name: 'Agent Manager',
                description: 'Supervision générale du système ERP et coordination des agents',
                icon: '📊',
                color: '#6366f1'
            },
            {
                id: 'hr',
                name: 'Agent HR',
                description: 'Gestion des ressources humaines et administration du personnel',
                icon: '👥',
                color: '#8b5cf6'
            },
            {
                id: 'sales',
                name: 'Agent Sales',
                description: 'Gestion commerciale, pipeline de ventes et suivi des prospects',
                icon: '📈',
                color: '#06b6d4'
            },
            {
                id: 'purchase',
                name: 'Agent Purchase',
                description: 'Gestion des achats, fournisseurs et commandes d\'approvisionnement',
                icon: '🛒',
                color: '#10b981'
            },
            {
                id: 'logistics',
                name: 'Agent Logistics',
                description: 'Transport, livraisons et gestion de la chaîne logistique',
                icon: '🚚',
                color: '#f59e0b'
            },
            {
                id: 'stock',
                name: 'Agent Stock',
                description: 'Gestion d\'inventaire, stocks et mouvements de marchandises',
                icon: '📦',
                color: '#84cc16'
            },
            {
                id: 'accounting',
                name: 'Agent Accounting',
                description: 'Comptabilité générale, écritures et rapports financiers',
                icon: '🏦',
                color: '#ef4444'
            },
            {
                id: 'finance',
                name: 'Agent Finance',
                description: 'Gestion financière, trésorerie et analyse des performances',
                icon: '💰',
                color: '#f97316'
            },
            {
                id: 'crm',
                name: 'Agent CRM',
                description: 'Relation client avancée et gestion de la satisfaction',
                icon: '🤝',
                color: '#ec4899'
            },
            {
                id: 'bi',
                name: 'Agent BI',
                description: 'Business Intelligence, analytics et tableaux de bord',
                icon: '📊',
                color: '#3b82f6'
            }
        ];

        function loadUserInfo() {
            const username = localStorage.getItem('username') || 'Utilisateur';
            const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
            const userAvatar = document.getElementById('userAvatar');
            const userName = document.getElementById('userName');

            const displayName = userData.first_name || username;
            userName.textContent = displayName;
            userAvatar.textContent = displayName.charAt(0).toUpperCase();
        }

        function loadAgents() {
            const agentsGrid = document.getElementById('agentsGrid');

            agents.forEach(agent => {
                const agentCard = document.createElement('div');
                agentCard.className = 'agent-card';
                agentCard.innerHTML = `
                    <div class="agent-header">
                        <div class="agent-icon" style="background: ${agent.color};">
                            ${agent.icon}
                        </div>
                        <div class="agent-info">
                            <h3>${agent.name}</h3>
                            <span class="status-badge">Actif</span>
                        </div>
                    </div>
                    <p class="agent-description">${agent.description}</p>
                    <div class="agent-actions">
                        <button class="btn btn-primary" onclick="openAgent('${agent.id}')">
                            Ouvrir
                        </button>
                        <button class="btn btn-outline" onclick="viewAgentDetails('${agent.id}')">
                            Détails
                        </button>
                    </div>
                `;

                agentsGrid.appendChild(agentCard);
            });
        }

        function openAgent(agentId) {
            // Redirection vers les pages de gestion spécifiques
            const agentPages = {
                'hr': 'hr-management.html',
                'sales': 'sales-management.html',
                'purchase': 'purchase-management.html',
                'logistics': 'logistics-management.html',
                'manager': 'dashboard-demo.html',
                'stock': 'dashboard-demo.html',
                'accounting': 'dashboard-demo.html',
                'finance': 'dashboard-demo.html',
                'crm': 'dashboard-demo.html',
                'bi': 'dashboard-demo.html'
            };

            if (agentPages[agentId] && agentPages[agentId] !== 'dashboard-demo.html') {
                window.location.href = agentPages[agentId];
            } else {
                alert(`Agent ${agentId} - Page de gestion en cours de développement.\n\nPages disponibles :\n- Agent HR : Gestion complète des employés\n- Agent Sales : Gestion du pipeline de ventes\n- Agent Purchase : Gestion des commandes d'achat\n- Agent Logistics : Gestion des expéditions et livraisons`);
            }
        }

        function viewAgentDetails(agentId) {
            const agent = agents.find(a => a.id === agentId);
            if (agent) {
                alert(`Détails de ${agent.name}:\n\n${agent.description}\n\nStatut: Actif\nDernière mise à jour: ${new Date().toLocaleString()}`);
            }
        }

        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                localStorage.removeItem('isAuthenticated');
                localStorage.removeItem('username');
                localStorage.removeItem('loginTime');

                alert('Déconnexion réussie !');
                window.location.href = 'homepage-demo.html';
            }
        }

        // Vérifier l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder au dashboard');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadUserInfo();
                loadAgents();
            }
        });
    </script>
</body>
</html>
