import React from 'react';
import { Theme<PERSON>rovider, CssBaseline, Box, Container, Typography, Grid, Card, CardContent } from '@mui/material';
import { erpTheme } from '../theme/erpTheme';
import { MetricCard } from '../components/ui/MetricCard';
import { AnimatedChart } from '../components/ui/AnimatedChart';
import { StatusIndicator } from '../components/ui/StatusIndicator';
import { TrendingUp, People, AttachMoney, Assessment } from '@mui/icons-material';

// Données de démonstration
const demoData = [
  { name: 'Jan', value: 4000, value2: 2400 },
  { name: 'Fév', value: 3000, value2: 1398 },
  { name: 'Mar', value: 2000, value2: 9800 },
  { name: 'Avr', value: 2780, value2: 3908 },
  { name: 'Mai', value: 1890, value2: 4800 },
  { name: 'Jun', value: 2390, value2: 3800 },
  { name: 'Jul', value: 3490, value2: 4300 }
];

export const DemoApp: React.FC = () => {
  return (
    <ThemeProvider theme={erpTheme}>
      <CssBaseline />
      <Box sx={{ minHeight: '100vh', backgroundColor: '#f8f9fa', py: 4 }}>
        <Container maxWidth="xl">
          <Typography variant="h3" fontWeight={700} color="primary" mb={4} textAlign="center">
            🚀 ERP HUB - Interface Moderne
          </Typography>
          
          <Typography variant="h6" color="text.secondary" mb={6} textAlign="center">
            Démonstration des composants UI améliorés avec Material-UI, animations et graphiques interactifs
          </Typography>

          {/* Métriques */}
          <Grid container spacing={3} mb={6}>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard
                title="Chiffre d'Affaires"
                value={2847500}
                icon={<AttachMoney />}
                color="primary"
                trend="up"
                trendValue={15.2}
                format="currency"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard
                title="Utilisateurs Actifs"
                value={342}
                icon={<People />}
                color="secondary"
                trend="up"
                trendValue={8.7}
                format="number"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard
                title="Performance"
                value={98.5}
                unit="%"
                icon={<TrendingUp />}
                color="success"
                trend="up"
                trendValue={2.1}
                format="percentage"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard
                title="Temps Réponse"
                value={245}
                unit="ms"
                icon={<Assessment />}
                color="info"
                trend="down"
                trendValue={-5.3}
                format="number"
              />
            </Grid>
          </Grid>

          {/* Graphiques */}
          <Grid container spacing={3} mb={6}>
            <Grid item xs={12} lg={8}>
              <AnimatedChart
                title="Évolution des Ventes"
                subtitle="Comparaison mensuelle (en milliers €)"
                data={demoData}
                type="area"
                height={400}
                color="#1976d2"
                secondaryColor="#42a5f5"
              />
            </Grid>
            <Grid item xs={12} lg={4}>
              <AnimatedChart
                title="Répartition par Catégorie"
                subtitle="Distribution des ventes"
                data={[
                  { name: 'Produits', value: 45 },
                  { name: 'Services', value: 30 },
                  { name: 'Support', value: 15 },
                  { name: 'Formation', value: 10 }
                ]}
                type="pie"
                height={400}
              />
            </Grid>
          </Grid>

          {/* Statuts */}
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight={600} mb={3}>
                État des Agents ERP
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box display="flex" alignItems="center" gap={2} p={2} borderRadius={2} bgcolor="success.light" color="white">
                    <StatusIndicator status="active" size="large" />
                    <Box>
                      <Typography variant="h6" fontWeight={600}>Agent Manager</Typography>
                      <Typography variant="body2">Opérationnel</Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box display="flex" alignItems="center" gap={2} p={2} borderRadius={2} bgcolor="secondary.light" color="white">
                    <StatusIndicator status="active" size="large" />
                    <Box>
                      <Typography variant="h6" fontWeight={600}>Agent HR</Typography>
                      <Typography variant="body2">Opérationnel</Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box display="flex" alignItems="center" gap={2} p={2} borderRadius={2} bgcolor="primary.light" color="white">
                    <StatusIndicator status="active" size="large" />
                    <Box>
                      <Typography variant="h6" fontWeight={600}>Agent Sales</Typography>
                      <Typography variant="body2">Opérationnel</Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box display="flex" alignItems="center" gap={2} p={2} borderRadius={2} bgcolor="warning.light" color="white">
                    <StatusIndicator status="warning" size="large" />
                    <Box>
                      <Typography variant="h6" fontWeight={600}>Agent Finance</Typography>
                      <Typography variant="body2">Maintenance</Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Footer */}
          <Box mt={6} textAlign="center">
            <Typography variant="body2" color="text.secondary">
              🎨 Interface modernisée avec Material-UI, Framer Motion, et Recharts
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ✨ Animations fluides, graphiques interactifs, et design responsive
            </Typography>
          </Box>
        </Container>
      </Box>
    </ThemeProvider>
  );
};
