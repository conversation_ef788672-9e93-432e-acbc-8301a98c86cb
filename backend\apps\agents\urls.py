from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Router pour les APIs REST
router = DefaultRouter()
router.register(r'manager', views.ManagerViewSet, basename='manager')
router.register(r'hr', views.HRViewSet, basename='hr')
router.register(r'sales', views.SalesViewSet, basename='sales')
router.register(r'purchase', views.PurchaseViewSet, basename='purchase')
router.register(r'logistics', views.LogisticsViewSet, basename='logistics')
router.register(r'stock', views.StockViewSet, basename='stock')
router.register(r'accounting', views.AccountingViewSet, basename='accounting')
router.register(r'finance', views.FinanceViewSet, basename='finance')
router.register(r'crm', views.CRMViewSet, basename='crm')
router.register(r'bi', views.BIViewSet, basename='bi')

urlpatterns = [
    # APIs REST pour chaque agent
    path('api/agents/', include(router.urls)),
    
    # APIs spécialisées
    path('api/agents/manager/dashboard/', views.ManagerDashboardView.as_view(), name='manager-dashboard'),
    path('api/agents/hr/employees/', views.HREmployeesView.as_view(), name='hr-employees'),
    path('api/agents/sales/pipeline/', views.SalesPipelineView.as_view(), name='sales-pipeline'),
    path('api/agents/purchase/orders/', views.PurchaseOrdersView.as_view(), name='purchase-orders'),
    path('api/agents/logistics/shipments/', views.LogisticsShipmentsView.as_view(), name='logistics-shipments'),
    path('api/agents/stock/inventory/', views.StockInventoryView.as_view(), name='stock-inventory'),
    path('api/agents/accounting/transactions/', views.AccountingTransactionsView.as_view(), name='accounting-transactions'),
    path('api/agents/finance/reports/', views.FinanceReportsView.as_view(), name='finance-reports'),
    path('api/agents/crm/customers/', views.CRMCustomersView.as_view(), name='crm-customers'),
    path('api/agents/bi/analytics/', views.BIAnalyticsView.as_view(), name='bi-analytics'),
    
    # APIs temps réel
    path('api/agents/realtime/notifications/', views.RealtimeNotificationsView.as_view(), name='realtime-notifications'),
    path('api/agents/realtime/metrics/', views.RealtimeMetricsView.as_view(), name='realtime-metrics'),
    
    # APIs de synchronisation inter-agents
    path('api/agents/sync/data/', views.AgentSyncView.as_view(), name='agent-sync'),
    path('api/agents/sync/status/', views.AgentStatusView.as_view(), name='agent-status'),
]
