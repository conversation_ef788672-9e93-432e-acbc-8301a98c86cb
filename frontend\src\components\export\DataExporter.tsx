import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ExportOption {
  id: string;
  name: string;
  description: string;
  format: 'pdf' | 'excel' | 'csv' | 'json';
  icon: string;
  size?: string;
}

interface ExportConfig {
  format: 'pdf' | 'excel' | 'csv' | 'json';
  dateRange: {
    start: string;
    end: string;
  };
  includeCharts: boolean;
  includeRawData: boolean;
  filters: {
    agents: string[];
    metrics: string[];
  };
}

export const DataExporter: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedExport, setSelectedExport] = useState<ExportOption | null>(null);
  const [config, setConfig] = useState<ExportConfig>({
    format: 'pdf',
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: new Date().toISOString().split('T')[0]
    },
    includeCharts: true,
    includeRawData: false,
    filters: {
      agents: [],
      metrics: []
    }
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const exportOptions: ExportOption[] = [
    {
      id: 'dashboard-report',
      name: 'Rapport Dashboard Complet',
      description: 'Export complet avec tous les graphiques et métriques',
      format: 'pdf',
      icon: '📊',
      size: '~2-5 MB'
    },
    {
      id: 'financial-data',
      name: 'Données Financières',
      description: 'Métriques financières et comptables',
      format: 'excel',
      icon: '💰',
      size: '~500 KB'
    },
    {
      id: 'sales-performance',
      name: 'Performance Commerciale',
      description: 'Données de ventes et pipeline',
      format: 'excel',
      icon: '📈',
      size: '~300 KB'
    },
    {
      id: 'hr-analytics',
      name: 'Analytiques RH',
      description: 'Données employés et recrutement',
      format: 'csv',
      icon: '👥',
      size: '~200 KB'
    },
    {
      id: 'stock-inventory',
      name: 'Inventaire Stock',
      description: 'État des stocks et mouvements',
      format: 'csv',
      icon: '📦',
      size: '~1 MB'
    },
    {
      id: 'raw-data',
      name: 'Données Brutes',
      description: 'Export JSON pour intégrations',
      format: 'json',
      icon: '🔧',
      size: '~10 MB'
    }
  ];

  const availableAgents = [
    'Manager', 'HR', 'Sales', 'Purchase', 'Logistics', 
    'Stock', 'Accounting', 'Finance', 'CRM', 'BI'
  ];

  const availableMetrics = [
    'Revenue', 'Expenses', 'Profit', 'Customers', 'Orders',
    'Inventory', 'Performance', 'Satisfaction', 'Growth'
  ];

  const simulateExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    // Simuler le processus d'export
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 200));
      setExportProgress(i);
    }

    // Simuler le téléchargement
    const blob = new Blob(['Données exportées simulées'], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `export-${selectedExport?.id}-${Date.now()}.${config.format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    setIsExporting(false);
    setExportProgress(0);
    setIsOpen(false);
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return '📄';
      case 'excel': return '📊';
      case 'csv': return '📋';
      case 'json': return '🔧';
      default: return '📁';
    }
  };

  const getFormatColor = (format: string) => {
    switch (format) {
      case 'pdf': return 'bg-red-100 text-red-700';
      case 'excel': return 'bg-green-100 text-green-700';
      case 'csv': return 'bg-blue-100 text-blue-700';
      case 'json': return 'bg-purple-100 text-purple-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <>
      {/* Bouton d'export */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <span>📊</span>
        <span>Exporter</span>
      </motion.button>

      {/* Modal d'export */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
              onClick={() => !isExporting && setIsOpen(false)}
            >
              {/* Modal */}
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6">
                  {/* En-tête */}
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      📊 Export de Données
                    </h2>
                    {!isExporting && (
                      <button
                        onClick={() => setIsOpen(false)}
                        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                      >
                        ✕
                      </button>
                    )}
                  </div>

                  {isExporting ? (
                    /* Écran de progression */
                    <div className="text-center py-12">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        className="text-6xl mb-4"
                      >
                        ⚙️
                      </motion.div>
                      <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                        Export en cours...
                      </h3>
                      <div className="w-full max-w-md mx-auto bg-gray-200 dark:bg-gray-700 rounded-full h-4 mb-4">
                        <motion.div
                          className="bg-green-600 h-4 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${exportProgress}%` }}
                          transition={{ duration: 0.3 }}
                        />
                      </div>
                      <p className="text-gray-600 dark:text-gray-300">
                        {exportProgress}% - Génération du fichier {selectedExport?.format.toUpperCase()}
                      </p>
                    </div>
                  ) : (
                    <>
                      {!selectedExport ? (
                        /* Sélection du type d'export */
                        <div>
                          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
                            Choisissez le type d'export
                          </h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {exportOptions.map((option) => (
                              <motion.div
                                key={option.id}
                                className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:shadow-md transition-all"
                                whileHover={{ scale: 1.02 }}
                                onClick={() => {
                                  setSelectedExport(option);
                                  setConfig(prev => ({ ...prev, format: option.format }));
                                }}
                              >
                                <div className="flex items-center gap-3 mb-3">
                                  <span className="text-2xl">{option.icon}</span>
                                  <div>
                                    <h4 className="font-semibold text-gray-900 dark:text-white">
                                      {option.name}
                                    </h4>
                                    <span className={`px-2 py-1 text-xs rounded-full ${getFormatColor(option.format)}`}>
                                      {getFormatIcon(option.format)} {option.format.toUpperCase()}
                                    </span>
                                  </div>
                                </div>
                                <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                                  {option.description}
                                </p>
                                {option.size && (
                                  <p className="text-xs text-gray-500">
                                    Taille estimée: {option.size}
                                  </p>
                                )}
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        /* Configuration de l'export */
                        <div>
                          <div className="flex items-center gap-3 mb-6">
                            <button
                              onClick={() => setSelectedExport(null)}
                              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                            >
                              ←
                            </button>
                            <span className="text-2xl">{selectedExport.icon}</span>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                {selectedExport.name}
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-gray-300">
                                {selectedExport.description}
                              </p>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Configuration générale */}
                            <div className="space-y-4">
                              <h4 className="font-semibold text-gray-900 dark:text-white">
                                Configuration
                              </h4>

                              {/* Période */}
                              <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                  Période
                                </label>
                                <div className="grid grid-cols-2 gap-2">
                                  <input
                                    type="date"
                                    value={config.dateRange.start}
                                    onChange={(e) => setConfig(prev => ({
                                      ...prev,
                                      dateRange: { ...prev.dateRange, start: e.target.value }
                                    }))}
                                    className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                  <input
                                    type="date"
                                    value={config.dateRange.end}
                                    onChange={(e) => setConfig(prev => ({
                                      ...prev,
                                      dateRange: { ...prev.dateRange, end: e.target.value }
                                    }))}
                                    className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                              </div>

                              {/* Options */}
                              <div className="space-y-2">
                                <label className="flex items-center gap-2">
                                  <input
                                    type="checkbox"
                                    checked={config.includeCharts}
                                    onChange={(e) => setConfig(prev => ({
                                      ...prev,
                                      includeCharts: e.target.checked
                                    }))}
                                    className="rounded"
                                  />
                                  <span className="text-sm text-gray-700 dark:text-gray-300">
                                    Inclure les graphiques
                                  </span>
                                </label>
                                <label className="flex items-center gap-2">
                                  <input
                                    type="checkbox"
                                    checked={config.includeRawData}
                                    onChange={(e) => setConfig(prev => ({
                                      ...prev,
                                      includeRawData: e.target.checked
                                    }))}
                                    className="rounded"
                                  />
                                  <span className="text-sm text-gray-700 dark:text-gray-300">
                                    Inclure les données brutes
                                  </span>
                                </label>
                              </div>
                            </div>

                            {/* Filtres */}
                            <div className="space-y-4">
                              <h4 className="font-semibold text-gray-900 dark:text-white">
                                Filtres
                              </h4>

                              {/* Agents */}
                              <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                  Agents à inclure
                                </label>
                                <div className="grid grid-cols-2 gap-1 max-h-32 overflow-y-auto">
                                  {availableAgents.map(agent => (
                                    <label key={agent} className="flex items-center gap-2">
                                      <input
                                        type="checkbox"
                                        checked={config.filters.agents.includes(agent)}
                                        onChange={(e) => {
                                          if (e.target.checked) {
                                            setConfig(prev => ({
                                              ...prev,
                                              filters: {
                                                ...prev.filters,
                                                agents: [...prev.filters.agents, agent]
                                              }
                                            }));
                                          } else {
                                            setConfig(prev => ({
                                              ...prev,
                                              filters: {
                                                ...prev.filters,
                                                agents: prev.filters.agents.filter(a => a !== agent)
                                              }
                                            }));
                                          }
                                        }}
                                        className="rounded"
                                      />
                                      <span className="text-xs text-gray-700 dark:text-gray-300">
                                        {agent}
                                      </span>
                                    </label>
                                  ))}
                                </div>
                              </div>

                              {/* Métriques */}
                              <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                  Métriques à inclure
                                </label>
                                <div className="grid grid-cols-2 gap-1 max-h-32 overflow-y-auto">
                                  {availableMetrics.map(metric => (
                                    <label key={metric} className="flex items-center gap-2">
                                      <input
                                        type="checkbox"
                                        checked={config.filters.metrics.includes(metric)}
                                        onChange={(e) => {
                                          if (e.target.checked) {
                                            setConfig(prev => ({
                                              ...prev,
                                              filters: {
                                                ...prev.filters,
                                                metrics: [...prev.filters.metrics, metric]
                                              }
                                            }));
                                          } else {
                                            setConfig(prev => ({
                                              ...prev,
                                              filters: {
                                                ...prev.filters,
                                                metrics: prev.filters.metrics.filter(m => m !== metric)
                                              }
                                            }));
                                          }
                                        }}
                                        className="rounded"
                                      />
                                      <span className="text-xs text-gray-700 dark:text-gray-300">
                                        {metric}
                                      </span>
                                    </label>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Actions */}
                          <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <button
                              onClick={() => setSelectedExport(null)}
                              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                            >
                              Retour
                            </button>
                            <button
                              onClick={simulateExport}
                              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                            >
                              🚀 Générer l'Export
                            </button>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </motion.div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};
