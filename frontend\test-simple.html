<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP HUB - Test Simple</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .agent-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        .agent-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.active {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function App() {
            const [agents] = useState([
                { id: 1, name: 'Agent Manager', status: 'active', description: 'Supervision générale du système ERP' },
                { id: 2, name: 'Agent HR', status: 'active', description: 'Gestion des ressources humaines' },
                { id: 3, name: 'Agent Sales', status: 'active', description: 'Gestion commerciale et ventes' },
                { id: 4, name: 'Agent Purchase', status: 'active', description: 'Gestion des achats' },
                { id: 5, name: 'Agent Logistics', status: 'active', description: 'Transport et livraisons' },
                { id: 6, name: 'Agent Stock', status: 'active', description: 'Gestion d\'inventaire' },
                { id: 7, name: 'Agent Accounting', status: 'active', description: 'Comptabilité' },
                { id: 8, name: 'Agent Finance', status: 'active', description: 'Gestion financière' },
                { id: 9, name: 'Agent CRM', status: 'active', description: 'Relation client' },
                { id: 10, name: 'Agent BI', status: 'active', description: 'Business Intelligence' }
            ]);

            return (
                <div className="container">
                    <div className="header">
                        <h1>🏢 ERP HUB - Système Multi-Agents</h1>
                        <p>Plateforme ERP modulaire avec architecture d'agents intelligents</p>
                    </div>
                    
                    <div className="agent-grid">
                        {agents.map(agent => (
                            <div key={agent.id} className="agent-card">
                                <h3>{agent.name}</h3>
                                <p>{agent.description}</p>
                                <span className={`status ${agent.status}`}>
                                    {agent.status === 'active' ? '✅ Actif' : '❌ Inactif'}
                                </span>
                            </div>
                        ))}
                    </div>
                    
                    <div style={{marginTop: '30px', textAlign: 'center'}}>
                        <p><strong>✅ Tous les agents sont opérationnels !</strong></p>
                        <p>L'application ERP HUB fonctionne correctement.</p>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
