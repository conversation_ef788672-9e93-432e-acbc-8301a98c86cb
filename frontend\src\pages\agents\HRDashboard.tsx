import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  LinearProgress,
  Button,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider
} from '@mui/material';
import {
  People,
  PersonAdd,
  School,
  TrendingUp,
  WorkOff,
  Assignment,
  Refresh,
  Download,
  FilterList,
  Celebration,
  Warning,
  Schedule
} from '@mui/icons-material';
import { MetricCard } from '../../components/ui/MetricCard';
import { AnimatedChart } from '../../components/ui/AnimatedChart';
import { StatusIndicator } from '../../components/ui/StatusIndicator';

interface HRMetrics {
  totalEmployees: number;
  newHires: number;
  turnoverRate: number;
  satisfactionScore: number;
  trainingHours: number;
  openPositions: number;
}

interface Employee {
  id: string;
  name: string;
  position: string;
  department: string;
  avatar: string;
  performance: number;
  status: 'active' | 'vacation' | 'training' | 'sick';
  joinDate: string;
}

interface Recruitment {
  id: string;
  position: string;
  department: string;
  candidates: number;
  stage: string;
  priority: 'high' | 'medium' | 'low';
  deadline: string;
}

export const HRDashboard: React.FC = () => {
  const [hrMetrics, setHrMetrics] = useState<HRMetrics>({
    totalEmployees: 247,
    newHires: 12,
    turnoverRate: 8.5,
    satisfactionScore: 4.2,
    trainingHours: 1847,
    openPositions: 8
  });

  const [topPerformers] = useState<Employee[]>([
    { id: '1', name: 'Sarah Johnson', position: 'Senior Developer', department: 'IT', avatar: '👩‍💻', performance: 98, status: 'active', joinDate: '2022-03-15' },
    { id: '2', name: 'Marc Dubois', position: 'Sales Manager', department: 'Sales', avatar: '👨‍💼', performance: 96, status: 'active', joinDate: '2021-08-20' },
    { id: '3', name: 'Lisa Chen', position: 'UX Designer', department: 'Design', avatar: '👩‍🎨', performance: 94, status: 'training', joinDate: '2022-01-10' },
    { id: '4', name: 'Alex Martin', position: 'Data Analyst', department: 'Analytics', avatar: '👨‍💻', performance: 92, status: 'active', joinDate: '2021-11-05' },
    { id: '5', name: 'Emma Wilson', position: 'HR Specialist', department: 'HR', avatar: '👩‍💼', performance: 90, status: 'vacation', joinDate: '2020-06-12' }
  ]);

  const [activeRecruitments] = useState<Recruitment[]>([
    { id: '1', position: 'Full Stack Developer', department: 'IT', candidates: 15, stage: 'Entretiens', priority: 'high', deadline: '2024-02-28' },
    { id: '2', position: 'Marketing Manager', department: 'Marketing', candidates: 8, stage: 'Sélection', priority: 'medium', deadline: '2024-03-15' },
    { id: '3', position: 'DevOps Engineer', department: 'IT', candidates: 12, stage: 'Tests techniques', priority: 'high', deadline: '2024-02-20' },
    { id: '4', position: 'Sales Representative', department: 'Sales', candidates: 6, stage: 'Première sélection', priority: 'low', deadline: '2024-03-30' }
  ]);

  const [departmentData] = useState([
    { name: 'IT', value: 45, satisfaction: 4.3 },
    { name: 'Sales', value: 38, satisfaction: 4.1 },
    { name: 'Marketing', value: 25, satisfaction: 4.4 },
    { name: 'HR', value: 18, satisfaction: 4.2 },
    { name: 'Finance', value: 22, satisfaction: 4.0 },
    { name: 'Operations', value: 35, satisfaction: 4.1 },
    { name: 'Design', value: 15, satisfaction: 4.5 }
  ]);

  const [satisfactionData] = useState([
    { name: 'Jan', value: 4.1, target: 4.0 },
    { name: 'Fév', value: 4.0, target: 4.0 },
    { name: 'Mar', value: 4.2, target: 4.0 },
    { name: 'Avr', value: 4.3, target: 4.0 },
    { name: 'Mai', value: 4.1, target: 4.0 },
    { name: 'Jun', value: 4.4, target: 4.0 },
    { name: 'Jul', value: 4.2, target: 4.0 }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return '🟢';
      case 'vacation': return '🏖️';
      case 'training': return '📚';
      case 'sick': return '🤒';
      default: return '⚪';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar sx={{ bgcolor: 'secondary.main', width: 56, height: 56 }}>
              <People fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={700} color="secondary">
                Agent HR
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Gestion des ressources humaines et talents
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={1}>
            <Tooltip title="Actualiser les données">
              <IconButton onClick={handleRefresh} disabled={isLoading}>
                <Refresh />
              </IconButton>
            </Tooltip>
            <Button variant="outlined" startIcon={<FilterList />}>
              Filtres
            </Button>
            <Button variant="contained" startIcon={<Download />} color="secondary">
              Rapport RH
            </Button>
          </Box>
        </Box>
      </motion.div>

      {/* Métriques principales */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Employés Total"
            value={hrMetrics.totalEmployees}
            icon={<People />}
            color="primary"
            trend="up"
            trendValue={3.2}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Nouvelles Embauches"
            value={hrMetrics.newHires}
            icon={<PersonAdd />}
            color="success"
            trend="up"
            trendValue={25.0}
            format="number"
            subtitle="Ce mois"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Taux de Rotation"
            value={hrMetrics.turnoverRate}
            unit="%"
            icon={<WorkOff />}
            color="warning"
            trend="down"
            trendValue={-1.5}
            format="percentage"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Satisfaction"
            value={hrMetrics.satisfactionScore}
            unit="/5"
            icon={<Celebration />}
            color="info"
            trend="up"
            trendValue={5.2}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Heures Formation"
            value={hrMetrics.trainingHours}
            icon={<School />}
            color="secondary"
            trend="up"
            trendValue={18.7}
            format="number"
            subtitle="Ce trimestre"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Postes Ouverts"
            value={hrMetrics.openPositions}
            icon={<Assignment />}
            color="error"
            trend="up"
            trendValue={12.5}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
      </Grid>

      {/* Top performers et recrutements */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} lg={6}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" fontWeight={600} mb={3}>
                  Top Performers du Mois
                </Typography>
                
                <List>
                  {topPerformers.map((employee, index) => (
                    <React.Fragment key={employee.id}>
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <ListItem sx={{ px: 0 }}>
                          <ListItemAvatar>
                            <Box position="relative">
                              <Typography variant="h3">{employee.avatar}</Typography>
                              <Box
                                position="absolute"
                                bottom={-2}
                                right={-2}
                                sx={{ fontSize: '12px' }}
                              >
                                {getStatusIcon(employee.status)}
                              </Box>
                            </Box>
                          </ListItemAvatar>
                          <ListItemText
                            primary={
                              <Box display="flex" justifyContent="space-between" alignItems="center">
                                <Typography variant="subtitle2" fontWeight={600}>
                                  {employee.name}
                                </Typography>
                                <Chip
                                  label={`${employee.performance}%`}
                                  color="success"
                                  size="small"
                                />
                              </Box>
                            }
                            secondary={
                              <Box>
                                <Typography variant="caption" color="text.secondary">
                                  {employee.position} • {employee.department}
                                </Typography>
                                <LinearProgress
                                  variant="determinate"
                                  value={employee.performance}
                                  sx={{
                                    mt: 1,
                                    height: 4,
                                    borderRadius: 2,
                                    backgroundColor: '#f0f0f0',
                                    '& .MuiLinearProgress-bar': {
                                      borderRadius: 2,
                                      backgroundColor: '#4caf50'
                                    }
                                  }}
                                />
                              </Box>
                            }
                          />
                        </ListItem>
                      </motion.div>
                      {index < topPerformers.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} lg={6}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" fontWeight={600} mb={3}>
                  Recrutements Actifs
                </Typography>
                
                <List>
                  {activeRecruitments.map((recruitment, index) => (
                    <React.Fragment key={recruitment.id}>
                      <motion.div
                        initial={{ opacity: 0, x: 10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <ListItem sx={{ px: 0 }}>
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: 'primary.main' }}>
                              <PersonAdd />
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={
                              <Box display="flex" justifyContent="space-between" alignItems="center">
                                <Typography variant="subtitle2" fontWeight={600}>
                                  {recruitment.position}
                                </Typography>
                                <Chip
                                  label={recruitment.priority.toUpperCase()}
                                  color={getPriorityColor(recruitment.priority) as any}
                                  size="small"
                                />
                              </Box>
                            }
                            secondary={
                              <Box>
                                <Typography variant="caption" color="text.secondary">
                                  {recruitment.department} • {recruitment.candidates} candidats
                                </Typography>
                                <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
                                  <Typography variant="caption" fontWeight={600}>
                                    {recruitment.stage}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Échéance: {new Date(recruitment.deadline).toLocaleDateString('fr-FR')}
                                  </Typography>
                                </Box>
                              </Box>
                            }
                          />
                        </ListItem>
                      </motion.div>
                      {index < activeRecruitments.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Graphiques */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <AnimatedChart
            title="Évolution de la Satisfaction Employés"
            subtitle="Score moyen mensuel (sur 5)"
            data={satisfactionData}
            type="area"
            height={350}
            color="#9c27b0"
            secondaryColor="#ba68c8"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} lg={4}>
          <AnimatedChart
            title="Répartition par Département"
            subtitle="Nombre d'employés"
            data={departmentData}
            type="pie"
            height={350}
            isLoading={isLoading}
          />
        </Grid>
      </Grid>
    </Box>
  );
};
