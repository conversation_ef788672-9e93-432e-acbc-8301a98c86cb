"""
Services pour l'Agent Logistics
Logique métier pour la gestion du transport et de la logistique
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg, F
from django.db import transaction
from decimal import Decimal

from core.models import Tenant, User
from agents.models import Agent
from agents.ai_service import ai_service
from .models import (
    Carrier, CarrierRate, Shipment, ShipmentItem, ShipmentPackage,
    ShipmentTracking, Route, RouteStop, DeliveryAttempt
)

logger = logging.getLogger('agents.logistics')


class LogisticsService:
    """
    Service principal pour l'Agent Logistics
    Gère toutes les opérations de transport et logistique
    """

    def __init__(self, tenant: Tenant):
        self.tenant = tenant
        self.logistics_agent = self._get_or_create_logistics_agent()

    def _get_or_create_logistics_agent(self) -> Agent:
        """Récupère ou crée l'agent Logistics pour le tenant"""
        agent, created = Agent.objects.get_or_create(
            tenant=self.tenant,
            agent_type='logistics',
            defaults={
                'name': 'Agent Logistics',
                'description': 'Gestion du transport et de la logistique',
                'ai_enabled': True,
                'ai_model': 'gpt-4',
                'capabilities': [
                    'carrier_management',
                    'shipment_tracking',
                    'route_optimization',
                    'delivery_management',
                    'cost_optimization',
                    'performance_analytics',
                    'real_time_tracking'
                ]
            }
        )
        if created:
            logger.info(f"Agent Logistics créé pour le tenant {self.tenant.name}")
        return agent

    def get_logistics_dashboard(self) -> Dict[str, Any]:
        """Retourne les données du dashboard Logistics"""

        # Expéditions
        shipments = Shipment.objects.filter(tenant=self.tenant)

        # Transporteurs
        carriers = Carrier.objects.filter(tenant=self.tenant, is_active=True)

        # Itinéraires
        routes = Route.objects.filter(tenant=self.tenant)

        # Expéditions récentes
        recent_shipments = shipments.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        )

        # Expéditions en cours
        active_shipments = shipments.filter(
            status__in=['confirmed', 'picked_up', 'in_transit', 'out_for_delivery']
        )

        # Expéditions en retard
        delayed_shipments = self._get_delayed_shipments()

        # Performance des livraisons
        delivery_performance = self._calculate_delivery_performance()

        # Coûts de transport
        transport_costs = self._calculate_transport_costs()

        return {
            'tenant': self.tenant.name,
            'timestamp': timezone.now().isoformat(),
            'shipments': {
                'total': shipments.count(),
                'active': active_shipments.count(),
                'delivered_today': shipments.filter(
                    status='delivered',
                    actual_delivery_date__date=timezone.now().date()
                ).count(),
                'delayed': len(delayed_shipments)
            },
            'carriers': {
                'total': carriers.count(),
                'active': carriers.filter(is_active=True).count(),
                'preferred': carriers.filter(is_preferred=True).count()
            },
            'routes': {
                'total': routes.count(),
                'active': routes.filter(status='in_progress').count(),
                'planned': routes.filter(status='planned').count()
            },
            'performance': {
                'on_time_delivery_rate': delivery_performance.get('on_time_rate', 0),
                'average_delivery_delay': delivery_performance.get('avg_delay', 0),
                'successful_delivery_rate': delivery_performance.get('success_rate', 0)
            },
            'costs': {
                'total_monthly': transport_costs.get('monthly_total', 0),
                'average_cost_per_shipment': transport_costs.get('avg_per_shipment', 0),
                'cost_trend': transport_costs.get('trend', 'stable')
            },
            'alerts': {
                'delayed_shipments': len(delayed_shipments),
                'failed_deliveries': self._get_failed_deliveries_count(),
                'carrier_issues': self._get_carrier_issues_count()
            },
            'recent_activities': self._get_recent_activities()
        }

    def _get_delayed_shipments(self) -> List[Dict[str, Any]]:
        """Identifie les expéditions en retard"""
        delayed_shipments = []

        active_shipments = Shipment.objects.filter(
            tenant=self.tenant,
            status__in=['confirmed', 'picked_up', 'in_transit', 'out_for_delivery'],
            planned_delivery_date__lt=timezone.now()
        )

        for shipment in active_shipments:
            delay_hours = (timezone.now() - shipment.planned_delivery_date).total_seconds() / 3600
            delayed_shipments.append({
                'shipment_id': str(shipment.id),
                'shipment_number': shipment.shipment_number,
                'recipient_name': shipment.recipient_name,
                'planned_delivery': shipment.planned_delivery_date.isoformat(),
                'delay_hours': round(delay_hours, 1),
                'carrier_name': shipment.carrier.name,
                'status': shipment.status
            })

        return delayed_shipments

    def _calculate_delivery_performance(self) -> Dict[str, float]:
        """Calcule les performances de livraison"""
        # Période d'analyse (30 derniers jours)
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)

        delivered_shipments = Shipment.objects.filter(
            tenant=self.tenant,
            status='delivered',
            actual_delivery_date__gte=start_date,
            actual_delivery_date__lte=end_date
        )

        total_delivered = delivered_shipments.count()
        if total_delivered == 0:
            return {'on_time_rate': 0, 'avg_delay': 0, 'success_rate': 0}

        # Livraisons à temps
        on_time_deliveries = 0
        total_delay_hours = 0

        for shipment in delivered_shipments:
            if shipment.planned_delivery_date and shipment.actual_delivery_date:
                delay = shipment.actual_delivery_date - shipment.planned_delivery_date
                delay_hours = delay.total_seconds() / 3600

                if delay_hours <= 0:  # Livré à temps ou en avance
                    on_time_deliveries += 1
                else:
                    total_delay_hours += delay_hours

        on_time_rate = (on_time_deliveries / total_delivered) * 100
        avg_delay = total_delay_hours / (total_delivered - on_time_deliveries) if total_delivered > on_time_deliveries else 0

        # Taux de succès (livraisons réussies vs tentatives)
        all_attempts = Shipment.objects.filter(
            tenant=self.tenant,
            created_at__gte=start_date,
            created_at__lte=end_date
        ).count()
        success_rate = (total_delivered / all_attempts) * 100 if all_attempts > 0 else 0

        return {
            'on_time_rate': round(on_time_rate, 1),
            'avg_delay': round(avg_delay, 1),
            'success_rate': round(success_rate, 1)
        }

    def _calculate_transport_costs(self) -> Dict[str, Any]:
        """Calcule les coûts de transport"""
        # Coûts du mois en cours
        current_month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        monthly_costs = Shipment.objects.filter(
            tenant=self.tenant,
            created_at__gte=current_month_start,
            actual_cost__isnull=False
        ).aggregate(total=Sum('actual_cost'))['total'] or Decimal('0.00')

        # Coût moyen par expédition
        avg_cost = Shipment.objects.filter(
            tenant=self.tenant,
            actual_cost__isnull=False
        ).aggregate(avg=Avg('actual_cost'))['avg'] or Decimal('0.00')

        # Tendance (comparaison avec le mois précédent)
        previous_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
        previous_month_costs = Shipment.objects.filter(
            tenant=self.tenant,
            created_at__gte=previous_month_start,
            created_at__lt=current_month_start,
            actual_cost__isnull=False
        ).aggregate(total=Sum('actual_cost'))['total'] or Decimal('0.00')

        if previous_month_costs > 0:
            trend_percentage = ((monthly_costs - previous_month_costs) / previous_month_costs) * 100
            if trend_percentage > 5:
                trend = 'increasing'
            elif trend_percentage < -5:
                trend = 'decreasing'
            else:
                trend = 'stable'
        else:
            trend = 'stable'

        return {
            'monthly_total': float(monthly_costs),
            'avg_per_shipment': float(avg_cost),
            'trend': trend
        }

    def _get_failed_deliveries_count(self) -> int:
        """Compte les échecs de livraison récents"""
        return Shipment.objects.filter(
            tenant=self.tenant,
            status='failed',
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()

    def _get_carrier_issues_count(self) -> int:
        """Compte les problèmes transporteurs récents"""
        # Transporteurs avec taux d'échec élevé
        problem_carriers = 0

        for carrier in Carrier.objects.filter(tenant=self.tenant, is_active=True):
            recent_shipments = Shipment.objects.filter(
                tenant=self.tenant,
                carrier=carrier,
                created_at__gte=timezone.now() - timedelta(days=30)
            )

            if recent_shipments.count() > 5:  # Minimum 5 expéditions pour évaluer
                failed_count = recent_shipments.filter(status='failed').count()
                failure_rate = (failed_count / recent_shipments.count()) * 100

                if failure_rate > 10:  # Plus de 10% d'échec
                    problem_carriers += 1

        return problem_carriers

    def _get_recent_activities(self) -> List[Dict[str, Any]]:
        """Récupère les activités récentes"""
        activities = []

        # Événements de suivi récents
        recent_tracking = ShipmentTracking.objects.filter(
            shipment__tenant=self.tenant,
            event_date__gte=timezone.now() - timedelta(days=7)
        ).order_by('-event_date')[:10]

        for event in recent_tracking:
            activities.append({
                'type': f'tracking_{event.event_type}',
                'description': f"{event.event_type.title()}: {event.shipment.shipment_number}",
                'date': event.event_date.isoformat(),
                'location': event.location or 'N/A',
                'carrier': event.shipment.carrier.name
            })

        return activities

    def create_shipment(self, shipment_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée une nouvelle expédition"""
        try:
            with transaction.atomic():
                # Générer le numéro d'expédition
                shipment_number = self._generate_shipment_number()

                # Créer l'expédition
                shipment = Shipment.objects.create(
                    tenant=self.tenant,
                    shipment_number=shipment_number,
                    shipment_type=shipment_data['shipment_type'],
                    carrier_id=shipment_data['carrier_id'],
                    service_level=shipment_data['service_level'],
                    sender_name=shipment_data['sender_name'],
                    sender_address=shipment_data['sender_address'],
                    sender_city=shipment_data['sender_city'],
                    sender_postal_code=shipment_data['sender_postal_code'],
                    sender_country=shipment_data['sender_country'],
                    recipient_name=shipment_data['recipient_name'],
                    recipient_address=shipment_data['recipient_address'],
                    recipient_city=shipment_data['recipient_city'],
                    recipient_postal_code=shipment_data['recipient_postal_code'],
                    recipient_country=shipment_data['recipient_country'],
                    planned_pickup_date=shipment_data.get('planned_pickup_date'),
                    planned_delivery_date=shipment_data.get('planned_delivery_date'),
                    priority=shipment_data.get('priority', 3),
                    special_instructions=shipment_data.get('special_instructions', ''),
                    delivery_instructions=shipment_data.get('delivery_instructions', ''),
                    customer_reference=shipment_data.get('customer_reference', ''),
                    order_reference=shipment_data.get('order_reference', ''),
                    declared_value=shipment_data.get('declared_value'),
                    insurance_required=shipment_data.get('insurance_required', False),
                    signature_required=shipment_data.get('signature_required', False),
                    created_by=user
                )

                # Ajouter les articles
                total_weight = Decimal('0.00')
                total_volume = Decimal('0.000')
                total_value = Decimal('0.00')

                for item_data in shipment_data.get('items', []):
                    item = ShipmentItem.objects.create(
                        shipment=shipment,
                        item_name=item_data['item_name'],
                        description=item_data.get('description', ''),
                        sku=item_data.get('sku', ''),
                        quantity=item_data['quantity'],
                        unit_of_measure=item_data.get('unit_of_measure', 'pcs'),
                        unit_weight=item_data.get('unit_weight'),
                        unit_volume=item_data.get('unit_volume'),
                        unit_value=item_data.get('unit_value'),
                        hs_code=item_data.get('hs_code', ''),
                        country_of_origin=item_data.get('country_of_origin', ''),
                        fragile=item_data.get('fragile', False),
                        hazardous=item_data.get('hazardous', False),
                        temperature_controlled=item_data.get('temperature_controlled', False)
                    )

                    # Calculer les totaux
                    if item.unit_weight:
                        total_weight += item.quantity * item.unit_weight
                    if item.unit_volume:
                        total_volume += item.quantity * item.unit_volume
                    if item.total_value:
                        total_value += item.total_value

                # Mettre à jour les totaux de l'expédition
                shipment.total_weight = total_weight
                shipment.total_volume = total_volume
                if not shipment.declared_value:
                    shipment.declared_value = total_value
                shipment.save()

                # Créer l'événement de suivi initial
                ShipmentTracking.objects.create(
                    shipment=shipment,
                    event_type='created',
                    event_date=timezone.now(),
                    event_description=f'Expédition {shipment.shipment_number} créée',
                    source='manual',
                    user=user
                )

                # Estimer le coût si possible
                estimated_cost = self._estimate_shipping_cost(shipment)
                if estimated_cost:
                    shipment.estimated_cost = estimated_cost
                    shipment.save()

                # Générer des recommandations IA si disponible
                if ai_service.is_available():
                    recommendations = self._generate_shipping_recommendations(shipment)
                    if recommendations:
                        self._apply_shipping_recommendations(shipment, recommendations)

                return {
                    'success': True,
                    'shipment': {
                        'id': str(shipment.id),
                        'shipment_number': shipment.shipment_number,
                        'status': shipment.status,
                        'estimated_cost': float(shipment.estimated_cost) if shipment.estimated_cost else None
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_shipment_number(self) -> str:
        """Génère un numéro d'expédition unique"""
        current_year = timezone.now().year
        last_shipment = Shipment.objects.filter(
            tenant=self.tenant,
            shipment_number__startswith=f"SHP{current_year}"
        ).order_by('-shipment_number').first()

        if last_shipment:
            last_number = int(last_shipment.shipment_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"SHP{current_year}{new_number:04d}"

    def _estimate_shipping_cost(self, shipment: Shipment) -> Optional[Decimal]:
        """Estime le coût d'expédition basé sur les tarifs du transporteur"""
        try:
            # Rechercher les tarifs applicables
            applicable_rates = CarrierRate.objects.filter(
                carrier=shipment.carrier,
                service_level=shipment.service_level,
                is_active=True,
                valid_from__lte=timezone.now().date()
            ).filter(
                Q(valid_to__isnull=True) | Q(valid_to__gte=timezone.now().date())
            )

            # Filtrer par poids et volume
            if shipment.total_weight > 0:
                applicable_rates = applicable_rates.filter(
                    Q(min_weight__isnull=True) | Q(min_weight__lte=shipment.total_weight),
                    Q(max_weight__isnull=True) | Q(max_weight__gte=shipment.total_weight)
                )

            if shipment.total_volume > 0:
                applicable_rates = applicable_rates.filter(
                    Q(min_volume__isnull=True) | Q(min_volume__lte=shipment.total_volume),
                    Q(max_volume__isnull=True) | Q(max_volume__gte=shipment.total_volume)
                )

            # Prendre le premier tarif applicable
            rate = applicable_rates.first()
            if not rate:
                return None

            # Calculer le coût
            if rate.rate_type == 'weight':
                cost = rate.base_rate + (shipment.total_weight * rate.rate_per_unit)
            elif rate.rate_type == 'volume':
                cost = rate.base_rate + (shipment.total_volume * rate.rate_per_unit)
            elif rate.rate_type == 'flat':
                cost = rate.base_rate
            elif rate.rate_type == 'percentage' and shipment.declared_value:
                cost = shipment.declared_value * (rate.rate_per_unit / 100)
            else:
                cost = rate.base_rate

            # Ajouter la surcharge carburant
            if rate.fuel_surcharge_percentage > 0:
                cost += cost * (rate.fuel_surcharge_percentage / 100)

            return cost

        except Exception as e:
            logger.error(f"Erreur lors de l'estimation du coût: {str(e)}")
            return None

    def _generate_shipping_recommendations(self, shipment: Shipment) -> Dict[str, Any]:
        """Génère des recommandations IA pour l'expédition"""
        try:
            # Analyser le contexte de l'expédition
            context = {
                'shipment': {
                    'type': shipment.shipment_type,
                    'weight': float(shipment.total_weight),
                    'volume': float(shipment.total_volume),
                    'value': float(shipment.declared_value) if shipment.declared_value else 0,
                    'priority': shipment.priority,
                    'destination_country': shipment.recipient_country
                },
                'carrier': {
                    'name': shipment.carrier.name,
                    'type': shipment.carrier.carrier_type,
                    'service_level': shipment.service_level
                }
            }

            prompt = f"""
            En tant qu'expert en logistique, analyse cette expédition et fournis des recommandations:

            Contexte: {context}

            Fournis des recommandations pour:
            1. Optimisation des coûts
            2. Amélioration des délais
            3. Réduction des risques
            4. Service client

            Réponds au format JSON avec les clés: cost_optimization, delivery_optimization, risk_mitigation, service_improvements
            """

            ai_response = ai_service.generate_response(prompt, "logistics", temperature=0.6)

            if ai_response.success:
                import json
                return json.loads(ai_response.content)

        except Exception as e:
            logger.error(f"Erreur lors de la génération de recommandations: {str(e)}")

        return {}

    def _apply_shipping_recommendations(self, shipment: Shipment, recommendations: Dict[str, Any]):
        """Applique les recommandations IA"""
        try:
            # Log des recommandations pour suivi
            logger.info(f"Recommandations expédition {shipment.shipment_number}: {recommendations}")

            # Dans un vrai système, on pourrait:
            # - Ajuster automatiquement le service level
            # - Proposer des transporteurs alternatifs
            # - Optimiser l'emballage
            # - Planifier des assurances supplémentaires

        except Exception as e:
            logger.error(f"Erreur lors de l'application des recommandations: {str(e)}")

    def update_shipment_tracking(self, shipment_id: str, tracking_data: Dict[str, Any], user: User = None) -> Dict[str, Any]:
        """Met à jour le suivi d'une expédition"""
        try:
            with transaction.atomic():
                shipment = Shipment.objects.get(id=shipment_id, tenant=self.tenant)

                # Créer l'événement de suivi
                tracking_event = ShipmentTracking.objects.create(
                    shipment=shipment,
                    event_type=tracking_data['event_type'],
                    event_date=tracking_data.get('event_date', timezone.now()),
                    event_description=tracking_data['event_description'],
                    location=tracking_data.get('location', ''),
                    city=tracking_data.get('city', ''),
                    country=tracking_data.get('country', ''),
                    carrier_event_code=tracking_data.get('carrier_event_code', ''),
                    next_expected_event=tracking_data.get('next_expected_event', ''),
                    estimated_delivery=tracking_data.get('estimated_delivery'),
                    source=tracking_data.get('source', 'manual'),
                    user=user
                )

                # Mettre à jour le statut de l'expédition
                status_mapping = {
                    'confirmed': 'confirmed',
                    'picked_up': 'picked_up',
                    'in_transit': 'in_transit',
                    'out_for_delivery': 'out_for_delivery',
                    'delivered': 'delivered',
                    'exception': 'failed',
                    'returned': 'returned',
                    'cancelled': 'cancelled'
                }

                if tracking_data['event_type'] in status_mapping:
                    shipment.status = status_mapping[tracking_data['event_type']]

                    # Mettre à jour les dates spécifiques
                    if tracking_data['event_type'] == 'picked_up':
                        shipment.actual_pickup_date = tracking_event.event_date
                    elif tracking_data['event_type'] == 'delivered':
                        shipment.actual_delivery_date = tracking_event.event_date

                    shipment.save()

                return {
                    'success': True,
                    'tracking_event': {
                        'id': str(tracking_event.id),
                        'event_type': tracking_event.event_type,
                        'event_date': tracking_event.event_date.isoformat()
                    }
                }

        except Shipment.DoesNotExist:
            return {
                'success': False,
                'error': 'Expédition non trouvée'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def create_route(self, route_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée un nouvel itinéraire"""
        try:
            with transaction.atomic():
                # Générer le numéro d'itinéraire
                route_number = self._generate_route_number()

                route = Route.objects.create(
                    tenant=self.tenant,
                    route_number=route_number,
                    route_name=route_data['route_name'],
                    planned_date=route_data['planned_date'],
                    start_time=route_data.get('start_time'),
                    end_time=route_data.get('end_time'),
                    carrier_id=route_data['carrier_id'],
                    vehicle_info=route_data.get('vehicle_info', ''),
                    driver_name=route_data.get('driver_name', ''),
                    driver_phone=route_data.get('driver_phone', ''),
                    max_weight=route_data.get('max_weight'),
                    max_volume=route_data.get('max_volume'),
                    estimated_cost=route_data.get('estimated_cost'),
                    planner=user,
                    notes=route_data.get('notes', '')
                )

                # Ajouter les arrêts
                for i, stop_data in enumerate(route_data.get('stops', []), 1):
                    stop = RouteStop.objects.create(
                        route=route,
                        stop_sequence=i,
                        stop_type=stop_data['stop_type'],
                        stop_name=stop_data['stop_name'],
                        address=stop_data['address'],
                        city=stop_data['city'],
                        postal_code=stop_data['postal_code'],
                        country=stop_data['country'],
                        contact_name=stop_data.get('contact_name', ''),
                        contact_phone=stop_data.get('contact_phone', ''),
                        contact_email=stop_data.get('contact_email', ''),
                        planned_arrival_time=stop_data.get('planned_arrival_time'),
                        planned_departure_time=stop_data.get('planned_departure_time'),
                        estimated_service_time=stop_data.get('estimated_service_time'),
                        special_instructions=stop_data.get('special_instructions', ''),
                        latitude=stop_data.get('latitude'),
                        longitude=stop_data.get('longitude')
                    )

                    # Associer les expéditions
                    if 'shipment_ids' in stop_data:
                        shipments = Shipment.objects.filter(
                            id__in=stop_data['shipment_ids'],
                            tenant=self.tenant
                        )
                        stop.shipments.set(shipments)

                # Optimiser l'itinéraire si demandé
                if route_data.get('optimize', False):
                    self._optimize_route(route)

                return {
                    'success': True,
                    'route': {
                        'id': str(route.id),
                        'route_number': route.route_number,
                        'stops_count': route.stops.count()
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_route_number(self) -> str:
        """Génère un numéro d'itinéraire unique"""
        current_year = timezone.now().year
        last_route = Route.objects.filter(
            tenant=self.tenant,
            route_number__startswith=f"RTE{current_year}"
        ).order_by('-route_number').first()

        if last_route:
            last_number = int(last_route.route_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"RTE{current_year}{new_number:04d}"

    def _optimize_route(self, route: Route):
        """Optimise un itinéraire (algorithme simple)"""
        try:
            # Dans un vrai système, on utiliserait des algorithmes d'optimisation
            # comme le TSP (Traveling Salesman Problem) ou des APIs de géolocalisation

            stops = list(route.stops.all().order_by('stop_sequence'))
            if len(stops) <= 2:
                return  # Pas besoin d'optimiser

            # Algorithme simple : garder le premier et dernier arrêt fixes
            # et optimiser les arrêts intermédiaires par distance
            if len(stops) > 2:
                first_stop = stops[0]
                last_stop = stops[-1]
                middle_stops = stops[1:-1]

                # Ici on pourrait implémenter un algorithme d'optimisation
                # Pour l'instant, on garde l'ordre existant

                # Recalculer les séquences
                for i, stop in enumerate([first_stop] + middle_stops + [last_stop], 1):
                    stop.stop_sequence = i
                    stop.save()

            logger.info(f"Itinéraire {route.route_number} optimisé")

        except Exception as e:
            logger.error(f"Erreur lors de l'optimisation de l'itinéraire: {str(e)}")

    def analyze_carrier_performance(self) -> Dict[str, Any]:
        """Analyse les performances des transporteurs"""

        # Période d'analyse (90 derniers jours)
        end_date = timezone.now()
        start_date = end_date - timedelta(days=90)

        carrier_performance = []

        for carrier in Carrier.objects.filter(tenant=self.tenant, is_active=True):
            # Expéditions du transporteur sur la période
            shipments = Shipment.objects.filter(
                tenant=self.tenant,
                carrier=carrier,
                created_at__gte=start_date,
                created_at__lte=end_date
            )

            total_shipments = shipments.count()
            if total_shipments == 0:
                continue

            # Expéditions livrées
            delivered_shipments = shipments.filter(status='delivered')
            delivered_count = delivered_shipments.count()

            # Taux de livraison réussie
            success_rate = (delivered_count / total_shipments) * 100

            # Délai moyen de livraison
            avg_delivery_time = 0
            on_time_deliveries = 0
            total_delay_hours = 0

            for shipment in delivered_shipments:
                if shipment.planned_delivery_date and shipment.actual_delivery_date:
                    planned = shipment.planned_delivery_date
                    actual = shipment.actual_delivery_date

                    delivery_time = (actual - shipment.created_at).total_seconds() / 3600
                    avg_delivery_time += delivery_time

                    delay = (actual - planned).total_seconds() / 3600
                    if delay <= 0:
                        on_time_deliveries += 1
                    else:
                        total_delay_hours += delay

            avg_delivery_time = avg_delivery_time / delivered_count if delivered_count > 0 else 0
            on_time_rate = (on_time_deliveries / delivered_count) * 100 if delivered_count > 0 else 0
            avg_delay = total_delay_hours / (delivered_count - on_time_deliveries) if delivered_count > on_time_deliveries else 0

            # Coût moyen
            avg_cost = shipments.filter(actual_cost__isnull=False).aggregate(
                avg=Avg('actual_cost')
            )['avg'] or Decimal('0.00')

            # Évaluation globale
            overall_score = (success_rate * 0.4) + (on_time_rate * 0.4) + (min(100, 100 - avg_delay) * 0.2)

            carrier_performance.append({
                'carrier_id': str(carrier.id),
                'carrier_name': carrier.name,
                'carrier_type': carrier.carrier_type,
                'total_shipments': total_shipments,
                'success_rate': round(success_rate, 1),
                'on_time_rate': round(on_time_rate, 1),
                'avg_delivery_time_hours': round(avg_delivery_time, 1),
                'avg_delay_hours': round(avg_delay, 1),
                'avg_cost': float(avg_cost),
                'overall_score': round(overall_score, 1),
                'reliability_rating': carrier.reliability_rating,
                'speed_rating': carrier.speed_rating,
                'cost_rating': carrier.cost_rating
            })

        # Trier par score global décroissant
        carrier_performance.sort(key=lambda x: x['overall_score'], reverse=True)

        return {
            'analysis_period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': 90
            },
            'carriers': carrier_performance,
            'summary': {
                'total_carriers': len(carrier_performance),
                'best_performer': carrier_performance[0] if carrier_performance else None,
                'avg_success_rate': sum(c['success_rate'] for c in carrier_performance) / len(carrier_performance) if carrier_performance else 0,
                'avg_on_time_rate': sum(c['on_time_rate'] for c in carrier_performance) / len(carrier_performance) if carrier_performance else 0
            }
        }

    def generate_logistics_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights IA sur la logistique"""
        insights = []

        try:
            if not ai_service.is_available():
                return insights

            # Récupérer les données d'analyse
            performance_data = self.analyze_carrier_performance()
            dashboard_data = self.get_logistics_dashboard()

            context = {
                'logistics_summary': dashboard_data,
                'carrier_performance': performance_data
            }

            prompt = f"""
            En tant qu'expert en logistique, analyse ces données et fournis des insights:

            Données: {context}

            Identifie:
            1. Les problèmes critiques de livraison
            2. Les opportunités d'optimisation des coûts
            3. Les recommandations d'amélioration des performances
            4. Les tendances et prévisions

            Pour chaque insight, fournis:
            - type: "critical", "warning", "opportunity", "info"
            - priority: "high", "medium", "low"
            - title: titre court
            - description: description détaillée
            - recommendation: action recommandée

            Réponds au format JSON avec une liste d'insights.
            """

            ai_response = ai_service.generate_response(prompt, "logistics", temperature=0.7)

            if ai_response.success:
                import json
                ai_insights = json.loads(ai_response.content)

                for insight in ai_insights:
                    insights.append({
                        'type': insight.get('type', 'info'),
                        'priority': insight.get('priority', 'medium'),
                        'title': insight.get('title', ''),
                        'description': insight.get('description', ''),
                        'recommendation': insight.get('recommendation', ''),
                        'generated_at': timezone.now().isoformat()
                    })

        except Exception as e:
            logger.error(f"Erreur lors de la génération d'insights logistique: {str(e)}")

        return insights