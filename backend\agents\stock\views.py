"""
Vues pour l'agent Stock
"""
import logging
from decimal import Decimal
from django.utils import timezone
from django.db import models
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema

from core.permissions import StockReadPermission, StockWritePermission
from .services import StockService
from .models import (
    Warehouse, Location, StockItem, StockLevel, StockMovement,
    StockReservation, StockInventory, StockInventoryLine
)
from .serializers import (
    WarehouseSerializer, LocationSerializer, StockItemSerializer, StockLevelSerializer,
    StockMovementSerializer, StockMovementCreateSerializer, StockReservationSerializer,
    StockReservationCreateSerializer, StockInventorySerializer, StockInventoryCreateSerializer,
    StockInventoryLineSerializer, StockDashboardSerializer, StockPerformanceSerializer,
    StockInsightSerializer, InventoryCountSerializer
)

logger = logging.getLogger('agents.stock')


@extend_schema(
    summary="Statut de l'agent Stock",
    description="Retourne le statut de l'agent Stock"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def stock_status(request):
    """Retourne le statut de l'agent Stock"""
    try:
        stock_service = StockService(request.user.tenant)

        return Response({
            'status': 'active',
            'agent': 'stock',
            'message': 'Agent Stock opérationnel',
            'capabilities': [
                'inventory_management',
                'stock_movement_tracking',
                'warehouse_optimization',
                'stock_level_monitoring',
                'abc_analysis',
                'demand_forecasting',
                'reorder_point_calculation'
            ]
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut Stock: {str(e)}")
        return Response({
            'status': 'error',
            'agent': 'stock',
            'message': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Dashboard de l'agent Stock",
    description="Retourne les données complètes du dashboard Stock"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, StockReadPermission])
def stock_dashboard(request):
    """Retourne les données du dashboard Stock"""
    try:
        stock_service = StockService(request.user.tenant)
        dashboard_data = stock_service.get_stock_dashboard()

        return Response(dashboard_data)
    except Exception as e:
        logger.error(f"Erreur lors de la génération du dashboard Stock: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Gestion des entrepôts
class WarehouseListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des entrepôts"""
    serializer_class = WarehouseSerializer
    permission_classes = [IsAuthenticated, StockReadPermission]

    def get_queryset(self):
        queryset = Warehouse.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        warehouse_type = self.request.query_params.get('type')
        if warehouse_type:
            queryset = queryset.filter(warehouse_type=warehouse_type)

        is_active = self.request.query_params.get('active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        manager = self.request.query_params.get('manager')
        if manager:
            queryset = queryset.filter(manager_id=manager)

        return queryset.order_by('name')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class WarehouseDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un entrepôt"""
    serializer_class = WarehouseSerializer
    permission_classes = [IsAuthenticated, StockWritePermission]

    def get_queryset(self):
        return Warehouse.objects.filter(tenant=self.request.user.tenant)


# Gestion des emplacements
class LocationListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des emplacements"""
    serializer_class = LocationSerializer
    permission_classes = [IsAuthenticated, StockReadPermission]

    def get_queryset(self):
        queryset = Location.objects.filter(warehouse__tenant=self.request.user.tenant)

        # Filtres
        warehouse_id = self.request.query_params.get('warehouse')
        if warehouse_id:
            queryset = queryset.filter(warehouse_id=warehouse_id)

        location_type = self.request.query_params.get('type')
        if location_type:
            queryset = queryset.filter(location_type=location_type)

        parent_id = self.request.query_params.get('parent')
        if parent_id:
            queryset = queryset.filter(parent_location_id=parent_id)
        elif self.request.query_params.get('root_only') == 'true':
            queryset = queryset.filter(parent_location__isnull=True)

        is_active = self.request.query_params.get('active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('warehouse__name', 'code')


class LocationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un emplacement"""
    serializer_class = LocationSerializer
    permission_classes = [IsAuthenticated, StockWritePermission]

    def get_queryset(self):
        return Location.objects.filter(warehouse__tenant=self.request.user.tenant)


# Gestion des articles
class StockItemListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des articles en stock"""
    serializer_class = StockItemSerializer
    permission_classes = [IsAuthenticated, StockReadPermission]

    def get_queryset(self):
        queryset = StockItem.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        item_type = self.request.query_params.get('type')
        if item_type:
            queryset = queryset.filter(item_type=item_type)

        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category__icontains=category)

        abc_class = self.request.query_params.get('abc_class')
        if abc_class:
            queryset = queryset.filter(abc_classification=abc_class)

        is_active = self.request.query_params.get('active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        # Recherche par SKU ou nom
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(sku__icontains=search) |
                models.Q(name__icontains=search) |
                models.Q(barcode__icontains=search)
            )

        return queryset.order_by('sku')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class StockItemDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un article"""
    serializer_class = StockItemSerializer
    permission_classes = [IsAuthenticated, StockWritePermission]

    def get_queryset(self):
        return StockItem.objects.filter(tenant=self.request.user.tenant)


# Gestion des niveaux de stock
class StockLevelListView(generics.ListAPIView):
    """Vue pour lister les niveaux de stock"""
    serializer_class = StockLevelSerializer
    permission_classes = [IsAuthenticated, StockReadPermission]

    def get_queryset(self):
        queryset = StockLevel.objects.filter(item__tenant=self.request.user.tenant)

        # Filtres
        item_id = self.request.query_params.get('item')
        if item_id:
            queryset = queryset.filter(item_id=item_id)

        warehouse_id = self.request.query_params.get('warehouse')
        if warehouse_id:
            queryset = queryset.filter(location__warehouse_id=warehouse_id)

        location_id = self.request.query_params.get('location')
        if location_id:
            queryset = queryset.filter(location_id=location_id)

        # Filtrer par stock disponible
        has_stock = self.request.query_params.get('has_stock')
        if has_stock == 'true':
            queryset = queryset.filter(quantity_on_hand__gt=0)
        elif has_stock == 'false':
            queryset = queryset.filter(quantity_on_hand=0)

        return queryset.order_by('item__sku', 'location__code')


# Gestion des mouvements de stock
class StockMovementListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des mouvements de stock"""
    permission_classes = [IsAuthenticated, StockReadPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return StockMovementCreateSerializer
        return StockMovementSerializer

    def get_queryset(self):
        queryset = StockMovement.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        item_id = self.request.query_params.get('item')
        if item_id:
            queryset = queryset.filter(item_id=item_id)

        movement_type = self.request.query_params.get('type')
        if movement_type:
            queryset = queryset.filter(movement_type=movement_type)

        location_id = self.request.query_params.get('location')
        if location_id:
            queryset = queryset.filter(
                models.Q(location_from_id=location_id) |
                models.Q(location_to_id=location_id)
            )

        # Filtres par date
        date_from = self.request.query_params.get('date_from')
        if date_from:
            queryset = queryset.filter(movement_date__date__gte=date_from)

        date_to = self.request.query_params.get('date_to')
        if date_to:
            queryset = queryset.filter(movement_date__date__lte=date_to)

        return queryset.order_by('-movement_date')


class StockMovementCreateView(APIView):
    """Vue pour créer des mouvements de stock avec logique métier"""
    permission_classes = [IsAuthenticated, StockWritePermission]

    @extend_schema(
        summary="Créer un mouvement de stock",
        description="Crée un nouveau mouvement de stock avec mise à jour automatique des niveaux",
        request=StockMovementCreateSerializer
    )
    def post(self, request):
        """Crée un nouveau mouvement de stock"""
        try:
            serializer = StockMovementCreateSerializer(data=request.data)
            if serializer.is_valid():
                stock_service = StockService(request.user.tenant)

                result = stock_service.create_stock_movement(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création du mouvement: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StockMovementDetailView(generics.RetrieveAPIView):
    """Vue pour consulter un mouvement de stock"""
    serializer_class = StockMovementSerializer
    permission_classes = [IsAuthenticated, StockReadPermission]

    def get_queryset(self):
        return StockMovement.objects.filter(tenant=self.request.user.tenant)


# Gestion des réservations
class StockReservationListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des réservations de stock"""
    permission_classes = [IsAuthenticated, StockReadPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return StockReservationCreateSerializer
        return StockReservationSerializer

    def get_queryset(self):
        queryset = StockReservation.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        item_id = self.request.query_params.get('item')
        if item_id:
            queryset = queryset.filter(item_id=item_id)

        location_id = self.request.query_params.get('location')
        if location_id:
            queryset = queryset.filter(location_id=location_id)

        reservation_type = self.request.query_params.get('type')
        if reservation_type:
            queryset = queryset.filter(reservation_type=reservation_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-reservation_date')


class StockReservationCreateView(APIView):
    """Vue pour créer des réservations de stock"""
    permission_classes = [IsAuthenticated, StockWritePermission]

    @extend_schema(
        summary="Créer une réservation de stock",
        description="Crée une nouvelle réservation avec vérification de disponibilité",
        request=StockReservationCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle réservation"""
        try:
            serializer = StockReservationCreateSerializer(data=request.data)
            if serializer.is_valid():
                stock_service = StockService(request.user.tenant)

                result = stock_service.create_stock_reservation(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de la réservation: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StockReservationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une réservation"""
    serializer_class = StockReservationSerializer
    permission_classes = [IsAuthenticated, StockWritePermission]

    def get_queryset(self):
        return StockReservation.objects.filter(tenant=self.request.user.tenant)


# Gestion des inventaires
class StockInventoryListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des inventaires"""
    permission_classes = [IsAuthenticated, StockReadPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return StockInventoryCreateSerializer
        return StockInventorySerializer

    def get_queryset(self):
        queryset = StockInventory.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        inventory_type = self.request.query_params.get('type')
        if inventory_type:
            queryset = queryset.filter(inventory_type=inventory_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        warehouse_id = self.request.query_params.get('warehouse')
        if warehouse_id:
            queryset = queryset.filter(warehouse_id=warehouse_id)

        supervisor_id = self.request.query_params.get('supervisor')
        if supervisor_id:
            queryset = queryset.filter(supervisor_id=supervisor_id)

        return queryset.order_by('-planned_date')


class StockInventoryCreateView(APIView):
    """Vue pour créer des inventaires avec génération automatique des lignes"""
    permission_classes = [IsAuthenticated, StockWritePermission]

    @extend_schema(
        summary="Créer un inventaire",
        description="Crée un nouvel inventaire avec génération automatique des lignes",
        request=StockInventoryCreateSerializer
    )
    def post(self, request):
        """Crée un nouvel inventaire"""
        try:
            serializer = StockInventoryCreateSerializer(data=request.data)
            if serializer.is_valid():
                stock_service = StockService(request.user.tenant)

                result = stock_service.create_inventory(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'inventaire: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StockInventoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un inventaire"""
    serializer_class = StockInventorySerializer
    permission_classes = [IsAuthenticated, StockWritePermission]

    def get_queryset(self):
        return StockInventory.objects.filter(tenant=self.request.user.tenant)


class InventoryCountView(APIView):
    """Vue pour traiter les comptages d'inventaire"""
    permission_classes = [IsAuthenticated, StockWritePermission]

    @extend_schema(
        summary="Traiter un comptage d'inventaire",
        description="Enregistre le comptage d'une ligne d'inventaire",
        request=InventoryCountSerializer
    )
    def post(self, request):
        """Traite un comptage d'inventaire"""
        try:
            serializer = InventoryCountSerializer(data=request.data)
            if serializer.is_valid():
                stock_service = StockService(request.user.tenant)

                result = stock_service.process_inventory_count(
                    serializer.validated_data['inventory_line_id'],
                    serializer.validated_data['counted_quantity'],
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_200_OK)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors du comptage: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class InventoryValidationView(APIView):
    """Vue pour valider un inventaire et appliquer les ajustements"""
    permission_classes = [IsAuthenticated, StockWritePermission]

    @extend_schema(
        summary="Valider un inventaire",
        description="Valide un inventaire et applique les ajustements de stock"
    )
    def post(self, request, inventory_id):
        """Valide un inventaire"""
        try:
            stock_service = StockService(request.user.tenant)

            result = stock_service.validate_inventory(inventory_id, request.user)

            if result['success']:
                return Response(result, status=status.HTTP_200_OK)
            else:
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la validation de l'inventaire: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Analytics et insights
@extend_schema(
    summary="Analyse des performances stock",
    description="Retourne l'analyse complète des performances du stock"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, StockReadPermission])
def stock_performance(request):
    """Retourne l'analyse des performances stock"""
    try:
        stock_service = StockService(request.user.tenant)
        performance_data = stock_service.analyze_stock_performance()

        return Response(performance_data)
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse des performances: {str(e)}")
        return Response(
            {'error': f'Erreur lors de l\'analyse: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Insights stock IA",
    description="Retourne les insights et recommandations stock basés sur l'IA"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, StockReadPermission])
def stock_insights(request):
    """Retourne les insights stock"""
    try:
        stock_service = StockService(request.user.tenant)
        insights = stock_service.generate_stock_insights()

        return Response({
            'insights': insights,
            'count': len(insights),
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'insights: {str(e)}")
        return Response(
            {'error': f'Erreur lors de la génération d\'insights: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Alertes de stock",
    description="Retourne les alertes de stock (ruptures, stock faible, etc.)"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, StockReadPermission])
def stock_alerts(request):
    """Retourne les alertes de stock"""
    try:
        stock_service = StockService(request.user.tenant)
        dashboard_data = stock_service.get_stock_dashboard()

        # Extraire les alertes du dashboard
        alerts = []

        # Alertes de stock faible
        low_stock_items = stock_service._get_low_stock_items()
        for item in low_stock_items:
            alerts.append({
                'alert_type': 'low_stock',
                'priority': 'high' if item['shortage'] > item['minimum_stock'] * 0.5 else 'medium',
                'item_id': item['item_id'],
                'item_sku': item['sku'],
                'item_name': item['name'],
                'message': f"Stock faible: {item['current_stock']} (min: {item['minimum_stock']})",
                'current_stock': item['current_stock'],
                'minimum_stock': item['minimum_stock'],
                'location_code': item['location'],
                'generated_at': timezone.now().isoformat()
            })

        # Alertes de rupture de stock
        out_of_stock_items = stock_service._get_out_of_stock_items()
        for item in out_of_stock_items:
            alerts.append({
                'alert_type': 'out_of_stock',
                'priority': 'high',
                'item_id': item['item_id'],
                'item_sku': item['sku'],
                'item_name': item['name'],
                'message': f"Rupture de stock depuis le {item['last_movement']}",
                'generated_at': timezone.now().isoformat()
            })

        return Response({
            'alerts': alerts,
            'count': len(alerts),
            'summary': {
                'low_stock': len(low_stock_items),
                'out_of_stock': len(out_of_stock_items),
                'expired_reservations': dashboard_data['alerts']['expired_reservations']
            }
        })
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'alertes: {str(e)}")
        return Response(
            {'error': f'Erreur lors de la génération d\'alertes: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Valorisation des stocks",
    description="Retourne la valorisation détaillée des stocks par article"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, StockReadPermission])
def stock_valuation(request):
    """Retourne la valorisation des stocks"""
    try:
        # Récupérer tous les niveaux de stock avec valeur
        stock_levels = StockLevel.objects.filter(
            item__tenant=request.user.tenant,
            quantity_on_hand__gt=0
        ).select_related('item', 'location')

        valuation_data = []
        total_value = Decimal('0.00')

        for level in stock_levels:
            item_data = {
                'item_id': str(level.item.id),
                'item_sku': level.item.sku,
                'item_name': level.item.name,
                'quantity_on_hand': float(level.quantity_on_hand),
                'average_cost': float(level.average_cost),
                'total_value': float(level.total_value),
                'valuation_method': level.item.valuation_method,
                'last_movement_date': level.last_movement_date.isoformat() if level.last_movement_date else None,
                'location_code': level.location.code,
                'warehouse_name': level.location.warehouse.name
            }
            valuation_data.append(item_data)
            total_value += level.total_value

        # Trier par valeur décroissante
        valuation_data.sort(key=lambda x: x['total_value'], reverse=True)

        return Response({
            'valuation': valuation_data,
            'total_items': len(valuation_data),
            'total_value': float(total_value),
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erreur lors de la valorisation: {str(e)}")
        return Response(
            {'error': f'Erreur lors de la valorisation: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Utilisation des emplacements",
    description="Retourne l'analyse d'utilisation des emplacements d'entrepôt"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, StockReadPermission])
def location_utilization(request):
    """Retourne l'utilisation des emplacements"""
    try:
        # Récupérer les emplacements avec leurs stocks
        locations = Location.objects.filter(
            warehouse__tenant=request.user.tenant,
            is_active=True
        ).prefetch_related('stock_levels__item')

        utilization_data = []

        for location in locations:
            # Calculer l'utilisation
            stock_levels = location.stock_levels.filter(quantity_on_hand__gt=0)

            used_capacity = Decimal('0.00')
            total_value = Decimal('0.00')
            items_count = stock_levels.count()

            for level in stock_levels:
                if level.item.volume:
                    used_capacity += level.quantity_on_hand * level.item.volume
                total_value += level.total_value

            utilization_percentage = 0
            if location.capacity and location.capacity > 0:
                utilization_percentage = float(used_capacity / location.capacity * 100)

            location_data = {
                'location_id': str(location.id),
                'location_code': location.code,
                'warehouse_name': location.warehouse.name,
                'capacity': float(location.capacity) if location.capacity else None,
                'used_capacity': float(used_capacity),
                'utilization_percentage': round(utilization_percentage, 2),
                'items_count': items_count,
                'total_value': float(total_value)
            }
            utilization_data.append(location_data)

        # Trier par taux d'utilisation décroissant
        utilization_data.sort(key=lambda x: x['utilization_percentage'], reverse=True)

        return Response({
            'locations': utilization_data,
            'total_locations': len(utilization_data),
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse d'utilisation: {str(e)}")
        return Response(
            {'error': f'Erreur lors de l\'analyse: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )