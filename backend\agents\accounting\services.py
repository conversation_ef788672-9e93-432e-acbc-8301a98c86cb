"""
Services pour l'Agent Accounting
Logique métier pour la comptabilité générale et analytique
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg, F
from django.db import transaction
from decimal import Decimal

from core.models import Tenant, User
from agents.models import Agent
from agents.ai_service import ai_service
from .models import (
    ChartOfAccounts, FiscalYear, Journal, AccountingEntry, AccountingEntryLine,
    TaxCode, Budget, BudgetLine, FinancialReport, ReconciliationRule
)

logger = logging.getLogger('agents.accounting')


class AccountingService:
    """
    Service principal pour l'Agent Accounting
    Gère toutes les opérations comptables
    """

    def __init__(self, tenant: Tenant):
        self.tenant = tenant
        self.accounting_agent = self._get_or_create_accounting_agent()

    def _get_or_create_accounting_agent(self) -> Agent:
        """Récupère ou crée l'agent Accounting pour le tenant"""
        agent, created = Agent.objects.get_or_create(
            tenant=self.tenant,
            agent_type='accounting',
            defaults={
                'name': 'Agent Accounting',
                'description': 'Comptabilité générale et analytique',
                'ai_enabled': True,
                'ai_model': 'gpt-4',
                'capabilities': [
                    'chart_of_accounts_management',
                    'journal_entries',
                    'financial_reporting',
                    'budget_management',
                    'tax_management',
                    'reconciliation',
                    'analytics',
                    'compliance_monitoring'
                ]
            }
        )
        if created:
            logger.info(f"Agent Accounting créé pour le tenant {self.tenant.name}")
        return agent

    def get_accounting_dashboard(self) -> Dict[str, Any]:
        """Retourne les données du dashboard Accounting"""

        # Exercice comptable en cours
        current_fiscal_year = FiscalYear.objects.filter(
            tenant=self.tenant,
            is_current=True
        ).first()

        if not current_fiscal_year:
            # Créer un exercice par défaut si aucun n'existe
            current_fiscal_year = self._create_default_fiscal_year()

        # Comptes comptables
        accounts = ChartOfAccounts.objects.filter(tenant=self.tenant)

        # Écritures comptables
        entries = AccountingEntry.objects.filter(tenant=self.tenant)

        # Écritures de l'exercice en cours
        current_year_entries = entries.filter(fiscal_year=current_fiscal_year)

        # Journaux
        journals = Journal.objects.filter(tenant=self.tenant, is_active=True)

        # Budgets
        budgets = Budget.objects.filter(tenant=self.tenant)
        current_year_budgets = budgets.filter(fiscal_year=current_fiscal_year)

        # Calculs financiers
        financial_summary = self._calculate_financial_summary(current_fiscal_year)

        # Écritures en attente de validation
        pending_entries = current_year_entries.filter(status='draft').count()

        # Comptes non lettrés
        unreconciled_accounts = self._get_unreconciled_accounts_count()

        return {
            'tenant': self.tenant.name,
            'timestamp': timezone.now().isoformat(),
            'fiscal_year': {
                'name': current_fiscal_year.name,
                'start_date': current_fiscal_year.start_date.isoformat(),
                'end_date': current_fiscal_year.end_date.isoformat(),
                'status': current_fiscal_year.status,
                'is_current': current_fiscal_year.is_current
            },
            'accounts': {
                'total': accounts.count(),
                'active': accounts.filter(is_active=True).count(),
                'by_type': {
                    'asset': accounts.filter(account_type='asset').count(),
                    'liability': accounts.filter(account_type='liability').count(),
                    'equity': accounts.filter(account_type='equity').count(),
                    'revenue': accounts.filter(account_type='revenue').count(),
                    'expense': accounts.filter(account_type='expense').count(),
                }
            },
            'entries': {
                'total': entries.count(),
                'current_year': current_year_entries.count(),
                'posted': current_year_entries.filter(status='posted').count(),
                'validated': current_year_entries.filter(status='validated').count(),
                'pending': pending_entries
            },
            'journals': {
                'total': journals.count(),
                'by_type': {
                    journal_type: journals.filter(journal_type=journal_type).count()
                    for journal_type, _ in Journal.JOURNAL_TYPES
                }
            },
            'budgets': {
                'total': budgets.count(),
                'current_year': current_year_budgets.count(),
                'approved': current_year_budgets.filter(status='approved').count(),
                'active': current_year_budgets.filter(status='active').count()
            },
            'financial_summary': financial_summary,
            'alerts': {
                'pending_entries': pending_entries,
                'unreconciled_accounts': unreconciled_accounts,
                'budget_variances': self._get_budget_variances_count(),
                'tax_deadlines': self._get_upcoming_tax_deadlines()
            },
            'recent_activities': self._get_recent_activities()
        }

    def _create_default_fiscal_year(self) -> FiscalYear:
        """Crée un exercice comptable par défaut"""
        current_year = timezone.now().year
        start_date = timezone.datetime(current_year, 1, 1).date()
        end_date = timezone.datetime(current_year, 12, 31).date()

        fiscal_year = FiscalYear.objects.create(
            tenant=self.tenant,
            name=f"Exercice {current_year}",
            start_date=start_date,
            end_date=end_date,
            is_current=True
        )

        logger.info(f"Exercice comptable par défaut créé: {fiscal_year.name}")
        return fiscal_year

    def _calculate_financial_summary(self, fiscal_year: FiscalYear) -> Dict[str, Any]:
        """Calcule le résumé financier pour un exercice"""

        # Récupérer les soldes des comptes
        revenue_accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            account_type='revenue',
            is_active=True
        )

        expense_accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            account_type='expense',
            is_active=True
        )

        asset_accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            account_type='asset',
            is_active=True
        )

        liability_accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            account_type='liability',
            is_active=True
        )

        equity_accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            account_type='equity',
            is_active=True
        )

        # Calculer les totaux
        total_revenue = sum(account.get_balance(fiscal_year.end_date) for account in revenue_accounts)
        total_expenses = sum(account.get_balance(fiscal_year.end_date) for account in expense_accounts)
        total_assets = sum(account.get_balance(fiscal_year.end_date) for account in asset_accounts)
        total_liabilities = sum(account.get_balance(fiscal_year.end_date) for account in liability_accounts)
        total_equity = sum(account.get_balance(fiscal_year.end_date) for account in equity_accounts)

        # Résultat net
        net_income = total_revenue - total_expenses

        # Ratios financiers
        debt_to_equity = (total_liabilities / total_equity) if total_equity != 0 else 0
        profit_margin = (net_income / total_revenue) if total_revenue != 0 else 0

        return {
            'revenue': float(total_revenue),
            'expenses': float(total_expenses),
            'net_income': float(net_income),
            'assets': float(total_assets),
            'liabilities': float(total_liabilities),
            'equity': float(total_equity),
            'ratios': {
                'debt_to_equity': float(debt_to_equity),
                'profit_margin': float(profit_margin * 100)  # En pourcentage
            }
        }

    def _get_unreconciled_accounts_count(self) -> int:
        """Compte les comptes avec des écritures non lettrées"""
        return ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            is_reconcilable=True,
            journal_entries__is_reconciled=False
        ).distinct().count()

    def _get_budget_variances_count(self) -> int:
        """Compte les écarts budgétaires significatifs"""
        # Logique simplifiée - dans un vrai système, on calculerait les écarts réels
        current_fiscal_year = FiscalYear.objects.filter(
            tenant=self.tenant,
            is_current=True
        ).first()

        if not current_fiscal_year:
            return 0

        # Compter les budgets avec des écarts > 10%
        variances = 0
        for budget in Budget.objects.filter(
            tenant=self.tenant,
            fiscal_year=current_fiscal_year,
            status='active'
        ):
            # Ici on pourrait calculer les écarts réels vs budgétés
            # Pour l'instant, on simule
            variances += 1 if budget.total_expense_budget > 0 else 0

        return min(variances, 5)  # Limiter à 5 pour l'exemple

    def _get_upcoming_tax_deadlines(self) -> int:
        """Compte les échéances fiscales à venir (30 prochains jours)"""
        # Dans un vrai système, on aurait une table des échéances fiscales
        # Pour l'instant, on simule basé sur la date
        current_date = timezone.now().date()

        # Échéances trimestrielles simulées
        quarterly_deadlines = [
            (3, 31),   # 31 mars
            (6, 30),   # 30 juin
            (9, 30),   # 30 septembre
            (12, 31),  # 31 décembre
        ]

        upcoming = 0
        for month, day in quarterly_deadlines:
            deadline = timezone.datetime(current_date.year, month, day).date()
            if current_date <= deadline <= current_date + timedelta(days=30):
                upcoming += 1

        return upcoming

    def _get_recent_activities(self) -> List[Dict[str, Any]]:
        """Récupère les activités comptables récentes"""
        activities = []

        # Écritures récentes
        recent_entries = AccountingEntry.objects.filter(
            tenant=self.tenant,
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:10]

        for entry in recent_entries:
            activities.append({
                'type': f'entry_{entry.status}',
                'description': f"Écriture {entry.entry_number}: {entry.description}",
                'date': entry.created_at.isoformat(),
                'amount': float(entry.total_debit),
                'journal': entry.journal.name,
                'user': entry.created_by.get_full_name()
            })

        return activities

    def create_journal_entry(self, entry_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée une nouvelle écriture comptable"""
        try:
            with transaction.atomic():
                # Récupérer l'exercice comptable
                fiscal_year = FiscalYear.objects.get(
                    tenant=self.tenant,
                    is_current=True
                )

                # Récupérer le journal
                journal = Journal.objects.get(
                    id=entry_data['journal_id'],
                    tenant=self.tenant
                )

                # Créer l'écriture
                entry = AccountingEntry.objects.create(
                    tenant=self.tenant,
                    journal=journal,
                    fiscal_year=fiscal_year,
                    entry_date=entry_data['entry_date'],
                    value_date=entry_data.get('value_date'),
                    due_date=entry_data.get('due_date'),
                    description=entry_data['description'],
                    notes=entry_data.get('notes', ''),
                    entry_type=entry_data.get('entry_type', 'manual'),
                    reference=entry_data.get('reference', ''),
                    created_by=user
                )

                # Ajouter les lignes d'écriture
                total_debit = Decimal('0.00')
                total_credit = Decimal('0.00')

                for line_data in entry_data['lines']:
                    account = ChartOfAccounts.objects.get(
                        id=line_data['account_id'],
                        tenant=self.tenant
                    )

                    line = AccountingEntryLine.objects.create(
                        entry=entry,
                        account=account,
                        debit_amount=line_data.get('debit_amount', Decimal('0.00')),
                        credit_amount=line_data.get('credit_amount', Decimal('0.00')),
                        description=line_data['description'],
                        partner_type=line_data.get('partner_type', ''),
                        partner_id=line_data.get('partner_id', ''),
                        partner_name=line_data.get('partner_name', ''),
                        analytic_account=line_data.get('analytic_account', ''),
                        cost_center=line_data.get('cost_center', ''),
                        project_code=line_data.get('project_code', ''),
                        tax_code=line_data.get('tax_code', ''),
                        tax_amount=line_data.get('tax_amount', Decimal('0.00'))
                    )

                    total_debit += line.debit_amount
                    total_credit += line.credit_amount

                # Vérifier l'équilibre
                if total_debit != total_credit:
                    raise ValueError(f"Écriture non équilibrée: Débit {total_debit} ≠ Crédit {total_credit}")

                # Mettre à jour les totaux
                entry.total_debit = total_debit
                entry.total_credit = total_credit
                entry.save()

                # Comptabiliser automatiquement si demandé
                if entry_data.get('auto_post', False):
                    entry.post(user)

                # Générer des recommandations IA si disponible
                if ai_service.is_available():
                    recommendations = self._generate_entry_recommendations(entry)
                    if recommendations:
                        self._apply_entry_recommendations(entry, recommendations)

                return {
                    'success': True,
                    'entry': {
                        'id': str(entry.id),
                        'entry_number': entry.entry_number,
                        'status': entry.status,
                        'total_debit': float(entry.total_debit),
                        'total_credit': float(entry.total_credit)
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_entry_recommendations(self, entry: AccountingEntry) -> Dict[str, Any]:
        """Génère des recommandations IA pour l'écriture"""
        try:
            # Analyser le contexte de l'écriture
            context = {
                'entry': {
                    'description': entry.description,
                    'amount': float(entry.total_debit),
                    'journal_type': entry.journal.journal_type,
                    'entry_type': entry.entry_type,
                    'accounts_used': [
                        {
                            'code': line.account.account_code,
                            'name': line.account.account_name,
                            'type': line.account.account_type,
                            'debit': float(line.debit_amount),
                            'credit': float(line.credit_amount)
                        }
                        for line in entry.lines.all()
                    ]
                }
            }

            prompt = f"""
            En tant qu'expert comptable, analyse cette écriture et fournis des recommandations:

            Contexte: {context}

            Fournis des recommandations pour:
            1. Vérification de la cohérence comptable
            2. Optimisation fiscale
            3. Amélioration de la classification
            4. Contrôles de conformité

            Réponds au format JSON avec les clés: consistency_check, tax_optimization, classification_improvement, compliance_controls
            """

            ai_response = ai_service.generate_response(prompt, "accounting", temperature=0.6)

            if ai_response.success:
                import json
                return json.loads(ai_response.content)

        except Exception as e:
            logger.error(f"Erreur lors de la génération de recommandations: {str(e)}")

        return {}

    def _apply_entry_recommendations(self, entry: AccountingEntry, recommendations: Dict[str, Any]):
        """Applique les recommandations IA"""
        try:
            # Log des recommandations pour suivi
            logger.info(f"Recommandations écriture {entry.entry_number}: {recommendations}")

            # Dans un vrai système, on pourrait:
            # - Ajouter des contrôles automatiques
            # - Proposer des comptes alternatifs
            # - Générer des alertes de conformité
            # - Optimiser la ventilation analytique

        except Exception as e:
            logger.error(f"Erreur lors de l'application des recommandations: {str(e)}")

    def generate_financial_report(self, report_type: str, start_date: str, end_date: str, user: User) -> Dict[str, Any]:
        """Génère un rapport financier"""
        try:
            with transaction.atomic():
                # Créer l'enregistrement du rapport
                report = FinancialReport.objects.create(
                    tenant=self.tenant,
                    name=f"{report_type.replace('_', ' ').title()} - {start_date} à {end_date}",
                    report_type=report_type,
                    start_date=start_date,
                    end_date=end_date,
                    generated_by=user
                )

                # Générer les données selon le type de rapport
                if report_type == 'balance_sheet':
                    report_data = self._generate_balance_sheet(start_date, end_date)
                elif report_type == 'income_statement':
                    report_data = self._generate_income_statement(start_date, end_date)
                elif report_type == 'trial_balance':
                    report_data = self._generate_trial_balance(start_date, end_date)
                elif report_type == 'budget_vs_actual':
                    report_data = self._generate_budget_vs_actual(start_date, end_date)
                else:
                    report_data = {'error': f'Type de rapport non supporté: {report_type}'}

                # Sauvegarder les données
                report.report_data = report_data
                report.status = 'completed'
                report.save()

                return {
                    'success': True,
                    'report': {
                        'id': str(report.id),
                        'name': report.name,
                        'type': report.report_type,
                        'status': report.status,
                        'data': report_data
                    }
                }

        except Exception as e:
            logger.error(f"Erreur lors de la génération du rapport: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_balance_sheet(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Génère un bilan"""
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

        # Actifs
        asset_accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            account_type='asset',
            is_active=True
        )

        current_assets = []
        fixed_assets = []

        for account in asset_accounts:
            balance = account.get_balance(end_date_obj)
            if balance != 0:
                account_data = {
                    'code': account.account_code,
                    'name': account.account_name,
                    'balance': float(balance)
                }

                if account.account_category == 'current_assets':
                    current_assets.append(account_data)
                else:
                    fixed_assets.append(account_data)

        # Passifs
        liability_accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            account_type='liability',
            is_active=True
        )

        current_liabilities = []
        long_term_liabilities = []

        for account in liability_accounts:
            balance = account.get_balance(end_date_obj)
            if balance != 0:
                account_data = {
                    'code': account.account_code,
                    'name': account.account_name,
                    'balance': float(balance)
                }

                if account.account_category == 'current_liabilities':
                    current_liabilities.append(account_data)
                else:
                    long_term_liabilities.append(account_data)

        # Capitaux propres
        equity_accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            account_type='equity',
            is_active=True
        )

        equity = []
        for account in equity_accounts:
            balance = account.get_balance(end_date_obj)
            if balance != 0:
                equity.append({
                    'code': account.account_code,
                    'name': account.account_name,
                    'balance': float(balance)
                })

        # Calculer les totaux
        total_current_assets = sum(item['balance'] for item in current_assets)
        total_fixed_assets = sum(item['balance'] for item in fixed_assets)
        total_assets = total_current_assets + total_fixed_assets

        total_current_liabilities = sum(item['balance'] for item in current_liabilities)
        total_long_term_liabilities = sum(item['balance'] for item in long_term_liabilities)
        total_liabilities = total_current_liabilities + total_long_term_liabilities

        total_equity = sum(item['balance'] for item in equity)

        return {
            'assets': {
                'current_assets': {
                    'items': current_assets,
                    'total': total_current_assets
                },
                'fixed_assets': {
                    'items': fixed_assets,
                    'total': total_fixed_assets
                },
                'total': total_assets
            },
            'liabilities': {
                'current_liabilities': {
                    'items': current_liabilities,
                    'total': total_current_liabilities
                },
                'long_term_liabilities': {
                    'items': long_term_liabilities,
                    'total': total_long_term_liabilities
                },
                'total': total_liabilities
            },
            'equity': {
                'items': equity,
                'total': total_equity
            },
            'total_liabilities_and_equity': total_liabilities + total_equity,
            'balanced': abs(total_assets - (total_liabilities + total_equity)) < 0.01
        }

    def _generate_income_statement(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Génère un compte de résultat"""
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

        # Produits
        revenue_accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            account_type='revenue',
            is_active=True
        )

        operating_revenue = []
        other_revenue = []

        for account in revenue_accounts:
            # Calculer le solde pour la période
            entries = AccountingEntryLine.objects.filter(
                account=account,
                entry__entry_date__gte=start_date_obj,
                entry__entry_date__lte=end_date_obj,
                entry__status__in=['posted', 'validated']
            )

            period_balance = entries.aggregate(
                total=Sum('credit_amount') - Sum('debit_amount')
            )['total'] or Decimal('0.00')

            if period_balance != 0:
                account_data = {
                    'code': account.account_code,
                    'name': account.account_name,
                    'balance': float(period_balance)
                }

                if account.account_category == 'operating_revenue':
                    operating_revenue.append(account_data)
                else:
                    other_revenue.append(account_data)

        # Charges
        expense_accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            account_type='expense',
            is_active=True
        )

        cost_of_sales = []
        operating_expenses = []
        financial_expenses = []

        for account in expense_accounts:
            entries = AccountingEntryLine.objects.filter(
                account=account,
                entry__entry_date__gte=start_date_obj,
                entry__entry_date__lte=end_date_obj,
                entry__status__in=['posted', 'validated']
            )

            period_balance = entries.aggregate(
                total=Sum('debit_amount') - Sum('credit_amount')
            )['total'] or Decimal('0.00')

            if period_balance != 0:
                account_data = {
                    'code': account.account_code,
                    'name': account.account_name,
                    'balance': float(period_balance)
                }

                if account.account_category == 'cost_of_sales':
                    cost_of_sales.append(account_data)
                elif account.account_category == 'financial_expenses':
                    financial_expenses.append(account_data)
                else:
                    operating_expenses.append(account_data)

        # Calculer les totaux
        total_operating_revenue = sum(item['balance'] for item in operating_revenue)
        total_other_revenue = sum(item['balance'] for item in other_revenue)
        total_revenue = total_operating_revenue + total_other_revenue

        total_cost_of_sales = sum(item['balance'] for item in cost_of_sales)
        total_operating_expenses = sum(item['balance'] for item in operating_expenses)
        total_financial_expenses = sum(item['balance'] for item in financial_expenses)
        total_expenses = total_cost_of_sales + total_operating_expenses + total_financial_expenses

        gross_profit = total_operating_revenue - total_cost_of_sales
        operating_profit = gross_profit - total_operating_expenses
        net_income = total_revenue - total_expenses

        return {
            'revenue': {
                'operating_revenue': {
                    'items': operating_revenue,
                    'total': total_operating_revenue
                },
                'other_revenue': {
                    'items': other_revenue,
                    'total': total_other_revenue
                },
                'total': total_revenue
            },
            'expenses': {
                'cost_of_sales': {
                    'items': cost_of_sales,
                    'total': total_cost_of_sales
                },
                'operating_expenses': {
                    'items': operating_expenses,
                    'total': total_operating_expenses
                },
                'financial_expenses': {
                    'items': financial_expenses,
                    'total': total_financial_expenses
                },
                'total': total_expenses
            },
            'profitability': {
                'gross_profit': gross_profit,
                'operating_profit': operating_profit,
                'net_income': net_income,
                'gross_margin': (gross_profit / total_operating_revenue * 100) if total_operating_revenue > 0 else 0,
                'operating_margin': (operating_profit / total_revenue * 100) if total_revenue > 0 else 0,
                'net_margin': (net_income / total_revenue * 100) if total_revenue > 0 else 0
            }
        }

    def _generate_trial_balance(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Génère une balance générale"""
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

        accounts = ChartOfAccounts.objects.filter(
            tenant=self.tenant,
            is_active=True
        ).order_by('account_code')

        trial_balance = []
        total_debit = Decimal('0.00')
        total_credit = Decimal('0.00')

        for account in accounts:
            balance = account.get_balance(end_date_obj)

            if balance != 0:
                if account.account_type in ['asset', 'expense']:
                    # Comptes de débit
                    debit_balance = balance if balance > 0 else Decimal('0.00')
                    credit_balance = abs(balance) if balance < 0 else Decimal('0.00')
                else:
                    # Comptes de crédit
                    credit_balance = balance if balance > 0 else Decimal('0.00')
                    debit_balance = abs(balance) if balance < 0 else Decimal('0.00')

                trial_balance.append({
                    'code': account.account_code,
                    'name': account.account_name,
                    'type': account.account_type,
                    'category': account.account_category,
                    'debit_balance': float(debit_balance),
                    'credit_balance': float(credit_balance)
                })

                total_debit += debit_balance
                total_credit += credit_balance

        return {
            'accounts': trial_balance,
            'totals': {
                'total_debit': float(total_debit),
                'total_credit': float(total_credit),
                'balanced': abs(total_debit - total_credit) < 0.01
            },
            'period': {
                'start_date': start_date,
                'end_date': end_date
            }
        }

    def _generate_budget_vs_actual(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Génère un rapport budget vs réalisé"""
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

        # Trouver les budgets pour la période
        budgets = Budget.objects.filter(
            tenant=self.tenant,
            start_date__lte=end_date_obj,
            end_date__gte=start_date_obj,
            status__in=['approved', 'active']
        )

        budget_comparison = []

        for budget in budgets:
            budget_lines = budget.lines.all()

            for line in budget_lines:
                # Calculer le réalisé pour la période
                actual_entries = AccountingEntryLine.objects.filter(
                    account=line.account,
                    entry__entry_date__gte=start_date_obj,
                    entry__entry_date__lte=end_date_obj,
                    entry__status__in=['posted', 'validated']
                )

                if line.account.account_type == 'revenue':
                    actual_amount = actual_entries.aggregate(
                        total=Sum('credit_amount') - Sum('debit_amount')
                    )['total'] or Decimal('0.00')
                else:
                    actual_amount = actual_entries.aggregate(
                        total=Sum('debit_amount') - Sum('credit_amount')
                    )['total'] or Decimal('0.00')

                variance = actual_amount - line.budgeted_amount
                variance_percent = (variance / line.budgeted_amount * 100) if line.budgeted_amount != 0 else 0

                budget_comparison.append({
                    'budget_name': budget.name,
                    'account_code': line.account.account_code,
                    'account_name': line.account.account_name,
                    'account_type': line.account.account_type,
                    'budgeted_amount': float(line.budgeted_amount),
                    'actual_amount': float(actual_amount),
                    'variance': float(variance),
                    'variance_percent': float(variance_percent),
                    'cost_center': line.cost_center,
                    'project_code': line.project_code
                })

        # Calculer les totaux
        total_budgeted = sum(item['budgeted_amount'] for item in budget_comparison)
        total_actual = sum(item['actual_amount'] for item in budget_comparison)
        total_variance = total_actual - total_budgeted
        total_variance_percent = (total_variance / total_budgeted * 100) if total_budgeted != 0 else 0

        return {
            'comparisons': budget_comparison,
            'summary': {
                'total_budgeted': total_budgeted,
                'total_actual': total_actual,
                'total_variance': total_variance,
                'total_variance_percent': total_variance_percent
            },
            'period': {
                'start_date': start_date,
                'end_date': end_date
            }
        }

    def create_budget(self, budget_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée un nouveau budget"""
        try:
            with transaction.atomic():
                # Récupérer l'exercice comptable
                fiscal_year = FiscalYear.objects.get(
                    id=budget_data['fiscal_year_id'],
                    tenant=self.tenant
                )

                # Créer le budget
                budget = Budget.objects.create(
                    tenant=self.tenant,
                    name=budget_data['name'],
                    code=budget_data['code'],
                    budget_type=budget_data['budget_type'],
                    fiscal_year=fiscal_year,
                    start_date=budget_data['start_date'],
                    end_date=budget_data['end_date'],
                    budget_manager=user,
                    description=budget_data.get('description', ''),
                    notes=budget_data.get('notes', '')
                )

                # Ajouter les lignes de budget
                for line_data in budget_data.get('lines', []):
                    account = ChartOfAccounts.objects.get(
                        id=line_data['account_id'],
                        tenant=self.tenant
                    )

                    BudgetLine.objects.create(
                        budget=budget,
                        account=account,
                        budgeted_amount=line_data['budgeted_amount'],
                        cost_center=line_data.get('cost_center', ''),
                        project_code=line_data.get('project_code', ''),
                        description=line_data.get('description', ''),
                        notes=line_data.get('notes', ''),
                        # Répartition mensuelle optionnelle
                        january=line_data.get('january', Decimal('0.00')),
                        february=line_data.get('february', Decimal('0.00')),
                        march=line_data.get('march', Decimal('0.00')),
                        april=line_data.get('april', Decimal('0.00')),
                        may=line_data.get('may', Decimal('0.00')),
                        june=line_data.get('june', Decimal('0.00')),
                        july=line_data.get('july', Decimal('0.00')),
                        august=line_data.get('august', Decimal('0.00')),
                        september=line_data.get('september', Decimal('0.00')),
                        october=line_data.get('october', Decimal('0.00')),
                        november=line_data.get('november', Decimal('0.00')),
                        december=line_data.get('december', Decimal('0.00'))
                    )

                # Calculer les totaux
                budget.calculate_totals()

                return {
                    'success': True,
                    'budget': {
                        'id': str(budget.id),
                        'name': budget.name,
                        'code': budget.code,
                        'status': budget.status,
                        'total_revenue_budget': float(budget.total_revenue_budget),
                        'total_expense_budget': float(budget.total_expense_budget),
                        'net_budget': float(budget.net_budget)
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def generate_accounting_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights IA sur la comptabilité"""
        insights = []

        try:
            if not ai_service.is_available():
                return insights

            # Récupérer les données d'analyse
            dashboard_data = self.get_accounting_dashboard()

            # Analyser les tendances récentes
            recent_entries = AccountingEntry.objects.filter(
                tenant=self.tenant,
                created_at__gte=timezone.now() - timedelta(days=30)
            ).count()

            context = {
                'accounting_summary': dashboard_data,
                'recent_activity': {
                    'entries_last_30_days': recent_entries
                }
            }

            prompt = f"""
            En tant qu'expert comptable, analyse ces données et fournis des insights:

            Données: {context}

            Identifie:
            1. Les problèmes de conformité comptable
            2. Les opportunités d'optimisation fiscale
            3. Les risques financiers potentiels
            4. Les recommandations d'amélioration des processus

            Pour chaque insight, fournis:
            - type: "critical", "warning", "opportunity", "info"
            - priority: "high", "medium", "low"
            - title: titre court
            - description: description détaillée
            - recommendation: action recommandée

            Réponds au format JSON avec une liste d'insights.
            """

            ai_response = ai_service.generate_response(prompt, "accounting", temperature=0.7)

            if ai_response.success:
                import json
                ai_insights = json.loads(ai_response.content)

                for insight in ai_insights:
                    insights.append({
                        'type': insight.get('type', 'info'),
                        'priority': insight.get('priority', 'medium'),
                        'title': insight.get('title', ''),
                        'description': insight.get('description', ''),
                        'recommendation': insight.get('recommendation', ''),
                        'generated_at': timezone.now().isoformat()
                    })

        except Exception as e:
            logger.error(f"Erreur lors de la génération d'insights comptables: {str(e)}")

        return insights

    def reconcile_accounts(self, account_ids: List[str], reconciliation_ref: str, user: User) -> Dict[str, Any]:
        """Effectue le lettrage de comptes"""
        try:
            with transaction.atomic():
                # Récupérer les lignes d'écriture à lettrer
                lines = AccountingEntryLine.objects.filter(
                    id__in=account_ids,
                    entry__tenant=self.tenant,
                    is_reconciled=False
                )

                if not lines.exists():
                    return {
                        'success': False,
                        'error': 'Aucune ligne d\'écriture trouvée'
                    }

                # Vérifier l'équilibre
                total_debit = sum(line.debit_amount for line in lines)
                total_credit = sum(line.credit_amount for line in lines)

                if abs(total_debit - total_credit) > Decimal('0.01'):
                    return {
                        'success': False,
                        'error': f'Lettrage non équilibré: Débit {total_debit} ≠ Crédit {total_credit}'
                    }

                # Effectuer le lettrage
                reconciled_count = 0
                for line in lines:
                    line.reconciliation_ref = reconciliation_ref
                    line.is_reconciled = True
                    line.reconciled_date = timezone.now().date()
                    line.save()
                    reconciled_count += 1

                return {
                    'success': True,
                    'reconciled_lines': reconciled_count,
                    'reconciliation_ref': reconciliation_ref
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }