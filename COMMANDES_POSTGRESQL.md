# 🗄️ **COMMANDES POSTGRESQL ESSENTIELLES**

## **🔧 ACCÈS À LA BASE DE DONNÉES**

### **🐳 Connexion via Docker**
```bash
# Accès interactif à PostgreSQL
docker exec -it erp_postgres psql -U erp_admin -d erp_hub

# Exécuter une commande directe
docker exec erp_postgres psql -U erp_admin -d erp_hub -c "SELECT * FROM employees;"
```

### **📊 Informations de Connexion**
- **Host** : localhost (ou erp_postgres depuis Docker)
- **Port** : 5432
- **Database** : erp_hub
- **Username** : erp_admin
- **Password** : erp_password

---

## **📋 COMMANDES DE BASE**

### **🔍 Navigation et Information**
```sql
-- Lister toutes les tables
\dt

-- Décrire une table (structure)
\d employees

-- Lister les bases de données
\l

-- Changer de base de données
\c nom_base

-- Quitter PostgreSQL
\q

-- Aide
\h
```

### **📊 Consultation des Données**
```sql
-- Voir tous les employés
SELECT * FROM employees;

-- Voir les 5 premiers employés
SELECT * FROM employees LIMIT 5;

-- Compter les employés
SELECT COUNT(*) FROM employees;

-- Voir les employés par département
SELECT department, COUNT(*) 
FROM employees 
GROUP BY department;

-- Voir tous les clients
SELECT * FROM customers;

-- Voir tous les produits
SELECT * FROM products;

-- Voir les budgets
SELECT * FROM budgets;
```

---

## **✏️ MODIFICATION DES DONNÉES**

### **➕ Ajouter des Données**
```sql
-- Ajouter un nouvel employé
INSERT INTO employees (
    id, employee_number, first_name, last_name, 
    email, position, department, salary, status
) VALUES (
    'EMP009', 'E009', 'Jean', 'Dupont', 
    '<EMAIL>', 'Développeur', 'IT', 45000, 'Actif'
);

-- Ajouter un nouveau client
INSERT INTO customers (
    id, customer_code, company_name, contact_person, 
    email, phone, city, country, customer_type, credit_limit, status
) VALUES (
    'CUST004', 'C004', 'Nouvelle Entreprise', 'Marie Martin',
    '<EMAIL>', '0123456789', 'Paris', 'France', 
    'Entreprise', 50000, 'Actif'
);

-- Ajouter un nouveau produit
INSERT INTO products (
    id, product_code, product_name, category, 
    unit_price, stock_quantity, reorder_level, supplier_id, status
) VALUES (
    'PROD005', 'P005', 'Nouveau Produit', 'Électronique',
    299.99, 100, 20, 'SUPP001', 'Actif'
);
```

### **🔄 Modifier des Données**
```sql
-- Modifier le salaire d'un employé
UPDATE employees 
SET salary = 50000 
WHERE employee_number = 'E001';

-- Modifier plusieurs champs
UPDATE employees 
SET salary = 55000, position = 'Senior Développeur'
WHERE employee_number = 'E001';

-- Modifier le prix d'un produit
UPDATE products 
SET unit_price = 199.99 
WHERE product_code = 'P001';

-- Modifier le statut d'un client
UPDATE customers 
SET status = 'Inactif' 
WHERE customer_code = 'C001';
```

### **🗑️ Supprimer des Données**
```sql
-- Supprimer un employé spécifique
DELETE FROM employees 
WHERE employee_number = 'E009';

-- Supprimer tous les employés inactifs
DELETE FROM employees 
WHERE status = 'Inactif';

-- Supprimer un produit
DELETE FROM products 
WHERE product_code = 'P005';

-- ⚠️ ATTENTION : Supprimer TOUTES les données d'une table
-- DELETE FROM employees; -- NE PAS FAIRE SANS SAUVEGARDE !
```

---

## **📊 REQUÊTES AVANCÉES**

### **🔗 Jointures entre Tables**
```sql
-- Voir les congés avec les noms des employés
SELECT 
    l.id, 
    e.first_name, 
    e.last_name, 
    l.leave_type, 
    l.start_date, 
    l.end_date, 
    l.status
FROM leaves l
JOIN employees e ON l.employee_id = e.id;

-- Voir les commandes avec les noms des clients
SELECT 
    o.order_number, 
    c.company_name, 
    o.order_date, 
    o.total_amount, 
    o.status
FROM orders o
JOIN customers c ON o.customer_id = c.id;

-- Voir l'inventaire avec les noms des produits
SELECT 
    p.product_name, 
    i.quantity_on_hand, 
    i.last_updated, 
    w.warehouse_name
FROM inventory i
JOIN products p ON i.product_id = p.id
JOIN warehouses w ON i.warehouse_id = w.id;
```

### **📈 Statistiques et Analyses**
```sql
-- Salaire moyen par département
SELECT 
    department, 
    AVG(salary) as salaire_moyen,
    COUNT(*) as nombre_employes
FROM employees 
GROUP BY department;

-- Chiffre d'affaires par client
SELECT 
    c.company_name,
    SUM(o.total_amount) as chiffre_affaires
FROM customers c
JOIN orders o ON c.id = o.customer_id
GROUP BY c.company_name
ORDER BY chiffre_affaires DESC;

-- Stock total par catégorie
SELECT 
    p.category,
    SUM(i.quantity_on_hand) as stock_total
FROM products p
JOIN inventory i ON p.id = i.product_id
GROUP BY p.category;
```

---

## **💾 SAUVEGARDE ET RESTAURATION**

### **📤 Créer des Sauvegardes**
```bash
# Sauvegarde complète de la base
docker exec erp_postgres pg_dump -U erp_admin erp_hub > backup_complet_$(date +%Y%m%d).sql

# Sauvegarde d'une table spécifique
docker exec erp_postgres pg_dump -U erp_admin -t employees erp_hub > backup_employees.sql

# Sauvegarde avec compression
docker exec erp_postgres pg_dump -U erp_admin -Fc erp_hub > backup_complet.dump
```

### **📥 Restaurer des Sauvegardes**
```bash
# Restaurer depuis un fichier SQL
docker exec -i erp_postgres psql -U erp_admin erp_hub < backup_complet.sql

# Restaurer depuis un dump compressé
docker exec erp_postgres pg_restore -U erp_admin -d erp_hub backup_complet.dump
```

---

## **🔧 MAINTENANCE ET OPTIMISATION**

### **📊 Informations sur la Base**
```sql
-- Taille de la base de données
SELECT pg_size_pretty(pg_database_size('erp_hub'));

-- Taille de chaque table
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Nombre d'enregistrements par table
SELECT 
    schemaname,
    tablename,
    n_tup_ins as insertions,
    n_tup_upd as updates,
    n_tup_del as deletions
FROM pg_stat_user_tables;
```

### **🧹 Nettoyage et Optimisation**
```sql
-- Analyser les statistiques des tables
ANALYZE;

-- Nettoyer et réorganiser une table
VACUUM employees;

-- Nettoyer toute la base
VACUUM;

-- Réindexer une table
REINDEX TABLE employees;
```

---

## **⚠️ SÉCURITÉ ET BONNES PRATIQUES**

### **🛡️ Règles de Sécurité**
1. **Toujours faire une sauvegarde** avant modification importante
2. **Tester sur une copie** avant modification en production
3. **Utiliser des transactions** pour les modifications complexes
4. **Vérifier les contraintes** avant suppression

### **🔄 Utilisation des Transactions**
```sql
-- Commencer une transaction
BEGIN;

-- Faire des modifications
UPDATE employees SET salary = salary * 1.1 WHERE department = 'IT';

-- Vérifier le résultat
SELECT * FROM employees WHERE department = 'IT';

-- Valider les changements
COMMIT;

-- OU annuler si problème
-- ROLLBACK;
```

### **🔍 Vérifications avant Modification**
```sql
-- Vérifier avant suppression
SELECT COUNT(*) FROM employees WHERE status = 'Inactif';

-- Vérifier les dépendances
SELECT * FROM leaves WHERE employee_id = 'EMP001';

-- Vérifier les contraintes
SELECT conname, contype FROM pg_constraint WHERE conrelid = 'employees'::regclass;
```

---

## **🎯 EXEMPLES PRATIQUES COURANTS**

### **📊 Rapports Utiles**
```sql
-- Rapport des employés par département
SELECT 
    department,
    COUNT(*) as nombre,
    AVG(salary) as salaire_moyen,
    MIN(salary) as salaire_min,
    MAX(salary) as salaire_max
FROM employees 
WHERE status = 'Actif'
GROUP BY department
ORDER BY nombre DESC;

-- Rapport des ventes par mois
SELECT 
    DATE_TRUNC('month', order_date) as mois,
    COUNT(*) as nombre_commandes,
    SUM(total_amount) as chiffre_affaires
FROM orders 
GROUP BY DATE_TRUNC('month', order_date)
ORDER BY mois DESC;

-- Rapport de stock faible
SELECT 
    p.product_name,
    i.quantity_on_hand,
    p.reorder_level,
    w.warehouse_name
FROM inventory i
JOIN products p ON i.product_id = p.id
JOIN warehouses w ON i.warehouse_id = w.id
WHERE i.quantity_on_hand <= p.reorder_level;
```

---

**🎉 Avec ces commandes, vous pouvez maintenant gérer complètement votre base PostgreSQL !**
