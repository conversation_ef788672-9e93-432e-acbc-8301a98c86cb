# 🚀 API FLASK AVEC POSTGRESQL VIA SUBPROCESS
# Contournement du problème psycopg2 sur Windows

from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import json
import os
from datetime import datetime
import secrets
import re

# Configuration de l'application
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(32)

# Extensions
CORS(app, origins=['*'])  # Permettre toutes les origines pour le développement

def execute_sql(sql_command):
    """Exécuter une commande SQL via Docker exec"""
    try:
        # Échapper les guillemets simples dans la commande SQL
        sql_escaped = sql_command.replace("'", "''")
        
        # Construire la commande Docker
        docker_cmd = [
            'docker', 'exec', 'erp_postgres', 
            'psql', '-U', 'erp_admin', '-d', 'erp_hub', 
            '-t', '-c', sql_command
        ]
        
        # Exécuter la commande
        result = subprocess.run(
            docker_cmd, 
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            return {'success': True, 'data': result.stdout.strip()}
        else:
            return {'success': False, 'error': result.stderr.strip()}
            
    except Exception as e:
        return {'success': False, 'error': str(e)}

def parse_sql_result(result_text):
    """Parser le résultat d'une requête SQL"""
    if not result_text:
        return []
    
    lines = result_text.strip().split('\n')
    if len(lines) < 1:
        return []
    
    # Supprimer les lignes vides et les séparateurs
    data_lines = [line.strip() for line in lines if line.strip() and not line.strip().startswith('-')]
    
    if not data_lines:
        return []
    
    # Parser chaque ligne
    results = []
    for line in data_lines:
        # Diviser par le pipe (|) et nettoyer
        fields = [field.strip() for field in line.split('|')]
        if len(fields) >= 5:  # Au moins id, name, type, forecast, realized
            results.append(fields)
    
    return results

# ===== ENDPOINTS =====

@app.route('/api/health', methods=['GET'])
def health_check():
    """Vérifier l'état du serveur et de PostgreSQL"""
    # Test de connexion PostgreSQL
    result = execute_sql("SELECT 1;")
    
    return jsonify({
        'success': True,
        'message': 'Serveur ERP HUB opérationnel',
        'database': 'PostgreSQL',
        'database_status': 'connected' if result['success'] else 'disconnected',
        'timestamp': datetime.now().isoformat()
    }), 200

@app.route('/api/budgets', methods=['GET'])
def get_budgets():
    """Récupérer tous les budgets"""
    try:
        # Requête SQL pour récupérer les budgets
        sql = """
        SELECT id, category_name, category_type, cost_center, cost_center_name, 
               analytic_code, analytic_code_name, responsible, department, 
               forecast, realized, monthly_data, created_date, modified_date
        FROM budgets 
        ORDER BY created_date DESC;
        """
        
        result = execute_sql(sql)
        
        if not result['success']:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
        # Parser les résultats
        raw_data = result['data']
        budgets = []
        
        if raw_data:
            lines = raw_data.strip().split('\n')
            for line in lines:
                if line.strip() and '|' in line:
                    fields = [field.strip() for field in line.split('|')]
                    if len(fields) >= 11:
                        budget = {
                            'id': fields[0],
                            'categoryName': fields[1],
                            'categoryType': fields[2],
                            'costCenter': fields[3] if fields[3] else '',
                            'costCenterName': fields[4] if fields[4] else '',
                            'analyticCode': fields[5] if fields[5] else '',
                            'analyticCodeName': fields[6] if fields[6] else '',
                            'responsible': fields[7] if fields[7] else '',
                            'department': fields[8] if fields[8] else '',
                            'forecast': float(fields[9]) if fields[9] else 0,
                            'realized': float(fields[10]) if fields[10] else 0,
                            'monthlyData': json.loads(fields[11]) if fields[11] and fields[11] != '' else {},
                            'createdDate': fields[12] if len(fields) > 12 else '',
                            'modifiedDate': fields[13] if len(fields) > 13 else ''
                        }
                        budgets.append(budget)
        
        return jsonify({
            'success': True,
            'data': budgets,
            'count': len(budgets)
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets', methods=['POST'])
def create_budget():
    """Créer un nouveau budget"""
    try:
        budget_data = request.get_json()
        
        # Validation des champs obligatoires
        required_fields = ['id', 'categoryName', 'categoryType']
        for field in required_fields:
            if field not in budget_data:
                return jsonify({
                    'success': False,
                    'error': f'Champ obligatoire manquant : {field}'
                }), 400
        
        # Construire la requête SQL d'insertion
        sql = f"""
        INSERT INTO budgets (
            id, category_name, category_type, cost_center, cost_center_name,
            analytic_code, analytic_code_name, responsible, department, 
            forecast, realized, monthly_data
        ) VALUES (
            '{budget_data['id']}',
            '{budget_data['categoryName']}',
            '{budget_data['categoryType']}',
            '{budget_data.get('costCenter', '')}',
            '{budget_data.get('costCenterName', '')}',
            '{budget_data.get('analyticCode', '')}',
            '{budget_data.get('analyticCodeName', '')}',
            '{budget_data.get('responsible', '')}',
            '{budget_data.get('department', '')}',
            {budget_data.get('forecast', 0)},
            {budget_data.get('realized', 0)},
            '{json.dumps(budget_data.get('monthlyData', {}))}'
        );
        """
        
        result = execute_sql(sql)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': 'Budget créé avec succès',
                'data': budget_data
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['PUT'])
def update_budget(budget_id):
    """Mettre à jour un budget"""
    try:
        budget_data = request.get_json()
        
        # Construire la requête SQL de mise à jour
        sql = f"""
        UPDATE budgets SET
            category_name = '{budget_data['categoryName']}',
            category_type = '{budget_data['categoryType']}',
            cost_center = '{budget_data.get('costCenter', '')}',
            cost_center_name = '{budget_data.get('costCenterName', '')}',
            analytic_code = '{budget_data.get('analyticCode', '')}',
            analytic_code_name = '{budget_data.get('analyticCodeName', '')}',
            responsible = '{budget_data.get('responsible', '')}',
            department = '{budget_data.get('department', '')}',
            forecast = {budget_data.get('forecast', 0)},
            realized = {budget_data.get('realized', 0)},
            monthly_data = '{json.dumps(budget_data.get('monthlyData', {}))}',
            modified_date = CURRENT_TIMESTAMP
        WHERE id = '{budget_id}';
        """
        
        result = execute_sql(sql)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': 'Budget mis à jour avec succès'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['DELETE'])
def delete_budget(budget_id):
    """Supprimer un budget"""
    try:
        sql = f"DELETE FROM budgets WHERE id = '{budget_id}';"
        
        result = execute_sql(sql)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': 'Budget supprimé avec succès'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/stats', methods=['GET'])
def get_budget_stats():
    """Récupérer les statistiques des budgets"""
    try:
        sql = """
        SELECT 
            COUNT(*) as total_budgets,
            SUM(forecast) as total_forecast,
            SUM(realized) as total_realized,
            category_type,
            COUNT(*) as count_by_type
        FROM budgets 
        GROUP BY category_type;
        """
        
        result = execute_sql(sql)
        
        if result['success']:
            return jsonify({
                'success': True,
                'data': result['data'],
                'message': 'Statistiques récupérées'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== LANCEMENT DU SERVEUR =====

if __name__ == '__main__':
    print("🚀 Démarrage du serveur API ERP HUB avec PostgreSQL...")
    print("🐘 Base de données : PostgreSQL via Docker")
    
    # Test de connexion PostgreSQL
    test_result = execute_sql("SELECT version();")
    if test_result['success']:
        print("✅ Connexion PostgreSQL réussie")
        print(f"📊 Version: {test_result['data'][:50]}...")
    else:
        print("❌ Erreur connexion PostgreSQL:", test_result['error'])
    
    print("🌐 Serveur disponible sur : http://localhost:5000")
    print("📋 Endpoints disponibles :")
    print("   GET  /api/health           - État du serveur")
    print("   GET  /api/budgets          - Récupérer tous les budgets")
    print("   POST /api/budgets          - Créer un nouveau budget")
    print("   PUT  /api/budgets/<id>     - Mettre à jour un budget")
    print("   DELETE /api/budgets/<id>   - Supprimer un budget")
    print("   GET  /api/budgets/stats    - Statistiques des budgets")
    
    # Lancer le serveur
    app.run(debug=True, host='0.0.0.0', port=5000)
