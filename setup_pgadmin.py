# 🗄️ SCRIPT D'INSTALLATION ET CONFIGURATION PGADMIN
# Interface graphique pour PostgreSQL

import subprocess
import os
import json
from pathlib import Path

def install_pgadmin():
    """Installe pgAdmin via Docker"""
    
    print("🗄️ INSTALLATION DE PGADMIN")
    print("=" * 50)
    
    # Configuration pgAdmin
    pgadmin_config = """
version: '3.8'

services:
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: erp_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - erp_network

volumes:
  pgadmin_data:

networks:
  erp_network:
    external: true
"""
    
    # Créer le fichier docker-compose pour pgAdmin
    with open("docker-compose-pgadmin.yml", "w") as f:
        f.write(pgadmin_config)
    
    print("📄 Fichier docker-compose-pgadmin.yml créé")
    
    # <PERSON><PERSON><PERSON> le réseau s'il n'existe pas
    try:
        subprocess.run(["docker", "network", "create", "erp_network"], 
                      capture_output=True, check=False)
        print("🌐 Réseau Docker créé")
    except:
        print("🌐 Réseau Docker existe déjà")
    
    # Démarrer pgAdmin
    try:
        result = subprocess.run([
            "docker-compose", "-f", "docker-compose-pgadmin.yml", "up", "-d"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ pgAdmin démarré avec succès")
            print("🌐 Accès : http://localhost:8080")
            print("📧 Email : <EMAIL>")
            print("🔑 Mot de passe : admin123")
            return True
        else:
            print(f"❌ Erreur : {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du démarrage : {e}")
        return False

def create_connection_guide():
    """Crée un guide de connexion à la base"""
    
    guide = """
# 🗄️ GUIDE DE CONNEXION POSTGRESQL

## 📊 INFORMATIONS DE CONNEXION

### 🐳 Via Docker (Ligne de commande)
```bash
# Accès direct à PostgreSQL
docker exec -it erp_postgres psql -U erp_admin -d erp_hub

# Commandes utiles une fois connecté :
\\dt                    # Lister toutes les tables
\\d table_name          # Décrire une table
\\q                     # Quitter
```

### 🌐 Via pgAdmin (Interface graphique)
1. **Ouvrir** : http://localhost:8080
2. **Email** : <EMAIL>
3. **Mot de passe** : admin123
4. **Ajouter serveur** :
   - Nom : ERP HUB PostgreSQL
   - Host : erp_postgres
   - Port : 5432
   - Database : erp_hub
   - Username : erp_admin
   - Password : erp_password

### 💻 Via Client Local
- **Host** : localhost
- **Port** : 5432
- **Database** : erp_hub
- **Username** : erp_admin
- **Password** : erp_password

## 📋 TABLES DISPONIBLES (22 tables)

### 👥 MODULE HR
- **employees** : Employés (8 enregistrements)
- **leaves** : Congés (3 enregistrements)
- **evaluations** : Évaluations

### 💼 MODULE SALES
- **customers** : Clients (3 enregistrements)
- **opportunities** : Opportunités (3 enregistrements)
- **orders** : Commandes (2 enregistrements)

### 🛒 MODULE PURCHASE
- **suppliers** : Fournisseurs (3 enregistrements)
- **purchase_orders** : Bons de commande (2 enregistrements)
- **purchase_requests** : Demandes d'achat

### 📦 MODULE STOCK
- **products** : Produits (4 enregistrements)
- **inventory** : Inventaire (4 enregistrements)
- **stock_movements** : Mouvements de stock

### 🚚 MODULE LOGISTICS
- **warehouses** : Entrepôts (3 enregistrements)
- **shipments** : Expéditions (2 enregistrements)

### 💰 MODULE FINANCE
- **budgets** : Budgets (3 enregistrements)
- **chart_of_accounts** : Plan comptable
- **journal_entries** : Écritures comptables
- **journal_entry_lines** : Lignes d'écriture

### 💼 MODULE CRM
- **contacts** : Contacts (4 enregistrements)
- **interactions** : Interactions (4 enregistrements)

### 📊 MODULE BI
- **kpis** : Indicateurs (5 enregistrements)
- **reports** : Rapports

## 🔧 COMMANDES SQL UTILES

### 📊 Consultation des données
```sql
-- Voir tous les employés
SELECT * FROM employees;

-- Voir tous les clients
SELECT * FROM customers;

-- Voir tous les produits
SELECT * FROM products;

-- Statistiques par table
SELECT 
    schemaname,
    tablename,
    n_tup_ins as insertions,
    n_tup_upd as updates,
    n_tup_del as deletions
FROM pg_stat_user_tables;
```

### ✏️ Modification des données
```sql
-- Ajouter un employé
INSERT INTO employees (id, employee_number, first_name, last_name, email, position, department, salary, status)
VALUES ('EMP009', 'E009', 'Jean', 'Dupont', '<EMAIL>', 'Développeur', 'IT', 45000, 'Actif');

-- Modifier un employé
UPDATE employees 
SET salary = 50000 
WHERE employee_number = 'E009';

-- Supprimer un employé
DELETE FROM employees 
WHERE employee_number = 'E009';
```

### 🗄️ Structure des tables
```sql
-- Voir la structure d'une table
\\d employees

-- Voir les contraintes
SELECT conname, contype, conrelid::regclass 
FROM pg_constraint 
WHERE conrelid = 'employees'::regclass;
```

## ⚠️ SAUVEGARDES

### 💾 Créer une sauvegarde
```bash
# Sauvegarde complète
docker exec erp_postgres pg_dump -U erp_admin erp_hub > backup_$(date +%Y%m%d).sql

# Sauvegarde d'une table
docker exec erp_postgres pg_dump -U erp_admin -t employees erp_hub > employees_backup.sql
```

### 🔄 Restaurer une sauvegarde
```bash
# Restaurer depuis un fichier
docker exec -i erp_postgres psql -U erp_admin erp_hub < backup.sql
```
"""
    
    with open("GUIDE_POSTGRESQL_ACCESS.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("📖 Guide créé : GUIDE_POSTGRESQL_ACCESS.md")

def main():
    """Fonction principale"""
    
    print("🗄️ CONFIGURATION D'ACCÈS POSTGRESQL")
    print("=" * 60)
    
    # Créer le guide
    create_connection_guide()
    
    # Proposer l'installation de pgAdmin
    response = input("\n❓ Voulez-vous installer pgAdmin (interface graphique) ? (o/n): ")
    
    if response.lower() in ['o', 'oui', 'y', 'yes']:
        success = install_pgadmin()
        
        if success:
            print("\n🎉 INSTALLATION TERMINÉE !")
            print("=" * 50)
            print("🌐 pgAdmin : http://localhost:8080")
            print("📧 Email : <EMAIL>")
            print("🔑 Mot de passe : admin123")
            print("\n📖 Consultez GUIDE_POSTGRESQL_ACCESS.md pour plus d'infos")
        else:
            print("\n❌ Échec de l'installation de pgAdmin")
            print("💡 Vous pouvez utiliser la ligne de commande à la place")
    else:
        print("\n✅ Guide créé sans pgAdmin")
        print("💡 Utilisez la ligne de commande pour accéder à PostgreSQL")
    
    print("\n🔧 COMMANDES RAPIDES :")
    print("=" * 50)
    print("# Accès PostgreSQL :")
    print("docker exec -it erp_postgres psql -U erp_admin -d erp_hub")
    print("\n# Voir toutes les tables :")
    print("\\dt")
    print("\n# Voir les employés :")
    print("SELECT * FROM employees;")

if __name__ == "__main__":
    main()
