import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'
import { 
  UserGroupIcon, 
  ClockIcon, 
  AcademicCapIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'

import { hrApi, type HRDashboard, type LeaveRequest, type Employee } from '@/services/api/agentsApi'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import Button from '@/components/ui/Button'

const HRPage: React.FC = () => {
  const queryClient = useQueryClient()
  const [selectedTab, setSelectedTab] = useState<'overview' | 'employees' | 'leaves' | 'training'>('overview')

  // Récupération des données du dashboard
  const { data: dashboard, isLoading, error, refetch } = useQuery<HRDashboard>(
    'hr-dashboard',
    hrApi.getDashboard,
    {
      refetchInterval: 60000, // Actualisation toutes les minutes
      staleTime: 30000,
    }
  )

  // Récupération des demandes de congés en attente
  const { data: pendingLeaves } = useQuery<LeaveRequest[]>(
    'pending-leaves',
    () => hrApi.getLeaveRequests({ status: 'pending' }),
    {
      enabled: selectedTab === 'leaves',
    }
  )

  // Récupération des employés
  const { data: employees } = useQuery<Employee[]>(
    'employees',
    () => hrApi.getEmployees({ status: 'active' }),
    {
      enabled: selectedTab === 'employees',
    }
  )

  // Mutation pour traiter les demandes de congés
  const processLeaveMutation = useMutation(
    ({ id, action, comments }: { id: string; action: 'approve' | 'reject'; comments?: string }) =>
      hrApi.processLeaveRequest(id, action, comments),
    {
      onSuccess: () => {
        toast.success('Demande de congé traitée avec succès')
        queryClient.invalidateQueries('pending-leaves')
        queryClient.invalidateQueries('hr-dashboard')
      },
      onError: (error: any) => {
        toast.error(error.message || 'Erreur lors du traitement de la demande')
      }
    }
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Erreur de chargement</h3>
        <p className="mt-1 text-sm text-gray-500">
          Impossible de charger les données RH
        </p>
        <div className="mt-6">
          <Button onClick={() => refetch()}>Réessayer</Button>
        </div>
      </div>
    )
  }

  if (!dashboard) {
    return null
  }

  const tabs = [
    { id: 'overview', name: 'Vue d\'ensemble', icon: ChartBarIcon },
    { id: 'employees', name: 'Employés', icon: UserGroupIcon },
    { id: 'leaves', name: 'Congés', icon: ClockIcon },
    { id: 'training', name: 'Formations', icon: AcademicCapIcon },
  ]

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Agent RH</h1>
            <p className="mt-2 text-blue-100">
              Gestion des ressources humaines - {dashboard.tenant}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">
              {dashboard.employees.active}/{dashboard.employees.total}
            </div>
            <div className="text-sm text-blue-100">Employés actifs</div>
          </div>
        </div>
      </div>

      {/* Métriques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Employés actifs"
          value={dashboard.employees.active.toString()}
          color="blue"
          icon={UserGroupIcon}
        />
        <MetricCard
          title="Taux de rotation"
          value={`${dashboard.employees.turnover_rate.toFixed(1)}%`}
          color={dashboard.employees.turnover_rate > 15 ? 'red' : 'green'}
          icon={ChartBarIcon}
        />
        <MetricCard
          title="Performance moyenne"
          value={`${dashboard.employees.avg_performance.toFixed(1)}/5`}
          color="yellow"
          icon={ChartBarIcon}
        />
        <MetricCard
          title="Actions en attente"
          value={dashboard.pending_actions.leave_requests.toString()}
          color="orange"
          icon={ClockIcon}
        />
      </div>

      {/* Actions en attente */}
      {dashboard.pending_actions.leave_requests > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Actions requises
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  {dashboard.pending_actions.leave_requests} demande(s) de congé en attente d'approbation
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation par onglets */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`
                  flex items-center py-2 px-1 border-b-2 font-medium text-sm
                  ${selectedTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Contenu des onglets */}
      <div className="mt-6">
        {selectedTab === 'overview' && (
          <OverviewTab dashboard={dashboard} />
        )}
        {selectedTab === 'employees' && (
          <EmployeesTab employees={employees} />
        )}
        {selectedTab === 'leaves' && (
          <LeavesTab 
            leaves={pendingLeaves} 
            onProcessLeave={(id, action, comments) => 
              processLeaveMutation.mutate({ id, action, comments })
            }
            isProcessing={processLeaveMutation.isLoading}
          />
        )}
        {selectedTab === 'training' && (
          <TrainingTab />
        )}
      </div>
    </div>
  )
}

// Composant pour les métriques
interface MetricCardProps {
  title: string
  value: string
  color: 'blue' | 'green' | 'red' | 'yellow' | 'orange'
  icon: React.ComponentType<{ className?: string }>
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, color, icon: Icon }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-700 border-blue-200',
    green: 'bg-green-50 text-green-700 border-green-200',
    red: 'bg-red-50 text-red-700 border-red-200',
    yellow: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    orange: 'bg-orange-50 text-orange-700 border-orange-200',
  }

  return (
    <div className={`card border ${colorClasses[color]}`}>
      <div className="card-body">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-8 w-8" />
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium opacity-75">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Onglet Vue d'ensemble
const OverviewTab: React.FC<{ dashboard: HRDashboard }> = ({ dashboard }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Départements */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Départements</h3>
        </div>
        <div className="card-body">
          <div className="space-y-3">
            {dashboard.departments.list.map((dept) => (
              <div key={dept.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">{dept.name}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{dept.employee_count} employés</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Activités récentes */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Activités Récentes</h3>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            {dashboard.recent_activities.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.type === 'new_hire' ? 'bg-green-400' : 'bg-blue-400'
                }`} />
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <p className="text-xs text-gray-500">
                    {new Date(activity.date).toLocaleDateString('fr-FR')}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Onglet Employés
const EmployeesTab: React.FC<{ employees?: Employee[] }> = ({ employees }) => {
  if (!employees) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Liste des Employés</h3>
      </div>
      <div className="card-body">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employé
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Poste
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Département
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Performance
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {employees.map((employee) => (
                <tr key={employee.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {employee.user_info.full_name}
                      </div>
                      <div className="text-sm text-gray-500">{employee.employee_id}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {employee.position_title}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {employee.department_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`badge ${
                      employee.employment_status === 'active' ? 'badge-success' : 'badge-secondary'
                    }`}>
                      {employee.employment_status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {employee.performance_rating ? `${employee.performance_rating}/5` : 'N/A'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

// Onglet Congés
const LeavesTab: React.FC<{
  leaves?: LeaveRequest[]
  onProcessLeave: (id: string, action: 'approve' | 'reject', comments?: string) => void
  isProcessing: boolean
}> = ({ leaves, onProcessLeave, isProcessing }) => {
  if (!leaves) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Demandes de Congés en Attente</h3>
      </div>
      <div className="card-body">
        {leaves.length === 0 ? (
          <p className="text-gray-500 text-center py-8">Aucune demande de congé en attente</p>
        ) : (
          <div className="space-y-4">
            {leaves.map((leave) => (
              <div key={leave.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{leave.employee_name}</h4>
                    <p className="text-sm text-gray-600">
                      {leave.leave_type_name} - {leave.days_requested} jour(s)
                    </p>
                    <p className="text-sm text-gray-500">
                      Du {new Date(leave.start_date).toLocaleDateString('fr-FR')} au{' '}
                      {new Date(leave.end_date).toLocaleDateString('fr-FR')}
                    </p>
                    {leave.reason && (
                      <p className="text-sm text-gray-600 mt-2">
                        <strong>Motif:</strong> {leave.reason}
                      </p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="success"
                      onClick={() => onProcessLeave(leave.id, 'approve')}
                      disabled={isProcessing}
                      leftIcon={<CheckCircleIcon className="h-4 w-4" />}
                    >
                      Approuver
                    </Button>
                    <Button
                      size="sm"
                      variant="danger"
                      onClick={() => onProcessLeave(leave.id, 'reject')}
                      disabled={isProcessing}
                      leftIcon={<XCircleIcon className="h-4 w-4" />}
                    >
                      Rejeter
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// Onglet Formations
const TrainingTab: React.FC = () => {
  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Gestion des Formations</h3>
      </div>
      <div className="card-body">
        <p className="text-gray-600">Module de formation en cours de développement...</p>
      </div>
    </div>
  )
}

export default HRPage
