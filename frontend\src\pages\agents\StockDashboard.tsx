import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  LinearProgress,
  Button,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert
} from '@mui/material';
import {
  Inventory,
  TrendingDown,
  Warning,
  CheckCircle,
  LocalShipping,
  Assessment,
  Refresh,
  Download,
  FilterList,
  QrCode,
  Timeline
} from '@mui/icons-material';
import { MetricCard } from '../../components/ui/MetricCard';
import { AnimatedChart } from '../../components/ui/AnimatedChart';
import { StatusIndicator } from '../../components/ui/StatusIndicator';

interface StockMetrics {
  totalItems: number;
  totalValue: number;
  lowStockItems: number;
  outOfStockItems: number;
  turnoverRate: number;
  averageAge: number;
}

interface StockItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  value: number;
  lastMovement: string;
  status: 'good' | 'low' | 'critical' | 'out';
  location: string;
}

interface StockMovement {
  id: string;
  type: 'in' | 'out';
  item: string;
  quantity: number;
  date: string;
  reason: string;
  user: string;
}

export const StockDashboard: React.FC = () => {
  const [stockMetrics, setStockMetrics] = useState<StockMetrics>({
    totalItems: 1247,
    totalValue: 2847500,
    lowStockItems: 23,
    outOfStockItems: 5,
    turnoverRate: 4.2,
    averageAge: 45
  });

  const [criticalItems] = useState<StockItem[]>([
    { id: '1', name: 'Processeur Intel i7', category: 'Électronique', currentStock: 2, minStock: 10, maxStock: 50, value: 45000, lastMovement: '2h', status: 'critical', location: 'A-12-3' },
    { id: '2', name: 'Écran 24" Dell', category: 'Électronique', currentStock: 0, minStock: 5, maxStock: 25, value: 0, lastMovement: '1j', status: 'out', location: 'B-08-1' },
    { id: '3', name: 'Clavier Mécanique', category: 'Périphériques', currentStock: 3, minStock: 8, maxStock: 30, value: 2400, lastMovement: '4h', status: 'low', location: 'C-15-2' },
    { id: '4', name: 'Souris Optique', category: 'Périphériques', currentStock: 1, minStock: 15, maxStock: 60, value: 350, lastMovement: '6h', status: 'critical', location: 'C-15-3' },
    { id: '5', name: 'Câble HDMI 2m', category: 'Câbles', currentStock: 4, minStock: 20, maxStock: 100, value: 800, lastMovement: '3h', status: 'low', location: 'D-22-1' }
  ]);

  const [recentMovements] = useState<StockMovement[]>([
    { id: '1', type: 'out', item: 'Processeur Intel i7', quantity: 5, date: '2024-01-27 14:30', reason: 'Commande client', user: 'Marie Dubois' },
    { id: '2', type: 'in', item: 'RAM DDR4 16GB', quantity: 20, date: '2024-01-27 10:15', reason: 'Réception fournisseur', user: 'Pierre Martin' },
    { id: '3', type: 'out', item: 'Écran 24" Dell', quantity: 3, date: '2024-01-27 09:45', reason: 'Installation bureau', user: 'Sophie Laurent' },
    { id: '4', type: 'in', item: 'Clavier Mécanique', quantity: 15, date: '2024-01-26 16:20', reason: 'Commande urgente', user: 'Thomas Bernard' },
    { id: '5', type: 'out', item: 'Souris Optique', quantity: 8, date: '2024-01-26 14:10', reason: 'Remplacement défectueux', user: 'Julie Moreau' }
  ]);

  const [stockEvolutionData] = useState([
    { name: 'Jan', value: 1180, movements: 245 },
    { name: 'Fév', value: 1205, movements: 267 },
    { name: 'Mar', value: 1190, movements: 289 },
    { name: 'Avr', value: 1220, movements: 312 },
    { name: 'Mai', value: 1235, movements: 298 },
    { name: 'Jun', value: 1260, movements: 334 },
    { name: 'Jul', value: 1247, movements: 356 }
  ]);

  const [categoryData] = useState([
    { name: 'Électronique', value: 450, percentage: 36 },
    { name: 'Périphériques', value: 320, percentage: 26 },
    { name: 'Câbles', value: 180, percentage: 14 },
    { name: 'Accessoires', value: 150, percentage: 12 },
    { name: 'Consommables', value: 97, percentage: 8 },
    { name: 'Autres', value: 50, percentage: 4 }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'success';
      case 'low': return 'warning';
      case 'critical': return 'error';
      case 'out': return 'error';
      default: return 'default';
    }
  };

  const getStockStatusLabel = (status: string) => {
    switch (status) {
      case 'good': return 'Stock OK';
      case 'low': return 'Stock Faible';
      case 'critical': return 'Stock Critique';
      case 'out': return 'Rupture';
      default: return 'Inconnu';
    }
  };

  const getMovementIcon = (type: string) => {
    return type === 'in' ? '📥' : '📤';
  };

  const getMovementColor = (type: string) => {
    return type === 'in' ? 'success' : 'info';
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar sx={{ bgcolor: 'info.main', width: 56, height: 56 }}>
              <Inventory fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={700} color="info.main">
                Agent Stock
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Gestion d'inventaire et suivi des stocks
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={1}>
            <Tooltip title="Actualiser les données">
              <IconButton onClick={handleRefresh} disabled={isLoading}>
                <Refresh />
              </IconButton>
            </Tooltip>
            <Button variant="outlined" startIcon={<QrCode />}>
              Scanner
            </Button>
            <Button variant="outlined" startIcon={<FilterList />}>
              Filtres
            </Button>
            <Button variant="contained" startIcon={<Download />} color="info">
              Inventaire
            </Button>
          </Box>
        </Box>
      </motion.div>

      {/* Alertes */}
      {stockMetrics.outOfStockItems > 0 && (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Alert 
            severity="error" 
            sx={{ mb: 3 }}
            action={
              <Button color="inherit" size="small">
                Commander
              </Button>
            }
          >
            {stockMetrics.outOfStockItems} article(s) en rupture de stock nécessitent une commande urgente
          </Alert>
        </motion.div>
      )}

      {/* Métriques principales */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Articles Total"
            value={stockMetrics.totalItems}
            icon={<Inventory />}
            color="primary"
            trend="up"
            trendValue={2.1}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Valeur Stock"
            value={stockMetrics.totalValue}
            icon={<Assessment />}
            color="success"
            trend="up"
            trendValue={8.5}
            format="currency"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Stock Faible"
            value={stockMetrics.lowStockItems}
            icon={<Warning />}
            color="warning"
            trend="down"
            trendValue={-12.3}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Ruptures"
            value={stockMetrics.outOfStockItems}
            icon={<TrendingDown />}
            color="error"
            trend="up"
            trendValue={25.0}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Rotation Stock"
            value={stockMetrics.turnoverRate}
            unit="x/an"
            icon={<Timeline />}
            color="info"
            trend="up"
            trendValue={5.2}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Âge Moyen"
            value={stockMetrics.averageAge}
            unit="jours"
            icon={<LocalShipping />}
            color="secondary"
            trend="down"
            trendValue={-3.1}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
      </Grid>

      {/* Articles critiques */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" fontWeight={600} mb={3}>
              Articles Nécessitant une Attention
            </Typography>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Article</TableCell>
                    <TableCell>Catégorie</TableCell>
                    <TableCell align="center">Stock Actuel</TableCell>
                    <TableCell align="center">Stock Min</TableCell>
                    <TableCell align="center">Statut</TableCell>
                    <TableCell>Emplacement</TableCell>
                    <TableCell align="right">Valeur</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {criticalItems.map((item) => (
                    <TableRow key={item.id} hover>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight={600}>
                          {item.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Dernière sortie: {item.lastMovement}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip label={item.category} size="small" variant="outlined" />
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2" fontWeight={600}>
                          {item.currentStock}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">
                          {item.minStock}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={getStockStatusLabel(item.status)}
                          color={getStockStatusColor(item.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {item.location}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" fontWeight={600}>
                          {formatCurrency(item.value)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Button
                          size="small"
                          variant="contained"
                          color={item.status === 'out' ? 'error' : 'warning'}
                          disabled={item.status === 'good'}
                        >
                          {item.status === 'out' ? 'Commander' : 'Réapprovisionner'}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </motion.div>

      {/* Graphiques et mouvements */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} lg={8}>
          <AnimatedChart
            title="Évolution du Stock"
            subtitle="Nombre d'articles et mouvements mensuels"
            data={stockEvolutionData}
            type="line"
            height={350}
            color="#0288d1"
            secondaryColor="#29b6f6"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} lg={4}>
          <AnimatedChart
            title="Répartition par Catégorie"
            subtitle="Nombre d'articles par type"
            data={categoryData}
            type="pie"
            height={350}
            isLoading={isLoading}
          />
        </Grid>
      </Grid>

      {/* Mouvements récents */}
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight={600} mb={3}>
            Mouvements Récents
          </Typography>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Type</TableCell>
                  <TableCell>Article</TableCell>
                  <TableCell align="center">Quantité</TableCell>
                  <TableCell>Date/Heure</TableCell>
                  <TableCell>Motif</TableCell>
                  <TableCell>Utilisateur</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {recentMovements.map((movement) => (
                  <TableRow key={movement.id} hover>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="h6">
                          {getMovementIcon(movement.type)}
                        </Typography>
                        <Chip
                          label={movement.type === 'in' ? 'Entrée' : 'Sortie'}
                          color={getMovementColor(movement.type) as any}
                          size="small"
                        />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight={600}>
                        {movement.item}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography 
                        variant="body2" 
                        fontWeight={600}
                        color={movement.type === 'in' ? 'success.main' : 'info.main'}
                      >
                        {movement.type === 'in' ? '+' : '-'}{movement.quantity}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(movement.date).toLocaleString('fr-FR')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {movement.reason}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {movement.user}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};
