"""
Vues pour l'agent Purchase
"""
import logging
from django.utils import timezone
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema

from core.permissions import PurchaseReadPermission, PurchaseWritePermission
from .services import PurchaseService
from .models import (
    Supplier, ProductCategory, PurchaseRequest, PurchaseRequestItem,
    PurchaseOrder, PurchaseOrderItem, GoodsReceipt, SupplierInvoice
)
from .serializers import (
    SupplierSerializer, ProductCategorySerializer, PurchaseRequestSerializer,
    PurchaseRequestCreateSerializer, PurchaseOrderSerializer, PurchaseOrderCreateSerializer,
    GoodsReceiptSerializer, GoodsReceiptCreateSerializer, SupplierInvoiceSerializer,
    PurchaseDashboardSerializer, PurchaseRequestApprovalSerializer,
    SupplierPerformanceSerializer, PurchaseInsightSerializer, CostSavingsSerializer
)

logger = logging.getLogger('agents.purchase')


@extend_schema(
    summary="Statut de l'agent Purchase",
    description="Retourne le statut de l'agent Purchase"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def purchase_status(request):
    """Retourne le statut de l'agent Purchase"""
    try:
        purchase_service = PurchaseService(request.user.tenant)

        return Response({
            'status': 'active',
            'agent': 'purchase',
            'message': 'Agent Achats opérationnel',
            'capabilities': [
                'supplier_management',
                'purchase_request_processing',
                'purchase_order_generation',
                'goods_receipt_management',
                'invoice_validation',
                'supplier_performance_analysis',
                'cost_optimization'
            ]
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut Purchase: {str(e)}")
        return Response({
            'status': 'error',
            'agent': 'purchase',
            'message': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Dashboard de l'agent Purchase",
    description="Retourne les données complètes du dashboard Purchase"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, PurchaseReadPermission])
def purchase_dashboard(request):
    """Retourne les données du dashboard Purchase"""
    try:
        purchase_service = PurchaseService(request.user.tenant)
        dashboard_data = purchase_service.get_purchase_dashboard()

        return Response(dashboard_data)
    except Exception as e:
        logger.error(f"Erreur lors de la génération du dashboard Purchase: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Gestion des fournisseurs
class SupplierListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des fournisseurs"""
    serializer_class = SupplierSerializer
    permission_classes = [IsAuthenticated, PurchaseReadPermission]

    def get_queryset(self):
        queryset = Supplier.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        supplier_type = self.request.query_params.get('type')
        if supplier_type:
            queryset = queryset.filter(supplier_type=supplier_type)

        is_active = self.request.query_params.get('active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        is_approved = self.request.query_params.get('approved')
        if is_approved is not None:
            queryset = queryset.filter(is_approved=is_approved.lower() == 'true')

        buyer = self.request.query_params.get('buyer')
        if buyer:
            queryset = queryset.filter(buyer_id=buyer)

        return queryset.order_by('name')

    def perform_create(self, serializer):
        # Générer le code fournisseur automatiquement
        supplier_code = self._generate_supplier_code()
        serializer.save(
            tenant=self.request.user.tenant,
            supplier_code=supplier_code
        )

    def _generate_supplier_code(self):
        """Génère un code fournisseur unique"""
        last_supplier = Supplier.objects.filter(
            tenant=self.request.user.tenant
        ).order_by('-supplier_code').first()

        if last_supplier and last_supplier.supplier_code.startswith('SUP'):
            try:
                last_number = int(last_supplier.supplier_code[3:])
                new_number = last_number + 1
            except ValueError:
                new_number = 1
        else:
            new_number = 1

        return f"SUP{new_number:06d}"


class SupplierDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un fournisseur"""
    serializer_class = SupplierSerializer
    permission_classes = [IsAuthenticated, PurchaseWritePermission]

    def get_queryset(self):
        return Supplier.objects.filter(tenant=self.request.user.tenant)


# Gestion des catégories
class ProductCategoryListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des catégories de produits"""
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticated, PurchaseReadPermission]

    def get_queryset(self):
        queryset = ProductCategory.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        parent_id = self.request.query_params.get('parent')
        if parent_id:
            queryset = queryset.filter(parent_category_id=parent_id)
        elif self.request.query_params.get('root_only') == 'true':
            queryset = queryset.filter(parent_category__isnull=True)

        return queryset.order_by('name')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class ProductCategoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une catégorie"""
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticated, PurchaseWritePermission]

    def get_queryset(self):
        return ProductCategory.objects.filter(tenant=self.request.user.tenant)


# Gestion des demandes d'achat
class PurchaseRequestListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des demandes d'achat"""
    permission_classes = [IsAuthenticated, PurchaseReadPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return PurchaseRequestCreateSerializer
        return PurchaseRequestSerializer

    def get_queryset(self):
        queryset = PurchaseRequest.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        requester = self.request.query_params.get('requester')
        if requester:
            queryset = queryset.filter(requester_id=requester)

        buyer = self.request.query_params.get('buyer')
        if buyer:
            queryset = queryset.filter(buyer_id=buyer)

        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        return queryset.order_by('-created_at')


class PurchaseRequestCreateView(APIView):
    """Vue pour créer des demandes d'achat avec l'aide de l'IA"""
    permission_classes = [IsAuthenticated, PurchaseWritePermission]

    @extend_schema(
        summary="Créer une demande d'achat",
        description="Crée une nouvelle demande d'achat avec recommandations IA",
        request=PurchaseRequestCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle demande d'achat"""
        try:
            serializer = PurchaseRequestCreateSerializer(data=request.data)
            if serializer.is_valid():
                purchase_service = PurchaseService(request.user.tenant)

                result = purchase_service.create_purchase_request(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de la demande d'achat: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PurchaseRequestDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une demande d'achat"""
    serializer_class = PurchaseRequestSerializer
    permission_classes = [IsAuthenticated, PurchaseWritePermission]

    def get_queryset(self):
        return PurchaseRequest.objects.filter(tenant=self.request.user.tenant)


class PurchaseRequestApprovalView(APIView):
    """Vue pour approuver/rejeter des demandes d'achat"""
    permission_classes = [IsAuthenticated, PurchaseWritePermission]

    @extend_schema(
        summary="Traiter une demande d'achat",
        description="Approuve ou rejette une demande d'achat",
        request=PurchaseRequestApprovalSerializer
    )
    def post(self, request, request_id):
        """Traite une demande d'achat"""
        try:
            serializer = PurchaseRequestApprovalSerializer(data=request.data)
            if serializer.is_valid():
                purchase_service = PurchaseService(request.user.tenant)

                if serializer.validated_data['action'] == 'approve':
                    result = purchase_service.approve_purchase_request(
                        request_id,
                        request.user,
                        serializer.validated_data.get('comments', '')
                    )
                else:
                    # Logique de rejet à implémenter
                    result = {'success': False, 'error': 'Rejet non implémenté'}

                if result['success']:
                    return Response(result, status=status.HTTP_200_OK)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors du traitement de la demande: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Gestion des bons de commande
class PurchaseOrderListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des bons de commande"""
    serializer_class = PurchaseOrderSerializer
    permission_classes = [IsAuthenticated, PurchaseReadPermission]

    def get_queryset(self):
        queryset = PurchaseOrder.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        supplier_id = self.request.query_params.get('supplier')
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)

        buyer = self.request.query_params.get('buyer')
        if buyer:
            queryset = queryset.filter(buyer_id=buyer)

        return queryset.order_by('-order_date')


class PurchaseOrderCreateView(APIView):
    """Vue pour créer des bons de commande"""
    permission_classes = [IsAuthenticated, PurchaseWritePermission]

    @extend_schema(
        summary="Créer un bon de commande",
        description="Génère un nouveau bon de commande avec calculs automatiques",
        request=PurchaseOrderCreateSerializer
    )
    def post(self, request):
        """Crée un nouveau bon de commande"""
        try:
            serializer = PurchaseOrderCreateSerializer(data=request.data)
            if serializer.is_valid():
                purchase_service = PurchaseService(request.user.tenant)

                result = purchase_service.generate_purchase_order(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création du bon de commande: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PurchaseOrderDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un bon de commande"""
    serializer_class = PurchaseOrderSerializer
    permission_classes = [IsAuthenticated, PurchaseWritePermission]

    def get_queryset(self):
        return PurchaseOrder.objects.filter(tenant=self.request.user.tenant)


# Gestion des réceptions
class GoodsReceiptListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des réceptions de marchandises"""
    serializer_class = GoodsReceiptSerializer
    permission_classes = [IsAuthenticated, PurchaseReadPermission]

    def get_queryset(self):
        queryset = GoodsReceipt.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        purchase_order_id = self.request.query_params.get('purchase_order')
        if purchase_order_id:
            queryset = queryset.filter(purchase_order_id=purchase_order_id)

        return queryset.order_by('-receipt_date')


class GoodsReceiptCreateView(APIView):
    """Vue pour créer des réceptions de marchandises"""
    permission_classes = [IsAuthenticated, PurchaseWritePermission]

    @extend_schema(
        summary="Créer une réception de marchandises",
        description="Enregistre la réception de marchandises avec contrôle qualité",
        request=GoodsReceiptCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle réception"""
        try:
            serializer = GoodsReceiptCreateSerializer(data=request.data)
            if serializer.is_valid():
                purchase_service = PurchaseService(request.user.tenant)

                result = purchase_service.create_goods_receipt(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de la réception: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GoodsReceiptDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une réception"""
    serializer_class = GoodsReceiptSerializer
    permission_classes = [IsAuthenticated, PurchaseWritePermission]

    def get_queryset(self):
        return GoodsReceipt.objects.filter(tenant=self.request.user.tenant)


# Gestion des factures fournisseurs
class SupplierInvoiceListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des factures fournisseurs"""
    serializer_class = SupplierInvoiceSerializer
    permission_classes = [IsAuthenticated, PurchaseReadPermission]

    def get_queryset(self):
        queryset = SupplierInvoice.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        supplier_id = self.request.query_params.get('supplier')
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)

        return queryset.order_by('-invoice_date')


class SupplierInvoiceDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une facture fournisseur"""
    serializer_class = SupplierInvoiceSerializer
    permission_classes = [IsAuthenticated, PurchaseWritePermission]

    def get_queryset(self):
        return SupplierInvoice.objects.filter(tenant=self.request.user.tenant)


# Analytics et insights
@extend_schema(
    summary="Performance des fournisseurs",
    description="Retourne l'analyse des performances des fournisseurs"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, PurchaseReadPermission])
def supplier_performance(request):
    """Retourne l'analyse des performances des fournisseurs"""
    try:
        purchase_service = PurchaseService(request.user.tenant)
        performance_data = purchase_service.analyze_supplier_performance()

        return Response(performance_data)
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse des performances: {str(e)}")
        return Response(
            {'error': f'Erreur lors de l\'analyse: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Insights d'achat",
    description="Retourne les insights et recommandations d'achat basés sur l'IA"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, PurchaseReadPermission])
def purchase_insights(request):
    """Retourne les insights d'achat"""
    try:
        purchase_service = PurchaseService(request.user.tenant)
        insights = purchase_service.generate_purchase_insights()

        return Response({
            'insights': insights,
            'count': len(insights),
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'insights: {str(e)}")
        return Response(
            {'error': f'Erreur lors de la génération d\'insights: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Opportunités d'économies",
    description="Identifie les opportunités d'économies et d'optimisation des coûts"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, PurchaseReadPermission])
def cost_savings_opportunities(request):
    """Retourne les opportunités d'économies"""
    try:
        purchase_service = PurchaseService(request.user.tenant)
        savings_data = purchase_service.get_cost_savings_opportunities()

        return Response(savings_data)
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse des économies: {str(e)}")
        return Response(
            {'error': f'Erreur lors de l\'analyse: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
