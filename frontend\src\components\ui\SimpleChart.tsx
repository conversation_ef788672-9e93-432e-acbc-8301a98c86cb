import React from 'react';
import { motion } from 'framer-motion';

interface ChartData {
  name: string;
  value: number;
  value2?: number;
}

interface SimpleChartProps {
  title: string;
  data: ChartData[];
  type?: 'bar' | 'line' | 'area';
  height?: number;
  color?: string;
}

export const SimpleChart: React.FC<SimpleChartProps> = ({
  title,
  data,
  type = 'bar',
  height = 300,
  color = '#3B82F6'
}) => {
  const maxValue = Math.max(...data.map(d => Math.max(d.value, d.value2 || 0)));

  const renderBar = (item: ChartData, index: number) => {
    const barHeight = (item.value / maxValue) * (height - 100);
    const bar2Height = item.value2 ? (item.value2 / maxValue) * (height - 100) : 0;

    return (
      <div key={index} className="flex flex-col items-center gap-2">
        <div className="flex items-end gap-1" style={{ height: height - 60 }}>
          {item.value2 && (
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: bar2Height }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className="w-4 bg-blue-300 rounded-t"
              style={{ backgroundColor: `${color}80` }}
            />
          )}
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: barHeight }}
            transition={{ duration: 0.8, delay: index * 0.1 }}
            className="w-4 rounded-t"
            style={{ backgroundColor: color }}
          />
        </div>
        <div className="text-xs text-gray-600 text-center">{item.name}</div>
      </div>
    );
  };

  const renderLine = () => {
    const points = data.map((item, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = 100 - (item.value / maxValue) * 80;
      return `${x},${y}`;
    }).join(' ');

    const points2 = data.map((item, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = item.value2 ? 100 - (item.value2 / maxValue) * 80 : 100;
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="relative" style={{ height: height - 60 }}>
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <motion.polyline
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 2 }}
            fill="none"
            stroke={color}
            strokeWidth="2"
            points={points}
          />
          {data[0]?.value2 && (
            <motion.polyline
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 2, delay: 0.5 }}
              fill="none"
              stroke={`${color}80`}
              strokeWidth="2"
              points={points2}
            />
          )}
          {data.map((item, index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = 100 - (item.value / maxValue) * 80;
            return (
              <motion.circle
                key={index}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 + 1 }}
                cx={x}
                cy={y}
                r="2"
                fill={color}
              />
            );
          })}
        </svg>
        <div className="flex justify-between mt-2">
          {data.map((item, index) => (
            <div key={index} className="text-xs text-gray-600">{item.name}</div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-white p-6 rounded-xl shadow-lg"
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        <div className="flex gap-2">
          <div className="w-3 h-3 rounded-full" style={{ backgroundColor: color }}></div>
          {data[0]?.value2 && (
            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: `${color}80` }}></div>
          )}
        </div>
      </div>
      
      <div className="overflow-x-auto">
        {type === 'bar' ? (
          <div className="flex items-end justify-between gap-2 px-4">
            {data.map(renderBar)}
          </div>
        ) : (
          renderLine()
        )}
      </div>
    </motion.div>
  );
};
