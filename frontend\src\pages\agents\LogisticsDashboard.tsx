import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { SimpleMetricCard } from '../../components/ui/SimpleMetricCard';
import { SimpleChart } from '../../components/ui/SimpleChart';

interface LogisticsMetrics {
  totalShipments: number;
  onTimeDelivery: number;
  averageDeliveryTime: number;
  transportCosts: number;
  activeVehicles: number;
  warehouseUtilization: number;
}

interface Shipment {
  id: string;
  destination: string;
  items: number;
  weight: number;
  status: 'preparing' | 'in_transit' | 'delivered' | 'delayed';
  departureDate: string;
  estimatedArrival: string;
  driver: string;
  vehicle: string;
}

interface Vehicle {
  id: string;
  type: string;
  driver: string;
  status: 'available' | 'in_transit' | 'maintenance' | 'loading';
  currentLocation: string;
  capacity: number;
  fuelLevel: number;
  nextMaintenance: string;
}

export const LogisticsDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<LogisticsMetrics>({
    totalShipments: 342,
    onTimeDelivery: 94.5,
    averageDeliveryTime: 2.8,
    transportCosts: 125000,
    activeVehicles: 18,
    warehouseUtilization: 78.5
  });

  const [activeShipments] = useState<Shipment[]>([
    { id: 'SH-001', destination: 'Paris', items: 45, weight: 1200, status: 'in_transit', departureDate: '2024-01-27', estimatedArrival: '2024-01-28', driver: 'Jean Dupont', vehicle: 'VH-001' },
    { id: 'SH-002', destination: 'Lyon', items: 32, weight: 850, status: 'preparing', departureDate: '2024-01-28', estimatedArrival: '2024-01-29', driver: 'Marie Martin', vehicle: 'VH-003' },
    { id: 'SH-003', destination: 'Marseille', items: 28, weight: 950, status: 'in_transit', departureDate: '2024-01-26', estimatedArrival: '2024-01-28', driver: 'Pierre Durand', vehicle: 'VH-005' },
    { id: 'SH-004', destination: 'Toulouse', items: 38, weight: 1100, status: 'delayed', departureDate: '2024-01-25', estimatedArrival: '2024-01-28', driver: 'Sophie Blanc', vehicle: 'VH-007' },
    { id: 'SH-005', destination: 'Nantes', items: 22, weight: 650, status: 'delivered', departureDate: '2024-01-25', estimatedArrival: '2024-01-27', driver: 'Marc Petit', vehicle: 'VH-002' }
  ];

  const [vehicleFleet] = useState<Vehicle[]>([
    { id: 'VH-001', type: 'Camion 20T', driver: 'Jean Dupont', status: 'in_transit', currentLocation: 'A6 - Sens', capacity: 20000, fuelLevel: 65, nextMaintenance: '2024-02-15' },
    { id: 'VH-002', type: 'Camion 15T', driver: 'Marc Petit', status: 'available', currentLocation: 'Dépôt Central', capacity: 15000, fuelLevel: 90, nextMaintenance: '2024-02-20' },
    { id: 'VH-003', type: 'Camion 20T', driver: 'Marie Martin', status: 'loading', currentLocation: 'Dépôt Central', capacity: 20000, fuelLevel: 85, nextMaintenance: '2024-02-10' },
    { id: 'VH-004', type: 'Fourgon 5T', driver: 'Paul Moreau', status: 'maintenance', currentLocation: 'Garage', capacity: 5000, fuelLevel: 20, nextMaintenance: '2024-01-30' },
    { id: 'VH-005', type: 'Camion 25T', driver: 'Pierre Durand', status: 'in_transit', currentLocation: 'A7 - Valence', capacity: 25000, fuelLevel: 45, nextMaintenance: '2024-02-25' }
  ];

  const [deliveryData] = useState([
    { name: 'Lun', value: 45, value2: 42 },
    { name: 'Mar', value: 52, value2: 48 },
    { name: 'Mer', value: 38, value2: 36 },
    { name: 'Jeu', value: 61, value2: 58 },
    { name: 'Ven', value: 55, value2: 52 },
    { name: 'Sam', value: 28, value2: 26 },
    { name: 'Dim', value: 15, value2: 14 }
  ]);

  const [routeData] = useState([
    { name: 'Paris', value: 85 },
    { name: 'Lyon', value: 62 },
    { name: 'Marseille', value: 48 },
    { name: 'Toulouse', value: 35 },
    { name: 'Nantes', value: 28 },
    { name: 'Autres', value: 84 }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const getShipmentStatusColor = (status: string) => {
    switch (status) {
      case 'preparing': return 'bg-blue-100 text-blue-800';
      case 'in_transit': return 'bg-yellow-100 text-yellow-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'delayed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getVehicleStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'in_transit': return 'bg-blue-100 text-blue-800';
      case 'loading': return 'bg-yellow-100 text-yellow-800';
      case 'maintenance': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getFuelLevelColor = (level: number) => {
    if (level > 70) return 'bg-green-500';
    if (level > 30) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getVehicleIcon = (type: string) => {
    if (type.includes('Fourgon')) return '🚐';
    if (type.includes('25T')) return '🚛';
    return '🚚';
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-3xl">
              🚚
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Agent Logistics</h1>
              <p className="text-gray-600 text-lg">Gestion logistique et transport</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              🔄 Actualiser
            </button>
            <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              📊 Rapport
            </button>
          </div>
        </div>
      </motion.div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <SimpleMetricCard
          title="Expéditions"
          value={metrics.totalShipments}
          icon="📦"
          color="blue"
          trend="up"
          trendValue={12.3}
        />
        <SimpleMetricCard
          title="Livraisons à Temps"
          value={metrics.onTimeDelivery}
          unit="%"
          icon="⏰"
          color="green"
          trend="up"
          trendValue={2.1}
        />
        <SimpleMetricCard
          title="Délai Moyen"
          value={metrics.averageDeliveryTime}
          unit="j"
          icon="🕐"
          color="purple"
          trend="down"
          trendValue={-8.5}
        />
        <SimpleMetricCard
          title="Coûts Transport"
          value={`${(metrics.transportCosts / 1000).toFixed(0)}k€`}
          icon="💰"
          color="yellow"
          trend="down"
          trendValue={-5.2}
        />
        <SimpleMetricCard
          title="Véhicules Actifs"
          value={metrics.activeVehicles}
          icon="🚛"
          color="blue"
          trend="neutral"
          trendValue={0}
        />
        <SimpleMetricCard
          title="Utilisation Entrepôt"
          value={metrics.warehouseUtilization}
          unit="%"
          icon="🏭"
          color="green"
          trend="up"
          trendValue={4.7}
        />
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <SimpleChart
            title="Livraisons Hebdomadaires (Planifiées vs Réalisées)"
            data={deliveryData}
            type="line"
            height={350}
            color="#059669"
          />
        </div>
        <div>
          <SimpleChart
            title="Destinations Principales"
            data={routeData}
            type="bar"
            height={350}
            color="#10B981"
          />
        </div>
      </div>

      {/* Expéditions actives et flotte */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Expéditions actives */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold mb-4">Expéditions Actives</h3>
          <div className="space-y-4">
            {activeShipments.map((shipment, index) => (
              <motion.div
                key={shipment.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-blue-600">{shipment.id}</span>
                    <span className="text-gray-600">→ {shipment.destination}</span>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getShipmentStatusColor(shipment.status)}`}>
                    {shipment.status}
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">Articles: {shipment.items}</div>
                    <div className="text-gray-600">Poids: {shipment.weight}kg</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Chauffeur: {shipment.driver}</div>
                    <div className="text-gray-600">Véhicule: {shipment.vehicle}</div>
                  </div>
                </div>
                <div className="mt-2 text-sm">
                  <span className="text-gray-500">
                    Arrivée prévue: {new Date(shipment.estimatedArrival).toLocaleDateString('fr-FR')}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Flotte de véhicules */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold mb-4">Flotte de Véhicules</h3>
          <div className="space-y-4">
            {vehicleFleet.map((vehicle, index) => (
              <motion.div
                key={vehicle.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="text-2xl">{getVehicleIcon(vehicle.type)}</div>
                  <div>
                    <div className="font-semibold">{vehicle.id} - {vehicle.type}</div>
                    <div className="text-sm text-gray-600">{vehicle.driver}</div>
                    <div className="text-xs text-gray-500">{vehicle.currentLocation}</div>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getVehicleStatusColor(vehicle.status)}`}>
                    {vehicle.status}
                  </span>
                  <div className="mt-2 flex items-center gap-2">
                    <div className="text-xs text-gray-600">Carburant:</div>
                    <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className={`h-full ${getFuelLevelColor(vehicle.fuelLevel)} transition-all duration-300`}
                        style={{ width: `${vehicle.fuelLevel}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-600">{vehicle.fuelLevel}%</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
