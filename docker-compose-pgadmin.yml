
version: '3.8'

services:
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: erp_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - erp_network

volumes:
  pgadmin_data:

networks:
  erp_network:
    external: true
