<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP HUB - Démonstration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .nav {
            display: flex;
            gap: 1rem;
        }

        .nav button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .hero {
            text-align: center;
            margin-bottom: 4rem;
        }

        .hero h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 3rem;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .status-card .emoji {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
        }

        .status-card h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .status-card p {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .agent-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .agent-card:hover {
            transform: translateY(-10px) scale(1.02);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .agent-card .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .agent-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .agent-card .features {
            list-style: none;
            margin-bottom: 1rem;
        }

        .agent-card .features li {
            padding: 0.25rem 0;
            opacity: 0.9;
        }

        .agent-card .features li:before {
            content: "✓ ";
            color: #4caf50;
            font-weight: bold;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .demo-section h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
            text-align: center;
        }

        .features-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .feature-demo {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }

        .feature-demo .icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .cta-section {
            text-align: center;
            margin-top: 3rem;
        }

        .cta-button {
            background: linear-gradient(45deg, #4caf50, #45a049);
            border: none;
            color: white;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .footer {
            text-align: center;
            padding: 2rem;
            opacity: 0.7;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 3rem;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @media (max-width: 768px) {
            .hero h1 { font-size: 2.5rem; }
            .container { padding: 1rem; }
            .agents-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">
            <span>🚀</span>
            <span>ERP HUB</span>
        </div>
        <nav class="nav">
            <button onclick="changeBackground()">🎨 Arrière-plan</button>
            <button onclick="toggleTheme()">🌙 Thème</button>
            <button onclick="showNotification()">🔔 Notifications</button>
        </nav>
    </header>

    <div class="container">
        <section class="hero">
            <h1 class="floating">ERP HUB</h1>
            <p>Système ERP Intelligent avec Architecture Multi-Agent</p>
            <div class="status-grid">
                <div class="status-card">
                    <span class="emoji">✅</span>
                    <h3>Système Opérationnel</h3>
                    <p>Tous les agents fonctionnels</p>
                </div>
                <div class="status-card">
                    <span class="emoji">🚀</span>
                    <h3>Performance Optimale</h3>
                    <p>Interface moderne et rapide</p>
                </div>
                <div class="status-card">
                    <span class="emoji">🔒</span>
                    <h3>Sécurisé</h3>
                    <p>Authentification JWT</p>
                </div>
                <div class="status-card">
                    <span class="emoji">📱</span>
                    <h3>Responsive</h3>
                    <p>Compatible mobile/desktop</p>
                </div>
            </div>
        </section>

        <section class="demo-section">
            <h2>🤖 Agents Intelligents</h2>
            <div class="agents-grid">
                <div class="agent-card" onclick="showAgentDemo('manager')">
                    <span class="icon">👑</span>
                    <h3>Agent Manager</h3>
                    <ul class="features">
                        <li>Supervision système</li>
                        <li>Métriques globales</li>
                        <li>Statut des agents</li>
                        <li>Tableau de bord exécutif</li>
                    </ul>
                </div>

                <div class="agent-card" onclick="showAgentDemo('hr')">
                    <span class="icon">👥</span>
                    <h3>Agent HR</h3>
                    <ul class="features">
                        <li>Gestion employés</li>
                        <li>Recrutements</li>
                        <li>Satisfaction équipe</li>
                        <li>Analytics RH</li>
                    </ul>
                </div>

                <div class="agent-card" onclick="showAgentDemo('sales')">
                    <span class="icon">💼</span>
                    <h3>Agent Sales</h3>
                    <ul class="features">
                        <li>Pipeline commercial</li>
                        <li>Performance équipe</li>
                        <li>Prévisions ventes</li>
                        <li>Gestion prospects</li>
                    </ul>
                </div>

                <div class="agent-card" onclick="showAgentDemo('finance')">
                    <span class="icon">💰</span>
                    <h3>Agent Finance</h3>
                    <ul class="features">
                        <li>Ratios financiers</li>
                        <li>Trésorerie</li>
                        <li>DSO/DPO</li>
                        <li>Analyse rentabilité</li>
                    </ul>
                </div>

                <div class="agent-card" onclick="showAgentDemo('stock')">
                    <span class="icon">📦</span>
                    <h3>Agent Stock</h3>
                    <ul class="features">
                        <li>Inventaire intelligent</li>
                        <li>Alertes rupture</li>
                        <li>Mouvements temps réel</li>
                        <li>Optimisation stock</li>
                    </ul>
                </div>

                <div class="agent-card" onclick="showAgentDemo('crm')">
                    <span class="icon">🤝</span>
                    <h3>Agent CRM</h3>
                    <ul class="features">
                        <li>Relation client</li>
                        <li>Segmentation avancée</li>
                        <li>Interactions tracking</li>
                        <li>Satisfaction client</li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="demo-section">
            <h2>✨ Fonctionnalités Avancées</h2>
            <div class="features-showcase">
                <div class="feature-demo">
                    <div class="icon">🎨</div>
                    <h3>Arrière-plans Personnalisés</h3>
                    <p>Couleurs, dégradés, motifs et animations</p>
                </div>
                <div class="feature-demo">
                    <div class="icon">🌙</div>
                    <h3>Mode Sombre Intelligent</h3>
                    <p>Basculement automatique selon préférences</p>
                </div>
                <div class="feature-demo">
                    <div class="icon">🔔</div>
                    <h3>Notifications Temps Réel</h3>
                    <p>Centre de notifications avec actions</p>
                </div>
                <div class="feature-demo">
                    <div class="icon">📊</div>
                    <h3>Export Avancé</h3>
                    <p>PDF, Excel, CSV avec configuration</p>
                </div>
                <div class="feature-demo">
                    <div class="icon">🧩</div>
                    <h3>Widgets Déplaçables</h3>
                    <p>Interface drag & drop personnalisable</p>
                </div>
                <div class="feature-demo">
                    <div class="icon">🧪</div>
                    <h3>Tests Automatisés</h3>
                    <p>Suite de validation UX complète</p>
                </div>
            </div>
        </section>

        <section class="cta-section">
            <h2>Prêt à Découvrir ERP HUB ?</h2>
            <p>Explorez toutes les fonctionnalités de notre système ERP intelligent</p>
            <button class="cta-button" onclick="startDemo()">🚀 Lancer la Démo</button>
            <button class="cta-button" onclick="showDocumentation()">📚 Documentation</button>
        </section>
    </div>

    <footer class="footer">
        <p>© 2024 ERP HUB - Système ERP Intelligent Multi-Agent</p>
        <p>Développé avec React, Django, PostgreSQL et Intelligence Artificielle</p>
    </footer>

    <script>
        // Fonctions interactives
        function changeBackground() {
            const backgrounds = [
                'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
                'linear-gradient(135deg, #2196F3 0%, #21CBF3 100%)',
                'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
                'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)'
            ];
            const randomBg = backgrounds[Math.floor(Math.random() * backgrounds.length)];
            document.body.style.background = randomBg;
            showNotification('🎨 Arrière-plan changé !');
        }

        function toggleTheme() {
            document.body.style.filter = document.body.style.filter ? '' : 'invert(1) hue-rotate(180deg)';
            showNotification('🌙 Thème basculé !');
        }

        function showNotification(message = '🔔 Nouvelle notification !') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.9);
                color: #333;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        function showAgentDemo(agent) {
            const agentNames = {
                manager: 'Agent Manager - Supervision Système',
                hr: 'Agent HR - Ressources Humaines',
                sales: 'Agent Sales - Gestion Commerciale',
                finance: 'Agent Finance - Gestion Financière',
                stock: 'Agent Stock - Gestion Inventaire',
                crm: 'Agent CRM - Relation Client'
            };
            showNotification(`🤖 ${agentNames[agent]} sélectionné !`);
        }

        function startDemo() {
            showNotification('🚀 Démo ERP HUB lancée !');
            setTimeout(() => {
                showNotification('📊 Chargement des dashboards...');
            }, 1000);
            setTimeout(() => {
                showNotification('✅ Système prêt !');
            }, 2000);
        }

        function showDocumentation() {
            showNotification('📚 Documentation ERP HUB ouverte !');
        }

        // Animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Message de bienvenue
        setTimeout(() => {
            showNotification('🎉 Bienvenue dans ERP HUB !');
        }, 1000);
    </script>
</body>
</html>
