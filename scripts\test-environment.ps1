# Script de test de l'environnement ERP HUB
# Usage: .\scripts\test-environment.ps1

Write-Host "🧪 Test de l'environnement ERP HUB" -ForegroundColor Cyan

# Test 1: Vérification de Docker
Write-Host "`n1. Vérification de Docker..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "   ✅ Docker installé: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Docker non trouvé" -ForegroundColor Red
    exit 1
}

# Test 2: Vérification de Docker Compose
Write-Host "`n2. Vérification de Docker Compose..." -ForegroundColor Yellow
try {
    $composeVersion = docker-compose --version
    Write-Host "   ✅ Docker Compose installé: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Docker Compose non trouvé" -ForegroundColor Red
    exit 1
}

# Test 3: Vérification des fichiers de configuration
Write-Host "`n3. Vérification des fichiers de configuration..." -ForegroundColor Yellow

$requiredFiles = @(
    "docker-compose.yml",
    "docker-compose.dev.yml",
    ".env.example",
    "backend/Dockerfile",
    "backend/requirements.txt",
    "frontend/package.json",
    "frontend/Dockerfile"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "   ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $file manquant" -ForegroundColor Red
    }
}

# Test 4: Vérification du fichier .env
Write-Host "`n4. Vérification du fichier .env..." -ForegroundColor Yellow
if (Test-Path ".env") {
    Write-Host "   ✅ Fichier .env présent" -ForegroundColor Green
} else {
    Write-Host "   ⚠️  Fichier .env manquant, création à partir de .env.example" -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "   ✅ Fichier .env créé" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Fichier .env.example manquant" -ForegroundColor Red
    }
}

# Test 5: Test de construction des images Docker
Write-Host "`n5. Test de construction des images Docker..." -ForegroundColor Yellow
try {
    Write-Host "   🔨 Construction de l'image backend..." -ForegroundColor Cyan
    docker build -t erp-hub-backend-test ./backend -f ./backend/Dockerfile.dev
    Write-Host "   ✅ Image backend construite avec succès" -ForegroundColor Green
    
    Write-Host "   🔨 Construction de l'image frontend..." -ForegroundColor Cyan
    docker build -t erp-hub-frontend-test ./frontend -f ./frontend/Dockerfile.dev
    Write-Host "   ✅ Image frontend construite avec succès" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Erreur lors de la construction des images" -ForegroundColor Red
    Write-Host "   Détails: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Nettoyage des images de test
Write-Host "`n6. Nettoyage des images de test..." -ForegroundColor Yellow
try {
    docker rmi erp-hub-backend-test -f 2>$null
    docker rmi erp-hub-frontend-test -f 2>$null
    Write-Host "   ✅ Images de test supprimées" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Erreur lors du nettoyage (non critique)" -ForegroundColor Yellow
}

# Résumé
Write-Host "`n📊 Résumé du test:" -ForegroundColor Cyan
Write-Host "   - Docker et Docker Compose sont installés" -ForegroundColor Green
Write-Host "   - Tous les fichiers de configuration sont présents" -ForegroundColor Green
Write-Host "   - Les images Docker se construisent correctement" -ForegroundColor Green
Write-Host ""
Write-Host "🎉 Environnement prêt pour le développement!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Prochaines étapes:" -ForegroundColor Cyan
Write-Host "   1. Exécuter: .\scripts\dev-start.ps1" -ForegroundColor White
Write-Host "   2. Accéder à http://localhost:3000 (Frontend)" -ForegroundColor White
Write-Host "   3. Accéder à http://localhost:8000 (Backend)" -ForegroundColor White
