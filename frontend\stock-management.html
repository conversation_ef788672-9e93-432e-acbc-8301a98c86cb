<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Stock - Gestion Inventaire | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #84cc16 30%, #65a30d 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #84cc16;
            color: white;
        }
        
        .btn-primary:hover {
            background: #65a30d;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn-warning:hover {
            background: #d97706;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #84cc16 0%, #65a30d 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #84cc16;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #84cc16;
            box-shadow: 0 0 0 3px rgba(132, 204, 22, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .stock-level {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stock-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .stock-high {
            background: #10b981;
        }
        
        .stock-medium {
            background: #f59e0b;
        }
        
        .stock-low {
            background: #ef4444;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #84cc16;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">📦 Agent Stock - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="openModal('productModal')">
                <span class="material-icons" style="font-size: 1rem;">add</span>
                Nouveau Produit
            </button>
            <button class="btn btn-warning" onclick="openModal('movementModal')">
                <span class="material-icons" style="font-size: 1rem;">swap_horiz</span>
                Mouvement Stock
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion des Stocks</h1>
            <p class="page-subtitle">Inventaire, mouvements et suivi des produits</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalProducts">0</div>
                <div class="stat-label">Produits Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="lowStock">0</div>
                <div class="stat-label">Stock Faible</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalValue">0€</div>
                <div class="stat-label">Valeur Stock</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalMovements">0</div>
                <div class="stat-label">Mouvements Mois</div>
            </div>
        </div>

        <!-- Liste des produits -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Inventaire des Produits</h2>
                <button class="btn btn-primary" onclick="refreshProducts()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div id="alertContainer"></div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Référence</th>
                                <th>Nom Produit</th>
                                <th>Catégorie</th>
                                <th>Quantité</th>
                                <th>Emplacement</th>
                                <th>Prix Unitaire</th>
                                <th>Valeur</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Produit -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="productModalTitle">Nouveau Produit</h3>
                <button class="close-btn" onclick="closeModal('productModal')">&times;</button>
            </div>
            <form id="productForm">
                <div id="productModalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="productRef">Référence *</label>
                        <input type="text" id="productRef" name="productRef" class="form-input" required placeholder="Ex: REF-001">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="productName">Nom du produit *</label>
                        <input type="text" id="productName" name="productName" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="category">Catégorie *</label>
                        <select id="category" name="category" class="form-select" required>
                            <option value="">Sélectionner une catégorie</option>
                            <option value="Informatique">Informatique</option>
                            <option value="Mobilier">Mobilier</option>
                            <option value="Fournitures">Fournitures</option>
                            <option value="Matières premières">Matières premières</option>
                            <option value="Outillage">Outillage</option>
                            <option value="Emballage">Emballage</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="quantity">Quantité initiale</label>
                        <input type="number" id="quantity" name="quantity" class="form-input" min="0" step="1" value="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="minStock">Stock minimum</label>
                        <input type="number" id="minStock" name="minStock" class="form-input" min="0" step="1" value="5">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="maxStock">Stock maximum</label>
                        <input type="number" id="maxStock" name="maxStock" class="form-input" min="0" step="1" value="100">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="unitPrice">Prix unitaire (€)</label>
                        <input type="number" id="unitPrice" name="unitPrice" class="form-input" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="location">Emplacement</label>
                        <select id="location" name="location" class="form-select">
                            <option value="">Sélectionner un emplacement</option>
                            <option value="Entrepôt A">Entrepôt A</option>
                            <option value="Entrepôt B">Entrepôt B</option>
                            <option value="Magasin">Magasin</option>
                            <option value="Bureau">Bureau</option>
                            <option value="Atelier">Atelier</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="supplier">Fournisseur</label>
                        <input type="text" id="supplier" name="supplier" class="form-input" placeholder="Nom du fournisseur">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="description">Description</label>
                    <textarea id="description" name="description" class="form-textarea" placeholder="Description du produit..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('productModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveProductBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal Mouvement Stock -->
    <div id="movementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Mouvement de Stock</h3>
                <button class="close-btn" onclick="closeModal('movementModal')">&times;</button>
            </div>
            <form id="movementForm">
                <div id="movementModalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="movementProduct">Produit *</label>
                        <select id="movementProduct" name="movementProduct" class="form-select" required>
                            <option value="">Sélectionner un produit</option>
                            <!-- Options chargées dynamiquement -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementType">Type de mouvement *</label>
                        <select id="movementType" name="movementType" class="form-select" required>
                            <option value="">Sélectionner un type</option>
                            <option value="in">Entrée (+)</option>
                            <option value="out">Sortie (-)</option>
                            <option value="adjustment">Ajustement</option>
                            <option value="transfer">Transfert</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementQuantity">Quantité *</label>
                        <input type="number" id="movementQuantity" name="movementQuantity" class="form-input" required min="1" step="1">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementDate">Date</label>
                        <input type="date" id="movementDate" name="movementDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementReason">Motif</label>
                        <select id="movementReason" name="movementReason" class="form-select">
                            <option value="">Sélectionner un motif</option>
                            <option value="purchase">Achat</option>
                            <option value="sale">Vente</option>
                            <option value="return">Retour</option>
                            <option value="damage">Dommage</option>
                            <option value="inventory">Inventaire</option>
                            <option value="production">Production</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementLocation">Emplacement</label>
                        <select id="movementLocation" name="movementLocation" class="form-select">
                            <option value="">Sélectionner un emplacement</option>
                            <option value="Entrepôt A">Entrepôt A</option>
                            <option value="Entrepôt B">Entrepôt B</option>
                            <option value="Magasin">Magasin</option>
                            <option value="Bureau">Bureau</option>
                            <option value="Atelier">Atelier</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="movementNotes">Notes</label>
                    <textarea id="movementNotes" name="movementNotes" class="form-textarea" placeholder="Notes sur le mouvement..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('movementModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveMovementBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let products = [];
        let movements = [];
        let editingProductId = null;
        let isLoading = false;

        // Données de démonstration réalistes
        const demoProducts = [
            {
                id: 'PROD-001',
                ref: 'LAP-DELL-5540',
                name: 'Ordinateur portable Dell Latitude 5540',
                category: 'Informatique',
                quantity: 25,
                minStock: 5,
                maxStock: 50,
                unitPrice: 800.00,
                location: 'Entrepôt A',
                supplier: 'TechSupply Co',
                description: 'Ordinateur portable professionnel 15.6", Intel i5, 8GB RAM, 256GB SSD'
            },
            {
                id: 'PROD-002',
                ref: 'CHR-ERG-001',
                name: 'Chaise ergonomique de bureau',
                category: 'Mobilier',
                quantity: 12,
                minStock: 3,
                maxStock: 30,
                unitPrice: 180.00,
                location: 'Entrepôt B',
                supplier: 'Office Solutions',
                description: 'Chaise ergonomique avec support lombaire, accoudoirs réglables'
            },
            {
                id: 'PROD-003',
                ref: 'PAP-A4-500',
                name: 'Papier A4 blanc 80g (ramette 500 feuilles)',
                category: 'Fournitures',
                quantity: 150,
                minStock: 20,
                maxStock: 200,
                unitPrice: 4.50,
                location: 'Magasin',
                supplier: 'Office Solutions',
                description: 'Papier blanc standard pour impression et photocopie'
            },
            {
                id: 'PROD-004',
                ref: 'ALU-PLAQ-2MM',
                name: 'Plaque aluminium 2mm',
                category: 'Matières premières',
                quantity: 8,
                minStock: 5,
                maxStock: 25,
                unitPrice: 35.00,
                location: 'Atelier',
                supplier: 'Global Materials',
                description: 'Plaque aluminium 1000x500mm épaisseur 2mm pour usinage'
            },
            {
                id: 'PROD-005',
                ref: 'ECR-DELL-24',
                name: 'Écran Dell 24 pouces Full HD',
                category: 'Informatique',
                quantity: 18,
                minStock: 5,
                maxStock: 40,
                unitPrice: 220.00,
                location: 'Entrepôt A',
                supplier: 'TechSupply Co',
                description: 'Écran LED 24" 1920x1080, connecteurs HDMI/VGA/USB'
            },
            {
                id: 'PROD-006',
                ref: 'OUT-PERC-SET',
                name: 'Set perceuse sans fil + accessoires',
                category: 'Outillage',
                quantity: 6,
                minStock: 2,
                maxStock: 15,
                unitPrice: 95.00,
                location: 'Atelier',
                supplier: 'Industrial Parts',
                description: 'Perceuse 18V avec batterie, chargeur et coffret d\'accessoires'
            },
            {
                id: 'PROD-007',
                ref: 'EMB-CART-M',
                name: 'Cartons d\'emballage taille M',
                category: 'Emballage',
                quantity: 3,
                minStock: 10,
                maxStock: 100,
                unitPrice: 2.50,
                location: 'Entrepôt B',
                supplier: 'Global Materials',
                description: 'Cartons 30x20x15cm pour expédition produits moyens'
            },
            {
                id: 'PROD-008',
                ref: 'SRV-DELL-R750',
                name: 'Serveur Dell PowerEdge R750',
                category: 'Informatique',
                quantity: 1,
                minStock: 1,
                maxStock: 3,
                unitPrice: 4500.00,
                location: 'Bureau',
                supplier: 'TechSupply Co',
                description: 'Serveur rack 2U, Intel Xeon, 32GB RAM, 2x1TB SSD'
            },
            {
                id: 'PROD-009',
                ref: 'LIC-OFF-365',
                name: 'Licence Microsoft Office 365',
                category: 'Fournitures',
                quantity: 45,
                minStock: 10,
                maxStock: 100,
                unitPrice: 120.00,
                location: 'Bureau',
                supplier: 'Office Solutions',
                description: 'Licence annuelle Office 365 Business Standard'
            },
            {
                id: 'PROD-010',
                ref: 'BUR-DESK-120',
                name: 'Bureau droit 120cm',
                category: 'Mobilier',
                quantity: 8,
                minStock: 2,
                maxStock: 20,
                unitPrice: 150.00,
                location: 'Entrepôt B',
                supplier: 'Office Solutions',
                description: 'Bureau droit 120x60cm, plateau mélaminé blanc, pieds métal'
            }
        ];

        const demoMovements = [
            {
                id: 'MOV-001',
                productId: 'PROD-001',
                productName: 'Ordinateur portable Dell Latitude 5540',
                type: 'in',
                quantity: 10,
                date: '2024-01-15',
                reason: 'purchase',
                location: 'Entrepôt A',
                notes: 'Réception commande PO-2024-001'
            },
            {
                id: 'MOV-002',
                productId: 'PROD-003',
                productName: 'Papier A4 blanc 80g',
                type: 'out',
                quantity: 25,
                date: '2024-01-20',
                reason: 'sale',
                location: 'Magasin',
                notes: 'Distribution aux services'
            },
            {
                id: 'MOV-003',
                productId: 'PROD-007',
                productName: 'Cartons d\'emballage taille M',
                type: 'adjustment',
                quantity: -7,
                date: '2024-01-22',
                reason: 'inventory',
                location: 'Entrepôt B',
                notes: 'Correction inventaire - cartons endommagés'
            }
        ];

        function showAlert(message, type = 'error', container = 'alertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            if (modalId === 'productModal') {
                document.getElementById('productModalTitle').textContent = editingProductId ? 'Modifier Produit' : 'Nouveau Produit';
            } else if (modalId === 'movementModal') {
                // Charger la liste des produits dans le select
                loadProductsInSelect();
                // Définir la date d'aujourd'hui par défaut
                document.getElementById('movementDate').value = new Date().toISOString().split('T')[0];
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            if (modalId === 'productModal') {
                document.getElementById('productForm').reset();
                document.getElementById('productModalAlertContainer').innerHTML = '';
                editingProductId = null;
            } else if (modalId === 'movementModal') {
                document.getElementById('movementForm').reset();
                document.getElementById('movementModalAlertContainer').innerHTML = '';
            }
        }

        function loadProductsInSelect() {
            const select = document.getElementById('movementProduct');
            select.innerHTML = '<option value="">Sélectionner un produit</option>';

            products.forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = `${product.ref} - ${product.name}`;
                select.appendChild(option);
            });
        }

        function updateStats() {
            const total = products.length;
            const lowStock = products.filter(p => p.quantity <= p.minStock).length;
            const totalValue = products.reduce((sum, p) => sum + (p.quantity * p.unitPrice), 0);

            // Mouvements du mois en cours
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();
            const monthlyMovements = movements.filter(m => {
                const moveDate = new Date(m.date);
                return moveDate.getMonth() === currentMonth && moveDate.getFullYear() === currentYear;
            }).length;

            document.getElementById('totalProducts').textContent = total;
            document.getElementById('lowStock').textContent = lowStock;
            document.getElementById('totalValue').textContent = totalValue.toLocaleString() + '€';
            document.getElementById('totalMovements').textContent = monthlyMovements;
        }

        function getStockStatus(product) {
            if (product.quantity <= product.minStock) {
                return { level: 'low', class: 'stock-low', text: 'Stock faible' };
            } else if (product.quantity >= product.maxStock * 0.8) {
                return { level: 'high', class: 'stock-high', text: 'Stock élevé' };
            } else {
                return { level: 'medium', class: 'stock-medium', text: 'Stock normal' };
            }
        }

        function renderProductsTable() {
            const tbody = document.getElementById('productsTableBody');

            if (products.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun produit trouvé. Cliquez sur "Nouveau Produit" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = products.map(product => {
                const stockStatus = getStockStatus(product);
                const totalValue = (product.quantity * product.unitPrice).toFixed(2);

                return `
                    <tr>
                        <td>${product.ref}</td>
                        <td>${product.name}</td>
                        <td>${product.category}</td>
                        <td>
                            <div class="stock-level">
                                <div class="stock-indicator ${stockStatus.class}"></div>
                                ${product.quantity}
                            </div>
                        </td>
                        <td>${product.location || '-'}</td>
                        <td>${product.unitPrice ? product.unitPrice.toFixed(2) + '€' : '-'}</td>
                        <td>${totalValue}€</td>
                        <td><span class="badge ${stockStatus.level === 'low' ? 'badge-danger' : stockStatus.level === 'high' ? 'badge-success' : 'badge-warning'}">${stockStatus.text}</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editProduct('${product.id}')" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteProduct('${product.id}')" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        async function loadProducts() {
            try {
                // Tentative de chargement depuis l'API
                const token = localStorage.getItem('access_token');
                if (token) {
                    const response = await fetch('http://localhost:8000/api/agents/stock/products/', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        products = await response.json();
                    } else {
                        throw new Error('Erreur API');
                    }
                } else {
                    throw new Error('Pas de token');
                }
            } catch (error) {
                console.log('Chargement des données de démonstration');
                products = [...demoProducts];
                movements = [...demoMovements];
            }

            renderProductsTable();
            updateStats();
        }

        function refreshProducts() {
            loadProducts();
            showAlert('Données actualisées avec succès', 'success');
        }

        function editProduct(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            editingProductId = id;

            // Remplir le formulaire
            document.getElementById('productRef').value = product.ref;
            document.getElementById('productName').value = product.name;
            document.getElementById('category').value = product.category;
            document.getElementById('quantity').value = product.quantity;
            document.getElementById('minStock').value = product.minStock;
            document.getElementById('maxStock').value = product.maxStock;
            document.getElementById('unitPrice').value = product.unitPrice || '';
            document.getElementById('location').value = product.location || '';
            document.getElementById('supplier').value = product.supplier || '';
            document.getElementById('description').value = product.description || '';

            openModal('productModal');
        }

        function deleteProduct(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
                products = products.filter(p => p.id !== id);
                renderProductsTable();
                updateStats();
                showAlert('Produit supprimé avec succès', 'success');
            }
        }

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Gestion du formulaire produit
        document.getElementById('productForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const productData = {
                ref: formData.get('productRef'),
                name: formData.get('productName'),
                category: formData.get('category'),
                quantity: parseInt(formData.get('quantity')) || 0,
                minStock: parseInt(formData.get('minStock')) || 0,
                maxStock: parseInt(formData.get('maxStock')) || 0,
                unitPrice: parseFloat(formData.get('unitPrice')) || 0,
                location: formData.get('location'),
                supplier: formData.get('supplier'),
                description: formData.get('description')
            };

            try {
                if (editingProductId) {
                    // Modification
                    const index = products.findIndex(p => p.id === editingProductId);
                    if (index !== -1) {
                        products[index] = { ...products[index], ...productData };
                        showAlert('Produit modifié avec succès', 'success');
                    }
                } else {
                    // Création
                    const newProduct = {
                        id: `PROD-${String(products.length + 1).padStart(3, '0')}`,
                        ...productData
                    };
                    products.push(newProduct);
                    showAlert('Produit créé avec succès', 'success');
                }

                renderProductsTable();
                updateStats();
                closeModal('productModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'productModalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Gestion du formulaire mouvement
        document.getElementById('movementForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const productId = formData.get('movementProduct');
            const product = products.find(p => p.id === productId);

            if (!product) {
                showAlert('Produit non trouvé', 'error', 'movementModalAlertContainer');
                isLoading = false;
                return;
            }

            const movementData = {
                productId: productId,
                productName: product.name,
                type: formData.get('movementType'),
                quantity: parseInt(formData.get('movementQuantity')),
                date: formData.get('movementDate'),
                reason: formData.get('movementReason'),
                location: formData.get('movementLocation'),
                notes: formData.get('movementNotes')
            };

            try {
                // Calculer la nouvelle quantité
                let newQuantity = product.quantity;
                if (movementData.type === 'in') {
                    newQuantity += movementData.quantity;
                } else if (movementData.type === 'out') {
                    newQuantity -= movementData.quantity;
                    if (newQuantity < 0) {
                        showAlert('Stock insuffisant pour cette sortie', 'error', 'movementModalAlertContainer');
                        isLoading = false;
                        return;
                    }
                } else if (movementData.type === 'adjustment') {
                    newQuantity = movementData.quantity;
                }

                // Mettre à jour le stock du produit
                const productIndex = products.findIndex(p => p.id === productId);
                if (productIndex !== -1) {
                    products[productIndex].quantity = newQuantity;
                }

                // Ajouter le mouvement à l'historique
                const newMovement = {
                    id: `MOV-${String(movements.length + 1).padStart(3, '0')}`,
                    ...movementData
                };
                movements.push(newMovement);

                renderProductsTable();
                updateStats();
                closeModal('movementModal');
                showAlert('Mouvement de stock enregistré avec succès', 'success');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement du mouvement', 'error', 'movementModalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadProducts();
            }
        });
    </script>
</body>
</html>
