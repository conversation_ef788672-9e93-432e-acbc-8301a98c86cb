"""
Modèles pour l'Agent Sales - Gestion des Ventes
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal

from core.models import TimeStampedModel, UUIDModel, Tenant, User


class Customer(UUIDModel, TimeStampedModel):
    """Modèle pour les clients"""
    
    CUSTOMER_TYPES = [
        ('individual', _('Particulier')),
        ('company', _('Entreprise')),
        ('government', _('Administration')),
    ]
    
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='customers',
        verbose_name=_("Tenant")
    )
    
    # Informations de base
    customer_code = models.CharField(_("Code client"), max_length=20, unique=True)
    customer_type = models.CharField(_("Type de client"), max_length=20, choices=CUSTOMER_TYPES)
    
    # Informations contact
    name = models.CharField(_("Nom/Raison sociale"), max_length=200)
    email = models.EmailField(_("Email"), blank=True)
    phone = models.CharField(_("Téléphone"), max_length=20, blank=True)
    website = models.URLField(_("Site web"), blank=True)
    
    # Adresse
    address_line1 = models.CharField(_("Adresse ligne 1"), max_length=200, blank=True)
    address_line2 = models.CharField(_("Adresse ligne 2"), max_length=200, blank=True)
    city = models.CharField(_("Ville"), max_length=100, blank=True)
    postal_code = models.CharField(_("Code postal"), max_length=20, blank=True)
    country = models.CharField(_("Pays"), max_length=100, blank=True)
    
    # Informations commerciales
    credit_limit = models.DecimalField(
        _("Limite de crédit"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )
    payment_terms = models.PositiveIntegerField(_("Délai de paiement (jours)"), default=30)
    
    # Commercial assigné
    sales_rep = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_customers',
        verbose_name=_("Commercial")
    )
    
    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)
    
    class Meta:
        verbose_name = _("Client")
        verbose_name_plural = _("Clients")
        unique_together = ['tenant', 'customer_code']
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.customer_code})"


class Product(UUIDModel, TimeStampedModel):
    """Modèle pour les produits"""
    
    PRODUCT_TYPES = [
        ('product', _('Produit')),
        ('service', _('Service')),
        ('bundle', _('Pack')),
    ]
    
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='products',
        verbose_name=_("Tenant")
    )
    
    # Informations de base
    product_code = models.CharField(_("Code produit"), max_length=50)
    name = models.CharField(_("Nom"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    product_type = models.CharField(_("Type"), max_length=20, choices=PRODUCT_TYPES, default='product')
    
    # Catégorie
    category = models.CharField(_("Catégorie"), max_length=100, blank=True)
    
    # Prix
    unit_price = models.DecimalField(
        _("Prix unitaire"),
        max_digits=10,
        decimal_places=2
    )
    cost_price = models.DecimalField(
        _("Prix de revient"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    
    # Unité de mesure
    unit_of_measure = models.CharField(_("Unité de mesure"), max_length=20, default='pcs')
    
    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)
    is_sellable = models.BooleanField(_("Vendable"), default=True)
    
    class Meta:
        verbose_name = _("Produit")
        verbose_name_plural = _("Produits")
        unique_together = ['tenant', 'product_code']
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.product_code})"
    
    @property
    def margin_percentage(self):
        """Calcule le pourcentage de marge"""
        if self.cost_price and self.cost_price > 0:
            return ((self.unit_price - self.cost_price) / self.unit_price) * 100
        return 0


class Opportunity(UUIDModel, TimeStampedModel):
    """Modèle pour les opportunités commerciales"""
    
    STAGES = [
        ('lead', _('Prospect')),
        ('qualified', _('Qualifié')),
        ('proposal', _('Proposition')),
        ('negotiation', _('Négociation')),
        ('won', _('Gagné')),
        ('lost', _('Perdu')),
    ]
    
    PRIORITIES = [
        (1, _('Très basse')),
        (2, _('Basse')),
        (3, _('Normale')),
        (4, _('Haute')),
        (5, _('Critique')),
    ]
    
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='opportunities',
        verbose_name=_("Tenant")
    )
    
    # Informations de base
    title = models.CharField(_("Titre"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    
    # Client
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='opportunities',
        verbose_name=_("Client")
    )
    
    # Commercial
    sales_rep = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='opportunities',
        verbose_name=_("Commercial")
    )
    
    # Détails commerciaux
    estimated_value = models.DecimalField(
        _("Valeur estimée"),
        max_digits=12,
        decimal_places=2
    )
    probability = models.PositiveIntegerField(
        _("Probabilité (%)"),
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=50
    )
    
    # Étapes et timing
    stage = models.CharField(_("Étape"), max_length=20, choices=STAGES, default='lead')
    priority = models.PositiveIntegerField(_("Priorité"), choices=PRIORITIES, default=3)
    expected_close_date = models.DateField(_("Date de clôture prévue"))
    actual_close_date = models.DateField(_("Date de clôture réelle"), null=True, blank=True)
    
    # Suivi
    last_contact_date = models.DateField(_("Dernier contact"), null=True, blank=True)
    next_action = models.TextField(_("Prochaine action"), blank=True)
    
    class Meta:
        verbose_name = _("Opportunité")
        verbose_name_plural = _("Opportunités")
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} - {self.customer.name}"
    
    @property
    def weighted_value(self):
        """Calcule la valeur pondérée par la probabilité"""
        return self.estimated_value * (self.probability / 100)


class Quote(UUIDModel, TimeStampedModel):
    """Modèle pour les devis"""
    
    STATUS_CHOICES = [
        ('draft', _('Brouillon')),
        ('sent', _('Envoyé')),
        ('accepted', _('Accepté')),
        ('rejected', _('Rejeté')),
        ('expired', _('Expiré')),
    ]
    
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='quotes',
        verbose_name=_("Tenant")
    )
    
    # Numérotation
    quote_number = models.CharField(_("Numéro de devis"), max_length=50, unique=True)
    
    # Client et opportunité
    customer = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name='quotes',
        verbose_name=_("Client")
    )
    opportunity = models.ForeignKey(
        Opportunity,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='quotes',
        verbose_name=_("Opportunité")
    )
    
    # Commercial
    sales_rep = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='quotes',
        verbose_name=_("Commercial")
    )
    
    # Dates
    quote_date = models.DateField(_("Date du devis"))
    valid_until = models.DateField(_("Valide jusqu'au"))
    
    # Statut
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Totaux (calculés automatiquement)
    subtotal = models.DecimalField(
        _("Sous-total"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )
    tax_amount = models.DecimalField(
        _("Montant TVA"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_amount = models.DecimalField(
        _("Montant total"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )
    
    # Notes
    notes = models.TextField(_("Notes"), blank=True)
    terms_conditions = models.TextField(_("Conditions générales"), blank=True)
    
    class Meta:
        verbose_name = _("Devis")
        verbose_name_plural = _("Devis")
        ordering = ['-quote_date']
    
    def __str__(self):
        return f"Devis {self.quote_number} - {self.customer.name}"


class QuoteItem(UUIDModel, TimeStampedModel):
    """Modèle pour les lignes de devis"""
    
    quote = models.ForeignKey(
        Quote,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_("Devis")
    )
    
    product = models.ForeignKey(
        Product,
        on_delete=models.PROTECT,
        related_name='quote_items',
        verbose_name=_("Produit")
    )
    
    # Détails de la ligne
    description = models.TextField(_("Description"), blank=True)
    quantity = models.DecimalField(_("Quantité"), max_digits=10, decimal_places=3)
    unit_price = models.DecimalField(_("Prix unitaire"), max_digits=10, decimal_places=2)
    discount_percentage = models.DecimalField(
        _("Remise (%)"),
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00')
    )
    
    # Montants calculés
    line_total = models.DecimalField(
        _("Total ligne"),
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00')
    )
    
    class Meta:
        verbose_name = _("Ligne de devis")
        verbose_name_plural = _("Lignes de devis")
        ordering = ['id']
    
    def save(self, *args, **kwargs):
        """Calcule automatiquement le total de la ligne"""
        subtotal = self.quantity * self.unit_price
        discount_amount = subtotal * (self.discount_percentage / 100)
        self.line_total = subtotal - discount_amount
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.product.name} x {self.quantity}"
