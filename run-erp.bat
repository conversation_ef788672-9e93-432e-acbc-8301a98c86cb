@echo off
cls
echo.
echo ========================================
echo    🚀 DÉMARRAGE ERP HUB
echo ========================================
echo.

echo 📊 Vérification des prérequis...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python n'est pas installé
    pause
    exit /b 1
)

echo ✅ Python détecté
echo.

echo 📡 Démarrage du backend Django...
start "Django Backend" cmd /k "cd backend && python manage.py runserver 8000"
echo ⏳ Attente du démarrage Django (5 secondes)...
timeout /t 5 /nobreak >nul

echo.
echo 🌐 Démarrage du serveur frontend...
start "Frontend Server" cmd /k "cd frontend && python python-server.py"
echo ⏳ Attente du démarrage frontend (3 secondes)...
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo    ✅ ERP HUB DÉMARRÉ !
echo ========================================
echo.
echo 📊 Services actifs :
echo    🔹 Django Backend : http://localhost:8000
echo    🔹 Frontend : http://localhost:3000
echo    🔹 Health Check : http://localhost:3000/health
echo.
echo 🌐 Ouverture de l'application...
start http://localhost:3000

echo.
echo 📝 Note : Gardez cette fenêtre ouverte
echo    Les serveurs fonctionnent en arrière-plan
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
