import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'
import { 
  BuildingStorefrontIcon, 
  CubeIcon, 
  ArrowsRightLeftIcon,
  ClipboardDocumentListIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

import { stockApi, type StockDashboard, type StockItem, type StockLevel, type StockMovement } from '@/services/api/agentsApi'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import Button from '@/components/ui/Button'

const StockPage: React.FC = () => {
  const queryClient = useQueryClient()
  const [selectedTab, setSelectedTab] = useState<'overview' | 'items' | 'levels' | 'movements' | 'inventories'>('overview')

  // Récupération des données du dashboard
  const { data: dashboard, isLoading, error, refetch } = useQuery<StockDashboard>(
    'stock-dashboard',
    stockApi.getDashboard,
    {
      refetchInterval: 60000, // Actualisation toutes les minutes
      staleTime: 30000,
    }
  )

  // Récupération des articles en stock
  const { data: stockItems } = useQuery<StockItem[]>(
    'stock-items',
    () => stockApi.getStockItems({ active: true }),
    {
      enabled: selectedTab === 'items',
    }
  )

  // Récupération des niveaux de stock
  const { data: stockLevels } = useQuery<StockLevel[]>(
    'stock-levels',
    () => stockApi.getStockLevels({ has_stock: true }),
    {
      enabled: selectedTab === 'levels',
    }
  )

  // Récupération des mouvements récents
  const { data: recentMovements } = useQuery<StockMovement[]>(
    'recent-movements',
    () => stockApi.getStockMovements({ 
      date_from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] 
    }),
    {
      enabled: selectedTab === 'movements',
    }
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Erreur de chargement</h3>
        <p className="mt-1 text-sm text-gray-500">
          Impossible de charger les données de stock
        </p>
        <div className="mt-6">
          <Button onClick={() => refetch()}>Réessayer</Button>
        </div>
      </div>
    )
  }

  if (!dashboard) {
    return null
  }

  const tabs = [
    { id: 'overview', name: 'Vue d\'ensemble', icon: ClipboardDocumentListIcon },
    { id: 'items', name: 'Articles', icon: CubeIcon },
    { id: 'levels', name: 'Niveaux', icon: BuildingStorefrontIcon },
    { id: 'movements', name: 'Mouvements', icon: ArrowsRightLeftIcon },
    { id: 'inventories', name: 'Inventaires', icon: ClipboardDocumentListIcon },
  ]

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-gradient-to-r from-green-500 to-teal-600 rounded-lg shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Agent Stock</h1>
            <p className="mt-2 text-green-100">
              Gestion des stocks et inventaires - {dashboard.tenant}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">
              {(dashboard.value.total_stock_value / 1000).toFixed(0)}k €
            </div>
            <div className="text-sm text-green-100">Valeur totale du stock</div>
          </div>
        </div>
      </div>

      {/* Métriques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Articles en stock"
          value={dashboard.items.in_stock.toString()}
          total={dashboard.items.total}
          color="blue"
          icon={CubeIcon}
        />
        <MetricCard
          title="Entrepôts actifs"
          value={dashboard.warehouses.active.toString()}
          total={dashboard.warehouses.total}
          color="green"
          icon={BuildingStorefrontIcon}
        />
        <MetricCard
          title="Mouvements (30j)"
          value={dashboard.movements.last_30_days.toString()}
          color="purple"
          icon={ArrowsRightLeftIcon}
        />
        <MetricCard
          title="Rotation moyenne"
          value={`${dashboard.value.average_turnover.toFixed(1)}x`}
          color="orange"
          icon={ClipboardDocumentListIcon}
        />
      </div>

      {/* Alertes de stock */}
      {(dashboard.alerts.low_stock > 0 || dashboard.alerts.out_of_stock > 0) && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-orange-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-orange-800">
                Alertes de stock
              </h3>
              <div className="mt-2 text-sm text-orange-700">
                <ul className="list-disc list-inside space-y-1">
                  {dashboard.alerts.low_stock > 0 && (
                    <li>{dashboard.alerts.low_stock} article(s) en stock faible</li>
                  )}
                  {dashboard.alerts.out_of_stock > 0 && (
                    <li>{dashboard.alerts.out_of_stock} article(s) en rupture de stock</li>
                  )}
                  {dashboard.alerts.expired_reservations > 0 && (
                    <li>{dashboard.alerts.expired_reservations} réservation(s) expirée(s)</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation par onglets */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`
                  flex items-center py-2 px-1 border-b-2 font-medium text-sm
                  ${selectedTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Contenu des onglets */}
      <div className="mt-6">
        {selectedTab === 'overview' && (
          <OverviewTab dashboard={dashboard} />
        )}
        {selectedTab === 'items' && (
          <ItemsTab items={stockItems} />
        )}
        {selectedTab === 'levels' && (
          <LevelsTab levels={stockLevels} />
        )}
        {selectedTab === 'movements' && (
          <MovementsTab movements={recentMovements} />
        )}
        {selectedTab === 'inventories' && (
          <InventoriesTab />
        )}
      </div>
    </div>
  )
}

// Composant pour les métriques
interface MetricCardProps {
  title: string
  value: string
  total?: number
  color: 'blue' | 'green' | 'red' | 'yellow' | 'orange' | 'purple'
  icon: React.ComponentType<{ className?: string }>
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, total, color, icon: Icon }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-700 border-blue-200',
    green: 'bg-green-50 text-green-700 border-green-200',
    red: 'bg-red-50 text-red-700 border-red-200',
    yellow: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    orange: 'bg-orange-50 text-orange-700 border-orange-200',
    purple: 'bg-purple-50 text-purple-700 border-purple-200',
  }

  return (
    <div className={`card border ${colorClasses[color]}`}>
      <div className="card-body">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-8 w-8" />
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium opacity-75">{title}</p>
            <p className="text-2xl font-bold">
              {value}
              {total && <span className="text-sm font-normal opacity-75">/{total}</span>}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Onglet Vue d'ensemble
const OverviewTab: React.FC<{ dashboard: StockDashboard }> = ({ dashboard }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Statistiques des stocks */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Statistiques des Stocks</h3>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Articles totaux</span>
              <span className="font-semibold">{dashboard.items.total}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Articles en stock</span>
              <span className="font-semibold">{dashboard.items.in_stock}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Valeur totale</span>
              <span className="font-semibold">{(dashboard.value.total_stock_value / 1000).toFixed(0)}k €</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Rotation moyenne</span>
              <span className="font-semibold">{dashboard.value.average_turnover.toFixed(1)}x</span>
            </div>
          </div>
        </div>
      </div>

      {/* Activités récentes */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Activités Récentes</h3>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            {dashboard.recent_activities.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.type.includes('receipt') ? 'bg-green-400' : 
                  activity.type.includes('issue') ? 'bg-red-400' : 'bg-blue-400'
                }`} />
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <div className="flex justify-between items-center mt-1">
                    <p className="text-xs text-gray-500">
                      {new Date(activity.date).toLocaleDateString('fr-FR')}
                    </p>
                    <p className="text-xs font-medium text-green-600">
                      {activity.location}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Onglet Articles
const ItemsTab: React.FC<{ items?: StockItem[] }> = ({ items }) => {
  if (!items) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Articles en Stock</h3>
      </div>
      <div className="card-body">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Article
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock actuel
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valeur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Classification
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {items.slice(0, 20).map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{item.name}</div>
                      <div className="text-sm text-gray-500">{item.sku}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="badge badge-secondary">
                      {item.item_type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className={`font-medium ${
                        item.current_stock <= item.minimum_stock ? 'text-red-600' : 'text-gray-900'
                      }`}>
                        {item.current_stock.toFixed(0)} {item.unit_of_measure}
                      </div>
                      <div className="text-xs text-gray-500">Min: {item.minimum_stock}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {(item.stock_value / 1000).toFixed(1)}k €
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {item.abc_classification && (
                      <span className={`badge ${
                        item.abc_classification === 'A' ? 'badge-danger' :
                        item.abc_classification === 'B' ? 'badge-warning' : 'badge-secondary'
                      }`}>
                        Classe {item.abc_classification}
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

// Onglet Niveaux
const LevelsTab: React.FC<{ levels?: StockLevel[] }> = ({ levels }) => {
  if (!levels) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Niveaux de Stock par Emplacement</h3>
      </div>
      <div className="card-body">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Article
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Emplacement
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  En stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Réservé
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Disponible
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valeur
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {levels.slice(0, 20).map((level) => (
                <tr key={level.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{level.item_name}</div>
                      <div className="text-sm text-gray-500">{level.item_sku}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{level.location_code}</div>
                      <div className="text-sm text-gray-500">{level.warehouse_name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {level.quantity_on_hand.toFixed(0)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {level.quantity_reserved.toFixed(0)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <span className={level.quantity_available <= 0 ? 'text-red-600 font-medium' : ''}>
                      {level.quantity_available.toFixed(0)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {(level.total_value / 1000).toFixed(1)}k €
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

// Onglet Mouvements
const MovementsTab: React.FC<{ movements?: StockMovement[] }> = ({ movements }) => {
  if (!movements) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Mouvements Récents (7 derniers jours)</h3>
      </div>
      <div className="card-body">
        <div className="space-y-4">
          {movements.slice(0, 20).map((movement) => (
            <div key={movement.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className={`badge ${
                      movement.movement_type === 'receipt' ? 'badge-success' :
                      movement.movement_type === 'issue' ? 'badge-danger' :
                      movement.movement_type === 'transfer' ? 'badge-warning' : 'badge-secondary'
                    }`}>
                      {movement.movement_type}
                    </span>
                    <span className="text-sm font-medium text-gray-900">{movement.movement_number}</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {movement.item_name} ({movement.item_sku})
                  </p>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                    <span>Quantité: {movement.quantity}</span>
                    <span>Date: {new Date(movement.movement_date).toLocaleDateString('fr-FR')}</span>
                    {movement.location_from_code && (
                      <span>De: {movement.location_from_code}</span>
                    )}
                    {movement.location_to_code && (
                      <span>Vers: {movement.location_to_code}</span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {movement.user_name}
                  </div>
                  {movement.total_cost && (
                    <div className="text-sm text-gray-500">
                      {(movement.total_cost / 1000).toFixed(1)}k €
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Onglet Inventaires
const InventoriesTab: React.FC = () => {
  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Inventaires</h3>
      </div>
      <div className="card-body">
        <div className="text-center py-8">
          <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Inventaires</h3>
          <p className="mt-1 text-sm text-gray-500">
            Fonctionnalité d'inventaire en cours de développement
          </p>
        </div>
      </div>
    </div>
  )
}

export default StockPage
