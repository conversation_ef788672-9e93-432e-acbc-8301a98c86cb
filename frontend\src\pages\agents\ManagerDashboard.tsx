import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  LinearProgress,
  Alert,
  Button,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Dashboard,
  TrendingUp,
  People,
  Assessment,
  Notifications,
  Settings,
  Refresh,
  FullscreenExit,
  Warning,
  CheckCircle,
  Speed
} from '@mui/icons-material';
import { MetricCard } from '../../components/ui/MetricCard';
import { AnimatedChart } from '../../components/ui/AnimatedChart';
import { StatusIndicator, StatusGroup } from '../../components/ui/StatusIndicator';
import { DashboardGrid } from '../../components/ui/DashboardGrid';

interface AgentStatus {
  id: string;
  name: string;
  status: 'active' | 'warning' | 'error' | 'pending';
  performance: number;
  lastUpdate: string;
  tasksCompleted: number;
  icon: string;
}

interface SystemMetrics {
  totalTransactions: number;
  activeUsers: number;
  systemHealth: number;
  responseTime: number;
  errorRate: number;
  uptime: number;
}

export const ManagerDashboard: React.FC = () => {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    totalTransactions: 15847,
    activeUsers: 342,
    systemHealth: 98.5,
    responseTime: 245,
    errorRate: 0.12,
    uptime: 99.8
  });

  const [agentsStatus, setAgentsStatus] = useState<AgentStatus[]>([
    { id: 'hr', name: 'Agent HR', status: 'active', performance: 95, lastUpdate: '2 min', tasksCompleted: 127, icon: '👥' },
    { id: 'sales', name: 'Agent Sales', status: 'active', performance: 92, lastUpdate: '1 min', tasksCompleted: 89, icon: '💼' },
    { id: 'purchase', name: 'Agent Purchase', status: 'warning', performance: 78, lastUpdate: '5 min', tasksCompleted: 45, icon: '🛒' },
    { id: 'logistics', name: 'Agent Logistics', status: 'active', performance: 88, lastUpdate: '3 min', tasksCompleted: 67, icon: '🚚' },
    { id: 'stock', name: 'Agent Stock', status: 'active', performance: 94, lastUpdate: '1 min', tasksCompleted: 156, icon: '📦' },
    { id: 'accounting', name: 'Agent Accounting', status: 'active', performance: 97, lastUpdate: '2 min', tasksCompleted: 203, icon: '📊' },
    { id: 'finance', name: 'Agent Finance', status: 'active', performance: 91, lastUpdate: '4 min', tasksCompleted: 78, icon: '💰' },
    { id: 'crm', name: 'Agent CRM', status: 'active', performance: 89, lastUpdate: '2 min', tasksCompleted: 134, icon: '🤝' },
    { id: 'bi', name: 'Agent BI', status: 'error', performance: 45, lastUpdate: '15 min', tasksCompleted: 23, icon: '📈' }
  ]);

  const [performanceData] = useState([
    { name: '00:00', value: 85, value2: 78 },
    { name: '04:00', value: 89, value2: 82 },
    { name: '08:00', value: 94, value2: 88 },
    { name: '12:00', value: 98, value2: 95 },
    { name: '16:00', value: 96, value2: 92 },
    { name: '20:00', value: 92, value2: 89 },
    { name: '24:00', value: 88, value2: 85 }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Simulation de mise à jour en temps réel
    const interval = setInterval(() => {
      setSystemMetrics(prev => ({
        ...prev,
        totalTransactions: prev.totalTransactions + Math.floor(Math.random() * 10),
        activeUsers: prev.activeUsers + Math.floor(Math.random() * 5) - 2,
        responseTime: prev.responseTime + Math.floor(Math.random() * 20) - 10
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleRefresh = async () => {
    setIsLoading(true);
    // Simulation d'une requête API
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const getActiveAgentsCount = () => agentsStatus.filter(agent => agent.status === 'active').length;
  const getWarningAgentsCount = () => agentsStatus.filter(agent => agent.status === 'warning').length;
  const getErrorAgentsCount = () => agentsStatus.filter(agent => agent.status === 'error').length;

  const statusSummary = [
    { id: 'active', status: 'active' as const, label: 'Actifs', count: getActiveAgentsCount() },
    { id: 'warning', status: 'warning' as const, label: 'Attention', count: getWarningAgentsCount() },
    { id: 'error', status: 'error' as const, label: 'Erreurs', count: getErrorAgentsCount() }
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
              <Dashboard fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={700} color="primary">
                Agent Manager
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Supervision et orchestration du système ERP
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={1}>
            <Tooltip title="Actualiser les données">
              <IconButton onClick={handleRefresh} disabled={isLoading}>
                <Refresh />
              </IconButton>
            </Tooltip>
            <Button variant="outlined" startIcon={<Settings />}>
              Configuration
            </Button>
            <Button variant="contained" startIcon={<Assessment />}>
              Rapport Complet
            </Button>
          </Box>
        </Box>
      </motion.div>

      {/* Alertes système */}
      {getErrorAgentsCount() > 0 && (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Alert 
            severity="error" 
            sx={{ mb: 3 }}
            action={
              <Button color="inherit" size="small">
                Résoudre
              </Button>
            }
          >
            {getErrorAgentsCount()} agent(s) en erreur nécessitent votre attention
          </Alert>
        </motion.div>
      )}

      {/* Métriques principales */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Transactions"
            value={systemMetrics.totalTransactions}
            icon={<TrendingUp />}
            color="primary"
            trend="up"
            trendValue={12.5}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Utilisateurs Actifs"
            value={systemMetrics.activeUsers}
            icon={<People />}
            color="secondary"
            trend="up"
            trendValue={8.2}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Santé Système"
            value={systemMetrics.systemHealth}
            unit="%"
            icon={<Speed />}
            color="success"
            trend="up"
            trendValue={2.1}
            format="percentage"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Temps de Réponse"
            value={systemMetrics.responseTime}
            unit="ms"
            icon={<Assessment />}
            color="info"
            trend="down"
            trendValue={-5.3}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
      </Grid>

      {/* Statut des agents */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6" fontWeight={600}>
                État des Agents
              </Typography>
              <StatusGroup statuses={statusSummary} />
            </Box>
            
            <Grid container spacing={2}>
              {agentsStatus.map((agent, index) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={agent.id}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Card 
                      variant="outlined"
                      sx={{ 
                        height: '100%',
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: 3
                        }
                      }}
                    >
                      <CardContent sx={{ p: 2 }}>
                        <Box display="flex" alignItems="center" gap={2} mb={2}>
                          <Typography variant="h4">{agent.icon}</Typography>
                          <Box flex={1}>
                            <Typography variant="subtitle2" fontWeight={600}>
                              {agent.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Mis à jour il y a {agent.lastUpdate}
                            </Typography>
                          </Box>
                          <StatusIndicator status={agent.status} size="small" />
                        </Box>
                        
                        <Box mb={2}>
                          <Box display="flex" justifyContent="space-between" mb={1}>
                            <Typography variant="caption">Performance</Typography>
                            <Typography variant="caption" fontWeight={600}>
                              {agent.performance}%
                            </Typography>
                          </Box>
                          <LinearProgress 
                            variant="determinate" 
                            value={agent.performance}
                            sx={{
                              height: 6,
                              borderRadius: 3,
                              backgroundColor: '#f0f0f0',
                              '& .MuiLinearProgress-bar': {
                                borderRadius: 3,
                                backgroundColor: agent.status === 'active' ? '#4caf50' : 
                                                agent.status === 'warning' ? '#ff9800' : '#f44336'
                              }
                            }}
                          />
                        </Box>
                        
                        <Typography variant="caption" color="text.secondary">
                          {agent.tasksCompleted} tâches complétées
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </motion.div>

      {/* Graphique de performance */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <AnimatedChart
            title="Performance Système (24h)"
            subtitle="Comparaison performance actuelle vs objectif"
            data={performanceData}
            type="area"
            height={350}
            color="#1976d2"
            secondaryColor="#42a5f5"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight={600} mb={3}>
                Métriques Détaillées
              </Typography>
              
              <Box mb={3}>
                <Typography variant="body2" color="text.secondary" mb={1}>
                  Taux d'erreur
                </Typography>
                <Typography variant="h5" color="success.main" fontWeight={600}>
                  {systemMetrics.errorRate}%
                </Typography>
              </Box>
              
              <Box mb={3}>
                <Typography variant="body2" color="text.secondary" mb={1}>
                  Disponibilité
                </Typography>
                <Typography variant="h5" color="primary.main" fontWeight={600}>
                  {systemMetrics.uptime}%
                </Typography>
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary" mb={1}>
                  Prochaine maintenance
                </Typography>
                <Chip 
                  label="Dans 3 jours" 
                  color="info" 
                  size="small"
                  icon={<Notifications />}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};
