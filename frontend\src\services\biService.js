import api from './api';

class BIService {
  // Dashboard et statut
  async getStatus() {
    const response = await api.get('/agents/bi/status/');
    return response.data;
  }

  async getDashboard() {
    const response = await api.get('/agents/bi/dashboard/');
    return response.data;
  }

  async getInsights() {
    const response = await api.get('/agents/bi/insights/');
    return response.data;
  }

  // KPIs
  async getKPIs(params = {}) {
    const response = await api.get('/agents/bi/kpis/', { params });
    return response.data;
  }

  async getKPI(id) {
    const response = await api.get(`/agents/bi/kpis/${id}/`);
    return response.data;
  }

  async createKPI(kpiData) {
    const response = await api.post('/agents/bi/kpis/create/', kpiData);
    return response.data;
  }

  async updateKPI(id, kpiData) {
    const response = await api.put(`/agents/bi/kpis/${id}/`, kpiData);
    return response.data;
  }

  async deleteKPI(id) {
    const response = await api.delete(`/agents/bi/kpis/${id}/`);
    return response.data;
  }

  async calculateKPI(id) {
    const response = await api.post(`/agents/bi/kpis/${id}/calculate/`);
    return response.data;
  }

  // Alertes
  async getAlerts(params = {}) {
    const response = await api.get('/agents/bi/alerts/', { params });
    return response.data;
  }

  async getAlert(id) {
    const response = await api.get(`/agents/bi/alerts/${id}/`);
    return response.data;
  }

  async createAlert(alertData) {
    const response = await api.post('/agents/bi/alerts/', alertData);
    return response.data;
  }

  async updateAlert(id, alertData) {
    const response = await api.put(`/agents/bi/alerts/${id}/`, alertData);
    return response.data;
  }

  async deleteAlert(id) {
    const response = await api.delete(`/agents/bi/alerts/${id}/`);
    return response.data;
  }

  // Tâches d'analyse
  async getAnalysisJobs(params = {}) {
    const response = await api.get('/agents/bi/analysis-jobs/', { params });
    return response.data;
  }

  async getAnalysisJob(id) {
    const response = await api.get(`/agents/bi/analysis-jobs/${id}/`);
    return response.data;
  }

  async createAnalysisJob(jobData) {
    const response = await api.post('/agents/bi/analysis-jobs/', jobData);
    return response.data;
  }

  async updateAnalysisJob(id, jobData) {
    const response = await api.put(`/agents/bi/analysis-jobs/${id}/`, jobData);
    return response.data;
  }

  async deleteAnalysisJob(id) {
    const response = await api.delete(`/agents/bi/analysis-jobs/${id}/`);
    return response.data;
  }

  // Utilitaires pour les choix
  getKPITypes() {
    return [
      { value: 'financial', label: 'Financier' },
      { value: 'sales', label: 'Commercial' },
      { value: 'operational', label: 'Opérationnel' },
      { value: 'customer', label: 'Client' },
      { value: 'hr', label: 'Ressources humaines' },
      { value: 'quality', label: 'Qualité' },
      { value: 'efficiency', label: 'Efficacité' },
      { value: 'growth', label: 'Croissance' }
    ];
  }

  getCalculationMethods() {
    return [
      { value: 'sum', label: 'Somme' },
      { value: 'average', label: 'Moyenne' },
      { value: 'count', label: 'Comptage' },
      { value: 'min', label: 'Minimum' },
      { value: 'max', label: 'Maximum' },
      { value: 'percentage', label: 'Pourcentage' },
      { value: 'ratio', label: 'Ratio' },
      { value: 'custom', label: 'Personnalisé' }
    ];
  }

  getTrendDirections() {
    return [
      { value: 'up', label: 'Hausse' },
      { value: 'down', label: 'Baisse' },
      { value: 'stable', label: 'Stable' },
      { value: 'unknown', label: 'Inconnu' }
    ];
  }

  getAlertTypes() {
    return [
      { value: 'threshold', label: 'Seuil' },
      { value: 'anomaly', label: 'Anomalie' },
      { value: 'trend', label: 'Tendance' },
      { value: 'data_quality', label: 'Qualité des données' },
      { value: 'performance', label: 'Performance' },
      { value: 'custom', label: 'Personnalisé' }
    ];
  }

  getAlertSeverities() {
    return [
      { value: 'info', label: 'Information' },
      { value: 'warning', label: 'Avertissement' },
      { value: 'critical', label: 'Critique' },
      { value: 'emergency', label: 'Urgence' }
    ];
  }

  getAlertStatuses() {
    return [
      { value: 'active', label: 'Actif' },
      { value: 'triggered', label: 'Déclenché' },
      { value: 'acknowledged', label: 'Acquitté' },
      { value: 'resolved', label: 'Résolu' },
      { value: 'disabled', label: 'Désactivé' }
    ];
  }

  getJobTypes() {
    return [
      { value: 'data_refresh', label: 'Rafraîchissement des données' },
      { value: 'kpi_calculation', label: 'Calcul des KPIs' },
      { value: 'report_generation', label: 'Génération de rapport' },
      { value: 'alert_check', label: 'Vérification des alertes' },
      { value: 'data_export', label: 'Export de données' },
      { value: 'analysis', label: 'Analyse' },
      { value: 'ml_training', label: 'Entraînement ML' },
      { value: 'prediction', label: 'Prédiction' }
    ];
  }

  getJobStatuses() {
    return [
      { value: 'pending', label: 'En attente' },
      { value: 'running', label: 'En cours' },
      { value: 'completed', label: 'Terminé' },
      { value: 'failed', label: 'Échec' },
      { value: 'cancelled', label: 'Annulé' }
    ];
  }

  getPriorityLevels() {
    return [
      { value: 'low', label: 'Faible' },
      { value: 'normal', label: 'Normale' },
      { value: 'high', label: 'Élevée' },
      { value: 'urgent', label: 'Urgente' }
    ];
  }

  getDataSourceTypes() {
    return [
      { value: 'database', label: 'Base de données' },
      { value: 'api', label: 'API REST' },
      { value: 'file', label: 'Fichier' },
      { value: 'agent', label: 'Agent ERP' },
      { value: 'external', label: 'Source externe' },
      { value: 'manual', label: 'Saisie manuelle' }
    ];
  }

  getConnectionStatuses() {
    return [
      { value: 'connected', label: 'Connecté' },
      { value: 'disconnected', label: 'Déconnecté' },
      { value: 'error', label: 'Erreur' },
      { value: 'testing', label: 'Test en cours' }
    ];
  }

  getDatasetTypes() {
    return [
      { value: 'table', label: 'Table' },
      { value: 'view', label: 'Vue' },
      { value: 'query', label: 'Requête' },
      { value: 'aggregation', label: 'Agrégation' },
      { value: 'calculation', label: 'Calcul' },
      { value: 'external', label: 'Externe' }
    ];
  }

  getReportTypes() {
    return [
      { value: 'table', label: 'Tableau' },
      { value: 'chart', label: 'Graphique' },
      { value: 'dashboard', label: 'Tableau de bord' },
      { value: 'kpi', label: 'Indicateur KPI' },
      { value: 'pivot', label: 'Tableau croisé' },
      { value: 'export', label: 'Export' }
    ];
  }

  getChartTypes() {
    return [
      { value: 'line', label: 'Ligne' },
      { value: 'bar', label: 'Barres' },
      { value: 'column', label: 'Colonnes' },
      { value: 'pie', label: 'Camembert' },
      { value: 'doughnut', label: 'Anneau' },
      { value: 'area', label: 'Aires' },
      { value: 'scatter', label: 'Nuage de points' },
      { value: 'gauge', label: 'Jauge' },
      { value: 'funnel', label: 'Entonnoir' },
      { value: 'heatmap', label: 'Carte de chaleur' }
    ];
  }

  getDashboardTypes() {
    return [
      { value: 'executive', label: 'Exécutif' },
      { value: 'operational', label: 'Opérationnel' },
      { value: 'analytical', label: 'Analytique' },
      { value: 'financial', label: 'Financier' },
      { value: 'sales', label: 'Commercial' },
      { value: 'hr', label: 'RH' },
      { value: 'custom', label: 'Personnalisé' }
    ];
  }

  getMetricTypes() {
    return [
      { value: 'usage', label: 'Utilisation' },
      { value: 'performance', label: 'Performance' },
      { value: 'data_quality', label: 'Qualité des données' },
      { value: 'user_engagement', label: 'Engagement utilisateur' },
      { value: 'system_health', label: 'Santé du système' },
      { value: 'business_impact', label: 'Impact business' }
    ];
  }

  getActivityTypes() {
    return [
      { value: 'dashboard_view', label: 'Vue tableau de bord' },
      { value: 'report_view', label: 'Vue rapport' },
      { value: 'report_export', label: 'Export rapport' },
      { value: 'kpi_check', label: 'Consultation KPI' },
      { value: 'alert_acknowledge', label: 'Acquittement alerte' },
      { value: 'data_query', label: 'Requête données' },
      { value: 'filter_apply', label: 'Application filtre' },
      { value: 'share', label: 'Partage' }
    ];
  }
}

export const biService = new BIService();
