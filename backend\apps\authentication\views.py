from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.utils import timezone
from .serializers import UserRegistrationSerializer, UserProfileSerializer
import logging

logger = logging.getLogger(__name__)

class CustomTokenObtainPairView(TokenObtainPairView):
    """Vue personnalisée pour l'obtention de tokens JWT"""
    
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        
        if response.status_code == 200:
            user = authenticate(
                username=request.data.get('username'),
                password=request.data.get('password')
            )
            
            if user:
                # Mise à jour de la dernière connexion
                user.last_login = timezone.now()
                user.save()
                
                # Ajout d'informations utilisateur à la réponse
                response.data.update({
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'is_staff': user.is_staff,
                        'is_superuser': user.is_superuser,
                        'last_login': user.last_login
                    }
                })
                
                logger.info(f"Connexion réussie pour l'utilisateur: {user.username}")
        
        return response

@api_view(['POST'])
@permission_classes([AllowAny])
def register_user(request):
    """Inscription d'un nouvel utilisateur"""
    try:
        serializer = UserRegistrationSerializer(data=request.data)
        
        if serializer.is_valid():
            user = serializer.save()
            
            # Génération des tokens JWT
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token
            
            logger.info(f"Nouvel utilisateur créé: {user.username}")
            
            return Response({
                'status': 'success',
                'message': 'Utilisateur créé avec succès',
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name
                },
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(access_token)
                }
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'status': 'error',
            'message': 'Données invalides',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except Exception as e:
        logger.error(f"Erreur lors de l'inscription: {str(e)}")
        return Response({
            'status': 'error',
            'message': 'Erreur interne du serveur'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """Profil de l'utilisateur connecté"""
    try:
        serializer = UserProfileSerializer(request.user)
        
        return Response({
            'status': 'success',
            'user': serializer.data
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du profil: {str(e)}")
        return Response({
            'status': 'error',
            'message': 'Erreur interne du serveur'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_profile(request):
    """Mise à jour du profil utilisateur"""
    try:
        serializer = UserProfileSerializer(
            request.user, 
            data=request.data, 
            partial=True
        )
        
        if serializer.is_valid():
            serializer.save()
            
            logger.info(f"Profil mis à jour pour: {request.user.username}")
            
            return Response({
                'status': 'success',
                'message': 'Profil mis à jour avec succès',
                'user': serializer.data
            })
        
        return Response({
            'status': 'error',
            'message': 'Données invalides',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du profil: {str(e)}")
        return Response({
            'status': 'error',
            'message': 'Erreur interne du serveur'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_user(request):
    """Déconnexion de l'utilisateur"""
    try:
        refresh_token = request.data.get('refresh_token')
        
        if refresh_token:
            token = RefreshToken(refresh_token)
            token.blacklist()
        
        logger.info(f"Déconnexion de l'utilisateur: {request.user.username}")
        
        return Response({
            'status': 'success',
            'message': 'Déconnexion réussie'
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de la déconnexion: {str(e)}")
        return Response({
            'status': 'error',
            'message': 'Erreur lors de la déconnexion'
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_permissions(request):
    """Permissions de l'utilisateur pour les agents"""
    try:
        user = request.user
        
        # Définition des permissions par défaut
        permissions = {
            'manager': user.is_staff or user.is_superuser,
            'hr': user.has_perm('agents.view_hragent'),
            'sales': user.has_perm('agents.view_salesagent'),
            'purchase': user.has_perm('agents.view_purchaseagent'),
            'logistics': user.has_perm('agents.view_logisticsagent'),
            'stock': user.has_perm('agents.view_stockagent'),
            'accounting': user.has_perm('agents.view_accountingagent'),
            'finance': user.has_perm('agents.view_financeagent'),
            'crm': user.has_perm('agents.view_crmagent'),
            'bi': user.has_perm('agents.view_biagent')
        }
        
        # Si superuser, accès à tout
        if user.is_superuser:
            permissions = {key: True for key in permissions.keys()}
        
        return Response({
            'status': 'success',
            'permissions': permissions,
            'is_admin': user.is_staff,
            'is_superuser': user.is_superuser
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des permissions: {str(e)}")
        return Response({
            'status': 'error',
            'message': 'Erreur interne du serveur'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def auth_status(request):
    """Statut de l'authentification"""
    if request.user.is_authenticated:
        return Response({
            'authenticated': True,
            'user': {
                'id': request.user.id,
                'username': request.user.username,
                'email': request.user.email,
                'is_staff': request.user.is_staff
            }
        })
    
    return Response({
        'authenticated': False
    })

@api_view(['POST'])
@permission_classes([AllowAny])
def refresh_token(request):
    """Rafraîchissement du token d'accès"""
    try:
        refresh_token = request.data.get('refresh')
        
        if not refresh_token:
            return Response({
                'status': 'error',
                'message': 'Token de rafraîchissement requis'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        token = RefreshToken(refresh_token)
        access_token = token.access_token
        
        return Response({
            'status': 'success',
            'access': str(access_token)
        })
        
    except Exception as e:
        logger.error(f"Erreur lors du rafraîchissement du token: {str(e)}")
        return Response({
            'status': 'error',
            'message': 'Token invalide'
        }, status=status.HTTP_401_UNAUTHORIZED)
