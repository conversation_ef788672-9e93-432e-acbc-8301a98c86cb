import React from 'react';
import { motion } from 'framer-motion';

interface SimpleMetricCardProps {
  title: string;
  value: number | string;
  unit?: string;
  icon?: string;
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple';
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: number;
}

export const SimpleMetricCard: React.FC<SimpleMetricCardProps> = ({
  title,
  value,
  unit = '',
  icon = '📊',
  color = 'blue',
  trend,
  trendValue
}) => {
  const getColorClasses = () => {
    switch (color) {
      case 'green': return 'bg-gradient-to-br from-green-500 to-green-600 text-white';
      case 'red': return 'bg-gradient-to-br from-red-500 to-red-600 text-white';
      case 'yellow': return 'bg-gradient-to-br from-yellow-500 to-yellow-600 text-white';
      case 'purple': return 'bg-gradient-to-br from-purple-500 to-purple-600 text-white';
      default: return 'bg-gradient-to-br from-blue-500 to-blue-600 text-white';
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return '📈';
      case 'down': return '📉';
      default: return '➡️';
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up': return 'text-green-400';
      case 'down': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.02 }}
      className={`p-6 rounded-xl shadow-lg ${getColorClasses()} relative overflow-hidden`}
    >
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
      
      <div className="flex items-center justify-between mb-4">
        <div className="text-3xl">{icon}</div>
        <div className="text-right">
          <div className="text-sm opacity-80">{title}</div>
        </div>
      </div>
      
      <div className="mb-2">
        <div className="text-3xl font-bold">
          {typeof value === 'number' ? value.toLocaleString('fr-FR') : value}
          {unit && <span className="text-lg ml-1">{unit}</span>}
        </div>
      </div>
      
      {trend && trendValue !== undefined && (
        <div className="flex items-center gap-1">
          <span className="text-lg">{getTrendIcon()}</span>
          <span className={`text-sm font-medium ${getTrendColor()}`}>
            {trendValue > 0 ? '+' : ''}{trendValue.toFixed(1)}%
          </span>
          <span className="text-xs opacity-70 ml-1">vs période précédente</span>
        </div>
      )}
    </motion.div>
  );
};
