import React from 'react'
import { useAuthStore } from '@/store/authStore'
import Button from '@/components/ui/Button'
import { BackgroundSelector } from '@/components/ui/BackgroundSelector'
import { ThemeToggle } from '@/components/ui/ThemeToggle'
import { NotificationCenter } from '@/components/notifications/NotificationCenter'

interface DashboardLayoutProps {
  children: React.ReactNode
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { user, logout } = useAuthStore()

  const handleLogout = () => {
    logout()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo et titre */}
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">ERP HUB</h1>
              <span className="ml-4 px-2 py-1 text-xs bg-primary-100 text-primary-800 rounded-full">
                {user?.tenant_info.name}
              </span>
            </div>

            {/* Navigation utilisateur */}
            <div className="flex items-center space-x-4">
              <BackgroundSelector />
              <ThemeToggle />
              <NotificationCenter />
              <span className="text-sm text-gray-700">
                Bonjour, {user?.first_name || user?.username}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
              >
                Déconnexion
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation principale */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 h-12 items-center">
            <a href="/dashboard" className="nav-link-active">
              Dashboard
            </a>
            <a href="/agents/manager" className="nav-link-inactive">
              Agent Manager
            </a>
            <a href="/agents/hr" className="nav-link-inactive">
              RH
            </a>
            <a href="/agents/sales" className="nav-link-inactive">
              Ventes
            </a>
            <a href="/agents/purchase" className="nav-link-inactive">
              Achats
            </a>
            <a href="/agents/stock" className="nav-link-inactive">
              Stock
            </a>
            <a href="/agents/accounting" className="nav-link-inactive">
              Comptabilité
            </a>
            <a href="/profile" className="nav-link-inactive">
              Profil
            </a>
          </div>
        </div>
      </nav>

      {/* Contenu principal */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {children}
      </main>
    </div>
  )
}

export default DashboardLayout
