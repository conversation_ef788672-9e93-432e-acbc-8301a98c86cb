// Configuration pour la scalabilité de l'ERP
export const SCALABILITY_CONFIG = {
  // Configuration des agents
  agents: {
    // Chargement paresseux des agents
    lazyLoading: true,
    // Cache des composants d'agents
    cacheComponents: true,
    // Préchargement des agents critiques
    preloadCritical: ['manager', 'hr', 'sales'],
    // Limite de mémoire par agent (MB)
    memoryLimit: 50,
  },

  // Configuration des performances
  performance: {
    // Virtualisation des listes longues
    virtualizeThreshold: 100,
    // Debounce pour les recherches (ms)
    searchDebounce: 300,
    // Cache des requêtes API (minutes)
    apiCacheTime: 5,
    // Pagination par défaut
    defaultPageSize: 25,
    // Limite de rendu simultané
    concurrentRenderLimit: 3,
  },

  // Configuration du cache
  cache: {
    // Stratégies de cache
    strategies: {
      dashboard: 'stale-while-revalidate',
      agents: 'cache-first',
      reports: 'network-first',
      realtime: 'no-cache',
    },
    // TTL par type de données (minutes)
    ttl: {
      dashboard: 5,
      agents: 15,
      reports: 30,
      static: 1440, // 24h
    },
  },

  // Configuration du code splitting
  codeSplitting: {
    // Routes à charger paresseusement
    lazyRoutes: [
      '/agents/accounting',
      '/agents/finance',
      '/agents/bi',
      '/agents/crm',
      '/reports',
      '/analytics',
    ],
    // Préchargement des routes critiques
    preloadRoutes: [
      '/dashboard',
      '/agents/manager',
      '/agents/hr',
    ],
  },

  // Configuration de la surveillance
  monitoring: {
    // Métriques de performance
    trackPerformance: true,
    // Surveillance de la mémoire
    memoryMonitoring: true,
    // Alertes de performance
    performanceAlerts: {
      renderTime: 100, // ms
      bundleSize: 500, // KB
      memoryUsage: 100, // MB
    },
  },

  // Configuration multi-tenant
  multiTenant: {
    // Support multi-entreprise
    enabled: true,
    // Isolation des données
    dataIsolation: 'strict',
    // Cache par tenant
    tenantCache: true,
  },

  // Configuration de l'internationalisation
  i18n: {
    // Langues supportées
    supportedLocales: ['fr', 'en', 'es', 'de'],
    // Chargement paresseux des traductions
    lazyLoadTranslations: true,
    // Fallback par défaut
    fallbackLocale: 'fr',
  },

  // Configuration de la sécurité
  security: {
    // Chiffrement des données sensibles
    encryptSensitiveData: true,
    // Validation des permissions par agent
    agentPermissions: true,
    // Audit des actions utilisateur
    auditTrail: true,
  },
}

// Types pour TypeScript
export interface AgentConfig {
  id: string
  name: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  memoryLimit: number
  preload: boolean
  permissions: string[]
}

export interface PerformanceMetrics {
  renderTime: number
  bundleSize: number
  memoryUsage: number
  cacheHitRate: number
}

export interface ScalabilityMetrics {
  activeAgents: number
  totalUsers: number
  dataVolume: number
  responseTime: number
}
