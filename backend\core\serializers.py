"""
Serializers pour l'API REST de l'application core
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import User, Tenant, Role, UserRole


class TenantSerializer(serializers.ModelSerializer):
    """Serializer pour les tenants"""
    
    class Meta:
        model = Tenant
        fields = [
            'id', 'name', 'slug', 'description', 'email', 'phone', 'website',
            'address_line1', 'address_line2', 'city', 'postal_code', 'country',
            'is_active', 'max_users', 'enabled_modules', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class RoleSerializer(serializers.ModelSerializer):
    """Serializer pour les rôles"""
    
    class Meta:
        model = Role
        fields = [
            'id', 'name', 'description', 'is_system_role', 'permissions',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'is_system_role']


class UserRoleSerializer(serializers.ModelSerializer):
    """Serializer pour les attributions de rôles"""
    role_name = serializers.CharField(source='role.name', read_only=True)
    role_description = serializers.CharField(source='role.description', read_only=True)
    
    class Meta:
        model = UserRole
        fields = [
            'id', 'role', 'role_name', 'role_description', 'is_active',
            'assigned_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class UserSerializer(serializers.ModelSerializer):
    """Serializer pour les utilisateurs"""
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)
    roles = UserRoleSerializer(source='user_roles', many=True, read_only=True)
    full_name = serializers.CharField(read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name', 'full_name',
            'phone', 'avatar', 'birth_date', 'language', 'timezone', 'theme',
            'is_active', 'is_tenant_admin', 'tenant', 'tenant_name', 'roles',
            'last_login', 'date_joined', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'full_name', 'tenant_name', 'roles', 'last_login', 
            'date_joined', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'password': {'write_only': True},
        }


class UserCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création d'utilisateurs"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name', 'phone', 'language', 'timezone'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Les mots de passe ne correspondent pas.")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        # Le tenant sera assigné automatiquement via le contexte de la requête
        user = User.objects.create_user(password=password, **validated_data)
        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour la mise à jour des utilisateurs"""
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'email', 'phone', 'avatar',
            'birth_date', 'language', 'timezone', 'theme'
        ]


class ChangePasswordSerializer(serializers.Serializer):
    """Serializer pour le changement de mot de passe"""
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(required=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("Les nouveaux mots de passe ne correspondent pas.")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("L'ancien mot de passe est incorrect.")
        return value


class LoginSerializer(serializers.Serializer):
    """Serializer pour l'authentification"""
    username = serializers.CharField()
    password = serializers.CharField()
    tenant_slug = serializers.CharField(required=False)
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        tenant_slug = attrs.get('tenant_slug')
        
        if username and password:
            # Si un tenant_slug est fourni, ajouter le suffixe au username
            if tenant_slug:
                if not username.endswith(f"@{tenant_slug}"):
                    username = f"{username}@{tenant_slug}"
            
            user = authenticate(
                request=self.context.get('request'),
                username=username,
                password=password
            )
            
            if not user:
                raise serializers.ValidationError("Identifiants invalides.")
            
            if not user.is_active:
                raise serializers.ValidationError("Ce compte est désactivé.")
            
            attrs['user'] = user
        else:
            raise serializers.ValidationError("Le nom d'utilisateur et le mot de passe sont requis.")
        
        return attrs


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer pour le profil utilisateur"""
    tenant_info = TenantSerializer(source='tenant', read_only=True)
    roles = UserRoleSerializer(source='user_roles', many=True, read_only=True)
    permissions = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'phone', 'avatar', 'birth_date', 'language', 'timezone', 'theme',
            'is_tenant_admin', 'tenant_info', 'roles', 'permissions',
            'last_login', 'date_joined'
        ]
        read_only_fields = ['id', 'username', 'last_login', 'date_joined']
    
    def get_permissions(self, obj):
        """Récupère toutes les permissions de l'utilisateur"""
        permissions = {}
        for user_role in obj.user_roles.filter(is_active=True):
            role_permissions = user_role.role.permissions
            for module, perms in role_permissions.items():
                if module not in permissions:
                    permissions[module] = set()
                permissions[module].update(perms)
        
        # Convertir les sets en listes pour la sérialisation JSON
        return {module: list(perms) for module, perms in permissions.items()}
