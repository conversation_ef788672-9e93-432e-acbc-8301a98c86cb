version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: erp_postgres_prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-erp_hub_prod}
      POSTGRES_USER: ${POSTGRES_USER:-erp_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - erp_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-erp_user}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Cache Redis
  redis:
    image: redis:7-alpine
    container_name: erp_redis_prod
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_secure_123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - erp_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Django
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: erp_backend_prod
    environment:
      - DJANGO_SETTINGS_MODULE=config.settings.production
      - DATABASE_URL=postgresql://${POSTGRES_USER:-erp_user}:${POSTGRES_PASSWORD:-secure_password_123}@postgres:5432/${POSTGRES_DB:-erp_hub_prod}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_secure_123}@redis:6379/0
      - SECRET_KEY=${DJANGO_SECRET_KEY}
      - DEBUG=False
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1}
    volumes:
      - static_files:/app/staticfiles
      - media_files:/app/media
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - erp_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React (Build de production)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
      args:
        - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:8000}
    container_name: erp_frontend_prod
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - erp_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: erp_nginx_prod
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/sites-available:/etc/nginx/sites-available
      - static_files:/var/www/static
      - media_files:/var/www/media
      - ./ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - erp_network

  # Celery Worker pour les tâches asynchrones
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: erp_celery_worker_prod
    command: celery -A config worker -l info --concurrency=4
    environment:
      - DJANGO_SETTINGS_MODULE=config.settings.production
      - DATABASE_URL=postgresql://${POSTGRES_USER:-erp_user}:${POSTGRES_PASSWORD:-secure_password_123}@postgres:5432/${POSTGRES_DB:-erp_hub_prod}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_secure_123}@redis:6379/0
      - SECRET_KEY=${DJANGO_SECRET_KEY}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - erp_network

  # Celery Beat pour les tâches programmées
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: erp_celery_beat_prod
    command: celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - DJANGO_SETTINGS_MODULE=config.settings.production
      - DATABASE_URL=postgresql://${POSTGRES_USER:-erp_user}:${POSTGRES_PASSWORD:-secure_password_123}@postgres:5432/${POSTGRES_DB:-erp_hub_prod}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_secure_123}@redis:6379/0
      - SECRET_KEY=${DJANGO_SECRET_KEY}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - erp_network

  # Monitoring avec Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: erp_prometheus_prod
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - erp_network

  # Grafana pour les dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: erp_grafana_prod
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - erp_network

  # Elasticsearch pour les logs
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: erp_elasticsearch_prod
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    restart: unless-stopped
    networks:
      - erp_network

  # Kibana pour visualiser les logs
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: erp_kibana_prod
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - erp_network

  # Backup automatique
  backup:
    image: postgres:15-alpine
    container_name: erp_backup_prod
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-erp_hub_prod}
      - POSTGRES_USER=${POSTGRES_USER:-erp_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password_123}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    command: /bin/sh -c "chmod +x /backup.sh && crond -f"
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - erp_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  static_files:
    driver: local
  media_files:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  erp_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
