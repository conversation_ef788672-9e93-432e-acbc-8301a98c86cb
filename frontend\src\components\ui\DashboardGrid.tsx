import React, { useState } from 'react';
import { Responsive, WidthProvider, Layout } from 'react-grid-layout';
import { Box, IconButton, Paper, Typography } from '@mui/material';
import { DragIndicator, Close, Fullscreen, FullscreenExit } from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface DashboardWidget {
  id: string;
  title: string;
  component: React.ReactNode;
  defaultSize: { w: number; h: number };
  minSize?: { w: number; h: number };
  maxSize?: { w: number; h: number };
  resizable?: boolean;
  removable?: boolean;
}

interface DashboardGridProps {
  widgets: DashboardWidget[];
  onLayoutChange?: (layout: Layout[]) => void;
  onWidgetRemove?: (widgetId: string) => void;
  editable?: boolean;
  cols?: { lg: number; md: number; sm: number; xs: number; xxs: number };
  rowHeight?: number;
}

export const DashboardGrid: React.FC<DashboardGridProps> = ({
  widgets,
  onLayoutChange,
  onWidgetRemove,
  editable = false,
  cols = { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },
  rowHeight = 150
}) => {
  const [layouts, setLayouts] = useState<{ [key: string]: Layout[] }>({});
  const [fullscreenWidget, setFullscreenWidget] = useState<string | null>(null);

  const generateLayout = (): Layout[] => {
    return widgets.map((widget, index) => ({
      i: widget.id,
      x: (index * widget.defaultSize.w) % cols.lg,
      y: Math.floor((index * widget.defaultSize.w) / cols.lg) * widget.defaultSize.h,
      w: widget.defaultSize.w,
      h: widget.defaultSize.h,
      minW: widget.minSize?.w || 1,
      minH: widget.minSize?.h || 1,
      maxW: widget.maxSize?.w || cols.lg,
      maxH: widget.maxSize?.h || 10,
      static: !editable
    }));
  };

  const handleLayoutChange = (layout: Layout[], allLayouts: { [key: string]: Layout[] }) => {
    setLayouts(allLayouts);
    onLayoutChange?.(layout);
  };

  const handleWidgetRemove = (widgetId: string) => {
    onWidgetRemove?.(widgetId);
  };

  const toggleFullscreen = (widgetId: string) => {
    setFullscreenWidget(fullscreenWidget === widgetId ? null : widgetId);
  };

  const renderWidget = (widget: DashboardWidget) => {
    const isFullscreen = fullscreenWidget === widget.id;

    return (
      <motion.div
        key={widget.id}
        layout
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.3 }}
      >
        <Paper
          elevation={2}
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            position: 'relative',
            '&:hover .widget-controls': {
              opacity: editable ? 1 : 0
            }
          }}
        >
          {/* En-tête du widget */}
          <Box
            sx={{
              p: 1,
              borderBottom: '1px solid #e0e0e0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              backgroundColor: '#f8f9fa',
              minHeight: 48
            }}
          >
            <Box display="flex" alignItems="center" gap={1}>
              {editable && (
                <DragIndicator
                  className="drag-handle"
                  sx={{ cursor: 'grab', color: '#666', fontSize: 18 }}
                />
              )}
              <Typography variant="subtitle2" fontWeight={600} noWrap>
                {widget.title}
              </Typography>
            </Box>

            <Box className="widget-controls" sx={{ opacity: 0, transition: 'opacity 0.2s' }}>
              <IconButton
                size="small"
                onClick={() => toggleFullscreen(widget.id)}
                sx={{ mr: 0.5 }}
              >
                {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
              </IconButton>
              {widget.removable && editable && (
                <IconButton
                  size="small"
                  onClick={() => handleWidgetRemove(widget.id)}
                  color="error"
                >
                  <Close />
                </IconButton>
              )}
            </Box>
          </Box>

          {/* Contenu du widget */}
          <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
            {widget.component}
          </Box>
        </Paper>
      </motion.div>
    );
  };

  return (
    <Box sx={{ width: '100%', position: 'relative' }}>
      <AnimatePresence>
        {fullscreenWidget && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              zIndex: 9999,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '20px'
            }}
            onClick={() => setFullscreenWidget(null)}
          >
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
              style={{
                width: '90%',
                height: '90%',
                maxWidth: '1200px',
                maxHeight: '800px'
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {widgets.find(w => w.id === fullscreenWidget) && 
                renderWidget(widgets.find(w => w.id === fullscreenWidget)!)
              }
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        onLayoutChange={handleLayoutChange}
        cols={cols}
        rowHeight={rowHeight}
        isDraggable={editable}
        isResizable={editable}
        dragHandleClassName="drag-handle"
        margin={[16, 16]}
        containerPadding={[0, 0]}
      >
        {widgets
          .filter(widget => widget.id !== fullscreenWidget)
          .map(renderWidget)}
      </ResponsiveGridLayout>
    </Box>
  );
};
