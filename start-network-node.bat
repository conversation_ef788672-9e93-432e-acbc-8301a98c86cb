@echo off
echo 🌐 ERP HUB - Serveur Node.js Réseau
echo ===================================
echo.

REM Vérifier si Node.js est installé
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js non trouvé
    echo 📥 Veuillez installer Node.js depuis https://nodejs.org/
    pause
    exit /b 1
)

REM Vérifier si http-server est installé
where http-server >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installation de http-server...
    npm install -g http-server
)

REM Obtenir l'adresse IP locale
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    set IP=%%a
    goto :found
)
:found
set IP=%IP: =%

echo ✅ Node.js détecté
echo 📡 Configuration du serveur réseau...
echo 🖥️  Adresse IP du serveur : %IP%
echo 🌐 URL d'accès réseau : http://%IP%:8080
echo.
echo 📱 Accès depuis autres appareils :
echo    - Ordinateurs : http://%IP%:8080
echo    - Smartphones : http://%IP%:8080
echo    - Tablettes : http://%IP%:8080
echo.
echo ⚠️  Assurez-vous que :
echo    1. Tous les appareils sont sur le même réseau WiFi
echo    2. Le firewall autorise le port 8080
echo    3. Ce serveur reste allumé pour l'accès réseau
echo.
echo 🔥 Serveur Node.js démarré ! Accès réseau disponible.
echo 🛑 Pour arrêter : Ctrl+C dans cette fenêtre
echo.

cd frontend
http-server . -p 8080 -a 0.0.0.0 --cors -o
