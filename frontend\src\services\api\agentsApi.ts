import { api } from './client'

// Types pour l'API des agents
export interface Agent {
  id: string
  name: string
  agent_type: string
  description: string
  status: 'active' | 'inactive' | 'error' | 'maintenance'
  is_enabled: boolean
  priority: number
  ai_enabled: boolean
  ai_model: string
  capabilities: string[]
  dependencies: string[]
  last_activity: string | null
  total_tasks: number
  successful_tasks: number
  failed_tasks: number
  success_rate: number
  created_at: string
  updated_at: string
}

export interface AgentTask {
  id: string
  title: string
  description: string
  task_type: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  priority: number
  input_data: Record<string, any>
  output_data: Record<string, any>
  error_message: string
  scheduled_at: string | null
  started_at: string | null
  completed_at: string | null
  agent: string
  agent_name: string
  agent_type: string
  parent_task: string | null
  assigned_by: string | null
  assigned_by_name: string | null
  created_at: string
  updated_at: string
}

export interface SystemMetrics {
  id: string
  total_agents: number
  active_agents: number
  total_tasks: number
  pending_tasks: number
  running_tasks: number
  completed_tasks: number
  failed_tasks: number
  average_task_duration: number | null
  system_load: number
  success_rate: number
  agent_metrics: Record<string, any>
  alerts: Array<{
    type: string
    severity: string
    message: string
  }>
  recommendations: Array<{
    type: string
    message: string
  }>
  created_at: string
}

export interface TaskAssignment {
  title: string
  description?: string
  task_type: string
  priority: number
  target_agent_type?: string
  input_data?: Record<string, any>
  scheduled_at?: string
}

export interface AgentCommunication {
  id: string
  message_type: 'request' | 'response' | 'notification' | 'broadcast' | 'error'
  subject: string
  content: Record<string, any>
  correlation_id: string | null
  is_read: boolean
  read_at: string | null
  from_agent: string
  from_agent_name: string
  to_agent: string | null
  to_agent_name: string | null
  related_task: string | null
  created_at: string
}

export interface MessageBroadcast {
  message_type: 'request' | 'response' | 'notification' | 'broadcast' | 'error'
  subject: string
  content: Record<string, any>
  target_agents?: string[]
}

export interface WorkflowOptimization {
  workflow_id: string
  workflow_name: string
  issue: string
  current_rate?: number
  current_duration?: number
  recommendation: string
  priority: 'haute' | 'moyenne' | 'basse'
}

export interface AIInsight {
  observation: string
  impact: string
  recommendation: string
  priority: 'haute' | 'moyenne' | 'basse'
  confidence: number
}

export interface ManagerDashboard {
  agent: string
  tenant: string
  timestamp: string
  system_metrics: SystemMetrics
  agents: Agent[]
  recent_tasks: AgentTask[]
  workflow_optimizations: WorkflowOptimization[]
  ai_insights: AIInsight[]
}

export const agentsApi = {
  // Agent Manager
  getManagerStatus: () =>
    api.get('/agents/manager/status/'),

  getManagerDashboard: (): Promise<ManagerDashboard> =>
    api.get('/agents/manager/dashboard/'),

  // Gestion des tâches
  assignTask: (taskData: TaskAssignment): Promise<AgentTask> =>
    api.post('/agents/manager/tasks/assign/', taskData),

  // Communication entre agents
  sendMessage: (messageData: MessageBroadcast): Promise<AgentCommunication[]> =>
    api.post('/agents/manager/communication/', messageData),

  // Métriques système
  getSystemMetrics: (): Promise<SystemMetrics> =>
    api.get('/agents/manager/metrics/'),

  // Optimisations de workflows
  getWorkflowOptimizations: (): Promise<{
    optimizations: WorkflowOptimization[]
    count: number
    timestamp: string
  }> =>
    api.get('/agents/manager/optimizations/'),

  // Agents spécialisés - Status
  getAgentStatus: (agentType: string) =>
    api.get(`/agents/${agentType}/status/`),

  getAgentDashboard: (agentType: string) =>
    api.get(`/agents/${agentType}/dashboard/`),

  // Gestion des agents
  getAgents: (): Promise<Agent[]> =>
    api.get('/agents/'),

  getAgent: (agentId: string): Promise<Agent> =>
    api.get(`/agents/${agentId}/`),

  updateAgent: (agentId: string, data: Partial<Agent>): Promise<Agent> =>
    api.patch(`/agents/${agentId}/`, data),

  // Gestion des tâches
  getTasks: (params?: {
    agent_type?: string
    status?: string
    priority?: number
    limit?: number
  }): Promise<AgentTask[]> =>
    api.get('/agents/tasks/', { params }),

  getTask: (taskId: string): Promise<AgentTask> =>
    api.get(`/agents/tasks/${taskId}/`),

  updateTask: (taskId: string, data: Partial<AgentTask>): Promise<AgentTask> =>
    api.patch(`/agents/tasks/${taskId}/`, data),

  cancelTask: (taskId: string): Promise<void> =>
    api.patch(`/agents/tasks/${taskId}/`, { status: 'cancelled' }),

  // Communication
  getMessages: (params?: {
    agent_type?: string
    message_type?: string
    is_read?: boolean
    limit?: number
  }): Promise<AgentCommunication[]> =>
    api.get('/agents/communications/', { params }),

  markMessageAsRead: (messageId: string): Promise<AgentCommunication> =>
    api.patch(`/agents/communications/${messageId}/`, { is_read: true }),

  // Workflows
  getWorkflows: () =>
    api.get('/agents/workflows/'),

  createWorkflow: (workflowData: any) =>
    api.post('/agents/workflows/', workflowData),

  executeWorkflow: (workflowId: string, inputData: Record<string, any>) =>
    api.post(`/agents/workflows/${workflowId}/execute/`, { input_data: inputData }),

  getWorkflowExecutions: (workflowId?: string) =>
    api.get('/agents/workflow-executions/', {
      params: workflowId ? { workflow: workflowId } : undefined
    }),
}

// Types et API pour l'Agent HR
export interface Department {
  id: string
  name: string
  code: string
  description: string
  parent_department: string | null
  manager: string | null
  manager_name: string | null
  is_active: boolean
  budget_annual: number | null
  employee_count: number
  created_at: string
  updated_at: string
}

export interface Employee {
  id: string
  user: string
  user_info: {
    id: string
    username: string
    email: string
    first_name: string
    last_name: string
    full_name: string
    phone: string | null
    is_active: boolean
  }
  employee_id: string
  position: string
  position_title: string
  department_name: string
  employment_status: 'active' | 'inactive' | 'terminated' | 'resigned' | 'retired'
  contract_type: 'cdi' | 'cdd' | 'stage' | 'freelance' | 'consultant'
  hire_date: string
  contract_start_date: string
  contract_end_date: string | null
  termination_date: string | null
  current_salary: number
  salary_currency: string
  manager: string | null
  manager_name: string | null
  emergency_contact_name: string
  emergency_contact_phone: string
  skills: string[]
  performance_rating: number | null
  created_at: string
  updated_at: string
}

export interface LeaveRequest {
  id: string
  employee: string
  employee_name: string
  leave_type: string
  leave_type_name: string
  start_date: string
  end_date: string
  days_requested: number
  reason: string
  comments: string
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  approved_by: string | null
  approved_by_name: string | null
  approved_at: string | null
  rejection_reason: string
  created_at: string
  updated_at: string
}

export interface HRDashboard {
  tenant: string
  timestamp: string
  employees: {
    total: number
    active: number
    inactive: number
    turnover_rate: number
    avg_performance: number
  }
  departments: {
    total: number
    list: Array<{
      id: string
      name: string
      employee_count: number
    }>
  }
  pending_actions: {
    leave_requests: number
    performance_reviews: number
    ongoing_trainings: number
  }
  recent_activities: Array<{
    type: string
    description: string
    date: string
    status: string
  }>
}

export const hrApi = {
  // Dashboard et statut
  getStatus: () =>
    api.get('/agents/hr/status/'),

  getDashboard: (): Promise<HRDashboard> =>
    api.get('/agents/hr/dashboard/'),

  getInsights: () =>
    api.get('/agents/hr/insights/'),

  // Départements
  getDepartments: (): Promise<Department[]> =>
    api.get('/agents/hr/departments/'),

  createDepartment: (data: Partial<Department>): Promise<Department> =>
    api.post('/agents/hr/departments/', data),

  updateDepartment: (id: string, data: Partial<Department>): Promise<Department> =>
    api.patch(`/agents/hr/departments/${id}/`, data),

  deleteDepartment: (id: string): Promise<void> =>
    api.delete(`/agents/hr/departments/${id}/`),

  // Employés
  getEmployees: (params?: {
    department?: string
    status?: string
  }): Promise<Employee[]> =>
    api.get('/agents/hr/employees/', { params }),

  getEmployee: (id: string): Promise<Employee> =>
    api.get(`/agents/hr/employees/${id}/`),

  updateEmployee: (id: string, data: Partial<Employee>): Promise<Employee> =>
    api.patch(`/agents/hr/employees/${id}/`, data),

  // Demandes de congés
  getLeaveRequests: (params?: {
    employee?: string
    status?: string
  }): Promise<LeaveRequest[]> =>
    api.get('/agents/hr/leave-requests/', { params }),

  createLeaveRequest: (data: {
    leave_type: string
    start_date: string
    end_date: string
    days_requested: number
    reason?: string
    comments?: string
  }): Promise<LeaveRequest> =>
    api.post('/agents/hr/leave-requests/', data),

  processLeaveRequest: (id: string, action: 'approve' | 'reject', comments?: string) =>
    api.post(`/agents/hr/leave-requests/${id}/action/`, {
      action,
      comments: comments || ''
    }),

  // Types de congés
  getLeaveTypes: () =>
    api.get('/agents/hr/leave-types/'),

  // Formations
  getTrainings: (params?: { status?: string }) =>
    api.get('/agents/hr/trainings/', { params }),

  createTraining: (data: any) =>
    api.post('/agents/hr/trainings/create/', data),

  enrollInTraining: (trainingId: string, employeeId: string) =>
    api.post(`/agents/hr/trainings/${trainingId}/enroll/${employeeId}/`),

  // Évaluations de performance
  getPerformanceReviews: (params?: {
    employee?: string
    type?: string
  }) =>
    api.get('/agents/hr/performance-reviews/', { params }),

  createPerformanceReview: (data: any) =>
    api.post('/agents/hr/performance-reviews/create/', data),
}

// Types et API pour l'Agent Sales
export interface Customer {
  id: string
  customer_code: string
  customer_type: 'individual' | 'company' | 'government'
  name: string
  email: string
  phone: string
  website: string
  address_line1: string
  address_line2: string
  city: string
  postal_code: string
  country: string
  credit_limit: number
  payment_terms: number
  sales_rep: string | null
  sales_rep_name: string | null
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  product_code: string
  name: string
  description: string
  product_type: 'product' | 'service' | 'bundle'
  category: string
  unit_price: number
  cost_price: number | null
  unit_of_measure: string
  is_active: boolean
  is_sellable: boolean
  margin_percentage: number
  created_at: string
  updated_at: string
}

export interface Opportunity {
  id: string
  title: string
  description: string
  customer: string
  customer_name: string
  sales_rep: string
  sales_rep_name: string
  estimated_value: number
  probability: number
  stage: 'lead' | 'qualified' | 'proposal' | 'negotiation' | 'won' | 'lost'
  priority: 1 | 2 | 3 | 4 | 5
  expected_close_date: string
  actual_close_date: string | null
  last_contact_date: string | null
  next_action: string
  weighted_value: number
  created_at: string
  updated_at: string
}

export interface Quote {
  id: string
  quote_number: string
  customer: string
  customer_name: string
  opportunity: string | null
  opportunity_title: string | null
  sales_rep: string
  sales_rep_name: string
  quote_date: string
  valid_until: string
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired'
  subtotal: number
  tax_amount: number
  total_amount: number
  notes: string
  terms_conditions: string
  items: QuoteItem[]
  created_at: string
  updated_at: string
}

export interface QuoteItem {
  id: string
  product: string
  product_name: string
  product_code: string
  description: string
  quantity: number
  unit_price: number
  discount_percentage: number
  line_total: number
  created_at: string
  updated_at: string
}

export interface SalesDashboard {
  tenant: string
  timestamp: string
  pipeline: {
    total_value: number
    weighted_value: number
    open_opportunities: number
    conversion_rate: number
  }
  quotes: {
    total: number
    pending: number
    pending_value: number
  }
  customers: {
    total: number
    new_this_month: number
  }
  revenue: {
    monthly: number
    target: number
    achievement: number
  }
  recent_activities: Array<{
    type: string
    description: string
    date: string
    value: number
  }>
}

export const salesApi = {
  // Dashboard et statut
  getStatus: () =>
    api.get('/agents/sales/status/'),

  getDashboard: (): Promise<SalesDashboard> =>
    api.get('/agents/sales/dashboard/'),

  getPerformance: () =>
    api.get('/agents/sales/performance/'),

  getInsights: () =>
    api.get('/agents/sales/insights/'),

  // Clients
  getCustomers: (params?: {
    type?: string
    active?: boolean
    sales_rep?: string
  }): Promise<Customer[]> =>
    api.get('/agents/sales/customers/', { params }),

  createCustomer: (data: Partial<Customer>): Promise<Customer> =>
    api.post('/agents/sales/customers/', data),

  updateCustomer: (id: string, data: Partial<Customer>): Promise<Customer> =>
    api.patch(`/agents/sales/customers/${id}/`, data),

  deleteCustomer: (id: string): Promise<void> =>
    api.delete(`/agents/sales/customers/${id}/`),

  // Produits
  getProducts: (params?: {
    type?: string
    category?: string
    sellable?: boolean
  }): Promise<Product[]> =>
    api.get('/agents/sales/products/', { params }),

  createProduct: (data: Partial<Product>): Promise<Product> =>
    api.post('/agents/sales/products/', data),

  updateProduct: (id: string, data: Partial<Product>): Promise<Product> =>
    api.patch(`/agents/sales/products/${id}/`, data),

  deleteProduct: (id: string): Promise<void> =>
    api.delete(`/agents/sales/products/${id}/`),

  // Opportunités
  getOpportunities: (params?: {
    stage?: string
    customer?: string
    sales_rep?: string
    priority?: number
  }): Promise<Opportunity[]> =>
    api.get('/agents/sales/opportunities/', { params }),

  createOpportunity: (data: {
    title: string
    description?: string
    customer: string
    estimated_value: number
    probability?: number
    stage?: string
    priority?: number
    expected_close_date: string
    next_action?: string
  }): Promise<Opportunity> =>
    api.post('/agents/sales/opportunities/create/', data),

  updateOpportunity: (id: string, data: Partial<Opportunity>): Promise<Opportunity> =>
    api.patch(`/agents/sales/opportunities/${id}/`, data),

  updateOpportunityStage: (id: string, stage: string, notes?: string) =>
    api.patch(`/agents/sales/opportunities/${id}/stage/`, {
      stage,
      notes: notes || ''
    }),

  deleteOpportunity: (id: string): Promise<void> =>
    api.delete(`/agents/sales/opportunities/${id}/`),

  // Devis
  getQuotes: (params?: {
    status?: string
    customer?: string
    sales_rep?: string
  }): Promise<Quote[]> =>
    api.get('/agents/sales/quotes/', { params }),

  createQuote: (data: {
    customer_id: string
    opportunity_id?: string
    quote_date?: string
    valid_until: string
    notes?: string
    terms_conditions?: string
    items: Array<{
      product_id: string
      description?: string
      quantity: number
      unit_price: number
      discount_percentage?: number
    }>
  }): Promise<Quote> =>
    api.post('/agents/sales/quotes/create/', data),

  updateQuote: (id: string, data: Partial<Quote>): Promise<Quote> =>
    api.patch(`/agents/sales/quotes/${id}/`, data),

  updateQuoteStatus: (id: string, status: string, comments?: string) =>
    api.patch(`/agents/sales/quotes/${id}/status/`, {
      status,
      comments: comments || ''
    }),

  deleteQuote: (id: string): Promise<void> =>
    api.delete(`/agents/sales/quotes/${id}/`),
}

// Types et API pour l'Agent Purchase
export interface Supplier {
  id: string
  supplier_code: string
  supplier_type: 'manufacturer' | 'distributor' | 'service_provider' | 'consultant'
  name: string
  contact_person: string
  email: string
  phone: string
  website: string
  address_line1: string
  address_line2: string
  city: string
  postal_code: string
  country: string
  payment_terms: number
  currency: string
  quality_rating: number | null
  delivery_rating: number | null
  service_rating: number | null
  overall_rating: number | null
  buyer: string | null
  buyer_name: string | null
  is_active: boolean
  is_approved: boolean
  bank_name: string
  bank_account: string
  created_at: string
  updated_at: string
}

export interface PurchaseRequest {
  id: string
  request_number: string
  requester: string
  requester_name: string
  title: string
  description: string
  justification: string
  requested_date: string
  required_date: string
  priority: 1 | 2 | 3 | 4 | 5
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'ordered' | 'cancelled'
  estimated_total: number
  approved_by: string | null
  approved_by_name: string | null
  approved_at: string | null
  rejection_reason: string
  buyer: string | null
  buyer_name: string | null
  items: PurchaseRequestItem[]
  created_at: string
  updated_at: string
}

export interface PurchaseRequestItem {
  id: string
  item_name: string
  description: string
  category: string | null
  category_name: string | null
  quantity: number
  unit_of_measure: string
  estimated_unit_price: number | null
  estimated_total: number
  suggested_supplier: string | null
  suggested_supplier_name: string | null
  specifications: Record<string, any>
  created_at: string
  updated_at: string
}

export interface PurchaseOrder {
  id: string
  order_number: string
  supplier: string
  supplier_name: string
  purchase_request: string | null
  purchase_request_number: string | null
  buyer: string
  buyer_name: string
  order_date: string
  expected_delivery_date: string
  actual_delivery_date: string | null
  status: 'draft' | 'sent' | 'confirmed' | 'partially_received' | 'received' | 'invoiced' | 'closed' | 'cancelled'
  subtotal: number
  tax_amount: number
  total_amount: number
  payment_terms: number
  delivery_address: string
  notes: string
  terms_conditions: string
  items: PurchaseOrderItem[]
  created_at: string
  updated_at: string
}

export interface PurchaseOrderItem {
  id: string
  request_item: string | null
  request_item_name: string | null
  item_name: string
  description: string
  supplier_reference: string
  quantity_ordered: number
  quantity_received: number
  unit_of_measure: string
  unit_price: number
  discount_percentage: number
  line_total: number
  expected_delivery_date: string | null
  quantity_pending: number
  is_fully_received: boolean
  created_at: string
  updated_at: string
}

export interface PurchaseDashboard {
  tenant: string
  timestamp: string
  requests: {
    total: number
    pending: number
    pending_value: number
  }
  orders: {
    total: number
    active: number
    active_value: number
  }
  suppliers: {
    total: number
    approved: number
    avg_rating: number
  }
  invoices: {
    pending: number
    pending_value: number
  }
  performance: {
    avg_delivery_delay: number
    on_time_delivery_rate: number
  }
  recent_activities: Array<{
    type: string
    description: string
    date: string
    value: number
  }>
}

export const purchaseApi = {
  // Dashboard et statut
  getStatus: () =>
    api.get('/agents/purchase/status/'),

  getDashboard: (): Promise<PurchaseDashboard> =>
    api.get('/agents/purchase/dashboard/'),

  getSupplierPerformance: () =>
    api.get('/agents/purchase/supplier-performance/'),

  getInsights: () =>
    api.get('/agents/purchase/insights/'),

  getCostSavings: () =>
    api.get('/agents/purchase/cost-savings/'),

  // Fournisseurs
  getSuppliers: (params?: {
    type?: string
    active?: boolean
    approved?: boolean
    buyer?: string
  }): Promise<Supplier[]> =>
    api.get('/agents/purchase/suppliers/', { params }),

  createSupplier: (data: Partial<Supplier>): Promise<Supplier> =>
    api.post('/agents/purchase/suppliers/', data),

  updateSupplier: (id: string, data: Partial<Supplier>): Promise<Supplier> =>
    api.patch(`/agents/purchase/suppliers/${id}/`, data),

  deleteSupplier: (id: string): Promise<void> =>
    api.delete(`/agents/purchase/suppliers/${id}/`),

  // Demandes d'achat
  getPurchaseRequests: (params?: {
    status?: string
    requester?: string
    buyer?: string
    priority?: number
  }): Promise<PurchaseRequest[]> =>
    api.get('/agents/purchase/requests/', { params }),

  createPurchaseRequest: (data: {
    title: string
    description?: string
    justification?: string
    requested_date?: string
    required_date: string
    priority?: number
    items: Array<{
      item_name: string
      description?: string
      category_id?: string
      quantity: number
      unit_of_measure?: string
      estimated_unit_price?: number
      suggested_supplier_id?: string
      specifications?: Record<string, any>
    }>
  }): Promise<PurchaseRequest> =>
    api.post('/agents/purchase/requests/create/', data),

  updatePurchaseRequest: (id: string, data: Partial<PurchaseRequest>): Promise<PurchaseRequest> =>
    api.patch(`/agents/purchase/requests/${id}/`, data),

  approvePurchaseRequest: (id: string, action: 'approve' | 'reject', comments?: string) =>
    api.post(`/agents/purchase/requests/${id}/approval/`, {
      action,
      comments: comments || ''
    }),

  deletePurchaseRequest: (id: string): Promise<void> =>
    api.delete(`/agents/purchase/requests/${id}/`),

  // Bons de commande
  getPurchaseOrders: (params?: {
    status?: string
    supplier?: string
    buyer?: string
  }): Promise<PurchaseOrder[]> =>
    api.get('/agents/purchase/orders/', { params }),

  createPurchaseOrder: (data: {
    supplier_id: string
    purchase_request_id?: string
    order_date?: string
    expected_delivery_date: string
    payment_terms?: number
    delivery_address?: string
    notes?: string
    terms_conditions?: string
    items: Array<{
      request_item_id?: string
      item_name: string
      description?: string
      supplier_reference?: string
      quantity_ordered: number
      unit_of_measure?: string
      unit_price: number
      discount_percentage?: number
      expected_delivery_date?: string
    }>
  }): Promise<PurchaseOrder> =>
    api.post('/agents/purchase/orders/create/', data),

  updatePurchaseOrder: (id: string, data: Partial<PurchaseOrder>): Promise<PurchaseOrder> =>
    api.patch(`/agents/purchase/orders/${id}/`, data),

  deletePurchaseOrder: (id: string): Promise<void> =>
    api.delete(`/agents/purchase/orders/${id}/`),
}

// Types et API pour l'Agent Stock
export interface Warehouse {
  id: string
  code: string
  name: string
  warehouse_type: 'main' | 'secondary' | 'transit' | 'quarantine' | 'returns'
  description: string
  address_line1: string
  address_line2: string
  city: string
  postal_code: string
  country: string
  manager: string | null
  manager_name: string | null
  total_capacity: number | null
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Location {
  id: string
  warehouse: string
  warehouse_name: string
  code: string
  name: string
  location_type: 'shelf' | 'bin' | 'floor' | 'rack' | 'cold'
  parent_location: string | null
  parent_location_code: string | null
  capacity: number | null
  max_weight: number | null
  temperature_controlled: boolean
  hazardous_materials: boolean
  is_active: boolean
  is_blocked: boolean
  created_at: string
  updated_at: string
}

export interface StockItem {
  id: string
  sku: string
  name: string
  description: string
  item_type: 'raw_material' | 'component' | 'finished_product' | 'consumable' | 'tool' | 'spare_part'
  barcode: string
  internal_reference: string
  supplier_reference: string
  unit_of_measure: string
  weight: number | null
  volume: number | null
  valuation_method: 'fifo' | 'lifo' | 'average' | 'standard'
  minimum_stock: number
  maximum_stock: number | null
  reorder_point: number | null
  reorder_quantity: number | null
  standard_cost: number | null
  last_purchase_price: number | null
  category: string
  abc_classification: 'A' | 'B' | 'C' | ''
  temperature_min: number | null
  temperature_max: number | null
  humidity_max: number | null
  expiry_tracking: boolean
  shelf_life_days: number | null
  is_active: boolean
  is_serialized: boolean
  is_lot_tracked: boolean
  current_stock: number
  stock_value: number
  created_at: string
  updated_at: string
}

export interface StockLevel {
  id: string
  item: string
  item_sku: string
  item_name: string
  location: string
  location_code: string
  warehouse_name: string
  quantity_on_hand: number
  quantity_reserved: number
  quantity_available: number
  average_cost: number
  total_value: number
  last_movement_date: string | null
  last_count_date: string | null
  created_at: string
  updated_at: string
}

export interface StockMovement {
  id: string
  movement_number: string
  item: string
  item_sku: string
  item_name: string
  location_from: string | null
  location_from_code: string | null
  location_to: string | null
  location_to_code: string | null
  movement_type: 'receipt' | 'issue' | 'transfer' | 'adjustment' | 'return' | 'scrap' | 'production' | 'consumption'
  movement_reason: 'purchase' | 'sale' | 'production' | 'transfer' | 'adjustment' | 'return_customer' | 'return_supplier' | 'damage' | 'expiry' | 'theft' | 'other'
  quantity: number
  unit_cost: number | null
  total_cost: number | null
  lot_number: string
  serial_number: string
  expiry_date: string | null
  reference_document: string
  reference_type: string
  user: string
  user_name: string
  movement_date: string
  comments: string
  created_at: string
  updated_at: string
}

export interface StockInventory {
  id: string
  inventory_number: string
  inventory_type: 'full' | 'partial' | 'cycle' | 'spot'
  status: 'planned' | 'in_progress' | 'completed' | 'validated' | 'cancelled'
  warehouse: string
  warehouse_name: string
  planned_date: string
  start_date: string | null
  end_date: string | null
  supervisor: string
  supervisor_name: string
  total_items_counted: number
  total_discrepancies: number
  total_value_adjustment: number
  description: string
  comments: string
  lines: StockInventoryLine[]
  created_at: string
  updated_at: string
}

export interface StockInventoryLine {
  id: string
  inventory: string
  item: string
  item_sku: string
  item_name: string
  location: string
  location_code: string
  system_quantity: number
  counted_quantity: number | null
  adjustment_quantity: number
  unit_cost: number
  adjustment_value: number
  lot_number: string
  serial_number: string
  counter: string | null
  counter_name: string | null
  count_date: string | null
  is_counted: boolean
  is_adjusted: boolean
  comments: string
  created_at: string
  updated_at: string
}

export interface StockDashboard {
  tenant: string
  timestamp: string
  items: {
    total: number
    in_stock: number
    low_stock: number
    out_of_stock: number
  }
  warehouses: {
    total: number
    active: number
  }
  value: {
    total_stock_value: number
    average_turnover: number
  }
  movements: {
    last_30_days: number
    receipts: number
    issues: number
  }
  inventories: {
    active: number
    planned: number
  }
  alerts: {
    low_stock: number
    out_of_stock: number
    expired_reservations: number
  }
  recent_activities: Array<{
    type: string
    description: string
    date: string
    location: string
  }>
}

export const stockApi = {
  // Dashboard et statut
  getStatus: () =>
    api.get('/agents/stock/status/'),

  getDashboard: (): Promise<StockDashboard> =>
    api.get('/agents/stock/dashboard/'),

  getPerformance: () =>
    api.get('/agents/stock/performance/'),

  getInsights: () =>
    api.get('/agents/stock/insights/'),

  getAlerts: () =>
    api.get('/agents/stock/alerts/'),

  getValuation: () =>
    api.get('/agents/stock/valuation/'),

  getLocationUtilization: () =>
    api.get('/agents/stock/location-utilization/'),

  // Entrepôts
  getWarehouses: (params?: {
    type?: string
    active?: boolean
    manager?: string
  }): Promise<Warehouse[]> =>
    api.get('/agents/stock/warehouses/', { params }),

  createWarehouse: (data: Partial<Warehouse>): Promise<Warehouse> =>
    api.post('/agents/stock/warehouses/', data),

  updateWarehouse: (id: string, data: Partial<Warehouse>): Promise<Warehouse> =>
    api.patch(`/agents/stock/warehouses/${id}/`, data),

  deleteWarehouse: (id: string): Promise<void> =>
    api.delete(`/agents/stock/warehouses/${id}/`),

  // Emplacements
  getLocations: (params?: {
    warehouse?: string
    type?: string
    parent?: string
    root_only?: boolean
    active?: boolean
  }): Promise<Location[]> =>
    api.get('/agents/stock/locations/', { params }),

  createLocation: (data: Partial<Location>): Promise<Location> =>
    api.post('/agents/stock/locations/', data),

  updateLocation: (id: string, data: Partial<Location>): Promise<Location> =>
    api.patch(`/agents/stock/locations/${id}/`, data),

  deleteLocation: (id: string): Promise<void> =>
    api.delete(`/agents/stock/locations/${id}/`),

  // Articles
  getStockItems: (params?: {
    type?: string
    category?: string
    abc_class?: string
    active?: boolean
    search?: string
  }): Promise<StockItem[]> =>
    api.get('/agents/stock/items/', { params }),

  createStockItem: (data: Partial<StockItem>): Promise<StockItem> =>
    api.post('/agents/stock/items/', data),

  updateStockItem: (id: string, data: Partial<StockItem>): Promise<StockItem> =>
    api.patch(`/agents/stock/items/${id}/`, data),

  deleteStockItem: (id: string): Promise<void> =>
    api.delete(`/agents/stock/items/${id}/`),

  // Niveaux de stock
  getStockLevels: (params?: {
    item?: string
    warehouse?: string
    location?: string
    has_stock?: boolean
  }): Promise<StockLevel[]> =>
    api.get('/agents/stock/levels/', { params }),

  // Mouvements de stock
  getStockMovements: (params?: {
    item?: string
    type?: string
    location?: string
    date_from?: string
    date_to?: string
  }): Promise<StockMovement[]> =>
    api.get('/agents/stock/movements/', { params }),

  createStockMovement: (data: {
    item_id: string
    location_from_id?: string
    location_to_id?: string
    movement_type: string
    movement_reason: string
    quantity: number
    unit_cost?: number
    lot_number?: string
    serial_number?: string
    expiry_date?: string
    reference_document?: string
    reference_type?: string
    movement_date?: string
    comments?: string
  }): Promise<StockMovement> =>
    api.post('/agents/stock/movements/create/', data),

  // Inventaires
  getInventories: (params?: {
    type?: string
    status?: string
    warehouse?: string
    supervisor?: string
  }): Promise<StockInventory[]> =>
    api.get('/agents/stock/inventories/', { params }),

  createInventory: (data: {
    inventory_type: string
    warehouse_id: string
    planned_date: string
    location_ids?: string[]
    item_ids?: string[]
    description?: string
    comments?: string
  }): Promise<StockInventory> =>
    api.post('/agents/stock/inventories/create/', data),

  processInventoryCount: (data: {
    inventory_line_id: string
    counted_quantity: number
    comments?: string
  }) =>
    api.post('/agents/stock/inventories/count/', data),

  validateInventory: (inventoryId: string) =>
    api.post(`/agents/stock/inventories/${inventoryId}/validate/`),
}
