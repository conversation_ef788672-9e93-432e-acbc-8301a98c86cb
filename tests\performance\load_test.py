#!/usr/bin/env python3
"""
Tests de charge pour l'ERP HUB
Simule des utilisateurs concurrents sur tous les agents
"""

import asyncio
import aiohttp
import time
import json
import statistics
from datetime import datetime
from typing import List, Dict, Any
import argparse

class ERPLoadTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.results = {
            'requests': [],
            'errors': [],
            'response_times': [],
            'start_time': None,
            'end_time': None
        }
    
    async def create_session(self):
        """Créer une session HTTP"""
        self.session = aiohttp.ClientSession()
    
    async def close_session(self):
        """Fermer la session HTTP"""
        if self.session:
            await self.session.close()
    
    async def login(self, username: str = "admin", password: str = "admin123") -> str:
        """Se connecter et récupérer le token JWT"""
        try:
            async with self.session.post(
                f"{self.base_url}/api/auth/login/",
                json={"username": username, "password": password}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('access', '')
                return None
        except Exception as e:
            print(f"Erreur de connexion: {e}")
            return None
    
    async def make_request(self, endpoint: str, token: str, method: str = "GET") -> Dict[str, Any]:
        """Faire une requête HTTP avec mesure du temps"""
        start_time = time.time()
        
        headers = {"Authorization": f"Bearer {token}"} if token else {}
        
        try:
            async with self.session.request(
                method, 
                f"{self.base_url}{endpoint}",
                headers=headers
            ) as response:
                end_time = time.time()
                response_time = (end_time - start_time) * 1000  # en ms
                
                result = {
                    'endpoint': endpoint,
                    'method': method,
                    'status': response.status,
                    'response_time': response_time,
                    'timestamp': datetime.now().isoformat(),
                    'success': 200 <= response.status < 400
                }
                
                self.results['requests'].append(result)
                self.results['response_times'].append(response_time)
                
                if not result['success']:
                    self.results['errors'].append(result)
                
                return result
                
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            error_result = {
                'endpoint': endpoint,
                'method': method,
                'status': 0,
                'response_time': response_time,
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'error': str(e)
            }
            
            self.results['requests'].append(error_result)
            self.results['errors'].append(error_result)
            
            return error_result
    
    async def test_agent_endpoints(self, token: str, agent: str) -> List[Dict[str, Any]]:
        """Tester tous les endpoints d'un agent"""
        endpoints = {
            'manager': [
                '/api/agents/manager/dashboard_data/',
                '/api/agents/manager/dashboard/',
                '/api/agents/realtime/metrics/',
                '/api/agents/sync/status/'
            ],
            'hr': [
                '/api/agents/hr/employees_summary/',
                '/api/agents/hr/employees/',
            ],
            'sales': [
                '/api/agents/sales/pipeline_data/',
                '/api/agents/sales/pipeline/',
            ],
            'purchase': [
                '/api/agents/purchase/orders_summary/',
                '/api/agents/purchase/orders/',
            ],
            'logistics': [
                '/api/agents/logistics/shipments_status/',
                '/api/agents/logistics/shipments/',
            ],
            'stock': [
                '/api/agents/stock/inventory_summary/',
                '/api/agents/stock/inventory/',
            ],
            'accounting': [
                '/api/agents/accounting/financial_summary/',
                '/api/agents/accounting/transactions/',
            ],
            'finance': [
                '/api/agents/finance/reports/',
            ],
            'crm': [
                '/api/agents/crm/customer_metrics/',
                '/api/agents/crm/customers/',
            ],
            'bi': [
                '/api/agents/bi/analytics/',
            ]
        }
        
        results = []
        for endpoint in endpoints.get(agent, []):
            result = await self.make_request(endpoint, token)
            results.append(result)
            # Petit délai entre les requêtes
            await asyncio.sleep(0.1)
        
        return results
    
    async def simulate_user_session(self, user_id: int, duration: int = 60) -> Dict[str, Any]:
        """Simuler une session utilisateur complète"""
        print(f"🔄 Démarrage session utilisateur {user_id}")
        
        # Connexion
        token = await self.login()
        if not token:
            return {'user_id': user_id, 'success': False, 'error': 'Login failed'}
        
        session_start = time.time()
        agents = ['manager', 'hr', 'sales', 'purchase', 'logistics', 'stock', 'accounting', 'finance', 'crm', 'bi']
        
        session_results = []
        
        # Simuler l'activité pendant la durée spécifiée
        while time.time() - session_start < duration:
            # Choisir un agent aléatoirement
            import random
            agent = random.choice(agents)
            
            # Tester les endpoints de l'agent
            agent_results = await self.test_agent_endpoints(token, agent)
            session_results.extend(agent_results)
            
            # Pause entre les actions
            await asyncio.sleep(random.uniform(1, 3))
        
        print(f"✅ Session utilisateur {user_id} terminée")
        return {
            'user_id': user_id,
            'success': True,
            'requests_count': len(session_results),
            'duration': time.time() - session_start
        }
    
    async def run_load_test(self, concurrent_users: int = 10, duration: int = 60):
        """Exécuter le test de charge"""
        print(f"🚀 Démarrage du test de charge")
        print(f"   👥 Utilisateurs concurrents: {concurrent_users}")
        print(f"   ⏱️  Durée: {duration} secondes")
        print(f"   🎯 URL cible: {self.base_url}")
        print("=" * 50)
        
        self.results['start_time'] = datetime.now()
        
        await self.create_session()
        
        try:
            # Lancer les sessions utilisateur en parallèle
            tasks = [
                self.simulate_user_session(i, duration)
                for i in range(concurrent_users)
            ]
            
            session_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            self.results['end_time'] = datetime.now()
            
            # Analyser les résultats
            await self.analyze_results(session_results)
            
        finally:
            await self.close_session()
    
    async def analyze_results(self, session_results: List[Dict[str, Any]]):
        """Analyser et afficher les résultats"""
        print("\n" + "=" * 50)
        print("📊 RÉSULTATS DU TEST DE CHARGE")
        print("=" * 50)
        
        # Statistiques générales
        total_requests = len(self.results['requests'])
        total_errors = len(self.results['errors'])
        success_rate = ((total_requests - total_errors) / total_requests * 100) if total_requests > 0 else 0
        
        print(f"📈 Statistiques générales:")
        print(f"   Total requêtes: {total_requests}")
        print(f"   Requêtes réussies: {total_requests - total_errors}")
        print(f"   Erreurs: {total_errors}")
        print(f"   Taux de succès: {success_rate:.2f}%")
        
        # Temps de réponse
        if self.results['response_times']:
            response_times = self.results['response_times']
            print(f"\n⏱️  Temps de réponse (ms):")
            print(f"   Minimum: {min(response_times):.2f}")
            print(f"   Maximum: {max(response_times):.2f}")
            print(f"   Moyenne: {statistics.mean(response_times):.2f}")
            print(f"   Médiane: {statistics.median(response_times):.2f}")
            if len(response_times) > 1:
                print(f"   Écart-type: {statistics.stdev(response_times):.2f}")
        
        # Analyse par endpoint
        endpoint_stats = {}
        for request in self.results['requests']:
            endpoint = request['endpoint']
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {
                    'count': 0,
                    'errors': 0,
                    'response_times': []
                }
            
            endpoint_stats[endpoint]['count'] += 1
            endpoint_stats[endpoint]['response_times'].append(request['response_time'])
            
            if not request['success']:
                endpoint_stats[endpoint]['errors'] += 1
        
        print(f"\n🎯 Performance par endpoint:")
        for endpoint, stats in sorted(endpoint_stats.items()):
            avg_time = statistics.mean(stats['response_times'])
            error_rate = (stats['errors'] / stats['count'] * 100) if stats['count'] > 0 else 0
            print(f"   {endpoint}")
            print(f"     Requêtes: {stats['count']}, Erreurs: {stats['errors']} ({error_rate:.1f}%)")
            print(f"     Temps moyen: {avg_time:.2f}ms")
        
        # Sessions utilisateur
        successful_sessions = [s for s in session_results if isinstance(s, dict) and s.get('success')]
        print(f"\n👥 Sessions utilisateur:")
        print(f"   Sessions réussies: {len(successful_sessions)}")
        print(f"   Sessions échouées: {len(session_results) - len(successful_sessions)}")
        
        # Recommandations
        print(f"\n💡 Recommandations:")
        if success_rate < 95:
            print("   ⚠️  Taux de succès faible - vérifier la stabilité du serveur")
        
        avg_response_time = statistics.mean(self.results['response_times']) if self.results['response_times'] else 0
        if avg_response_time > 1000:
            print("   ⚠️  Temps de réponse élevé - optimiser les performances")
        elif avg_response_time > 500:
            print("   ⚠️  Temps de réponse modéré - surveiller les performances")
        else:
            print("   ✅ Temps de réponse acceptable")
        
        # Sauvegarder les résultats
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"load_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump({
                'summary': {
                    'total_requests': total_requests,
                    'total_errors': total_errors,
                    'success_rate': success_rate,
                    'avg_response_time': avg_response_time
                },
                'detailed_results': self.results,
                'endpoint_stats': endpoint_stats
            }, f, indent=2, default=str)
        
        print(f"\n💾 Résultats sauvegardés dans: {filename}")

async def main():
    parser = argparse.ArgumentParser(description='Test de charge ERP HUB')
    parser.add_argument('--users', type=int, default=10, help='Nombre d\'utilisateurs concurrents')
    parser.add_argument('--duration', type=int, default=60, help='Durée du test en secondes')
    parser.add_argument('--url', type=str, default='http://localhost:8000', help='URL de base de l\'API')
    
    args = parser.parse_args()
    
    tester = ERPLoadTester(args.url)
    await tester.run_load_test(args.users, args.duration)

if __name__ == "__main__":
    asyncio.run(main())
