@echo off
echo 🚀 ERP HUB - Démarrage Simple
echo ================================
echo.

REM Vérifier si Python est disponible
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python détecté - Démarrage du serveur...
    echo 📡 Serveur démarré sur http://localhost:8080
    echo 🌐 Ouverture automatique du navigateur...
    echo.
    echo ⚠️  Pour arrêter le serveur : Ctrl+C dans cette fenêtre
    echo.
    
    REM Ouvrir le navigateur après 2 secondes
    start "" timeout /t 2 /nobreak >nul && start http://localhost:8080/finance-management.html
    
    REM Démarrer le serveur Python
    cd frontend
    python -m http.server 8080
) else (
    echo ❌ Python non trouvé
    echo.
    echo 💡 Solutions alternatives :
    echo 1. Installer Python depuis https://python.org
    echo 2. Utiliser Node.js : npm install -g http-server
    echo 3. Ouvrir directement le fichier HTML dans le navigateur
    echo.
    echo 🌐 Ouverture directe du fichier...
    start "" "frontend\finance-management.html"
)

echo.
echo Appuyez sur une touche pour fermer...
pause >nul
