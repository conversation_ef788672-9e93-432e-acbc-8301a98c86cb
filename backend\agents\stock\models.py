"""
Modèles pour l'Agent Stock - Gestion des Stocks et Inventaires
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid

from core.models import TimeStampedModel, UUIDModel, Tenant, User


class Warehouse(UUIDModel, TimeStampedModel):
    """Modèle pour les entrepôts"""

    WAREHOUSE_TYPES = [
        ('main', _('Principal')),
        ('secondary', _('Secondaire')),
        ('transit', _('Transit')),
        ('quarantine', _('Quarantaine')),
        ('returns', _('Retours')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='warehouses',
        verbose_name=_("Tenant")
    )

    # Informations de base
    code = models.CharField(_("Code entrepôt"), max_length=20)
    name = models.CharField(_("Nom"), max_length=200)
    warehouse_type = models.CharField(_("Type"), max_length=20, choices=WAREHOUSE_TYPES, default='main')
    description = models.TextField(_("Description"), blank=True)

    # Adresse
    address_line1 = models.CharField(_("Adresse ligne 1"), max_length=200, blank=True)
    address_line2 = models.CharField(_("Adresse ligne 2"), max_length=200, blank=True)
    city = models.CharField(_("Ville"), max_length=100, blank=True)
    postal_code = models.CharField(_("Code postal"), max_length=20, blank=True)
    country = models.CharField(_("Pays"), max_length=100, blank=True)

    # Responsable
    manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_warehouses',
        verbose_name=_("Responsable")
    )

    # Capacité
    total_capacity = models.DecimalField(
        _("Capacité totale (m³)"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)

    class Meta:
        verbose_name = _("Entrepôt")
        verbose_name_plural = _("Entrepôts")
        unique_together = ['tenant', 'code']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Location(UUIDModel, TimeStampedModel):
    """Modèle pour les emplacements dans les entrepôts"""

    LOCATION_TYPES = [
        ('shelf', _('Étagère')),
        ('bin', _('Casier')),
        ('floor', _('Sol')),
        ('rack', _('Rack')),
        ('cold', _('Chambre froide')),
    ]

    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        related_name='locations',
        verbose_name=_("Entrepôt")
    )

    # Identification
    code = models.CharField(_("Code emplacement"), max_length=50)
    name = models.CharField(_("Nom"), max_length=200)
    location_type = models.CharField(_("Type"), max_length=20, choices=LOCATION_TYPES, default='shelf')

    # Hiérarchie (Zone > Allée > Étagère > Casier)
    parent_location = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='sub_locations',
        verbose_name=_("Emplacement parent")
    )

    # Caractéristiques
    capacity = models.DecimalField(
        _("Capacité (m³)"),
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True
    )
    max_weight = models.DecimalField(
        _("Poids maximum (kg)"),
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Conditions spéciales
    temperature_controlled = models.BooleanField(_("Température contrôlée"), default=False)
    hazardous_materials = models.BooleanField(_("Matières dangereuses"), default=False)

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)
    is_blocked = models.BooleanField(_("Bloqué"), default=False)

    class Meta:
        verbose_name = _("Emplacement")
        verbose_name_plural = _("Emplacements")
        unique_together = ['warehouse', 'code']
        ordering = ['code']

    def __str__(self):
        return f"{self.warehouse.code}-{self.code}"


class StockItem(UUIDModel, TimeStampedModel):
    """Modèle pour les articles en stock"""

    ITEM_TYPES = [
        ('raw_material', _('Matière première')),
        ('component', _('Composant')),
        ('finished_product', _('Produit fini')),
        ('consumable', _('Consommable')),
        ('tool', _('Outillage')),
        ('spare_part', _('Pièce de rechange')),
    ]

    VALUATION_METHODS = [
        ('fifo', _('FIFO - Premier entré, premier sorti')),
        ('lifo', _('LIFO - Dernier entré, premier sorti')),
        ('average', _('Coût moyen pondéré')),
        ('standard', _('Coût standard')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='stock_items',
        verbose_name=_("Tenant")
    )

    # Identification
    sku = models.CharField(_("SKU"), max_length=50)  # Stock Keeping Unit
    name = models.CharField(_("Nom"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    item_type = models.CharField(_("Type d'article"), max_length=20, choices=ITEM_TYPES)

    # Codes et références
    barcode = models.CharField(_("Code-barres"), max_length=100, blank=True)
    internal_reference = models.CharField(_("Référence interne"), max_length=100, blank=True)
    supplier_reference = models.CharField(_("Référence fournisseur"), max_length=100, blank=True)

    # Caractéristiques physiques
    unit_of_measure = models.CharField(_("Unité de mesure"), max_length=20, default='pcs')
    weight = models.DecimalField(
        _("Poids unitaire (kg)"),
        max_digits=8,
        decimal_places=3,
        null=True,
        blank=True
    )
    volume = models.DecimalField(
        _("Volume unitaire (m³)"),
        max_digits=8,
        decimal_places=6,
        null=True,
        blank=True
    )

    # Gestion des stocks
    valuation_method = models.CharField(
        _("Méthode de valorisation"),
        max_length=20,
        choices=VALUATION_METHODS,
        default='average'
    )

    # Seuils de stock
    minimum_stock = models.DecimalField(
        _("Stock minimum"),
        max_digits=10,
        decimal_places=3,
        default=Decimal('0.000')
    )
    maximum_stock = models.DecimalField(
        _("Stock maximum"),
        max_digits=10,
        decimal_places=3,
        null=True,
        blank=True
    )
    reorder_point = models.DecimalField(
        _("Point de commande"),
        max_digits=10,
        decimal_places=3,
        null=True,
        blank=True
    )
    reorder_quantity = models.DecimalField(
        _("Quantité de commande"),
        max_digits=10,
        decimal_places=3,
        null=True,
        blank=True
    )

    # Coûts
    standard_cost = models.DecimalField(
        _("Coût standard"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    last_purchase_price = models.DecimalField(
        _("Dernier prix d'achat"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Catégorisation
    category = models.CharField(_("Catégorie"), max_length=100, blank=True)
    abc_classification = models.CharField(
        _("Classification ABC"),
        max_length=1,
        choices=[('A', 'A'), ('B', 'B'), ('C', 'C')],
        blank=True
    )

    # Conditions de stockage
    temperature_min = models.DecimalField(
        _("Température min (°C)"),
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True
    )
    temperature_max = models.DecimalField(
        _("Température max (°C)"),
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True
    )
    humidity_max = models.DecimalField(
        _("Humidité max (%)"),
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Dates importantes
    expiry_tracking = models.BooleanField(_("Suivi des dates d'expiration"), default=False)
    shelf_life_days = models.PositiveIntegerField(_("Durée de vie (jours)"), null=True, blank=True)

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)
    is_serialized = models.BooleanField(_("Suivi par numéro de série"), default=False)
    is_lot_tracked = models.BooleanField(_("Suivi par lot"), default=False)

    class Meta:
        verbose_name = _("Article")
        verbose_name_plural = _("Articles")
        unique_together = ['tenant', 'sku']
        ordering = ['sku']

    def __str__(self):
        return f"{self.sku} - {self.name}"


class StockLevel(UUIDModel, TimeStampedModel):
    """Modèle pour les niveaux de stock par emplacement"""

    item = models.ForeignKey(
        StockItem,
        on_delete=models.CASCADE,
        related_name='stock_levels',
        verbose_name=_("Article")
    )

    location = models.ForeignKey(
        Location,
        on_delete=models.CASCADE,
        related_name='stock_levels',
        verbose_name=_("Emplacement")
    )

    # Quantités
    quantity_on_hand = models.DecimalField(
        _("Quantité en stock"),
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000')
    )
    quantity_reserved = models.DecimalField(
        _("Quantité réservée"),
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000')
    )
    quantity_available = models.DecimalField(
        _("Quantité disponible"),
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000')
    )

    # Valorisation
    average_cost = models.DecimalField(
        _("Coût moyen"),
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_value = models.DecimalField(
        _("Valeur totale"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Dates
    last_movement_date = models.DateTimeField(_("Dernier mouvement"), null=True, blank=True)
    last_count_date = models.DateTimeField(_("Dernier comptage"), null=True, blank=True)

    class Meta:
        verbose_name = _("Niveau de stock")
        verbose_name_plural = _("Niveaux de stock")
        unique_together = ['item', 'location']
        ordering = ['item__sku', 'location__code']

    def save(self, *args, **kwargs):
        """Calcule automatiquement la quantité disponible et la valeur totale"""
        self.quantity_available = self.quantity_on_hand - self.quantity_reserved
        self.total_value = self.quantity_on_hand * self.average_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item.sku} @ {self.location.code}: {self.quantity_on_hand}"


class StockMovement(UUIDModel, TimeStampedModel):
    """Modèle pour les mouvements de stock"""

    MOVEMENT_TYPES = [
        ('receipt', _('Réception')),
        ('issue', _('Sortie')),
        ('transfer', _('Transfert')),
        ('adjustment', _('Ajustement')),
        ('return', _('Retour')),
        ('scrap', _('Mise au rebut')),
        ('production', _('Production')),
        ('consumption', _('Consommation')),
    ]

    MOVEMENT_REASONS = [
        ('purchase', _('Achat')),
        ('sale', _('Vente')),
        ('production', _('Production')),
        ('transfer', _('Transfert')),
        ('adjustment', _('Ajustement inventaire')),
        ('return_customer', _('Retour client')),
        ('return_supplier', _('Retour fournisseur')),
        ('damage', _('Dommage')),
        ('expiry', _('Expiration')),
        ('theft', _('Vol')),
        ('other', _('Autre')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='stock_movements',
        verbose_name=_("Tenant")
    )

    # Numérotation
    movement_number = models.CharField(_("Numéro de mouvement"), max_length=50, unique=True)

    # Article et emplacement
    item = models.ForeignKey(
        StockItem,
        on_delete=models.PROTECT,
        related_name='movements',
        verbose_name=_("Article")
    )

    location_from = models.ForeignKey(
        Location,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='movements_from',
        verbose_name=_("Emplacement source")
    )

    location_to = models.ForeignKey(
        Location,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='movements_to',
        verbose_name=_("Emplacement destination")
    )

    # Type et détails du mouvement
    movement_type = models.CharField(_("Type de mouvement"), max_length=20, choices=MOVEMENT_TYPES)
    movement_reason = models.CharField(_("Motif"), max_length=20, choices=MOVEMENT_REASONS)

    # Quantités
    quantity = models.DecimalField(
        _("Quantité"),
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(Decimal('0.001'))]
    )

    # Coûts
    unit_cost = models.DecimalField(
        _("Coût unitaire"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    total_cost = models.DecimalField(
        _("Coût total"),
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Traçabilité
    lot_number = models.CharField(_("Numéro de lot"), max_length=100, blank=True)
    serial_number = models.CharField(_("Numéro de série"), max_length=100, blank=True)
    expiry_date = models.DateField(_("Date d'expiration"), null=True, blank=True)

    # Références externes
    reference_document = models.CharField(_("Document de référence"), max_length=100, blank=True)
    reference_type = models.CharField(
        _("Type de référence"),
        max_length=20,
        choices=[
            ('purchase_order', _('Bon de commande')),
            ('sales_order', _('Commande client')),
            ('production_order', _('Ordre de production')),
            ('inventory', _('Inventaire')),
            ('transfer', _('Transfert')),
            ('manual', _('Manuel')),
        ],
        blank=True
    )

    # Utilisateur et dates
    user = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='stock_movements',
        verbose_name=_("Utilisateur")
    )
    movement_date = models.DateTimeField(_("Date du mouvement"))

    # Commentaires
    comments = models.TextField(_("Commentaires"), blank=True)

    class Meta:
        verbose_name = _("Mouvement de stock")
        verbose_name_plural = _("Mouvements de stock")
        ordering = ['-movement_date']

    def save(self, *args, **kwargs):
        """Calcule automatiquement le coût total"""
        if self.unit_cost:
            self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.movement_number} - {self.item.sku} ({self.quantity})"


class StockReservation(UUIDModel, TimeStampedModel):
    """Modèle pour les réservations de stock"""

    RESERVATION_TYPES = [
        ('sales_order', _('Commande client')),
        ('production_order', _('Ordre de production')),
        ('transfer', _('Transfert')),
        ('maintenance', _('Maintenance')),
        ('other', _('Autre')),
    ]

    RESERVATION_STATUS = [
        ('active', _('Active')),
        ('fulfilled', _('Honorée')),
        ('cancelled', _('Annulée')),
        ('expired', _('Expirée')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='stock_reservations',
        verbose_name=_("Tenant")
    )

    # Numérotation
    reservation_number = models.CharField(_("Numéro de réservation"), max_length=50, unique=True)

    # Article et emplacement
    item = models.ForeignKey(
        StockItem,
        on_delete=models.PROTECT,
        related_name='reservations',
        verbose_name=_("Article")
    )

    location = models.ForeignKey(
        Location,
        on_delete=models.PROTECT,
        related_name='reservations',
        verbose_name=_("Emplacement")
    )

    # Détails de la réservation
    reservation_type = models.CharField(_("Type de réservation"), max_length=20, choices=RESERVATION_TYPES)
    status = models.CharField(_("Statut"), max_length=20, choices=RESERVATION_STATUS, default='active')

    # Quantités
    quantity_reserved = models.DecimalField(
        _("Quantité réservée"),
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(Decimal('0.001'))]
    )
    quantity_fulfilled = models.DecimalField(
        _("Quantité honorée"),
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000')
    )

    # Dates
    reservation_date = models.DateTimeField(_("Date de réservation"))
    required_date = models.DateTimeField(_("Date requise"))
    expiry_date = models.DateTimeField(_("Date d'expiration"), null=True, blank=True)

    # Références
    reference_document = models.CharField(_("Document de référence"), max_length=100, blank=True)

    # Utilisateur
    user = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='stock_reservations',
        verbose_name=_("Utilisateur")
    )

    # Commentaires
    comments = models.TextField(_("Commentaires"), blank=True)

    class Meta:
        verbose_name = _("Réservation de stock")
        verbose_name_plural = _("Réservations de stock")
        ordering = ['-reservation_date']

    def __str__(self):
        return f"{self.reservation_number} - {self.item.sku} ({self.quantity_reserved})"

    @property
    def quantity_remaining(self):
        """Quantité restant à honorer"""
        return self.quantity_reserved - self.quantity_fulfilled


class StockInventory(UUIDModel, TimeStampedModel):
    """Modèle pour les inventaires physiques"""

    INVENTORY_TYPES = [
        ('full', _('Inventaire complet')),
        ('partial', _('Inventaire partiel')),
        ('cycle', _('Inventaire tournant')),
        ('spot', _('Inventaire ponctuel')),
    ]

    INVENTORY_STATUS = [
        ('planned', _('Planifié')),
        ('in_progress', _('En cours')),
        ('completed', _('Terminé')),
        ('validated', _('Validé')),
        ('cancelled', _('Annulé')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='stock_inventories',
        verbose_name=_("Tenant")
    )

    # Numérotation
    inventory_number = models.CharField(_("Numéro d'inventaire"), max_length=50, unique=True)

    # Détails de l'inventaire
    inventory_type = models.CharField(_("Type d'inventaire"), max_length=20, choices=INVENTORY_TYPES)
    status = models.CharField(_("Statut"), max_length=20, choices=INVENTORY_STATUS, default='planned')

    # Périmètre
    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='inventories',
        verbose_name=_("Entrepôt")
    )

    locations = models.ManyToManyField(
        Location,
        related_name='inventories',
        verbose_name=_("Emplacements"),
        blank=True
    )

    items = models.ManyToManyField(
        StockItem,
        related_name='inventories',
        verbose_name=_("Articles"),
        blank=True
    )

    # Dates
    planned_date = models.DateField(_("Date planifiée"))
    start_date = models.DateTimeField(_("Date de début"), null=True, blank=True)
    end_date = models.DateTimeField(_("Date de fin"), null=True, blank=True)

    # Responsables
    supervisor = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='supervised_inventories',
        verbose_name=_("Superviseur")
    )

    counters = models.ManyToManyField(
        User,
        related_name='counted_inventories',
        verbose_name=_("Compteurs"),
        blank=True
    )

    # Résultats
    total_items_counted = models.PositiveIntegerField(_("Articles comptés"), default=0)
    total_discrepancies = models.PositiveIntegerField(_("Écarts détectés"), default=0)
    total_value_adjustment = models.DecimalField(
        _("Ajustement de valeur"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Commentaires
    description = models.TextField(_("Description"), blank=True)
    comments = models.TextField(_("Commentaires"), blank=True)

    class Meta:
        verbose_name = _("Inventaire")
        verbose_name_plural = _("Inventaires")
        ordering = ['-planned_date']

    def __str__(self):
        return f"{self.inventory_number} - {self.warehouse.name}"


class StockInventoryLine(UUIDModel, TimeStampedModel):
    """Modèle pour les lignes d'inventaire"""

    inventory = models.ForeignKey(
        StockInventory,
        on_delete=models.CASCADE,
        related_name='lines',
        verbose_name=_("Inventaire")
    )

    item = models.ForeignKey(
        StockItem,
        on_delete=models.PROTECT,
        related_name='inventory_lines',
        verbose_name=_("Article")
    )

    location = models.ForeignKey(
        Location,
        on_delete=models.PROTECT,
        related_name='inventory_lines',
        verbose_name=_("Emplacement")
    )

    # Quantités
    system_quantity = models.DecimalField(
        _("Quantité système"),
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000')
    )
    counted_quantity = models.DecimalField(
        _("Quantité comptée"),
        max_digits=12,
        decimal_places=3,
        null=True,
        blank=True
    )
    adjustment_quantity = models.DecimalField(
        _("Quantité d'ajustement"),
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000')
    )

    # Coûts
    unit_cost = models.DecimalField(
        _("Coût unitaire"),
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    adjustment_value = models.DecimalField(
        _("Valeur d'ajustement"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Traçabilité
    lot_number = models.CharField(_("Numéro de lot"), max_length=100, blank=True)
    serial_number = models.CharField(_("Numéro de série"), max_length=100, blank=True)

    # Comptage
    counter = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='inventory_counts',
        verbose_name=_("Compteur")
    )
    count_date = models.DateTimeField(_("Date de comptage"), null=True, blank=True)

    # Statut
    is_counted = models.BooleanField(_("Compté"), default=False)
    is_adjusted = models.BooleanField(_("Ajusté"), default=False)

    # Commentaires
    comments = models.TextField(_("Commentaires"), blank=True)

    class Meta:
        verbose_name = _("Ligne d'inventaire")
        verbose_name_plural = _("Lignes d'inventaire")
        unique_together = ['inventory', 'item', 'location', 'lot_number', 'serial_number']
        ordering = ['item__sku', 'location__code']

    def save(self, *args, **kwargs):
        """Calcule automatiquement les ajustements"""
        if self.counted_quantity is not None:
            self.adjustment_quantity = self.counted_quantity - self.system_quantity
            self.adjustment_value = self.adjustment_quantity * self.unit_cost
            self.is_counted = True
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.inventory.inventory_number} - {self.item.sku} @ {self.location.code}"
