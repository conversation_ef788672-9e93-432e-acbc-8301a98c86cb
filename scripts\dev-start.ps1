# Script PowerShell pour démarrer l'environnement de développement ERP HUB
# Usage: .\scripts\dev-start.ps1

Write-Host "🚀 Démarrage de l'environnement de développement ERP HUB" -ForegroundColor Green

# Vérification de Docker
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker n'est pas installé ou n'est pas dans le PATH" -ForegroundColor Red
    exit 1
}

# Vérification de Docker Compose
if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker Compose n'est pas installé ou n'est pas dans le PATH" -ForegroundColor Red
    exit 1
}

# Création du fichier .env s'il n'existe pas
if (-not (Test-Path ".env")) {
    Write-Host "📝 Création du fichier .env à partir de .env.example" -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
}

# Arrêt des conteneurs existants
Write-Host "🛑 Arrêt des conteneurs existants..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml down

# Construction et démarrage des conteneurs
Write-Host "🔨 Construction et démarrage des conteneurs..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml up --build -d

# Attente que la base de données soit prête
Write-Host "⏳ Attente de la base de données..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Vérification de l'état des conteneurs
Write-Host "📊 État des conteneurs:" -ForegroundColor Cyan
docker-compose -f docker-compose.dev.yml ps

# Affichage des URLs d'accès
Write-Host ""
Write-Host "✅ Environnement de développement démarré avec succès!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 URLs d'accès:" -ForegroundColor Cyan
Write-Host "   Frontend React:     http://localhost:3000" -ForegroundColor White
Write-Host "   Backend Django:     http://localhost:8000" -ForegroundColor White
Write-Host "   Admin Django:       http://localhost:8000/admin" -ForegroundColor White
Write-Host "   API Documentation:  http://localhost:8000/api/docs/" -ForegroundColor White
Write-Host "   Adminer (DB):       http://localhost:8080" -ForegroundColor White
Write-Host ""
Write-Host "📋 Commandes utiles:" -ForegroundColor Cyan
Write-Host "   Voir les logs:      docker-compose -f docker-compose.dev.yml logs -f" -ForegroundColor White
Write-Host "   Arrêter:           docker-compose -f docker-compose.dev.yml down" -ForegroundColor White
Write-Host "   Redémarrer:        docker-compose -f docker-compose.dev.yml restart" -ForegroundColor White
Write-Host ""

# Ouverture automatique du navigateur (optionnel)
$openBrowser = Read-Host "Voulez-vous ouvrir le navigateur automatiquement? (y/N)"
if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
    Start-Process "http://localhost:3000"
}
