import React from 'react'
import { useAuthStore } from '@/store/authStore'

const ProfilePage: React.FC = () => {
  const { user } = useAuthStore()

  if (!user) {
    return <div>Chargement...</div>
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Profil Utilisateur</h1>
        <p className="mt-2 text-gray-600">
          Gérez vos informations personnelles et préférences
        </p>
      </div>

      {/* Informations personnelles */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-gray-900">Informations Personnelles</h2>
        </div>
        <div className="card-body">
          <dl className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <dt className="text-sm font-medium text-gray-600">Nom d'utilisateur</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.username}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-600">Email</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.email}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-600">Prénom</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.first_name || 'Non renseigné'}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-600">Nom</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.last_name || 'Non renseigné'}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-600">Téléphone</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.phone || 'Non renseigné'}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-600">Langue</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.language}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-600">Fuseau horaire</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.timezone}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-600">Thème</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {user.theme === 'light' ? 'Clair' : 'Sombre'}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Organisation */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-gray-900">Organisation</h2>
        </div>
        <div className="card-body">
          <dl className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <dt className="text-sm font-medium text-gray-600">Nom</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.tenant_info.name}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-600">Slug</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.tenant_info.slug}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-600">Email</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.tenant_info.email || 'Non renseigné'}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-600">Téléphone</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.tenant_info.phone || 'Non renseigné'}</dd>
            </div>
            <div className="md:col-span-2">
              <dt className="text-sm font-medium text-gray-600">Description</dt>
              <dd className="mt-1 text-sm text-gray-900">{user.tenant_info.description || 'Aucune description'}</dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Rôles et permissions */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-gray-900">Rôles et Permissions</h2>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-600 mb-2">Rôles assignés</h3>
              <div className="flex flex-wrap gap-2">
                {user.roles.map((userRole) => (
                  <span
                    key={userRole.id}
                    className={`badge ${userRole.is_active ? 'badge-primary' : 'badge-secondary'}`}
                  >
                    {userRole.role_name}
                  </span>
                ))}
                {user.roles.length === 0 && (
                  <span className="text-sm text-gray-500">Aucun rôle assigné</span>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-600 mb-2">Permissions par module</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(user.permissions).map(([module, permissions]) => (
                  <div key={module} className="border border-gray-200 rounded-lg p-3">
                    <h4 className="font-medium text-gray-900 capitalize mb-2">{module}</h4>
                    <div className="flex flex-wrap gap-1">
                      {permissions.map((permission) => (
                        <span
                          key={permission}
                          className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                        >
                          {permission}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
                {Object.keys(user.permissions).length === 0 && (
                  <span className="text-sm text-gray-500 col-span-full">Aucune permission</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statut administrateur */}
      {user.is_tenant_admin && (
        <div className="card border-primary-200 bg-primary-50">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-primary-800">
                  Administrateur du Tenant
                </h3>
                <p className="text-sm text-primary-700">
                  Vous avez les privilèges d'administrateur pour votre organisation.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ProfilePage
