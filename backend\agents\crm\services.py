"""
Services pour l'Agent CRM
Logique métier pour la relation client avancée
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg, F
from django.db import transaction
from decimal import Decimal

from core.models import Tenant, User
from agents.models import Agent
from agents.ai_service import ai_service
from .models import (
    Contact, Opportunity, Campaign, SupportTicket, Interaction,
    CustomerSegment, CRMAnalytics
)

logger = logging.getLogger('agents.crm')


class CRMService:
    """
    Service principal pour l'Agent CRM
    Gère toutes les opérations de relation client
    """

    def __init__(self, tenant: Tenant):
        self.tenant = tenant
        self.crm_agent = self._get_or_create_crm_agent()

    def _get_or_create_crm_agent(self) -> Agent:
        """Récupère ou crée l'agent CRM pour le tenant"""
        agent, created = Agent.objects.get_or_create(
            tenant=self.tenant,
            agent_type='crm',
            defaults={
                'name': 'Agent CRM',
                'description': 'Relation client avancée',
                'ai_enabled': True,
                'ai_model': 'gpt-4',
                'capabilities': [
                    'contact_management',
                    'opportunity_tracking',
                    'campaign_management',
                    'customer_support',
                    'customer_segmentation',
                    'sales_analytics',
                    'lead_scoring',
                    'customer_insights'
                ]
            }
        )
        if created:
            logger.info(f"Agent CRM créé pour le tenant {self.tenant.name}")
        return agent

    def get_crm_dashboard(self) -> Dict[str, Any]:
        """Retourne les données du dashboard CRM"""

        # Contacts
        contacts = Contact.objects.filter(tenant=self.tenant)

        # Opportunités
        opportunities = Opportunity.objects.filter(tenant=self.tenant)

        # Campagnes
        campaigns = Campaign.objects.filter(tenant=self.tenant)

        # Tickets de support
        tickets = SupportTicket.objects.filter(tenant=self.tenant)

        # Interactions récentes
        recent_interactions = Interaction.objects.filter(
            tenant=self.tenant,
            interaction_date__gte=timezone.now() - timedelta(days=30)
        )

        # Calculs de performance
        sales_performance = self._calculate_sales_performance()

        # Analyse de la satisfaction client
        customer_satisfaction = self._analyze_customer_satisfaction()

        # Métriques de campagnes
        campaign_metrics = self._get_campaign_metrics()

        # Alertes CRM
        crm_alerts = self._get_crm_alerts()

        return {
            'tenant': self.tenant.name,
            'timestamp': timezone.now().isoformat(),
            'contacts': {
                'total': contacts.count(),
                'prospects': contacts.filter(contact_type='prospect').count(),
                'customers': contacts.filter(contact_type='customer').count(),
                'leads': contacts.filter(contact_type='lead').count(),
                'active': contacts.filter(status='active').count(),
                'qualified': contacts.filter(status='qualified').count(),
                'converted': contacts.filter(status='converted').count(),
                'by_source': self._get_contacts_by_source(),
                'total_value': float(sum(contact.customer_value for contact in contacts)),
                'average_score': contacts.aggregate(avg_score=Avg('lead_score'))['avg_score'] or 0
            },
            'opportunities': {
                'total': opportunities.count(),
                'open': opportunities.filter(stage__in=['prospecting', 'qualification', 'needs_analysis', 'proposal', 'negotiation']).count(),
                'won': opportunities.filter(stage='closed_won').count(),
                'lost': opportunities.filter(stage='closed_lost').count(),
                'total_value': float(opportunities.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')),
                'weighted_value': float(opportunities.aggregate(total=Sum('weighted_amount'))['total'] or Decimal('0.00')),
                'average_deal_size': float(opportunities.aggregate(avg=Avg('amount'))['avg'] or Decimal('0.00')),
                'by_stage': self._get_opportunities_by_stage(),
                'win_rate': self._calculate_win_rate()
            },
            'campaigns': {
                'total': campaigns.count(),
                'active': campaigns.filter(status='active').count(),
                'completed': campaigns.filter(status='completed').count(),
                'total_budget': float(campaigns.aggregate(total=Sum('budget'))['total'] or Decimal('0.00')),
                'total_cost': float(campaigns.aggregate(total=Sum('actual_cost'))['total'] or Decimal('0.00')),
                'total_leads': campaigns.aggregate(total=Sum('total_leads'))['total'] or 0,
                'total_revenue': float(campaigns.aggregate(total=Sum('actual_revenue'))['total'] or Decimal('0.00')),
                'average_roi': self._calculate_average_campaign_roi()
            },
            'support': {
                'total_tickets': tickets.count(),
                'open_tickets': tickets.filter(status__in=['new', 'open', 'in_progress', 'pending']).count(),
                'resolved_tickets': tickets.filter(status='resolved').count(),
                'closed_tickets': tickets.filter(status='closed').count(),
                'average_satisfaction': tickets.filter(satisfaction_rating__isnull=False).aggregate(
                    avg=Avg('satisfaction_rating'))['avg'] or 0,
                'average_response_time': tickets.filter(response_time_hours__isnull=False).aggregate(
                    avg=Avg('response_time_hours'))['avg'] or 0,
                'average_resolution_time': tickets.filter(resolution_time_hours__isnull=False).aggregate(
                    avg=Avg('resolution_time_hours'))['avg'] or 0,
                'by_priority': self._get_tickets_by_priority(),
                'by_type': self._get_tickets_by_type()
            },
            'interactions': {
                'total_recent': recent_interactions.count(),
                'by_type': self._get_interactions_by_type(),
                'by_outcome': self._get_interactions_by_outcome()
            },
            'performance': sales_performance,
            'satisfaction': customer_satisfaction,
            'campaign_metrics': campaign_metrics,
            'alerts': crm_alerts,
            'recent_activities': self._get_recent_activities()
        }

    def _calculate_sales_performance(self) -> Dict[str, Any]:
        """Calcule les performances de vente"""
        # Opportunités des 30 derniers jours
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)

        recent_opportunities = Opportunity.objects.filter(
            tenant=self.tenant,
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        )

        won_opportunities = recent_opportunities.filter(stage='closed_won')

        # Calculs
        total_pipeline = float(recent_opportunities.aggregate(total=Sum('amount'))['total'] or Decimal('0.00'))
        won_revenue = float(won_opportunities.aggregate(total=Sum('amount'))['total'] or Decimal('0.00'))
        average_deal_cycle = self._calculate_average_deal_cycle()

        return {
            'pipeline_value': total_pipeline,
            'won_revenue': won_revenue,
            'deals_created': recent_opportunities.count(),
            'deals_won': won_opportunities.count(),
            'average_deal_cycle_days': average_deal_cycle,
            'conversion_rate': (won_opportunities.count() / recent_opportunities.count() * 100) if recent_opportunities.count() > 0 else 0
        }

    def _analyze_customer_satisfaction(self) -> Dict[str, Any]:
        """Analyse la satisfaction client"""
        # Tickets avec évaluation des 90 derniers jours
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=90)

        rated_tickets = SupportTicket.objects.filter(
            tenant=self.tenant,
            satisfaction_rating__isnull=False,
            created_at__date__gte=start_date
        )

        if not rated_tickets.exists():
            return {
                'average_rating': 0,
                'total_ratings': 0,
                'satisfaction_distribution': {},
                'nps_score': 0
            }

        # Distribution des notes
        distribution = {}
        for i in range(1, 6):
            count = rated_tickets.filter(satisfaction_rating=i).count()
            distribution[str(i)] = count

        # Calcul NPS (approximation basée sur les notes de satisfaction)
        promoters = rated_tickets.filter(satisfaction_rating__gte=4).count()
        detractors = rated_tickets.filter(satisfaction_rating__lte=2).count()
        total = rated_tickets.count()
        nps = ((promoters - detractors) / total * 100) if total > 0 else 0

        return {
            'average_rating': float(rated_tickets.aggregate(avg=Avg('satisfaction_rating'))['avg']),
            'total_ratings': total,
            'satisfaction_distribution': distribution,
            'nps_score': nps
        }

    def _get_campaign_metrics(self) -> Dict[str, Any]:
        """Récupère les métriques de campagnes"""
        active_campaigns = Campaign.objects.filter(tenant=self.tenant, status='active')

        if not active_campaigns.exists():
            return {
                'total_sent': 0,
                'delivery_rate': 0,
                'open_rate': 0,
                'click_rate': 0,
                'conversion_rate': 0
            }

        total_sent = active_campaigns.aggregate(total=Sum('total_sent'))['total'] or 0
        total_delivered = active_campaigns.aggregate(total=Sum('total_delivered'))['total'] or 0
        total_opened = active_campaigns.aggregate(total=Sum('total_opened'))['total'] or 0
        total_clicked = active_campaigns.aggregate(total=Sum('total_clicked'))['total'] or 0
        total_leads = active_campaigns.aggregate(total=Sum('total_leads'))['total'] or 0

        return {
            'total_sent': total_sent,
            'delivery_rate': (total_delivered / total_sent * 100) if total_sent > 0 else 0,
            'open_rate': (total_opened / total_delivered * 100) if total_delivered > 0 else 0,
            'click_rate': (total_clicked / total_opened * 100) if total_opened > 0 else 0,
            'conversion_rate': (total_leads / total_delivered * 100) if total_delivered > 0 else 0
        }

    def _get_contacts_by_source(self) -> Dict[str, int]:
        """Répartition des contacts par source"""
        sources = Contact.objects.filter(tenant=self.tenant).values('lead_source').annotate(
            count=Count('id')
        ).order_by('-count')

        return {source['lead_source'] or 'unknown': source['count'] for source in sources}

    def _get_opportunities_by_stage(self) -> Dict[str, Any]:
        """Répartition des opportunités par étape"""
        stages = Opportunity.objects.filter(tenant=self.tenant).values('stage').annotate(
            count=Count('id'),
            total_value=Sum('amount')
        ).order_by('stage')

        return {
            stage['stage']: {
                'count': stage['count'],
                'value': float(stage['total_value'] or Decimal('0.00'))
            }
            for stage in stages
        }

    def _calculate_win_rate(self) -> float:
        """Calcule le taux de gain"""
        closed_opportunities = Opportunity.objects.filter(
            tenant=self.tenant,
            stage__in=['closed_won', 'closed_lost']
        )

        if not closed_opportunities.exists():
            return 0

        won_count = closed_opportunities.filter(stage='closed_won').count()
        return (won_count / closed_opportunities.count()) * 100

    def _calculate_average_campaign_roi(self) -> float:
        """Calcule le ROI moyen des campagnes"""
        completed_campaigns = Campaign.objects.filter(
            tenant=self.tenant,
            status='completed',
            actual_cost__gt=0
        )

        if not completed_campaigns.exists():
            return 0

        total_roi = sum(campaign.roi for campaign in completed_campaigns)
        return total_roi / completed_campaigns.count()

    def _get_tickets_by_priority(self) -> Dict[str, int]:
        """Répartition des tickets par priorité"""
        priorities = SupportTicket.objects.filter(tenant=self.tenant).values('priority').annotate(
            count=Count('id')
        ).order_by('priority')

        return {priority['priority']: priority['count'] for priority in priorities}

    def _get_tickets_by_type(self) -> Dict[str, int]:
        """Répartition des tickets par type"""
        types = SupportTicket.objects.filter(tenant=self.tenant).values('ticket_type').annotate(
            count=Count('id')
        ).order_by('-count')

        return {ticket_type['ticket_type']: ticket_type['count'] for ticket_type in types}

    def _get_interactions_by_type(self) -> Dict[str, int]:
        """Répartition des interactions par type"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)

        types = Interaction.objects.filter(
            tenant=self.tenant,
            interaction_date__gte=start_date
        ).values('interaction_type').annotate(
            count=Count('id')
        ).order_by('-count')

        return {interaction_type['interaction_type']: interaction_type['count'] for interaction_type in types}

    def _get_interactions_by_outcome(self) -> Dict[str, int]:
        """Répartition des interactions par résultat"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)

        outcomes = Interaction.objects.filter(
            tenant=self.tenant,
            interaction_date__gte=start_date,
            outcome__isnull=False
        ).values('outcome').annotate(
            count=Count('id')
        ).order_by('-count')

        return {outcome['outcome']: outcome['count'] for outcome in outcomes}

    def _calculate_average_deal_cycle(self) -> float:
        """Calcule le cycle de vente moyen"""
        won_opportunities = Opportunity.objects.filter(
            tenant=self.tenant,
            stage='closed_won',
            actual_close_date__isnull=False
        )

        if not won_opportunities.exists():
            return 0

        total_days = 0
        count = 0

        for opp in won_opportunities:
            if opp.actual_close_date and opp.created_at:
                days = (opp.actual_close_date - opp.created_at.date()).days
                total_days += days
                count += 1

        return total_days / count if count > 0 else 0

    def _get_crm_alerts(self) -> Dict[str, int]:
        """Alertes CRM"""
        alerts = {
            'overdue_opportunities': 0,
            'high_value_prospects': 0,
            'unassigned_leads': 0,
            'overdue_tickets': 0,
            'low_satisfaction_tickets': 0
        }

        # Opportunités en retard
        alerts['overdue_opportunities'] = Opportunity.objects.filter(
            tenant=self.tenant,
            expected_close_date__lt=timezone.now().date(),
            stage__in=['prospecting', 'qualification', 'needs_analysis', 'proposal', 'negotiation']
        ).count()

        # Prospects à forte valeur
        alerts['high_value_prospects'] = Contact.objects.filter(
            tenant=self.tenant,
            contact_type='prospect',
            lead_score__gte=80
        ).count()

        # Leads non assignés
        alerts['unassigned_leads'] = Contact.objects.filter(
            tenant=self.tenant,
            contact_type='lead',
            assigned_to__isnull=True
        ).count()

        # Tickets en retard
        alerts['overdue_tickets'] = SupportTicket.objects.filter(
            tenant=self.tenant,
            due_date__lt=timezone.now(),
            status__in=['new', 'open', 'in_progress', 'pending']
        ).count()

        # Tickets avec faible satisfaction
        alerts['low_satisfaction_tickets'] = SupportTicket.objects.filter(
            tenant=self.tenant,
            satisfaction_rating__lte=2,
            created_at__gte=timezone.now() - timedelta(days=30)
        ).count()

        return alerts

    def _get_recent_activities(self) -> List[Dict[str, Any]]:
        """Activités récentes"""
        activities = []

        # Interactions récentes
        recent_interactions = Interaction.objects.filter(
            tenant=self.tenant,
            interaction_date__gte=timezone.now() - timedelta(days=7)
        ).order_by('-interaction_date')[:10]

        for interaction in recent_interactions:
            activities.append({
                'type': f'interaction_{interaction.interaction_type}',
                'description': f"{interaction.interaction_type.title()}: {interaction.subject}",
                'date': interaction.interaction_date.isoformat(),
                'contact': interaction.contact.display_name,
                'user': interaction.user.get_full_name(),
                'outcome': interaction.outcome
            })

        return activities

    def create_contact(self, contact_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée un nouveau contact"""
        try:
            with transaction.atomic():
                contact = Contact.objects.create(
                    tenant=self.tenant,
                    first_name=contact_data['first_name'],
                    last_name=contact_data['last_name'],
                    company_name=contact_data.get('company_name', ''),
                    job_title=contact_data.get('job_title', ''),
                    contact_type=contact_data['contact_type'],
                    status=contact_data.get('status', 'active'),
                    lead_source=contact_data.get('lead_source', ''),
                    email=contact_data.get('email', ''),
                    phone=contact_data.get('phone', ''),
                    mobile=contact_data.get('mobile', ''),
                    website=contact_data.get('website', ''),
                    address_line1=contact_data.get('address_line1', ''),
                    address_line2=contact_data.get('address_line2', ''),
                    city=contact_data.get('city', ''),
                    state=contact_data.get('state', ''),
                    postal_code=contact_data.get('postal_code', ''),
                    country=contact_data.get('country', ''),
                    industry=contact_data.get('industry', ''),
                    company_size=contact_data.get('company_size', ''),
                    annual_revenue=contact_data.get('annual_revenue'),
                    assigned_to=user,
                    first_contact_date=timezone.now().date(),
                    tags=contact_data.get('tags', ''),
                    notes=contact_data.get('notes', '')
                )

                # Calculer le score de lead si c'est un lead ou prospect
                if contact.contact_type in ['lead', 'prospect']:
                    lead_score = self._calculate_lead_score(contact)
                    contact.lead_score = lead_score
                    contact.save(update_fields=['lead_score'])

                # Générer des recommandations IA si disponible
                if ai_service.is_available():
                    recommendations = self._generate_contact_recommendations(contact)
                    if recommendations:
                        self._apply_contact_recommendations(contact, recommendations)

                return {
                    'success': True,
                    'contact': {
                        'id': str(contact.id),
                        'contact_number': contact.contact_number,
                        'display_name': contact.display_name,
                        'contact_type': contact.contact_type,
                        'status': contact.status,
                        'lead_score': contact.lead_score
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _calculate_lead_score(self, contact: Contact) -> int:
        """Calcule le score de lead basé sur différents critères"""
        score = 0

        # Score basé sur les informations de contact
        if contact.email:
            score += 10
        if contact.phone or contact.mobile:
            score += 10
        if contact.company_name:
            score += 15
        if contact.job_title:
            score += 10

        # Score basé sur la source
        source_scores = {
            'website': 20,
            'referral': 25,
            'social_media': 15,
            'email_campaign': 10,
            'trade_show': 20,
            'partner': 25
        }
        score += source_scores.get(contact.lead_source, 5)

        # Score basé sur l'industrie et la taille de l'entreprise
        if contact.industry:
            score += 10
        if contact.company_size:
            score += 10
        if contact.annual_revenue:
            if contact.annual_revenue >= 1000000:  # 1M+
                score += 20
            elif contact.annual_revenue >= 100000:  # 100K+
                score += 10

        return min(score, 100)  # Maximum 100

    def _generate_contact_recommendations(self, contact: Contact) -> Dict[str, Any]:
        """Génère des recommandations IA pour le contact"""
        try:
            # Analyser le contexte du contact
            context = {
                'contact': {
                    'type': contact.contact_type,
                    'company': contact.company_name,
                    'industry': contact.industry,
                    'job_title': contact.job_title,
                    'lead_source': contact.lead_source,
                    'lead_score': contact.lead_score
                }
            }

            prompt = f"""
            En tant qu'expert CRM, analyse ce contact et fournis des recommandations:

            Contexte: {context}

            Fournis des recommandations pour:
            1. Stratégie d'approche
            2. Prochaines actions
            3. Segmentation
            4. Priorité de suivi

            Réponds au format JSON avec les clés: approach_strategy, next_actions, segmentation, follow_up_priority
            """

            ai_response = ai_service.generate_response(prompt, "crm", temperature=0.6)

            if ai_response.success:
                import json
                return json.loads(ai_response.content)

        except Exception as e:
            logger.error(f"Erreur lors de la génération de recommandations: {str(e)}")

        return {}

    def _apply_contact_recommendations(self, contact: Contact, recommendations: Dict[str, Any]):
        """Applique les recommandations IA"""
        try:
            # Log des recommandations pour suivi
            logger.info(f"Recommandations contact {contact.contact_number}: {recommendations}")

            # Appliquer la segmentation automatique
            if 'segmentation' in recommendations and recommendations['segmentation']:
                suggested_tags = recommendations['segmentation'].get('suggested_tags')
                if suggested_tags:
                    existing_tags = contact.tags.split(',') if contact.tags else []
                    new_tags = list(set(existing_tags + suggested_tags))
                    contact.tags = ','.join(new_tags)
                    contact.save(update_fields=['tags'])

        except Exception as e:
            logger.error(f"Erreur lors de l'application des recommandations: {str(e)}")

    def create_opportunity(self, opportunity_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée une nouvelle opportunité"""
        try:
            with transaction.atomic():
                # Récupérer le contact
                contact = Contact.objects.get(
                    id=opportunity_data['contact_id'],
                    tenant=self.tenant
                )

                opportunity = Opportunity.objects.create(
                    tenant=self.tenant,
                    contact=contact,
                    name=opportunity_data['name'],
                    description=opportunity_data.get('description', ''),
                    opportunity_type=opportunity_data['opportunity_type'],
                    stage=opportunity_data.get('stage', 'prospecting'),
                    priority=opportunity_data.get('priority', 'medium'),
                    amount=opportunity_data['amount'],
                    probability=opportunity_data.get('probability', 50),
                    expected_close_date=opportunity_data['expected_close_date'],
                    assigned_to=user,
                    competitors=opportunity_data.get('competitors', ''),
                    competitive_advantage=opportunity_data.get('competitive_advantage', ''),
                    source=opportunity_data.get('source', ''),
                    campaign=opportunity_data.get('campaign', ''),
                    next_step=opportunity_data.get('next_step', ''),
                    notes=opportunity_data.get('notes', '')
                )

                # Mettre à jour le statut du contact si nécessaire
                if contact.contact_type == 'lead' and contact.status != 'qualified':
                    contact.status = 'qualified'
                    contact.save(update_fields=['status'])

                return {
                    'success': True,
                    'opportunity': {
                        'id': str(opportunity.id),
                        'opportunity_number': opportunity.opportunity_number,
                        'name': opportunity.name,
                        'stage': opportunity.stage,
                        'amount': float(opportunity.amount),
                        'weighted_amount': float(opportunity.weighted_amount),
                        'probability': opportunity.probability
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def create_campaign(self, campaign_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée une nouvelle campagne"""
        try:
            with transaction.atomic():
                campaign = Campaign.objects.create(
                    tenant=self.tenant,
                    name=campaign_data['name'],
                    description=campaign_data.get('description', ''),
                    campaign_type=campaign_data['campaign_type'],
                    status=campaign_data.get('status', 'draft'),
                    start_date=campaign_data['start_date'],
                    end_date=campaign_data['end_date'],
                    budget=campaign_data.get('budget', Decimal('0.00')),
                    target_audience=campaign_data.get('target_audience', ''),
                    expected_leads=campaign_data.get('expected_leads', 0),
                    expected_revenue=campaign_data.get('expected_revenue', Decimal('0.00')),
                    campaign_manager=user,
                    subject=campaign_data.get('subject', ''),
                    content=campaign_data.get('content', ''),
                    call_to_action=campaign_data.get('call_to_action', ''),
                    segment_criteria=campaign_data.get('segment_criteria', {}),
                    notes=campaign_data.get('notes', '')
                )

                return {
                    'success': True,
                    'campaign': {
                        'id': str(campaign.id),
                        'campaign_number': campaign.campaign_number,
                        'name': campaign.name,
                        'campaign_type': campaign.campaign_type,
                        'status': campaign.status,
                        'budget': float(campaign.budget)
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def create_support_ticket(self, ticket_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée un nouveau ticket de support"""
        try:
            with transaction.atomic():
                # Récupérer le contact
                contact = Contact.objects.get(
                    id=ticket_data['contact_id'],
                    tenant=self.tenant
                )

                ticket = SupportTicket.objects.create(
                    tenant=self.tenant,
                    contact=contact,
                    subject=ticket_data['subject'],
                    description=ticket_data['description'],
                    ticket_type=ticket_data['ticket_type'],
                    status=ticket_data.get('status', 'new'),
                    priority=ticket_data.get('priority', 'medium'),
                    assigned_to=user,
                    due_date=ticket_data.get('due_date'),
                    tags=ticket_data.get('tags', ''),
                    internal_notes=ticket_data.get('internal_notes', '')
                )

                return {
                    'success': True,
                    'ticket': {
                        'id': str(ticket.id),
                        'ticket_number': ticket.ticket_number,
                        'subject': ticket.subject,
                        'ticket_type': ticket.ticket_type,
                        'status': ticket.status,
                        'priority': ticket.priority
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def create_interaction(self, interaction_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée une nouvelle interaction"""
        try:
            with transaction.atomic():
                # Récupérer le contact
                contact = Contact.objects.get(
                    id=interaction_data['contact_id'],
                    tenant=self.tenant
                )

                # Récupérer l'opportunité si spécifiée
                opportunity = None
                if interaction_data.get('opportunity_id'):
                    opportunity = Opportunity.objects.get(
                        id=interaction_data['opportunity_id'],
                        tenant=self.tenant
                    )

                interaction = Interaction.objects.create(
                    tenant=self.tenant,
                    contact=contact,
                    opportunity=opportunity,
                    interaction_type=interaction_data['interaction_type'],
                    direction=interaction_data['direction'],
                    subject=interaction_data['subject'],
                    description=interaction_data['description'],
                    interaction_date=interaction_data['interaction_date'],
                    duration_minutes=interaction_data.get('duration_minutes'),
                    outcome=interaction_data.get('outcome', ''),
                    next_action=interaction_data.get('next_action', ''),
                    next_action_date=interaction_data.get('next_action_date'),
                    user=user,
                    location=interaction_data.get('location', ''),
                    attendees=interaction_data.get('attendees', ''),
                    notes=interaction_data.get('notes', '')
                )

                # Mettre à jour la date de dernier contact
                contact.last_contact_date = interaction.interaction_date.date()
                contact.save(update_fields=['last_contact_date'])

                return {
                    'success': True,
                    'interaction': {
                        'id': str(interaction.id),
                        'interaction_type': interaction.interaction_type,
                        'subject': interaction.subject,
                        'interaction_date': interaction.interaction_date.isoformat(),
                        'outcome': interaction.outcome
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def generate_crm_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights CRM avec IA"""
        insights = []

        try:
            # Analyser les contacts
            contact_insights = self._analyze_contact_insights()
            insights.extend(contact_insights)

            # Analyser les opportunités
            opportunity_insights = self._analyze_opportunity_insights()
            insights.extend(opportunity_insights)

            # Analyser les campagnes
            campaign_insights = self._analyze_campaign_insights()
            insights.extend(campaign_insights)

            # Analyser le support client
            support_insights = self._analyze_support_insights()
            insights.extend(support_insights)

            # Générer des insights IA si disponible
            if ai_service.is_available():
                ai_insights = self._generate_ai_insights()
                insights.extend(ai_insights)

        except Exception as e:
            logger.error(f"Erreur lors de la génération d'insights: {str(e)}")
            insights.append({
                'type': 'error',
                'priority': 'high',
                'title': 'Erreur de génération d\'insights',
                'description': f'Impossible de générer les insights: {str(e)}',
                'recommendation': 'Vérifier la configuration du système',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _analyze_contact_insights(self) -> List[Dict[str, Any]]:
        """Analyse les insights de contacts"""
        insights = []

        # Leads non assignés
        unassigned_leads = Contact.objects.filter(
            tenant=self.tenant,
            contact_type='lead',
            assigned_to__isnull=True
        ).count()

        if unassigned_leads > 0:
            insights.append({
                'type': 'warning',
                'priority': 'medium',
                'title': 'Leads non assignés',
                'description': f"{unassigned_leads} lead(s) ne sont assignés à aucun commercial",
                'recommendation': 'Assigner ces leads aux commerciaux appropriés pour éviter la perte d\'opportunités',
                'generated_at': timezone.now().isoformat()
            })

        # Prospects à fort score non contactés récemment
        high_score_prospects = Contact.objects.filter(
            tenant=self.tenant,
            contact_type='prospect',
            lead_score__gte=80,
            last_contact_date__lt=timezone.now().date() - timedelta(days=7)
        ).count()

        if high_score_prospects > 0:
            insights.append({
                'type': 'opportunity',
                'priority': 'high',
                'title': 'Prospects à fort potentiel',
                'description': f"{high_score_prospects} prospect(s) avec un score élevé n'ont pas été contactés récemment",
                'recommendation': 'Relancer ces prospects prioritaires pour maximiser les chances de conversion',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _analyze_opportunity_insights(self) -> List[Dict[str, Any]]:
        """Analyse les insights d'opportunités"""
        insights = []

        # Opportunités en retard
        overdue_opportunities = Opportunity.objects.filter(
            tenant=self.tenant,
            expected_close_date__lt=timezone.now().date(),
            stage__in=['prospecting', 'qualification', 'needs_analysis', 'proposal', 'negotiation']
        ).count()

        if overdue_opportunities > 0:
            insights.append({
                'type': 'warning',
                'priority': 'high',
                'title': 'Opportunités en retard',
                'description': f"{overdue_opportunities} opportunité(s) ont dépassé leur date de clôture prévue",
                'recommendation': 'Revoir ces opportunités et mettre à jour les dates ou les clôturer',
                'generated_at': timezone.now().isoformat()
            })

        # Opportunités stagnantes
        stagnant_opportunities = Opportunity.objects.filter(
            tenant=self.tenant,
            stage__in=['prospecting', 'qualification', 'needs_analysis'],
            created_at__lt=timezone.now() - timedelta(days=30)
        ).count()

        if stagnant_opportunities > 0:
            insights.append({
                'type': 'warning',
                'priority': 'medium',
                'title': 'Opportunités stagnantes',
                'description': f"{stagnant_opportunities} opportunité(s) sont dans les premières étapes depuis plus de 30 jours",
                'recommendation': 'Accélérer le processus de vente ou qualifier ces opportunités',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _analyze_campaign_insights(self) -> List[Dict[str, Any]]:
        """Analyse les insights de campagnes"""
        insights = []

        # Campagnes avec faible performance
        poor_campaigns = Campaign.objects.filter(
            tenant=self.tenant,
            status='active',
            total_delivered__gt=0
        )

        low_performance_count = 0
        for campaign in poor_campaigns:
            if campaign.conversion_rate < 2:  # Moins de 2% de conversion
                low_performance_count += 1

        if low_performance_count > 0:
            insights.append({
                'type': 'warning',
                'priority': 'medium',
                'title': 'Campagnes sous-performantes',
                'description': f"{low_performance_count} campagne(s) ont un taux de conversion inférieur à 2%",
                'recommendation': 'Optimiser le contenu, la segmentation ou suspendre ces campagnes',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _analyze_support_insights(self) -> List[Dict[str, Any]]:
        """Analyse les insights de support"""
        insights = []

        # Tickets en retard
        overdue_tickets = SupportTicket.objects.filter(
            tenant=self.tenant,
            due_date__lt=timezone.now(),
            status__in=['new', 'open', 'in_progress', 'pending']
        ).count()

        if overdue_tickets > 0:
            insights.append({
                'type': 'critical',
                'priority': 'high',
                'title': 'Tickets en retard',
                'description': f"{overdue_tickets} ticket(s) de support ont dépassé leur échéance",
                'recommendation': 'Traiter ces tickets en priorité pour maintenir la satisfaction client',
                'generated_at': timezone.now().isoformat()
            })

        # Satisfaction client faible
        low_satisfaction = SupportTicket.objects.filter(
            tenant=self.tenant,
            satisfaction_rating__lte=2,
            created_at__gte=timezone.now() - timedelta(days=30)
        ).count()

        if low_satisfaction > 0:
            insights.append({
                'type': 'warning',
                'priority': 'high',
                'title': 'Satisfaction client faible',
                'description': f"{low_satisfaction} ticket(s) ont reçu une note de satisfaction faible ce mois",
                'recommendation': 'Analyser les causes et améliorer les processus de support',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _generate_ai_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights avec IA"""
        try:
            # Préparer le contexte CRM
            dashboard_data = self.get_crm_dashboard()

            prompt = f"""
            En tant qu'expert CRM, analyse cette situation et fournis 3-5 insights stratégiques:

            Données CRM: {dashboard_data}

            Fournis des insights sur:
            1. Optimisation du pipeline de vente
            2. Amélioration de la conversion
            3. Stratégies de rétention client
            4. Efficacité des campagnes
            5. Performance du support client

            Pour chaque insight, fournis:
            - type: 'opportunity', 'warning', 'critical', 'info'
            - priority: 'high', 'medium', 'low'
            - title: titre court
            - description: description détaillée
            - recommendation: action recommandée

            Réponds au format JSON avec une liste d'insights.
            """

            ai_response = ai_service.generate_response(prompt, "crm", temperature=0.7)

            if ai_response.success:
                import json
                ai_insights_data = json.loads(ai_response.content)

                # Ajouter la date de génération
                for insight in ai_insights_data:
                    insight['generated_at'] = timezone.now().isoformat()

                return ai_insights_data

        except Exception as e:
            logger.error(f"Erreur lors de la génération d'insights IA: {str(e)}")

        return []

    def update_lead_scores(self) -> Dict[str, Any]:
        """Met à jour les scores de tous les leads et prospects"""
        try:
            contacts = Contact.objects.filter(
                tenant=self.tenant,
                contact_type__in=['lead', 'prospect']
            )

            updated_count = 0
            for contact in contacts:
                old_score = contact.lead_score
                new_score = self._calculate_lead_score(contact)

                if old_score != new_score:
                    contact.lead_score = new_score
                    contact.save(update_fields=['lead_score'])
                    updated_count += 1

            return {
                'success': True,
                'total_contacts': contacts.count(),
                'updated_count': updated_count
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }