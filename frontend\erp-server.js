const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// Configuration du serveur
const PORT = 3000;
const HOST = 'localhost';

// Types MIME pour les fichiers statiques
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon',
  '.svg': 'image/svg+xml'
};

// Fonction pour servir les fichiers statiques
function serveStaticFile(filePath, res) {
  const extname = path.extname(filePath).toLowerCase();
  const contentType = mimeTypes[extname] || 'application/octet-stream';

  fs.readFile(filePath, (err, content) => {
    if (err) {
      if (err.code === 'ENOENT') {
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end('<h1>404 - Fichier non trouvé</h1>');
      } else {
        res.writeHead(500);
        res.end(`Erreur serveur: ${err.code}`);
      }
    } else {
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(content, 'utf-8');
    }
  });
}

// Création du serveur HTTP
const server = http.createServer((req, res) => {
  // Ajouter les headers CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Gérer les requêtes OPTIONS (preflight)
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url);
  let pathname = parsedUrl.pathname;

  // Routes principales de l'ERP
  const routes = {
    '/': 'dashboard-demo.html',
    '/dashboard': 'dashboard-demo.html',
    '/login': 'login-demo.html',
    '/hr': 'hr-management.html',
    '/sales': 'sales-management.html',
    '/purchase': 'purchase-management.html',
    '/logistics': 'logistics-management.html',
    '/stock': 'stock-management.html',
    '/accounting': 'accounting-management.html',
    '/finance': 'finance-management.html',
    '/crm': 'crm-management.html',
    '/bi': 'bi-management.html',
    '/manager': 'manager-management.html'
  };

  // Redirection vers le bon fichier
  if (routes[pathname]) {
    pathname = '/' + routes[pathname];
  }

  // Construire le chemin du fichier
  let filePath = path.join(__dirname, pathname);

  // Si c'est un répertoire, chercher index.html
  if (fs.existsSync(filePath) && fs.statSync(filePath).isDirectory()) {
    filePath = path.join(filePath, 'index.html');
  }

  // Vérifier si le fichier existe
  if (!fs.existsSync(filePath)) {
    // Si le fichier n'existe pas, rediriger vers le dashboard
    filePath = path.join(__dirname, 'dashboard-demo.html');
  }

  // Servir le fichier
  serveStaticFile(filePath, res);
});

// Gestion des erreurs du serveur
server.on('error', (err) => {
  console.error('❌ Erreur du serveur:', err);
});

// Démarrage du serveur
server.listen(PORT, HOST, () => {
  console.log('🎉 ===============================================');
  console.log('🚀 ERP HUB SERVER DÉMARRÉ AVEC SUCCÈS !');
  console.log('🎉 ===============================================');
  console.log('');
  console.log('📍 Serveur: http://localhost:' + PORT);
  console.log('🏠 Dashboard: http://localhost:' + PORT + '/dashboard');
  console.log('🔐 Login: http://localhost:' + PORT + '/login');
  console.log('');
  console.log('📋 AGENTS ERP DISPONIBLES:');
  console.log('👥 HR: http://localhost:' + PORT + '/hr');
  console.log('📈 Sales: http://localhost:' + PORT + '/sales');
  console.log('🛒 Purchase: http://localhost:' + PORT + '/purchase');
  console.log('🚚 Logistics: http://localhost:' + PORT + '/logistics');
  console.log('📦 Stock: http://localhost:' + PORT + '/stock');
  console.log('🏦 Accounting: http://localhost:' + PORT + '/accounting');
  console.log('💰 Finance: http://localhost:' + PORT + '/finance');
  console.log('🤝 CRM: http://localhost:' + PORT + '/crm');
  console.log('📊 BI: http://localhost:' + PORT + '/bi');
  console.log('👨‍💼 Manager: http://localhost:' + PORT + '/manager');
  console.log('');
  console.log('✅ AMÉLIORATIONS DATES: 100% COMPLÈTES !');
  console.log('📅 Formatage français DD/MM/YYYY dans tous les agents');
  console.log('⏰ Horodatage automatique des modifications');
  console.log('🔧 Bibliothèque date-utils.js intégrée');
  console.log('');
  console.log('🌟 Votre ERP HUB est prêt à l\'utilisation !');
  console.log('🎉 ===============================================');
});

// Gestion de l'arrêt propre du serveur
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur ERP HUB...');
  server.close(() => {
    console.log('✅ Serveur arrêté proprement');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur ERP HUB...');
  server.close(() => {
    console.log('✅ Serveur arrêté proprement');
    process.exit(0);
  });
});
