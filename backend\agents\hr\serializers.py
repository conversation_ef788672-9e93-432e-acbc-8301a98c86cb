"""
Serializers pour l'Agent HR
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    Department, Position, Employee, LeaveType, LeaveRequest,
    PerformanceReview, Training, TrainingEnrollment
)

User = get_user_model()


class DepartmentSerializer(serializers.ModelSerializer):
    """Serializer pour les départements"""
    manager_name = serializers.CharField(source='manager.get_full_name', read_only=True)
    employee_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = [
            'id', 'name', 'code', 'description', 'parent_department',
            'manager', 'manager_name', 'is_active', 'budget_annual',
            'employee_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_employee_count(self, obj):
        """Compte le nombre d'employés actifs dans le département"""
        return obj.positions.filter(
            employees__employment_status='active'
        ).count()


class PositionSerializer(serializers.ModelSerializer):
    """Serializer pour les postes"""
    department_name = serializers.CharField(source='department.name', read_only=True)
    current_employees = serializers.SerializerMethodField()
    
    class Meta:
        model = Position
        fields = [
            'id', 'title', 'code', 'description', 'department', 'department_name',
            'level', 'salary_min', 'salary_max', 'required_skills', 'is_active',
            'max_employees', 'current_employees', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_current_employees(self, obj):
        """Compte le nombre d'employés actuels dans ce poste"""
        return obj.employees.filter(employment_status='active').count()


class EmployeeSerializer(serializers.ModelSerializer):
    """Serializer pour les employés"""
    user_info = serializers.SerializerMethodField()
    position_title = serializers.CharField(source='position.title', read_only=True)
    department_name = serializers.CharField(source='position.department.name', read_only=True)
    manager_name = serializers.CharField(source='manager.user.get_full_name', read_only=True)
    
    class Meta:
        model = Employee
        fields = [
            'id', 'user', 'user_info', 'employee_id', 'position', 'position_title',
            'department_name', 'employment_status', 'contract_type', 'hire_date',
            'contract_start_date', 'contract_end_date', 'termination_date',
            'current_salary', 'salary_currency', 'manager', 'manager_name',
            'emergency_contact_name', 'emergency_contact_phone', 'skills',
            'performance_rating', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_user_info(self, obj):
        """Informations de base de l'utilisateur"""
        return {
            'id': str(obj.user.id),
            'username': obj.user.username,
            'email': obj.user.email,
            'first_name': obj.user.first_name,
            'last_name': obj.user.last_name,
            'full_name': obj.user.get_full_name(),
            'phone': obj.user.phone,
            'is_active': obj.user.is_active
        }


class LeaveTypeSerializer(serializers.ModelSerializer):
    """Serializer pour les types de congés"""
    
    class Meta:
        model = LeaveType
        fields = [
            'id', 'name', 'code', 'description', 'is_paid', 'requires_approval',
            'max_days_per_year', 'advance_notice_days', 'color', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class LeaveRequestSerializer(serializers.ModelSerializer):
    """Serializer pour les demandes de congés"""
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)
    leave_type_name = serializers.CharField(source='leave_type.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    
    class Meta:
        model = LeaveRequest
        fields = [
            'id', 'employee', 'employee_name', 'leave_type', 'leave_type_name',
            'start_date', 'end_date', 'days_requested', 'reason', 'comments',
            'status', 'approved_by', 'approved_by_name', 'approved_at',
            'rejection_reason', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class LeaveRequestCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de demandes de congés"""
    
    class Meta:
        model = LeaveRequest
        fields = [
            'leave_type', 'start_date', 'end_date', 'days_requested',
            'reason', 'comments'
        ]
    
    def validate(self, attrs):
        """Validation des dates"""
        start_date = attrs['start_date']
        end_date = attrs['end_date']
        
        if start_date >= end_date:
            raise serializers.ValidationError(
                "La date de fin doit être postérieure à la date de début"
            )
        
        # Calculer automatiquement les jours si non fourni
        if 'days_requested' not in attrs:
            attrs['days_requested'] = (end_date - start_date).days + 1
        
        return attrs


class PerformanceReviewSerializer(serializers.ModelSerializer):
    """Serializer pour les évaluations de performance"""
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)
    reviewer_name = serializers.CharField(source='reviewer.get_full_name', read_only=True)
    
    class Meta:
        model = PerformanceReview
        fields = [
            'id', 'employee', 'employee_name', 'reviewer', 'reviewer_name',
            'review_period_start', 'review_period_end', 'review_type',
            'overall_rating', 'criteria_scores', 'strengths', 'areas_for_improvement',
            'goals_next_period', 'employee_comments', 'status', 'completed_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class TrainingSerializer(serializers.ModelSerializer):
    """Serializer pour les formations"""
    enrolled_count = serializers.SerializerMethodField()
    available_spots = serializers.SerializerMethodField()
    
    class Meta:
        model = Training
        fields = [
            'id', 'title', 'description', 'trainer', 'location', 'is_online',
            'start_date', 'end_date', 'duration_hours', 'max_participants',
            'cost_per_participant', 'skills_developed', 'status',
            'enrolled_count', 'available_spots', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_enrolled_count(self, obj):
        """Nombre d'inscrits"""
        return obj.enrollments.filter(status__in=['enrolled', 'attended']).count()
    
    def get_available_spots(self, obj):
        """Places disponibles"""
        enrolled = self.get_enrolled_count(obj)
        return max(0, obj.max_participants - enrolled)


class TrainingEnrollmentSerializer(serializers.ModelSerializer):
    """Serializer pour les inscriptions aux formations"""
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)
    training_title = serializers.CharField(source='training.title', read_only=True)
    
    class Meta:
        model = TrainingEnrollment
        fields = [
            'id', 'training', 'training_title', 'employee', 'employee_name',
            'status', 'completion_score', 'enrolled_at', 'completed_at',
            'feedback', 'rating', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'enrolled_at', 'created_at', 'updated_at']


class HRDashboardSerializer(serializers.Serializer):
    """Serializer pour le dashboard RH"""
    tenant = serializers.CharField()
    timestamp = serializers.CharField()
    employees = serializers.DictField()
    departments = serializers.DictField()
    pending_actions = serializers.DictField()
    recent_activities = serializers.ListField()


class LeaveRequestActionSerializer(serializers.Serializer):
    """Serializer pour les actions sur les demandes de congés"""
    action = serializers.ChoiceField(choices=['approve', 'reject'])
    comments = serializers.CharField(required=False, allow_blank=True)


class PerformanceReviewCreateSerializer(serializers.Serializer):
    """Serializer pour la création d'évaluations"""
    employee_id = serializers.UUIDField()
    review_period_start = serializers.DateField()
    review_period_end = serializers.DateField()
    review_type = serializers.ChoiceField(choices=PerformanceReview.REVIEW_TYPES)
    overall_rating = serializers.FloatField(min_value=0, max_value=5, required=False)
    criteria_scores = serializers.DictField(required=False)
    strengths = serializers.CharField(required=False, allow_blank=True)
    areas_for_improvement = serializers.CharField(required=False, allow_blank=True)
    goals_next_period = serializers.CharField(required=False, allow_blank=True)


class TrainingCreateSerializer(serializers.Serializer):
    """Serializer pour la création de formations"""
    title = serializers.CharField(max_length=200)
    description = serializers.CharField(required=False, allow_blank=True)
    trainer = serializers.CharField(max_length=100, required=False, allow_blank=True)
    location = serializers.CharField(max_length=200, required=False, allow_blank=True)
    is_online = serializers.BooleanField(default=False)
    start_date = serializers.DateTimeField()
    end_date = serializers.DateTimeField()
    duration_hours = serializers.IntegerField(min_value=1)
    max_participants = serializers.IntegerField(min_value=1, default=20)
    cost_per_participant = serializers.DecimalField(
        max_digits=8, decimal_places=2, required=False
    )
    skills_developed = serializers.ListField(
        child=serializers.CharField(), required=False
    )


class HRInsightSerializer(serializers.Serializer):
    """Serializer pour les insights RH"""
    type = serializers.CharField()
    priority = serializers.ChoiceField(choices=['high', 'medium', 'low'])
    title = serializers.CharField()
    description = serializers.CharField()
    recommendation = serializers.CharField()
