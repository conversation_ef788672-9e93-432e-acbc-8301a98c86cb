"""
Services pour l'Agent Manager
Logique m<PERSON>tier pour l'orchestration des agents
"""
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.conf import settings

from core.models import Tenant, User
from agents.models import Agent, AgentTask, AgentCommunication, Workflow
from .models import ManagerDecision, SystemMetrics

logger = logging.getLogger('agents.manager')


class AgentManagerService:
    """
    Service principal pour l'Agent Manager
    Gère l'orchestration et la coordination des agents
    """
    
    def __init__(self, tenant: Tenant):
        self.tenant = tenant
        self.manager_agent = self._get_or_create_manager_agent()
    
    def _get_or_create_manager_agent(self) -> Agent:
        """Récupère ou crée l'agent manager pour le tenant"""
        agent, created = Agent.objects.get_or_create(
            tenant=self.tenant,
            agent_type='manager',
            defaults={
                'name': 'Agent Manager',
                'description': 'Orchestrateur central du système ERP',
                'ai_enabled': True,
                'ai_model': 'gpt-4',
                'capabilities': [
                    'task_orchestration',
                    'resource_management',
                    'performance_monitoring',
                    'decision_making',
                    'workflow_optimization'
                ]
            }
        )
        if created:
            logger.info(f"Agent Manager créé pour le tenant {self.tenant.name}")
        return agent
    
    def get_system_status(self) -> Dict[str, Any]:
        """Retourne le statut global du système"""
        agents = Agent.objects.filter(tenant=self.tenant)
        tasks = AgentTask.objects.filter(agent__tenant=self.tenant)
        
        # Calcul des métriques
        total_agents = agents.count()
        active_agents = agents.filter(status='active', is_enabled=True).count()
        
        total_tasks = tasks.count()
        pending_tasks = tasks.filter(status='pending').count()
        running_tasks = tasks.filter(status='running').count()
        completed_tasks = tasks.filter(status='completed').count()
        failed_tasks = tasks.filter(status='failed').count()
        
        # Taux de réussite
        success_rate = 0
        if total_tasks > 0:
            success_rate = (completed_tasks / total_tasks) * 100
        
        # Charge système (basée sur les tâches en cours)
        system_load = 0
        if active_agents > 0:
            system_load = (running_tasks + pending_tasks) / (active_agents * 10) * 100
            system_load = min(system_load, 100)  # Cap à 100%
        
        return {
            'tenant': self.tenant.name,
            'timestamp': timezone.now().isoformat(),
            'agents': {
                'total': total_agents,
                'active': active_agents,
                'inactive': total_agents - active_agents
            },
            'tasks': {
                'total': total_tasks,
                'pending': pending_tasks,
                'running': running_tasks,
                'completed': completed_tasks,
                'failed': failed_tasks
            },
            'performance': {
                'success_rate': round(success_rate, 2),
                'system_load': round(system_load, 2),
                'health_status': self._calculate_health_status(success_rate, system_load)
            }
        }
    
    def _calculate_health_status(self, success_rate: float, system_load: float) -> str:
        """Calcule le statut de santé du système"""
        if success_rate >= 95 and system_load <= 70:
            return 'excellent'
        elif success_rate >= 85 and system_load <= 85:
            return 'good'
        elif success_rate >= 70 and system_load <= 95:
            return 'warning'
        else:
            return 'critical'
    
    def assign_task_to_agent(self, task_data: Dict[str, Any], target_agent_type: str = None) -> AgentTask:
        """Assigne une tâche à l'agent le plus approprié"""
        
        # Sélection de l'agent cible
        if target_agent_type:
            agent = Agent.objects.filter(
                tenant=self.tenant,
                agent_type=target_agent_type,
                status='active',
                is_enabled=True
            ).first()
        else:
            agent = self._select_best_agent_for_task(task_data)
        
        if not agent:
            raise ValueError(f"Aucun agent disponible pour le type {target_agent_type}")
        
        # Création de la tâche
        task = AgentTask.objects.create(
            agent=agent,
            title=task_data.get('title', 'Tâche sans titre'),
            description=task_data.get('description', ''),
            task_type=task_data.get('task_type', 'general'),
            priority=task_data.get('priority', 3),
            input_data=task_data.get('input_data', {}),
            scheduled_at=task_data.get('scheduled_at'),
            assigned_by=task_data.get('assigned_by')
        )
        
        # Enregistrement de la décision
        self._record_decision(
            'task_assignment',
            {
                'task_id': str(task.id),
                'agent_type': agent.agent_type,
                'task_type': task.task_type,
                'priority': task.priority
            },
            f"Tâche '{task.title}' assignée à l'agent {agent.name}",
            [agent]
        )
        
        logger.info(f"Tâche {task.id} assignée à l'agent {agent.name}")
        return task
    
    def _select_best_agent_for_task(self, task_data: Dict[str, Any]) -> Optional[Agent]:
        """Sélectionne le meilleur agent pour une tâche donnée"""
        task_type = task_data.get('task_type', 'general')
        priority = task_data.get('priority', 3)
        
        # Mapping des types de tâches vers les types d'agents
        task_to_agent_mapping = {
            'hr_management': 'hr',
            'sales_process': 'sales',
            'purchase_order': 'purchase',
            'inventory_management': 'stock',
            'logistics_planning': 'logistics',
            'financial_analysis': 'finance',
            'accounting_entry': 'accounting',
            'customer_interaction': 'crm',
            'data_analysis': 'bi',
        }
        
        # Déterminer le type d'agent préféré
        preferred_agent_type = task_to_agent_mapping.get(task_type)
        
        # Requête de base pour les agents disponibles
        available_agents = Agent.objects.filter(
            tenant=self.tenant,
            status='active',
            is_enabled=True
        ).annotate(
            current_tasks=Count('tasks', filter=Q(tasks__status__in=['pending', 'running']))
        )
        
        # Prioriser l'agent spécialisé s'il existe
        if preferred_agent_type:
            specialized_agent = available_agents.filter(agent_type=preferred_agent_type).first()
            if specialized_agent:
                return specialized_agent
        
        # Sinon, sélectionner l'agent avec la charge la plus faible
        return available_agents.order_by('current_tasks', 'priority').first()
    
    def _record_decision(self, decision_type: str, context: Dict, reasoning: str, agents: List[Agent]):
        """Enregistre une décision du manager"""
        decision = ManagerDecision.objects.create(
            tenant=self.tenant,
            decision_type=decision_type,
            context=context,
            decision_data={},
            reasoning=reasoning
        )
        decision.involved_agents.set(agents)
        return decision
    
    def optimize_workflows(self) -> List[Dict[str, Any]]:
        """Analyse et optimise les workflows existants"""
        workflows = Workflow.objects.filter(tenant=self.tenant, is_enabled=True)
        optimizations = []
        
        for workflow in workflows:
            # Analyser les performances du workflow
            recent_executions = workflow.executions.filter(
                started_at__gte=timezone.now() - timedelta(days=30)
            )
            
            if recent_executions.exists():
                avg_duration = recent_executions.aggregate(
                    avg_duration=Avg('completed_at') - Avg('started_at')
                )['avg_duration']
                
                success_rate = (
                    recent_executions.filter(status='completed').count() /
                    recent_executions.count() * 100
                )
                
                # Identifier les optimisations possibles
                if success_rate < 80:
                    optimizations.append({
                        'workflow_id': str(workflow.id),
                        'workflow_name': workflow.name,
                        'issue': 'low_success_rate',
                        'current_rate': success_rate,
                        'recommendation': 'Réviser les étapes du workflow et ajouter des vérifications'
                    })
                
                if avg_duration and avg_duration.total_seconds() > 3600:  # Plus d'1 heure
                    optimizations.append({
                        'workflow_id': str(workflow.id),
                        'workflow_name': workflow.name,
                        'issue': 'long_duration',
                        'current_duration': avg_duration.total_seconds(),
                        'recommendation': 'Paralléliser certaines étapes ou optimiser les tâches'
                    })
        
        return optimizations
    
    def collect_system_metrics(self) -> SystemMetrics:
        """Collecte et sauvegarde les métriques système"""
        status = self.get_system_status()
        
        # Métriques par agent
        agent_metrics = {}
        for agent in Agent.objects.filter(tenant=self.tenant):
            agent_metrics[agent.agent_type] = {
                'status': agent.status,
                'total_tasks': agent.total_tasks,
                'success_rate': agent.success_rate,
                'last_activity': agent.last_activity.isoformat() if agent.last_activity else None
            }
        
        # Génération d'alertes
        alerts = []
        if status['performance']['success_rate'] < 70:
            alerts.append({
                'type': 'performance',
                'severity': 'high',
                'message': f"Taux de réussite faible: {status['performance']['success_rate']}%"
            })
        
        if status['performance']['system_load'] > 90:
            alerts.append({
                'type': 'load',
                'severity': 'high',
                'message': f"Charge système élevée: {status['performance']['system_load']}%"
            })
        
        # Génération de recommandations
        recommendations = []
        if status['agents']['inactive'] > 0:
            recommendations.append({
                'type': 'agents',
                'message': f"Réactiver {status['agents']['inactive']} agent(s) inactif(s)"
            })
        
        # Sauvegarde des métriques
        metrics = SystemMetrics.objects.create(
            tenant=self.tenant,
            total_agents=status['agents']['total'],
            active_agents=status['agents']['active'],
            total_tasks=status['tasks']['total'],
            pending_tasks=status['tasks']['pending'],
            running_tasks=status['tasks']['running'],
            completed_tasks=status['tasks']['completed'],
            failed_tasks=status['tasks']['failed'],
            system_load=status['performance']['system_load'],
            success_rate=status['performance']['success_rate'],
            agent_metrics=agent_metrics,
            alerts=alerts,
            recommendations=recommendations
        )
        
        return metrics
    
    def send_message_to_agent(self, target_agent_type: str, message_type: str, 
                            subject: str, content: Dict[str, Any]) -> AgentCommunication:
        """Envoie un message à un agent spécifique"""
        target_agent = Agent.objects.filter(
            tenant=self.tenant,
            agent_type=target_agent_type
        ).first()
        
        if not target_agent:
            raise ValueError(f"Agent {target_agent_type} non trouvé")
        
        message = AgentCommunication.objects.create(
            from_agent=self.manager_agent,
            to_agent=target_agent,
            message_type=message_type,
            subject=subject,
            content=content
        )
        
        logger.info(f"Message envoyé de Manager à {target_agent.name}: {subject}")
        return message
    
    def broadcast_message(self, message_type: str, subject: str, content: Dict[str, Any]) -> List[AgentCommunication]:
        """Diffuse un message à tous les agents"""
        agents = Agent.objects.filter(
            tenant=self.tenant,
            status='active',
            is_enabled=True
        ).exclude(agent_type='manager')
        
        messages = []
        for agent in agents:
            message = AgentCommunication.objects.create(
                from_agent=self.manager_agent,
                to_agent=agent,
                message_type=message_type,
                subject=subject,
                content=content
            )
            messages.append(message)
        
        logger.info(f"Message diffusé à {len(messages)} agents: {subject}")
        return messages
