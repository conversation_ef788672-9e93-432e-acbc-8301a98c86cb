import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  IconButton,
  Alert,
  CircularProgress,
  LinearProgress,
  Divider,
  Badge
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  Dashboard as DashboardIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Speed as SpeedIcon,
  DataUsage as DataUsageIcon,
  Timeline as TimelineIcon,
  Insights as InsightsIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayArrowIcon,
  Storage as StorageIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { biService } from '../services/biService';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`bi-tabpanel-${index}`}
      aria-labelledby={`bi-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const BIPage = () => {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [dashboardData, setDashboardData] = useState(null);
  const [insights, setInsights] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
    loadInsights();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const data = await biService.getDashboard();
      setDashboardData(data);
    } catch (err) {
      setError('Erreur lors du chargement du dashboard BI');
      console.error('Erreur dashboard BI:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadInsights = async () => {
    try {
      const data = await biService.getInsights();
      setInsights(data.insights || []);
    } catch (err) {
      console.error('Erreur insights BI:', err);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const getInsightIcon = (type) => {
    switch (type) {
      case 'critical':
        return <ErrorIcon color="error" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'opportunity':
        return <TrendingUpIcon color="success" />;
      case 'info':
        return <InfoIcon color="info" />;
      default:
        return <InsightsIcon color="primary" />;
    }
  };

  const getInsightColor = (type) => {
    switch (type) {
      case 'critical':
        return 'error';
      case 'warning':
        return 'warning';
      case 'opportunity':
        return 'success';
      case 'info':
        return 'info';
      default:
        return 'primary';
    }
  };

  const getHealthColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 75) return 'info';
    if (score >= 50) return 'warning';
    return 'error';
  };

  const getHealthStatus = (status) => {
    const statusMap = {
      'excellent': 'Excellent',
      'good': 'Bon',
      'warning': 'Attention',
      'critical': 'Critique'
    };
    return statusMap[status] || status;
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error" action={
          <Button color="inherit" size="small" onClick={loadDashboardData}>
            Réessayer
          </Button>
        }>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* En-tête */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Agent BI - Business Intelligence
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Analyse avancée, rapports intelligents et tableaux de bord interactifs
        </Typography>
      </Box>

      {/* Onglets */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          aria-label="BI tabs"
        >
          <Tab icon={<DashboardIcon />} label="Dashboard" />
          <Tab icon={<SpeedIcon />} label="KPIs" />
          <Tab icon={<AssessmentIcon />} label="Rapports" />
          <Tab icon={<WarningIcon />} label="Alertes" />
          <Tab icon={<StorageIcon />} label="Données" />
        </Tabs>
      </Paper>

      {/* Dashboard */}
      <TabPanel value={tabValue} index={0}>
        {dashboardData && (
          <Grid container spacing={3}>
            {/* Santé du système */}
            {dashboardData.system_health && (
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={2}>
                      <Avatar sx={{ bgcolor: `${getHealthColor(dashboardData.system_health.score)}.main`, mr: 2 }}>
                        <SpeedIcon />
                      </Avatar>
                      <Typography variant="h6">Santé du Système</Typography>
                    </Box>
                    <Typography variant="h3" color={getHealthColor(dashboardData.system_health.score)}>
                      {dashboardData.system_health.score}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {getHealthStatus(dashboardData.system_health.status)}
                    </Typography>
                    <Box mt={2}>
                      <LinearProgress 
                        variant="determinate" 
                        value={dashboardData.system_health.score} 
                        color={getHealthColor(dashboardData.system_health.score)}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Sources de données */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      <StorageIcon />
                    </Avatar>
                    <Typography variant="h6">Sources de Données</Typography>
                  </Box>
                  <Typography variant="h4" color="primary">
                    {dashboardData.data_sources?.total || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {dashboardData.data_sources?.connected || 0} connectées • {dashboardData.data_sources?.disconnected || 0} déconnectées
                  </Typography>
                  <Box mt={2}>
                    <Typography variant="caption" display="block">
                      Taux de connexion: {dashboardData.data_sources?.total > 0 ? 
                        Math.round((dashboardData.data_sources.connected / dashboardData.data_sources.total) * 100) : 0}%
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={dashboardData.data_sources?.total > 0 ? 
                        (dashboardData.data_sources.connected / dashboardData.data_sources.total) * 100 : 0} 
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* KPIs */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                      <TrendingUpIcon />
                    </Avatar>
                    <Typography variant="h6">KPIs</Typography>
                  </Box>
                  <Typography variant="h4" color="success.main">
                    {dashboardData.kpis?.total || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {dashboardData.kpis?.active || 0} actifs • {dashboardData.kpis?.on_target || 0} sur cible
                  </Typography>
                  <Box display="flex" gap={1} mt={2}>
                    {dashboardData.kpis?.warning > 0 && (
                      <Chip 
                        label={`${dashboardData.kpis.warning} alertes`} 
                        size="small" 
                        color="warning" 
                      />
                    )}
                    {dashboardData.kpis?.critical > 0 && (
                      <Chip 
                        label={`${dashboardData.kpis.critical} critiques`} 
                        size="small" 
                        color="error" 
                      />
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Rapports */}
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                      <AssessmentIcon />
                    </Avatar>
                    <Typography variant="h6">Rapports</Typography>
                  </Box>
                  <Typography variant="h4" color="info.main">
                    {dashboardData.reports?.total || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {dashboardData.reports?.published || 0} publiés • {dashboardData.reports?.scheduled || 0} planifiés
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Tableaux de bord */}
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                      <DashboardIcon />
                    </Avatar>
                    <Typography variant="h6">Dashboards</Typography>
                  </Box>
                  <Typography variant="h4" color="secondary.main">
                    {dashboardData.dashboards?.total || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {dashboardData.dashboards?.active || 0} actifs • {dashboardData.dashboards?.public || 0} publics
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Alertes */}
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                      <WarningIcon />
                    </Avatar>
                    <Typography variant="h6">Alertes</Typography>
                  </Box>
                  <Typography variant="h4" color="warning.main">
                    {dashboardData.alerts?.triggered || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {dashboardData.alerts?.critical || 0} critiques • {dashboardData.alerts?.total || 0} total
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Tâches d'analyse */}
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      <AnalyticsIcon />
                    </Avatar>
                    <Typography variant="h6">Analyses</Typography>
                  </Box>
                  <Typography variant="h4" color="primary">
                    {dashboardData.analysis_jobs?.running || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    en cours • {dashboardData.analysis_jobs?.completed || 0} terminées
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Insights et recommandations */}
            {insights.length > 0 && (
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Insights et Recommandations IA
                    </Typography>
                    <List>
                      {insights.slice(0, 5).map((insight, index) => (
                        <React.Fragment key={index}>
                          <ListItem>
                            <ListItemAvatar>
                              <Avatar sx={{ bgcolor: `${getInsightColor(insight.type)}.main` }}>
                                {getInsightIcon(insight.type)}
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={
                                <Box display="flex" alignItems="center" gap={1}>
                                  <Typography variant="subtitle2">
                                    {insight.title}
                                  </Typography>
                                  <Chip 
                                    label={insight.priority} 
                                    size="small" 
                                    color={insight.priority === 'high' ? 'error' : insight.priority === 'medium' ? 'warning' : 'default'}
                                  />
                                </Box>
                              }
                              secondary={
                                <Box>
                                  <Typography variant="body2" color="text.secondary">
                                    {insight.description}
                                  </Typography>
                                  <Typography variant="caption" color="primary" sx={{ fontWeight: 'medium' }}>
                                    💡 {insight.recommendation}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                          {index < insights.length - 1 && <Divider variant="inset" component="li" />}
                        </React.Fragment>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Recommandations système */}
            {dashboardData.recommendations && dashboardData.recommendations.length > 0 && (
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Recommandations Système
                    </Typography>
                    <List>
                      {dashboardData.recommendations.map((rec, index) => (
                        <ListItem key={index}>
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: `${getInsightColor(rec.type)}.main` }}>
                              {getInsightIcon(rec.type)}
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={rec.title}
                            secondary={rec.description}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        )}
      </TabPanel>

      {/* KPIs */}
      <TabPanel value={tabValue} index={1}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5">Indicateurs de Performance (KPIs)</Typography>
          <Button variant="contained" startIcon={<AddIcon />}>
            Nouveau KPI
          </Button>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Interface de gestion des KPIs en cours de développement...
        </Typography>
      </TabPanel>

      {/* Rapports */}
      <TabPanel value={tabValue} index={2}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5">Rapports et Analyses</Typography>
          <Button variant="contained" startIcon={<AddIcon />}>
            Nouveau Rapport
          </Button>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Interface de gestion des rapports en cours de développement...
        </Typography>
      </TabPanel>

      {/* Alertes */}
      <TabPanel value={tabValue} index={3}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5">Alertes et Notifications</Typography>
          <Button variant="contained" startIcon={<AddIcon />}>
            Nouvelle Alerte
          </Button>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Interface de gestion des alertes en cours de développement...
        </Typography>
      </TabPanel>

      {/* Données */}
      <TabPanel value={tabValue} index={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5">Sources de Données</Typography>
          <Button variant="contained" startIcon={<AddIcon />}>
            Nouvelle Source
          </Button>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Interface de gestion des sources de données en cours de développement...
        </Typography>
      </TabPanel>

      {/* Bouton de rafraîchissement */}
      <Box position="fixed" bottom={16} right={16}>
        <IconButton
          color="primary"
          onClick={loadDashboardData}
          sx={{
            bgcolor: 'background.paper',
            boxShadow: 2,
            '&:hover': {
              bgcolor: 'background.paper',
              boxShadow: 4,
            },
          }}
        >
          <RefreshIcon />
        </IconButton>
      </Box>
    </Container>
  );
};

export default BIPage;
