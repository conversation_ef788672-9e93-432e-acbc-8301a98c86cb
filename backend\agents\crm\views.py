"""
Vues pour l'agent CRM
"""
import logging
from django.db import models
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema

from core.permissions import CRMReadPermission, CRMWritePermission
from .services import CRMService
from .models import Contact, Opportunity, Campaign, SupportTicket, Interaction
from .serializers import (
    ContactSerializer, ContactCreateSerializer, OpportunitySerializer,
    OpportunityCreateSerializer, CampaignSerializer, CampaignCreateSerializer,
    SupportTicketSerializer, SupportTicketCreateSerializer, InteractionSerializer,
    InteractionCreateSerializer, CRMDashboardSerializer, CRMInsightSerializer,
    LeadScoreUpdateSerializer
)

logger = logging.getLogger('agents.crm')


@extend_schema(
    summary="Statut de l'agent CRM",
    description="Retourne le statut de l'agent CRM"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def crm_status(request):
    """Retourne le statut de l'agent CRM"""
    try:
        crm_service = CRMService(request.user.tenant)

        return Response({
            'status': 'active',
            'agent': 'crm',
            'message': 'Agent CRM opérationnel',
            'capabilities': [
                'contact_management',
                'opportunity_tracking',
                'campaign_management',
                'customer_support',
                'customer_segmentation',
                'sales_analytics',
                'lead_scoring',
                'customer_insights'
            ]
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut CRM: {str(e)}")
        return Response({
            'status': 'error',
            'agent': 'crm',
            'message': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Dashboard de l'agent CRM",
    description="Retourne les données complètes du dashboard CRM"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, CRMReadPermission])
def crm_dashboard(request):
    """Retourne les données du dashboard CRM"""
    try:
        crm_service = CRMService(request.user.tenant)
        dashboard_data = crm_service.get_crm_dashboard()

        return Response(dashboard_data)
    except Exception as e:
        logger.error(f"Erreur lors de la génération du dashboard CRM: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Gestion des contacts
class ContactListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des contacts"""
    serializer_class = ContactSerializer
    permission_classes = [IsAuthenticated, CRMReadPermission]

    def get_queryset(self):
        queryset = Contact.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        contact_type = self.request.query_params.get('type')
        if contact_type:
            queryset = queryset.filter(contact_type=contact_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        lead_source = self.request.query_params.get('source')
        if lead_source:
            queryset = queryset.filter(lead_source=lead_source)

        assigned_to = self.request.query_params.get('assigned_to')
        if assigned_to:
            queryset = queryset.filter(assigned_to_id=assigned_to)

        # Filtre par score de lead
        min_score = self.request.query_params.get('min_score')
        if min_score:
            queryset = queryset.filter(lead_score__gte=min_score)

        # Recherche par nom, entreprise ou email
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(first_name__icontains=search) |
                models.Q(last_name__icontains=search) |
                models.Q(company_name__icontains=search) |
                models.Q(email__icontains=search)
            )

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class ContactDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un contact"""
    serializer_class = ContactSerializer
    permission_classes = [IsAuthenticated, CRMWritePermission]

    def get_queryset(self):
        return Contact.objects.filter(tenant=self.request.user.tenant)


class ContactCreateView(APIView):
    """Vue pour créer des contacts avec logique métier"""
    permission_classes = [IsAuthenticated, CRMWritePermission]

    @extend_schema(
        summary="Créer un contact",
        description="Crée un nouveau contact avec calcul automatique du score de lead",
        request=ContactCreateSerializer
    )
    def post(self, request):
        """Crée un nouveau contact"""
        try:
            serializer = ContactCreateSerializer(data=request.data)
            if serializer.is_valid():
                crm_service = CRMService(request.user.tenant)

                result = crm_service.create_contact(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création du contact: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Gestion des opportunités
class OpportunityListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des opportunités"""
    serializer_class = OpportunitySerializer
    permission_classes = [IsAuthenticated, CRMReadPermission]

    def get_queryset(self):
        queryset = Opportunity.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        stage = self.request.query_params.get('stage')
        if stage:
            queryset = queryset.filter(stage=stage)

        opportunity_type = self.request.query_params.get('type')
        if opportunity_type:
            queryset = queryset.filter(opportunity_type=opportunity_type)

        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        assigned_to = self.request.query_params.get('assigned_to')
        if assigned_to:
            queryset = queryset.filter(assigned_to_id=assigned_to)

        contact_id = self.request.query_params.get('contact')
        if contact_id:
            queryset = queryset.filter(contact_id=contact_id)

        # Filtre par montant
        min_amount = self.request.query_params.get('min_amount')
        if min_amount:
            queryset = queryset.filter(amount__gte=min_amount)

        # Filtre par date de clôture
        close_date_from = self.request.query_params.get('close_date_from')
        if close_date_from:
            queryset = queryset.filter(expected_close_date__gte=close_date_from)

        close_date_to = self.request.query_params.get('close_date_to')
        if close_date_to:
            queryset = queryset.filter(expected_close_date__lte=close_date_to)

        # Recherche par nom
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(contact__company_name__icontains=search) |
                models.Q(contact__first_name__icontains=search) |
                models.Q(contact__last_name__icontains=search)
            )

        return queryset.order_by('-expected_close_date', '-amount')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class OpportunityDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une opportunité"""
    serializer_class = OpportunitySerializer
    permission_classes = [IsAuthenticated, CRMWritePermission]

    def get_queryset(self):
        return Opportunity.objects.filter(tenant=self.request.user.tenant)


class OpportunityCreateView(APIView):
    """Vue pour créer des opportunités avec logique métier"""
    permission_classes = [IsAuthenticated, CRMWritePermission]

    @extend_schema(
        summary="Créer une opportunité",
        description="Crée une nouvelle opportunité avec calcul automatique du montant pondéré",
        request=OpportunityCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle opportunité"""
        try:
            serializer = OpportunityCreateSerializer(data=request.data)
            if serializer.is_valid():
                crm_service = CRMService(request.user.tenant)

                result = crm_service.create_opportunity(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'opportunité: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Gestion des campagnes
class CampaignListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des campagnes"""
    serializer_class = CampaignSerializer
    permission_classes = [IsAuthenticated, CRMReadPermission]

    def get_queryset(self):
        queryset = Campaign.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        campaign_type = self.request.query_params.get('type')
        if campaign_type:
            queryset = queryset.filter(campaign_type=campaign_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        campaign_manager = self.request.query_params.get('manager')
        if campaign_manager:
            queryset = queryset.filter(campaign_manager_id=campaign_manager)

        # Recherche par nom
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(description__icontains=search)
            )

        return queryset.order_by('-start_date')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class CampaignDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une campagne"""
    serializer_class = CampaignSerializer
    permission_classes = [IsAuthenticated, CRMWritePermission]

    def get_queryset(self):
        return Campaign.objects.filter(tenant=self.request.user.tenant)


class CampaignCreateView(APIView):
    """Vue pour créer des campagnes avec logique métier"""
    permission_classes = [IsAuthenticated, CRMWritePermission]

    @extend_schema(
        summary="Créer une campagne",
        description="Crée une nouvelle campagne marketing avec génération automatique du numéro",
        request=CampaignCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle campagne"""
        try:
            serializer = CampaignCreateSerializer(data=request.data)
            if serializer.is_valid():
                crm_service = CRMService(request.user.tenant)

                result = crm_service.create_campaign(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de la campagne: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Gestion des tickets de support
class SupportTicketListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des tickets de support"""
    serializer_class = SupportTicketSerializer
    permission_classes = [IsAuthenticated, CRMReadPermission]

    def get_queryset(self):
        queryset = SupportTicket.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        ticket_type = self.request.query_params.get('type')
        if ticket_type:
            queryset = queryset.filter(ticket_type=ticket_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        assigned_to = self.request.query_params.get('assigned_to')
        if assigned_to:
            queryset = queryset.filter(assigned_to_id=assigned_to)

        contact_id = self.request.query_params.get('contact')
        if contact_id:
            queryset = queryset.filter(contact_id=contact_id)

        # Filtre par satisfaction
        satisfaction = self.request.query_params.get('satisfaction')
        if satisfaction:
            queryset = queryset.filter(satisfaction_rating=satisfaction)

        # Recherche par sujet ou description
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(subject__icontains=search) |
                models.Q(description__icontains=search) |
                models.Q(ticket_number__icontains=search)
            )

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class SupportTicketDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un ticket de support"""
    serializer_class = SupportTicketSerializer
    permission_classes = [IsAuthenticated, CRMWritePermission]

    def get_queryset(self):
        return SupportTicket.objects.filter(tenant=self.request.user.tenant)


class SupportTicketCreateView(APIView):
    """Vue pour créer des tickets de support avec logique métier"""
    permission_classes = [IsAuthenticated, CRMWritePermission]

    @extend_schema(
        summary="Créer un ticket de support",
        description="Crée un nouveau ticket de support avec génération automatique du numéro",
        request=SupportTicketCreateSerializer
    )
    def post(self, request):
        """Crée un nouveau ticket de support"""
        try:
            serializer = SupportTicketCreateSerializer(data=request.data)
            if serializer.is_valid():
                crm_service = CRMService(request.user.tenant)

                result = crm_service.create_support_ticket(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création du ticket: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Gestion des interactions
class InteractionListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des interactions"""
    serializer_class = InteractionSerializer
    permission_classes = [IsAuthenticated, CRMReadPermission]

    def get_queryset(self):
        queryset = Interaction.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        interaction_type = self.request.query_params.get('type')
        if interaction_type:
            queryset = queryset.filter(interaction_type=interaction_type)

        direction = self.request.query_params.get('direction')
        if direction:
            queryset = queryset.filter(direction=direction)

        outcome = self.request.query_params.get('outcome')
        if outcome:
            queryset = queryset.filter(outcome=outcome)

        contact_id = self.request.query_params.get('contact')
        if contact_id:
            queryset = queryset.filter(contact_id=contact_id)

        opportunity_id = self.request.query_params.get('opportunity')
        if opportunity_id:
            queryset = queryset.filter(opportunity_id=opportunity_id)

        user_id = self.request.query_params.get('user')
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # Filtre par date
        date_from = self.request.query_params.get('date_from')
        if date_from:
            queryset = queryset.filter(interaction_date__gte=date_from)

        date_to = self.request.query_params.get('date_to')
        if date_to:
            queryset = queryset.filter(interaction_date__lte=date_to)

        # Recherche par sujet ou description
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(subject__icontains=search) |
                models.Q(description__icontains=search)
            )

        return queryset.order_by('-interaction_date')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class InteractionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une interaction"""
    serializer_class = InteractionSerializer
    permission_classes = [IsAuthenticated, CRMWritePermission]

    def get_queryset(self):
        return Interaction.objects.filter(tenant=self.request.user.tenant)


class InteractionCreateView(APIView):
    """Vue pour créer des interactions avec logique métier"""
    permission_classes = [IsAuthenticated, CRMWritePermission]

    @extend_schema(
        summary="Créer une interaction",
        description="Crée une nouvelle interaction avec mise à jour automatique du contact",
        request=InteractionCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle interaction"""
        try:
            serializer = InteractionCreateSerializer(data=request.data)
            if serializer.is_valid():
                crm_service = CRMService(request.user.tenant)

                result = crm_service.create_interaction(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'interaction: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Insights et analytics
@extend_schema(
    summary="Insights CRM IA",
    description="Génère des insights et recommandations CRM basés sur l'IA"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, CRMReadPermission])
def crm_insights(request):
    """Génère des insights CRM avec IA"""
    try:
        crm_service = CRMService(request.user.tenant)
        insights = crm_service.generate_crm_insights()

        return Response({'insights': insights})
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'insights: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération d\'insights: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Mise à jour des scores de leads
@extend_schema(
    summary="Mettre à jour les scores de leads",
    description="Recalcule les scores de tous les leads et prospects"
)
@api_view(['POST'])
@permission_classes([IsAuthenticated, CRMWritePermission])
def update_lead_scores(request):
    """Met à jour les scores de leads"""
    try:
        crm_service = CRMService(request.user.tenant)
        result = crm_service.update_lead_scores()

        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des scores: {str(e)}")
        return Response({
            'error': f'Erreur interne: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)