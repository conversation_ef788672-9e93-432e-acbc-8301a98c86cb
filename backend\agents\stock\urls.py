"""
URLs pour l'agent Stock
"""
from django.urls import path
from . import views

app_name = 'stock'

urlpatterns = [
    # Statut et dashboard
    path('status/', views.stock_status, name='status'),
    path('dashboard/', views.stock_dashboard, name='dashboard'),
    path('performance/', views.stock_performance, name='performance'),
    path('insights/', views.stock_insights, name='insights'),
    path('alerts/', views.stock_alerts, name='alerts'),
    path('valuation/', views.stock_valuation, name='valuation'),
    path('location-utilization/', views.location_utilization, name='location_utilization'),

    # Gestion des entrepôts
    path('warehouses/', views.WarehouseListCreateView.as_view(), name='warehouse_list_create'),
    path('warehouses/<uuid:pk>/', views.WarehouseDetailView.as_view(), name='warehouse_detail'),

    # Gestion des emplacements
    path('locations/', views.LocationListCreateView.as_view(), name='location_list_create'),
    path('locations/<uuid:pk>/', views.LocationDetailView.as_view(), name='location_detail'),

    # Gestion des articles
    path('items/', views.StockItemListCreateView.as_view(), name='item_list_create'),
    path('items/<uuid:pk>/', views.StockItemDetailView.as_view(), name='item_detail'),

    # Niveaux de stock
    path('levels/', views.StockLevelListView.as_view(), name='level_list'),

    # Gestion des mouvements
    path('movements/', views.StockMovementListCreateView.as_view(), name='movement_list_create'),
    path('movements/create/', views.StockMovementCreateView.as_view(), name='movement_create'),
    path('movements/<uuid:pk>/', views.StockMovementDetailView.as_view(), name='movement_detail'),

    # Gestion des réservations
    path('reservations/', views.StockReservationListCreateView.as_view(), name='reservation_list_create'),
    path('reservations/create/', views.StockReservationCreateView.as_view(), name='reservation_create'),
    path('reservations/<uuid:pk>/', views.StockReservationDetailView.as_view(), name='reservation_detail'),

    # Gestion des inventaires
    path('inventories/', views.StockInventoryListCreateView.as_view(), name='inventory_list_create'),
    path('inventories/create/', views.StockInventoryCreateView.as_view(), name='inventory_create'),
    path('inventories/<uuid:pk>/', views.StockInventoryDetailView.as_view(), name='inventory_detail'),
    path('inventories/count/', views.InventoryCountView.as_view(), name='inventory_count'),
    path('inventories/<uuid:inventory_id>/validate/', views.InventoryValidationView.as_view(), name='inventory_validate'),
]
