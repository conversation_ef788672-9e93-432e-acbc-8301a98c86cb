// Script de test simple pour vérifier que Node.js fonctionne
const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

// Serveur HTTP simple
const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  
  // Servir le fichier index.html
  if (req.url === '/' || req.url === '/index.html') {
    const indexPath = path.join(__dirname, 'index.html');
    
    if (fs.existsSync(indexPath)) {
      const content = fs.readFileSync(indexPath, 'utf8');
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(content);
    } else {
      // HTML de test si index.html n'existe pas
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(`
        <!DOCTYPE html>
        <html lang="fr">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>ERP HUB - Test Server</title>
          <style>
            body {
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
              margin: 0;
              padding: 20px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              min-height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .container {
              text-align: center;
              background: rgba(255, 255, 255, 0.1);
              padding: 40px;
              border-radius: 20px;
              backdrop-filter: blur(10px);
              border: 1px solid rgba(255, 255, 255, 0.2);
            }
            h1 { font-size: 3rem; margin-bottom: 20px; }
            p { font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9; }
            .status { 
              background: #4caf50; 
              padding: 10px 20px; 
              border-radius: 25px; 
              display: inline-block;
              margin: 10px;
            }
            .features {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 20px;
              margin-top: 30px;
            }
            .feature {
              background: rgba(255, 255, 255, 0.1);
              padding: 20px;
              border-radius: 10px;
              border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .emoji { font-size: 2rem; margin-bottom: 10px; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🚀 ERP HUB</h1>
            <p>Serveur de test fonctionnel !</p>
            <div class="status">✅ Node.js opérationnel</div>
            <div class="status">✅ Serveur HTTP actif</div>
            
            <div class="features">
              <div class="feature">
                <div class="emoji">👑</div>
                <h3>Agent Manager</h3>
                <p>Supervision système</p>
              </div>
              <div class="feature">
                <div class="emoji">👥</div>
                <h3>Agent HR</h3>
                <p>Ressources humaines</p>
              </div>
              <div class="feature">
                <div class="emoji">💼</div>
                <h3>Agent Sales</h3>
                <p>Gestion commerciale</p>
              </div>
              <div class="feature">
                <div class="emoji">💰</div>
                <h3>Agent Finance</h3>
                <p>Gestion financière</p>
              </div>
              <div class="feature">
                <div class="emoji">📦</div>
                <h3>Agent Stock</h3>
                <p>Gestion inventaire</p>
              </div>
              <div class="feature">
                <div class="emoji">🤝</div>
                <h3>Agent CRM</h3>
                <p>Relation client</p>
              </div>
            </div>
            
            <p style="margin-top: 30px; font-size: 1rem; opacity: 0.7;">
              Port: ${PORT} | Node.js: ${process.version} | Heure: ${new Date().toLocaleString('fr-FR')}
            </p>
          </div>
        </body>
        </html>
      `);
    }
  } else {
    // 404 pour les autres routes
    res.writeHead(404, { 'Content-Type': 'text/html' });
    res.end(`
      <html>
        <body style="font-family: Arial; text-align: center; padding: 50px;">
          <h1>404 - Page non trouvée</h1>
          <p>Retour à <a href="/">l'accueil</a></p>
        </body>
      </html>
    `);
  }
});

server.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 ERP HUB Test Server démarré !');
  console.log(`📍 URL: http://localhost:${PORT}`);
  console.log(`🌐 Réseau: http://0.0.0.0:${PORT}`);
  console.log(`⏰ Démarré à: ${new Date().toLocaleString('fr-FR')}`);
  console.log('📊 Serveur prêt à recevoir les connexions...');
});

server.on('error', (err) => {
  console.error('❌ Erreur serveur:', err.message);
  if (err.code === 'EADDRINUSE') {
    console.log(`⚠️  Le port ${PORT} est déjà utilisé. Essayez un autre port.`);
  }
});

// Gestion propre de l'arrêt
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur...');
  server.close(() => {
    console.log('✅ Serveur arrêté proprement.');
    process.exit(0);
  });
});

console.log('🔧 Script de test ERP HUB chargé...');
