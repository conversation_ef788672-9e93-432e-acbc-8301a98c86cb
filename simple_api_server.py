# 🚀 API SIMPLE POUR ERP HUB
# Serveur Flask simple avec SQLite (fallback pour problèmes PostgreSQL)

from flask import Flask, request, jsonify
from flask_cors import CORS
import sqlite3
import json
import os
from datetime import datetime
import secrets

# Configuration de l'application
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(32)

# Extensions
CORS(app, origins=['*'])  # Permettre toutes les origines pour le développement

# Base de données SQLite
DATABASE = 'erp_hub_simple.db'

def get_db_connection():
    """Obtenir une connexion à la base SQLite"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """Initialiser la base SQLite avec les tables nécessaires"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Table des budgets simplifiée
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS budgets (
            id TEXT PRIMARY KEY,
            category_name TEXT NOT NULL,
            category_type TEXT NOT NULL,
            cost_center TEXT,
            cost_center_name TEXT,
            analytic_code TEXT,
            analytic_code_name TEXT,
            responsible TEXT,
            department TEXT,
            notes TEXT,
            forecast REAL DEFAULT 0,
            realized REAL DEFAULT 0,
            monthly_data TEXT,  -- JSON des données mensuelles
            created_date TEXT,
            modified_date TEXT
        )
    ''')
    
    conn.commit()
    conn.close()
    print("✅ Base SQLite initialisée")

# ===== ENDPOINTS BUDGETS =====

@app.route('/api/health', methods=['GET'])
def health_check():
    """Vérifier l'état du serveur"""
    return jsonify({
        'success': True,
        'message': 'Serveur ERP HUB opérationnel',
        'database': 'SQLite',
        'timestamp': datetime.now().isoformat()
    }), 200

@app.route('/api/budgets', methods=['GET'])
def get_budgets():
    """Récupérer tous les budgets"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM budgets ORDER BY created_date DESC')
        rows = cursor.fetchall()
        
        budgets = []
        for row in rows:
            budget = {
                'id': row['id'],
                'categoryName': row['category_name'],
                'categoryType': row['category_type'],
                'costCenter': row['cost_center'],
                'costCenterName': row['cost_center_name'],
                'analyticCode': row['analytic_code'],
                'analyticCodeName': row['analytic_code_name'],
                'responsible': row['responsible'],
                'department': row['department'],
                'notes': row['notes'],
                'forecast': row['forecast'],
                'realized': row['realized'],
                'monthlyData': json.loads(row['monthly_data']) if row['monthly_data'] else {},
                'createdDate': row['created_date'],
                'modifiedDate': row['modified_date']
            }
            budgets.append(budget)
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': budgets,
            'count': len(budgets)
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets', methods=['POST'])
def create_budget():
    """Créer un nouveau budget"""
    try:
        budget_data = request.get_json()
        
        # Validation des champs obligatoires
        required_fields = ['id', 'categoryName', 'categoryType']
        for field in required_fields:
            if field not in budget_data:
                return jsonify({
                    'success': False,
                    'error': f'Champ obligatoire manquant : {field}'
                }), 400
        
        # Ajouter les dates
        budget_data['createdDate'] = datetime.now().isoformat()
        budget_data['modifiedDate'] = datetime.now().isoformat()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO budgets (
                id, category_name, category_type, cost_center, cost_center_name,
                analytic_code, analytic_code_name, responsible, department, notes,
                forecast, realized, monthly_data, created_date, modified_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            budget_data['id'],
            budget_data['categoryName'],
            budget_data['categoryType'],
            budget_data.get('costCenter', ''),
            budget_data.get('costCenterName', ''),
            budget_data.get('analyticCode', ''),
            budget_data.get('analyticCodeName', ''),
            budget_data.get('responsible', ''),
            budget_data.get('department', ''),
            budget_data.get('notes', ''),
            budget_data.get('forecast', 0),
            budget_data.get('realized', 0),
            json.dumps(budget_data.get('monthlyData', {})),
            budget_data['createdDate'],
            budget_data['modifiedDate']
        ))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Budget créé avec succès',
            'data': budget_data
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['PUT'])
def update_budget(budget_id):
    """Mettre à jour un budget"""
    try:
        budget_data = request.get_json()
        budget_data['modifiedDate'] = datetime.now().isoformat()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE budgets SET
                category_name = ?, category_type = ?, cost_center = ?, cost_center_name = ?,
                analytic_code = ?, analytic_code_name = ?, responsible = ?, department = ?,
                notes = ?, forecast = ?, realized = ?, monthly_data = ?, modified_date = ?
            WHERE id = ?
        ''', (
            budget_data['categoryName'],
            budget_data['categoryType'],
            budget_data.get('costCenter', ''),
            budget_data.get('costCenterName', ''),
            budget_data.get('analyticCode', ''),
            budget_data.get('analyticCodeName', ''),
            budget_data.get('responsible', ''),
            budget_data.get('department', ''),
            budget_data.get('notes', ''),
            budget_data.get('forecast', 0),
            budget_data.get('realized', 0),
            json.dumps(budget_data.get('monthlyData', {})),
            budget_data['modifiedDate'],
            budget_id
        ))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Budget mis à jour avec succès'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['DELETE'])
def delete_budget(budget_id):
    """Supprimer un budget"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM budgets WHERE id = ?', (budget_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Budget supprimé avec succès'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== LANCEMENT DU SERVEUR =====

if __name__ == '__main__':
    print("🚀 Démarrage du serveur API ERP HUB SIMPLE...")
    print("🗄️ Base de données : SQLite")
    
    # Initialiser la base de données
    init_database()
    
    print("🌐 Serveur disponible sur : http://localhost:5000")
    print("📋 Endpoints disponibles :")
    print("   GET  /api/health           - État du serveur")
    print("   GET  /api/budgets          - Récupérer tous les budgets")
    print("   POST /api/budgets          - Créer un nouveau budget")
    print("   PUT  /api/budgets/<id>     - Mettre à jour un budget")
    print("   DELETE /api/budgets/<id>   - Supprimer un budget")
    
    # Lancer le serveur
    app.run(debug=True, host='0.0.0.0', port=5000)
