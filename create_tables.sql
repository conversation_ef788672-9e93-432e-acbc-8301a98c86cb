-- 🗄️ CRÉATION DES TABLES POSTGRESQL POUR ERP HUB
-- Script SQL pour initialiser la base de données

-- Table des budgets
CREATE TABLE IF NOT EXISTS budgets (
    id VARCHAR(255) PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    category_type VARCHAR(20) NOT NULL CHECK (category_type IN ('revenue', 'expense', 'investment', 'financial', 'exceptional')),
    cost_center VARCHAR(50),
    cost_center_name VARCHAR(100),
    analytic_code VARCHAR(50),
    analytic_code_name VARCHAR(100),
    responsible VARCHAR(100),
    department VARCHAR(50),
    notes TEXT,
    forecast DECIMAL(15,2) DEFAULT 0,
    realized DECIMAL(15,2) DEFAULT 0,
    monthly_data JSONB,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des mouvements de trésorerie
CREATE TABLE IF NOT EXISTS movements (
    id VARCHAR(255) PRIMARY KEY,
    account_id VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    description TEXT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('debit', 'credit')),
    category VARCHAR(50),
    reference VARCHAR(50),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des comptes bancaires
CREATE TABLE IF NOT EXISTS accounts (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    bank VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0,
    alert_threshold DECIMAL(15,2) DEFAULT 0,
    iban VARCHAR(34),
    currency VARCHAR(3) DEFAULT 'EUR',
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table de l'historique des actions
CREATE TABLE IF NOT EXISTS action_history (
    id SERIAL PRIMARY KEY,
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id VARCHAR(255),
    old_data JSONB,
    new_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_budgets_category_type ON budgets(category_type);
CREATE INDEX IF NOT EXISTS idx_budgets_cost_center ON budgets(cost_center);
CREATE INDEX IF NOT EXISTS idx_budgets_created_date ON budgets(created_date);
CREATE INDEX IF NOT EXISTS idx_movements_date ON movements(date);
CREATE INDEX IF NOT EXISTS idx_movements_account ON movements(account_id);
CREATE INDEX IF NOT EXISTS idx_history_created ON action_history(created_date);

-- Insérer des données de démonstration
INSERT INTO budgets (id, category_name, category_type, cost_center, cost_center_name, analytic_code, analytic_code_name, responsible, department, forecast, realized, monthly_data) VALUES
('budget_001', 'Marketing Digital', 'expense', 'CC001', 'Marketing', 'AC001', 'Publicité', 'Jean Dupont', 'Marketing', 50000, 12500, '{"janvier": {"forecast": 4167, "realized": 4200}, "février": {"forecast": 4167, "realized": 3800}, "mars": {"forecast": 4167, "realized": 4500}}'),
('budget_002', 'Ventes Produits', 'revenue', 'CC002', 'Commercial', 'AC002', 'Ventes', 'Marie Martin', 'Commercial', 150000, 45000, '{"janvier": {"forecast": 12500, "realized": 15000}, "février": {"forecast": 12500, "realized": 14000}, "mars": {"forecast": 12500, "realized": 16000}}'),
('budget_003', 'Formation Personnel', 'expense', 'CC003', 'RH', 'AC003', 'Formation', 'Pierre Durand', 'Ressources Humaines', 25000, 8500, '{"janvier": {"forecast": 2083, "realized": 2500}, "février": {"forecast": 2083, "realized": 3000}, "mars": {"forecast": 2083, "realized": 3000}}'),
('budget_004', 'Investissement IT', 'investment', 'CC004', 'IT', 'AC004', 'Matériel', 'Sophie Leroy', 'Informatique', 75000, 25000, '{"janvier": {"forecast": 6250, "realized": 8000}, "février": {"forecast": 6250, "realized": 7000}, "mars": {"forecast": 6250, "realized": 10000}}'),
('budget_005', 'Charges Financières', 'financial', 'CC005', 'Finance', 'AC005', 'Intérêts', 'Luc Bernard', 'Finance', 15000, 3750, '{"janvier": {"forecast": 1250, "realized": 1200}, "février": {"forecast": 1250, "realized": 1300}, "mars": {"forecast": 1250, "realized": 1250}}')
ON CONFLICT (id) DO NOTHING;

-- Message de confirmation
SELECT 'Tables créées et données de démonstration insérées avec succès !' as message;
