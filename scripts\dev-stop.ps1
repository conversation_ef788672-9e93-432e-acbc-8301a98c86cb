# Script PowerShell pour arrêter l'environnement de développement ERP HUB
# Usage: .\scripts\dev-stop.ps1

Write-Host "🛑 Arrêt de l'environnement de développement ERP HUB" -ForegroundColor Yellow

# Arrêt des conteneurs
docker-compose -f docker-compose.dev.yml down

# Option pour nettoyer les volumes (optionnel)
$cleanVolumes = Read-Host "Voulez-vous supprimer les volumes (données de développement)? (y/N)"
if ($cleanVolumes -eq "y" -or $cleanVolumes -eq "Y") {
    Write-Host "🗑️ Suppression des volumes..." -ForegroundColor Red
    docker-compose -f docker-compose.dev.yml down -v
    docker volume prune -f
}

# Option pour nettoyer les images (optionnel)
$cleanImages = Read-Host "Voulez-vous supprimer les images Docker? (y/N)"
if ($cleanImages -eq "y" -or $cleanImages -eq "Y") {
    Write-Host "🗑️ Suppression des images..." -ForegroundColor Red
    docker-compose -f docker-compose.dev.yml down --rmi all
}

Write-Host "✅ Environnement de développement arrêté" -ForegroundColor Green
