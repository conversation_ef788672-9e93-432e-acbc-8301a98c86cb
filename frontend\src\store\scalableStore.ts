import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// Store principal pour la scalabilité
interface ScalableState {
  // État des agents
  agents: {
    [key: string]: {
      loaded: boolean
      active: boolean
      lastAccessed: number
      memoryUsage: number
      data: any
    }
  }
  
  // Métriques de performance
  performance: {
    renderTimes: number[]
    memoryUsage: number
    cacheHitRate: number
    activeConnections: number
  }
  
  // Configuration utilisateur
  userPreferences: {
    theme: 'light' | 'dark'
    language: string
    dashboardLayout: any[]
    agentOrder: string[]
  }
  
  // Cache des données
  cache: {
    [key: string]: {
      data: any
      timestamp: number
      ttl: number
    }
  }
  
  // Actions
  loadAgent: (agentId: string) => Promise<void>
  unloadAgent: (agentId: string) => void
  updatePerformanceMetrics: (metrics: Partial<ScalableState['performance']>) => void
  setCacheData: (key: string, data: any, ttl?: number) => void
  getCacheData: (key: string) => any | null
  clearExpiredCache: () => void
  optimizeMemory: () => void
}

export const useScalableStore = create<ScalableState>()(
  persist(
    immer((set, get) => ({
      agents: {},
      performance: {
        renderTimes: [],
        memoryUsage: 0,
        cacheHitRate: 0,
        activeConnections: 0,
      },
      userPreferences: {
        theme: 'light',
        language: 'fr',
        dashboardLayout: [],
        agentOrder: [],
      },
      cache: {},

      loadAgent: async (agentId: string) => {
        set((state) => {
          if (!state.agents[agentId]) {
            state.agents[agentId] = {
              loaded: false,
              active: false,
              lastAccessed: Date.now(),
              memoryUsage: 0,
              data: null,
            }
          }
          state.agents[agentId].loaded = true
          state.agents[agentId].active = true
          state.agents[agentId].lastAccessed = Date.now()
        })

        // Simulation du chargement d'agent
        try {
          // Ici, vous chargeriez dynamiquement le composant agent
          const agentModule = await import(`../pages/agents/${agentId}Page.tsx`)
          
          set((state) => {
            state.agents[agentId].data = agentModule.default
            state.agents[agentId].memoryUsage = Math.random() * 20 // Simulation
          })
        } catch (error) {
          console.error(`Erreur lors du chargement de l'agent ${agentId}:`, error)
        }
      },

      unloadAgent: (agentId: string) => {
        set((state) => {
          if (state.agents[agentId]) {
            state.agents[agentId].active = false
            state.agents[agentId].data = null
            state.agents[agentId].memoryUsage = 0
          }
        })
      },

      updatePerformanceMetrics: (metrics) => {
        set((state) => {
          Object.assign(state.performance, metrics)
          
          // Garder seulement les 100 derniers temps de rendu
          if (state.performance.renderTimes.length > 100) {
            state.performance.renderTimes = state.performance.renderTimes.slice(-100)
          }
        })
      },

      setCacheData: (key: string, data: any, ttl = 300000) => { // 5 min par défaut
        set((state) => {
          state.cache[key] = {
            data,
            timestamp: Date.now(),
            ttl,
          }
        })
      },

      getCacheData: (key: string) => {
        const cached = get().cache[key]
        if (!cached) return null
        
        const now = Date.now()
        if (now - cached.timestamp > cached.ttl) {
          // Cache expiré
          set((state) => {
            delete state.cache[key]
          })
          return null
        }
        
        return cached.data
      },

      clearExpiredCache: () => {
        const now = Date.now()
        set((state) => {
          Object.keys(state.cache).forEach(key => {
            const cached = state.cache[key]
            if (now - cached.timestamp > cached.ttl) {
              delete state.cache[key]
            }
          })
        })
      },

      optimizeMemory: () => {
        const now = Date.now()
        const INACTIVE_THRESHOLD = 30 * 60 * 1000 // 30 minutes
        
        set((state) => {
          Object.keys(state.agents).forEach(agentId => {
            const agent = state.agents[agentId]
            if (agent.active && now - agent.lastAccessed > INACTIVE_THRESHOLD) {
              // Décharger les agents inactifs
              agent.active = false
              agent.data = null
              agent.memoryUsage = 0
            }
          })
        })
        
        // Nettoyer le cache expiré
        get().clearExpiredCache()
      },
    })),
    {
      name: 'erp-scalable-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        userPreferences: state.userPreferences,
        // Ne pas persister les données temporaires
      }),
    }
  )
)

// Hook pour surveiller les performances
export const usePerformanceMonitor = () => {
  const updateMetrics = useScalableStore(state => state.updatePerformanceMetrics)
  
  const trackRenderTime = (componentName: string, renderTime: number) => {
    updateMetrics({
      renderTimes: [...useScalableStore.getState().performance.renderTimes, renderTime]
    })
    
    // Alerte si le rendu est trop lent
    if (renderTime > 100) {
      console.warn(`Rendu lent détecté pour ${componentName}: ${renderTime}ms`)
    }
  }
  
  return { trackRenderTime }
}
