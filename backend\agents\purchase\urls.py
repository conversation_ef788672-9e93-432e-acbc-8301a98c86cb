"""
URLs pour l'agent Purchase
"""
from django.urls import path
from . import views

app_name = 'purchase'

urlpatterns = [
    # Statut et dashboard
    path('status/', views.purchase_status, name='status'),
    path('dashboard/', views.purchase_dashboard, name='dashboard'),
    path('supplier-performance/', views.supplier_performance, name='supplier_performance'),
    path('insights/', views.purchase_insights, name='insights'),
    path('cost-savings/', views.cost_savings_opportunities, name='cost_savings'),

    # Gestion des fournisseurs
    path('suppliers/', views.SupplierListCreateView.as_view(), name='supplier_list_create'),
    path('suppliers/<uuid:pk>/', views.SupplierDetailView.as_view(), name='supplier_detail'),

    # Gestion des catégories
    path('categories/', views.ProductCategoryListCreateView.as_view(), name='category_list_create'),
    path('categories/<uuid:pk>/', views.ProductCategoryDetailView.as_view(), name='category_detail'),

    # Gestion des demandes d'achat
    path('requests/', views.PurchaseRequestListCreateView.as_view(), name='request_list_create'),
    path('requests/create/', views.PurchaseRequestCreateView.as_view(), name='request_create'),
    path('requests/<uuid:pk>/', views.PurchaseRequestDetailView.as_view(), name='request_detail'),
    path('requests/<uuid:request_id>/approval/', views.PurchaseRequestApprovalView.as_view(), name='request_approval'),

    # Gestion des bons de commande
    path('orders/', views.PurchaseOrderListCreateView.as_view(), name='order_list_create'),
    path('orders/create/', views.PurchaseOrderCreateView.as_view(), name='order_create'),
    path('orders/<uuid:pk>/', views.PurchaseOrderDetailView.as_view(), name='order_detail'),

    # Gestion des réceptions
    path('receipts/', views.GoodsReceiptListCreateView.as_view(), name='receipt_list_create'),
    path('receipts/create/', views.GoodsReceiptCreateView.as_view(), name='receipt_create'),
    path('receipts/<uuid:pk>/', views.GoodsReceiptDetailView.as_view(), name='receipt_detail'),

    # Gestion des factures fournisseurs
    path('invoices/', views.SupplierInvoiceListCreateView.as_view(), name='invoice_list_create'),
    path('invoices/<uuid:pk>/', views.SupplierInvoiceDetailView.as_view(), name='invoice_detail'),
]
