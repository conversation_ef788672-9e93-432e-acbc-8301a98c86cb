import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'
import { 
  UserGroupIcon, 
  CurrencyDollarIcon, 
  DocumentTextIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline'

import { salesApi, type SalesDashboard, type Opportunity, type Customer, type Quote } from '@/services/api/agentsApi'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import Button from '@/components/ui/Button'

const SalesPage: React.FC = () => {
  const queryClient = useQueryClient()
  const [selectedTab, setSelectedTab] = useState<'overview' | 'pipeline' | 'customers' | 'quotes'>('overview')

  // Récupération des données du dashboard
  const { data: dashboard, isLoading, error, refetch } = useQuery<SalesDashboard>(
    'sales-dashboard',
    salesApi.getDashboard,
    {
      refetchInterval: 60000, // Actualisation toutes les minutes
      staleTime: 30000,
    }
  )

  // Récupération des opportunités
  const { data: opportunities } = useQuery<Opportunity[]>(
    'opportunities',
    () => salesApi.getOpportunities({ stage: 'qualified,proposal,negotiation' }),
    {
      enabled: selectedTab === 'pipeline',
    }
  )

  // Récupération des clients
  const { data: customers } = useQuery<Customer[]>(
    'customers',
    () => salesApi.getCustomers({ active: true }),
    {
      enabled: selectedTab === 'customers',
    }
  )

  // Récupération des devis
  const { data: quotes } = useQuery<Quote[]>(
    'quotes',
    () => salesApi.getQuotes({ status: 'draft,sent' }),
    {
      enabled: selectedTab === 'quotes',
    }
  )

  // Mutation pour mettre à jour l'étape d'une opportunité
  const updateStageMutation = useMutation(
    ({ id, stage, notes }: { id: string; stage: string; notes?: string }) =>
      salesApi.updateOpportunityStage(id, stage, notes),
    {
      onSuccess: () => {
        toast.success('Étape mise à jour avec succès')
        queryClient.invalidateQueries('opportunities')
        queryClient.invalidateQueries('sales-dashboard')
      },
      onError: (error: any) => {
        toast.error(error.message || 'Erreur lors de la mise à jour')
      }
    }
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Erreur de chargement</h3>
        <p className="mt-1 text-sm text-gray-500">
          Impossible de charger les données de vente
        </p>
        <div className="mt-6">
          <Button onClick={() => refetch()}>Réessayer</Button>
        </div>
      </div>
    )
  }

  if (!dashboard) {
    return null
  }

  const tabs = [
    { id: 'overview', name: 'Vue d\'ensemble', icon: ChartBarIcon },
    { id: 'pipeline', name: 'Pipeline', icon: ArrowTrendingUpIcon },
    { id: 'customers', name: 'Clients', icon: UserGroupIcon },
    { id: 'quotes', name: 'Devis', icon: DocumentTextIcon },
  ]

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Agent Ventes</h1>
            <p className="mt-2 text-green-100">
              Gestion commerciale et pipeline - {dashboard.tenant}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">
              {dashboard.pipeline.open_opportunities}
            </div>
            <div className="text-sm text-green-100">Opportunités ouvertes</div>
          </div>
        </div>
      </div>

      {/* Métriques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Pipeline total"
          value={`${(dashboard.pipeline.total_value / 1000).toFixed(0)}k €`}
          color="blue"
          icon={CurrencyDollarIcon}
        />
        <MetricCard
          title="Valeur pondérée"
          value={`${(dashboard.pipeline.weighted_value / 1000).toFixed(0)}k €`}
          color="green"
          icon={ArrowTrendingUpIcon}
        />
        <MetricCard
          title="Taux de conversion"
          value={`${dashboard.pipeline.conversion_rate.toFixed(1)}%`}
          color={dashboard.pipeline.conversion_rate > 20 ? 'green' : 'orange'}
          icon={ChartBarIcon}
        />
        <MetricCard
          title="Revenus du mois"
          value={`${(dashboard.revenue.monthly / 1000).toFixed(0)}k €`}
          color="purple"
          icon={CurrencyDollarIcon}
        />
      </div>

      {/* Objectif mensuel */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Objectif Mensuel</h3>
          <span className={`text-sm font-medium ${
            dashboard.revenue.achievement >= 100 ? 'text-green-600' : 'text-orange-600'
          }`}>
            {dashboard.revenue.achievement.toFixed(1)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-300 ${
              dashboard.revenue.achievement >= 100 ? 'bg-green-500' : 'bg-blue-500'
            }`}
            style={{ width: `${Math.min(dashboard.revenue.achievement, 100)}%` }}
          />
        </div>
        <div className="flex justify-between text-sm text-gray-600 mt-2">
          <span>{(dashboard.revenue.monthly / 1000).toFixed(0)}k €</span>
          <span>{(dashboard.revenue.target / 1000).toFixed(0)}k €</span>
        </div>
      </div>

      {/* Navigation par onglets */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`
                  flex items-center py-2 px-1 border-b-2 font-medium text-sm
                  ${selectedTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Contenu des onglets */}
      <div className="mt-6">
        {selectedTab === 'overview' && (
          <OverviewTab dashboard={dashboard} />
        )}
        {selectedTab === 'pipeline' && (
          <PipelineTab 
            opportunities={opportunities} 
            onUpdateStage={(id, stage, notes) => 
              updateStageMutation.mutate({ id, stage, notes })
            }
            isUpdating={updateStageMutation.isLoading}
          />
        )}
        {selectedTab === 'customers' && (
          <CustomersTab customers={customers} />
        )}
        {selectedTab === 'quotes' && (
          <QuotesTab quotes={quotes} />
        )}
      </div>
    </div>
  )
}

// Composant pour les métriques
interface MetricCardProps {
  title: string
  value: string
  color: 'blue' | 'green' | 'red' | 'yellow' | 'orange' | 'purple'
  icon: React.ComponentType<{ className?: string }>
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, color, icon: Icon }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-700 border-blue-200',
    green: 'bg-green-50 text-green-700 border-green-200',
    red: 'bg-red-50 text-red-700 border-red-200',
    yellow: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    orange: 'bg-orange-50 text-orange-700 border-orange-200',
    purple: 'bg-purple-50 text-purple-700 border-purple-200',
  }

  return (
    <div className={`card border ${colorClasses[color]}`}>
      <div className="card-body">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-8 w-8" />
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium opacity-75">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Onglet Vue d'ensemble
const OverviewTab: React.FC<{ dashboard: SalesDashboard }> = ({ dashboard }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Statistiques du pipeline */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Pipeline Commercial</h3>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Opportunités ouvertes</span>
              <span className="font-semibold">{dashboard.pipeline.open_opportunities}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Valeur totale</span>
              <span className="font-semibold">{(dashboard.pipeline.total_value / 1000).toFixed(0)}k €</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Valeur pondérée</span>
              <span className="font-semibold">{(dashboard.pipeline.weighted_value / 1000).toFixed(0)}k €</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Taux de conversion</span>
              <span className="font-semibold">{dashboard.pipeline.conversion_rate.toFixed(1)}%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Activités récentes */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Activités Récentes</h3>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            {dashboard.recent_activities.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.type === 'opportunity_created' ? 'bg-blue-400' : 'bg-green-400'
                }`} />
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <div className="flex justify-between items-center mt-1">
                    <p className="text-xs text-gray-500">
                      {new Date(activity.date).toLocaleDateString('fr-FR')}
                    </p>
                    <p className="text-xs font-medium text-green-600">
                      {(activity.value / 1000).toFixed(0)}k €
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Onglet Pipeline
const PipelineTab: React.FC<{
  opportunities?: Opportunity[]
  onUpdateStage: (id: string, stage: string, notes?: string) => void
  isUpdating: boolean
}> = ({ opportunities, onUpdateStage, isUpdating }) => {
  if (!opportunities) {
    return <LoadingSpinner size="lg" />
  }

  const stages = [
    { key: 'qualified', label: 'Qualifié', color: 'blue' },
    { key: 'proposal', label: 'Proposition', color: 'yellow' },
    { key: 'negotiation', label: 'Négociation', color: 'orange' },
  ]

  return (
    <div className="space-y-6">
      {stages.map((stage) => {
        const stageOpportunities = opportunities.filter(opp => opp.stage === stage.key)
        const stageValue = stageOpportunities.reduce((sum, opp) => sum + opp.estimated_value, 0)

        return (
          <div key={stage.key} className="card">
            <div className="card-header">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">{stage.label}</h3>
                <div className="text-right">
                  <div className="text-sm text-gray-600">{stageOpportunities.length} opportunités</div>
                  <div className="font-semibold">{(stageValue / 1000).toFixed(0)}k €</div>
                </div>
              </div>
            </div>
            <div className="card-body">
              {stageOpportunities.length === 0 ? (
                <p className="text-gray-500 text-center py-4">Aucune opportunité à cette étape</p>
              ) : (
                <div className="space-y-3">
                  {stageOpportunities.map((opportunity) => (
                    <div key={opportunity.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{opportunity.title}</h4>
                          <p className="text-sm text-gray-600">{opportunity.customer_name}</p>
                          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                            <span>{(opportunity.estimated_value / 1000).toFixed(0)}k €</span>
                            <span>{opportunity.probability}% de probabilité</span>
                            <span>Échéance: {new Date(opportunity.expected_close_date).toLocaleDateString('fr-FR')}</span>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          {stage.key !== 'negotiation' && (
                            <Button
                              size="sm"
                              onClick={() => {
                                const nextStage = stage.key === 'qualified' ? 'proposal' : 'negotiation'
                                onUpdateStage(opportunity.id, nextStage)
                              }}
                              disabled={isUpdating}
                            >
                              Avancer
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="success"
                            onClick={() => onUpdateStage(opportunity.id, 'won')}
                            disabled={isUpdating}
                          >
                            Gagner
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}

// Onglet Clients
const CustomersTab: React.FC<{ customers?: Customer[] }> = ({ customers }) => {
  if (!customers) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Liste des Clients</h3>
      </div>
      <div className="card-body">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Commercial
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Limite crédit
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {customers.map((customer) => (
                <tr key={customer.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                      <div className="text-sm text-gray-500">{customer.customer_code}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`badge ${
                      customer.customer_type === 'company' ? 'badge-primary' : 'badge-secondary'
                    }`}>
                      {customer.customer_type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div>{customer.email}</div>
                      <div className="text-gray-500">{customer.phone}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {customer.sales_rep_name || 'Non assigné'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {(customer.credit_limit / 1000).toFixed(0)}k €
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

// Onglet Devis
const QuotesTab: React.FC<{ quotes?: Quote[] }> = ({ quotes }) => {
  if (!quotes) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Devis en Cours</h3>
      </div>
      <div className="card-body">
        {quotes.length === 0 ? (
          <p className="text-gray-500 text-center py-8">Aucun devis en cours</p>
        ) : (
          <div className="space-y-4">
            {quotes.map((quote) => (
              <div key={quote.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{quote.quote_number}</h4>
                    <p className="text-sm text-gray-600">{quote.customer_name}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>Montant: {(quote.total_amount / 1000).toFixed(1)}k €</span>
                      <span>Valide jusqu'au: {new Date(quote.valid_until).toLocaleDateString('fr-FR')}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`badge ${
                      quote.status === 'sent' ? 'badge-warning' : 'badge-secondary'
                    }`}>
                      {quote.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default SalesPage
