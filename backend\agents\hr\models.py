"""
Modèles pour l'Agent HR - Gestion des Ressources Humaines
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal

from core.models import TimeStampedModel, UUIDModel, Tenant, User


class Department(UUIDModel, TimeStampedModel):
    """Modèle pour les départements de l'entreprise"""

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='departments',
        verbose_name=_("Tenant")
    )

    name = models.CharField(_("Nom"), max_length=100)
    code = models.CharField(_("Code"), max_length=20)
    description = models.TextField(_("Description"), blank=True)

    # Hiérarchie
    parent_department = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='sub_departments',
        verbose_name=_("Département parent")
    )

    # Responsable
    manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_departments',
        verbose_name=_("Responsable")
    )

    # Configuration
    is_active = models.BooleanField(_("Actif"), default=True)
    budget_annual = models.DecimalField(
        _("Budget annuel"),
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = _("Département")
        verbose_name_plural = _("Départements")
        unique_together = ['tenant', 'code']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Position(UUIDModel, TimeStampedModel):
    """Modèle pour les postes/fonctions"""

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='positions',
        verbose_name=_("Tenant")
    )

    title = models.CharField(_("Titre du poste"), max_length=100)
    code = models.CharField(_("Code"), max_length=20)
    description = models.TextField(_("Description"), blank=True)

    # Département
    department = models.ForeignKey(
        Department,
        on_delete=models.CASCADE,
        related_name='positions',
        verbose_name=_("Département")
    )

    # Niveau hiérarchique
    level = models.PositiveIntegerField(_("Niveau hiérarchique"), default=1)

    # Salaire
    salary_min = models.DecimalField(
        _("Salaire minimum"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    salary_max = models.DecimalField(
        _("Salaire maximum"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Compétences requises
    required_skills = models.JSONField(_("Compétences requises"), default=list, blank=True)

    # Configuration
    is_active = models.BooleanField(_("Actif"), default=True)
    max_employees = models.PositiveIntegerField(_("Nombre max d'employés"), default=1)

    class Meta:
        verbose_name = _("Poste")
        verbose_name_plural = _("Postes")
        unique_together = ['tenant', 'code']
        ordering = ['department', 'level', 'title']

    def __str__(self):
        return f"{self.title} - {self.department.name}"


class Employee(UUIDModel, TimeStampedModel):
    """Modèle pour les employés"""

    EMPLOYMENT_STATUS = [
        ('active', _('Actif')),
        ('inactive', _('Inactif')),
        ('terminated', _('Licencié')),
        ('resigned', _('Démissionnaire')),
        ('retired', _('Retraité')),
    ]

    CONTRACT_TYPES = [
        ('cdi', _('CDI')),
        ('cdd', _('CDD')),
        ('stage', _('Stage')),
        ('freelance', _('Freelance')),
        ('consultant', _('Consultant')),
    ]

    # Relation avec l'utilisateur système
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='employee_profile',
        verbose_name=_("Utilisateur")
    )

    # Informations professionnelles
    employee_id = models.CharField(_("ID Employé"), max_length=20, unique=True)
    position = models.ForeignKey(
        Position,
        on_delete=models.PROTECT,
        related_name='employees',
        verbose_name=_("Poste")
    )

    # Statut et contrat
    employment_status = models.CharField(
        _("Statut d'emploi"),
        max_length=20,
        choices=EMPLOYMENT_STATUS,
        default='active'
    )
    contract_type = models.CharField(
        _("Type de contrat"),
        max_length=20,
        choices=CONTRACT_TYPES,
        default='cdi'
    )

    # Dates importantes
    hire_date = models.DateField(_("Date d'embauche"))
    contract_start_date = models.DateField(_("Début de contrat"))
    contract_end_date = models.DateField(_("Fin de contrat"), null=True, blank=True)
    termination_date = models.DateField(_("Date de fin"), null=True, blank=True)

    # Informations salariales
    current_salary = models.DecimalField(
        _("Salaire actuel"),
        max_digits=10,
        decimal_places=2
    )
    salary_currency = models.CharField(_("Devise"), max_length=3, default='EUR')

    # Responsable hiérarchique
    manager = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='subordinates',
        verbose_name=_("Manager")
    )

    # Informations personnelles étendues
    emergency_contact_name = models.CharField(_("Contact d'urgence"), max_length=100, blank=True)
    emergency_contact_phone = models.CharField(_("Téléphone d'urgence"), max_length=20, blank=True)

    # Compétences et évaluations
    skills = models.JSONField(_("Compétences"), default=list, blank=True)
    performance_rating = models.FloatField(
        _("Note de performance"),
        validators=[MinValueValidator(0), MaxValueValidator(5)],
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = _("Employé")
        verbose_name_plural = _("Employés")
        ordering = ['user__last_name', 'user__first_name']

    def __str__(self):
        return f"{self.user.get_full_name()} ({self.employee_id})"

    @property
    def tenant(self):
        return self.user.tenant


class LeaveType(UUIDModel, TimeStampedModel):
    """Types de congés"""

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='leave_types',
        verbose_name=_("Tenant")
    )

    name = models.CharField(_("Nom"), max_length=100)
    code = models.CharField(_("Code"), max_length=20)
    description = models.TextField(_("Description"), blank=True)

    # Configuration
    is_paid = models.BooleanField(_("Payé"), default=True)
    requires_approval = models.BooleanField(_("Nécessite approbation"), default=True)
    max_days_per_year = models.PositiveIntegerField(_("Jours max par an"), null=True, blank=True)
    advance_notice_days = models.PositiveIntegerField(_("Préavis en jours"), default=7)

    # Couleur pour l'affichage
    color = models.CharField(_("Couleur"), max_length=7, default='#3B82F6')

    is_active = models.BooleanField(_("Actif"), default=True)

    class Meta:
        verbose_name = _("Type de congé")
        verbose_name_plural = _("Types de congés")
        unique_together = ['tenant', 'code']
        ordering = ['name']

    def __str__(self):
        return self.name


class LeaveRequest(UUIDModel, TimeStampedModel):
    """Demandes de congés"""

    STATUS_CHOICES = [
        ('pending', _('En attente')),
        ('approved', _('Approuvée')),
        ('rejected', _('Rejetée')),
        ('cancelled', _('Annulée')),
    ]

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='leave_requests',
        verbose_name=_("Employé")
    )

    leave_type = models.ForeignKey(
        LeaveType,
        on_delete=models.PROTECT,
        related_name='requests',
        verbose_name=_("Type de congé")
    )

    # Dates
    start_date = models.DateField(_("Date de début"))
    end_date = models.DateField(_("Date de fin"))
    days_requested = models.PositiveIntegerField(_("Jours demandés"))

    # Détails
    reason = models.TextField(_("Motif"), blank=True)
    comments = models.TextField(_("Commentaires"), blank=True)

    # Statut et approbation
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='pending')
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_leaves',
        verbose_name=_("Approuvé par")
    )
    approved_at = models.DateTimeField(_("Approuvé le"), null=True, blank=True)
    rejection_reason = models.TextField(_("Motif de rejet"), blank=True)

    class Meta:
        verbose_name = _("Demande de congé")
        verbose_name_plural = _("Demandes de congés")
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee} - {self.leave_type} ({self.start_date} - {self.end_date})"


class PerformanceReview(UUIDModel, TimeStampedModel):
    """Évaluations de performance"""

    REVIEW_TYPES = [
        ('annual', _('Annuelle')),
        ('quarterly', _('Trimestrielle')),
        ('probation', _('Période d\'essai')),
        ('project', _('Fin de projet')),
    ]

    STATUS_CHOICES = [
        ('draft', _('Brouillon')),
        ('in_progress', _('En cours')),
        ('completed', _('Terminée')),
        ('approved', _('Approuvée')),
    ]

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='performance_reviews',
        verbose_name=_("Employé")
    )

    reviewer = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='conducted_reviews',
        verbose_name=_("Évaluateur")
    )

    # Période d'évaluation
    review_period_start = models.DateField(_("Début de période"))
    review_period_end = models.DateField(_("Fin de période"))
    review_type = models.CharField(_("Type d'évaluation"), max_length=20, choices=REVIEW_TYPES)

    # Évaluation
    overall_rating = models.FloatField(
        _("Note globale"),
        validators=[MinValueValidator(0), MaxValueValidator(5)],
        null=True,
        blank=True
    )

    # Critères d'évaluation (JSON)
    criteria_scores = models.JSONField(_("Scores par critère"), default=dict, blank=True)

    # Commentaires
    strengths = models.TextField(_("Points forts"), blank=True)
    areas_for_improvement = models.TextField(_("Axes d'amélioration"), blank=True)
    goals_next_period = models.TextField(_("Objectifs période suivante"), blank=True)
    employee_comments = models.TextField(_("Commentaires employé"), blank=True)

    # Statut
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='draft')
    completed_at = models.DateTimeField(_("Terminée le"), null=True, blank=True)

    class Meta:
        verbose_name = _("Évaluation de performance")
        verbose_name_plural = _("Évaluations de performance")
        ordering = ['-review_period_end']

    def __str__(self):
        return f"Évaluation {self.employee} - {self.review_period_end.year}"


class Training(UUIDModel, TimeStampedModel):
    """Formations"""

    STATUS_CHOICES = [
        ('planned', _('Planifiée')),
        ('ongoing', _('En cours')),
        ('completed', _('Terminée')),
        ('cancelled', _('Annulée')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='trainings',
        verbose_name=_("Tenant")
    )

    title = models.CharField(_("Titre"), max_length=200)
    description = models.TextField(_("Description"), blank=True)

    # Détails de la formation
    trainer = models.CharField(_("Formateur"), max_length=100, blank=True)
    location = models.CharField(_("Lieu"), max_length=200, blank=True)
    is_online = models.BooleanField(_("Formation en ligne"), default=False)

    # Dates et durée
    start_date = models.DateTimeField(_("Date de début"))
    end_date = models.DateTimeField(_("Date de fin"))
    duration_hours = models.PositiveIntegerField(_("Durée en heures"))

    # Capacité et coût
    max_participants = models.PositiveIntegerField(_("Participants max"), default=20)
    cost_per_participant = models.DecimalField(
        _("Coût par participant"),
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Compétences développées
    skills_developed = models.JSONField(_("Compétences développées"), default=list, blank=True)

    # Statut
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='planned')

    class Meta:
        verbose_name = _("Formation")
        verbose_name_plural = _("Formations")
        ordering = ['-start_date']

    def __str__(self):
        return self.title


class TrainingEnrollment(UUIDModel, TimeStampedModel):
    """Inscriptions aux formations"""

    STATUS_CHOICES = [
        ('enrolled', _('Inscrit')),
        ('attended', _('Présent')),
        ('completed', _('Terminé')),
        ('failed', _('Échoué')),
        ('cancelled', _('Annulé')),
    ]

    training = models.ForeignKey(
        Training,
        on_delete=models.CASCADE,
        related_name='enrollments',
        verbose_name=_("Formation")
    )

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='training_enrollments',
        verbose_name=_("Employé")
    )

    # Statut et résultats
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='enrolled')
    completion_score = models.FloatField(
        _("Score de réussite"),
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        null=True,
        blank=True
    )

    # Dates
    enrolled_at = models.DateTimeField(_("Inscrit le"), auto_now_add=True)
    completed_at = models.DateTimeField(_("Terminé le"), null=True, blank=True)

    # Feedback
    feedback = models.TextField(_("Commentaires"), blank=True)
    rating = models.PositiveIntegerField(
        _("Note de satisfaction"),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = _("Inscription formation")
        verbose_name_plural = _("Inscriptions formations")
        unique_together = ['training', 'employee']
        ordering = ['-enrolled_at']

    def __str__(self):
        return f"{self.employee} - {self.training.title}"
