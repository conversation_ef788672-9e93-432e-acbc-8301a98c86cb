import axios from 'axios';

// Configuration de base de l'API
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// Instance Axios configurée
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les réponses et erreurs
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token expiré, essayer de le rafraîchir
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh/`, {
            refresh: refreshToken,
          });
          const newToken = response.data.access;
          localStorage.setItem('access_token', newToken);
          
          // Retry la requête originale avec le nouveau token
          error.config.headers.Authorization = `Bearer ${newToken}`;
          return apiClient.request(error.config);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          window.location.href = '/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

// Types pour les réponses API
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: string;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Services API pour chaque agent

// Service Manager
export const managerService = {
  getSystemHealth: () => apiClient.get<ApiResponse<any>>('/manager/health/'),
  getAgentsStatus: () => apiClient.get<ApiResponse<any>>('/manager/agents/'),
  getSystemMetrics: () => apiClient.get<ApiResponse<any>>('/manager/metrics/'),
  getPerformanceData: () => apiClient.get<ApiResponse<any>>('/manager/performance/'),
};

// Service HR
export const hrService = {
  getEmployees: () => apiClient.get<PaginatedResponse<any>>('/hr/employees/'),
  getMetrics: () => apiClient.get<ApiResponse<any>>('/hr/metrics/'),
  getRecruitments: () => apiClient.get<ApiResponse<any>>('/hr/recruitments/'),
  getSatisfactionData: () => apiClient.get<ApiResponse<any>>('/hr/satisfaction/'),
};

// Service Sales
export const salesService = {
  getMetrics: () => apiClient.get<ApiResponse<any>>('/sales/metrics/'),
  getDeals: () => apiClient.get<PaginatedResponse<any>>('/sales/deals/'),
  getSalesReps: () => apiClient.get<ApiResponse<any>>('/sales/reps/'),
  getPipeline: () => apiClient.get<ApiResponse<any>>('/sales/pipeline/'),
  getRevenueData: () => apiClient.get<ApiResponse<any>>('/sales/revenue/'),
};

// Service Purchase
export const purchaseService = {
  getMetrics: () => apiClient.get<ApiResponse<any>>('/purchase/metrics/'),
  getOrders: () => apiClient.get<PaginatedResponse<any>>('/purchase/orders/'),
  getSuppliers: () => apiClient.get<ApiResponse<any>>('/purchase/suppliers/'),
  getPurchaseData: () => apiClient.get<ApiResponse<any>>('/purchase/data/'),
};

// Service Logistics
export const logisticsService = {
  getMetrics: () => apiClient.get<ApiResponse<any>>('/logistics/metrics/'),
  getShipments: () => apiClient.get<PaginatedResponse<any>>('/logistics/shipments/'),
  getVehicles: () => apiClient.get<ApiResponse<any>>('/logistics/vehicles/'),
  getDeliveryData: () => apiClient.get<ApiResponse<any>>('/logistics/delivery/'),
};

// Service Stock
export const stockService = {
  getMetrics: () => apiClient.get<ApiResponse<any>>('/stock/metrics/'),
  getItems: () => apiClient.get<PaginatedResponse<any>>('/stock/items/'),
  getMovements: () => apiClient.get<ApiResponse<any>>('/stock/movements/'),
  getCriticalItems: () => apiClient.get<ApiResponse<any>>('/stock/critical/'),
  getStockData: () => apiClient.get<ApiResponse<any>>('/stock/data/'),
};

// Service Accounting
export const accountingService = {
  getMetrics: () => apiClient.get<ApiResponse<any>>('/accounting/metrics/'),
  getTransactions: () => apiClient.get<PaginatedResponse<any>>('/accounting/transactions/'),
  getAccounts: () => apiClient.get<ApiResponse<any>>('/accounting/accounts/'),
  getProfitLoss: () => apiClient.get<ApiResponse<any>>('/accounting/profit-loss/'),
};

// Service Finance
export const financeService = {
  getMetrics: () => apiClient.get<ApiResponse<any>>('/finance/metrics/'),
  getDSO: () => apiClient.get<ApiResponse<any>>('/finance/dso/'),
  getWorkingCapital: () => apiClient.get<ApiResponse<any>>('/finance/working-capital/'),
  getFinancialRatios: () => apiClient.get<ApiResponse<any>>('/finance/ratios/'),
};

// Service CRM
export const crmService = {
  getMetrics: () => apiClient.get<ApiResponse<any>>('/crm/metrics/'),
  getCustomers: () => apiClient.get<PaginatedResponse<any>>('/crm/customers/'),
  getInteractions: () => apiClient.get<ApiResponse<any>>('/crm/interactions/'),
  getCustomerData: () => apiClient.get<ApiResponse<any>>('/crm/data/'),
};

// Service BI
export const biService = {
  getMetrics: () => apiClient.get<ApiResponse<any>>('/bi/metrics/'),
  getReports: () => apiClient.get<PaginatedResponse<any>>('/bi/reports/'),
  getDataSources: () => apiClient.get<ApiResponse<any>>('/bi/sources/'),
  getInsights: () => apiClient.get<ApiResponse<any>>('/bi/insights/'),
  generateReport: (reportType: string) => apiClient.post<ApiResponse<any>>('/bi/generate/', { type: reportType }),
};

// Service d'authentification
export const authService = {
  login: (credentials: { username: string; password: string }) =>
    apiClient.post<ApiResponse<{ access: string; refresh: string }>>('/auth/login/', credentials),
  
  logout: () => apiClient.post('/auth/logout/'),
  
  register: (userData: any) =>
    apiClient.post<ApiResponse<any>>('/auth/register/', userData),
  
  refreshToken: (refreshToken: string) =>
    apiClient.post<ApiResponse<{ access: string }>>('/auth/refresh/', { refresh: refreshToken }),
  
  getProfile: () => apiClient.get<ApiResponse<any>>('/auth/profile/'),
};

// Hooks React Query pour chaque service
export const useApiHooks = {
  // Manager hooks
  useSystemHealth: () => ({
    queryKey: ['manager', 'health'],
    queryFn: () => managerService.getSystemHealth().then(res => res.data),
  }),
  
  useAgentsStatus: () => ({
    queryKey: ['manager', 'agents'],
    queryFn: () => managerService.getAgentsStatus().then(res => res.data),
  }),

  // Sales hooks
  useSalesMetrics: () => ({
    queryKey: ['sales', 'metrics'],
    queryFn: () => salesService.getMetrics().then(res => res.data),
  }),
  
  useSalesDeals: () => ({
    queryKey: ['sales', 'deals'],
    queryFn: () => salesService.getDeals().then(res => res.data),
  }),

  // HR hooks
  useHRMetrics: () => ({
    queryKey: ['hr', 'metrics'],
    queryFn: () => hrService.getMetrics().then(res => res.data),
  }),
  
  useEmployees: () => ({
    queryKey: ['hr', 'employees'],
    queryFn: () => hrService.getEmployees().then(res => res.data),
  }),

  // Stock hooks
  useStockMetrics: () => ({
    queryKey: ['stock', 'metrics'],
    queryFn: () => stockService.getMetrics().then(res => res.data),
  }),
  
  useCriticalItems: () => ({
    queryKey: ['stock', 'critical'],
    queryFn: () => stockService.getCriticalItems().then(res => res.data),
  }),

  // Finance hooks
  useFinanceMetrics: () => ({
    queryKey: ['finance', 'metrics'],
    queryFn: () => financeService.getMetrics().then(res => res.data),
  }),
  
  useFinancialRatios: () => ({
    queryKey: ['finance', 'ratios'],
    queryFn: () => financeService.getFinancialRatios().then(res => res.data),
  }),
};

// Utilitaires
export const apiUtils = {
  // Gestion des erreurs
  handleApiError: (error: any) => {
    if (error.response) {
      // Erreur de réponse du serveur
      const message = error.response.data?.message || 'Erreur serveur';
      return { type: 'server', message, status: error.response.status };
    } else if (error.request) {
      // Erreur de réseau
      return { type: 'network', message: 'Erreur de connexion' };
    } else {
      // Autre erreur
      return { type: 'unknown', message: error.message };
    }
  },

  // Formatage des données
  formatApiResponse: <T>(response: any): T => {
    return response.data?.data || response.data;
  },

  // Gestion du cache
  invalidateQueries: (queryClient: any, keys: string[]) => {
    keys.forEach(key => {
      queryClient.invalidateQueries([key]);
    });
  },
};

export default apiClient;
