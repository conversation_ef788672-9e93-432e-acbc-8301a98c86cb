import { useState, useEffect } from 'react';

export type BackgroundType = 'solid' | 'gradient' | 'pattern' | 'image' | 'animated';

export interface BackgroundConfig {
  id: string;
  name: string;
  type: BackgroundType;
  preview: string;
  config: {
    // Pour les couleurs solides
    color?: string;
    
    // Pour les gradients
    gradient?: {
      type: 'linear' | 'radial';
      direction?: string;
      colors: string[];
      stops?: number[];
    };
    
    // Pour les patterns
    pattern?: {
      type: 'dots' | 'grid' | 'waves' | 'geometric' | 'circuit';
      color: string;
      backgroundColor: string;
      size?: number;
      opacity?: number;
    };
    
    // Pour les images
    image?: {
      url: string;
      position: string;
      size: string;
      repeat: string;
      opacity?: number;
      overlay?: string;
    };
    
    // Pour les animations
    animation?: {
      type: 'particles' | 'waves' | 'geometric' | 'matrix';
      speed: number;
      density: number;
      colors: string[];
    };
  };
}

export const useBackground = () => {
  const [currentBackground, setCurrentBackground] = useState<BackgroundConfig>(() => {
    const saved = localStorage.getItem('app-background');
    return saved ? JSON.parse(saved) : getDefaultBackgrounds()[0];
  });

  const [isAnimated, setIsAnimated] = useState(true);

  // Appliquer l'arrière-plan au document
  useEffect(() => {
    applyBackground(currentBackground);
  }, [currentBackground]);

  const applyBackground = (background: BackgroundConfig) => {
    const body = document.body;
    const root = document.documentElement;
    
    // Reset des styles précédents
    body.style.background = '';
    body.style.backgroundImage = '';
    body.className = body.className.replace(/bg-\w+/g, '');
    
    // Supprimer les éléments d'animation précédents
    const existingAnimations = document.querySelectorAll('.background-animation');
    existingAnimations.forEach(el => el.remove());

    switch (background.type) {
      case 'solid':
        body.style.backgroundColor = background.config.color || '#f8f9fa';
        break;
        
      case 'gradient':
        if (background.config.gradient) {
          const { type, direction, colors } = background.config.gradient;
          if (type === 'linear') {
            body.style.background = `linear-gradient(${direction || '135deg'}, ${colors.join(', ')})`;
          } else {
            body.style.background = `radial-gradient(circle, ${colors.join(', ')})`;
          }
        }
        break;
        
      case 'pattern':
        if (background.config.pattern) {
          const pattern = generatePattern(background.config.pattern);
          body.style.backgroundColor = background.config.pattern.backgroundColor;
          body.style.backgroundImage = pattern;
        }
        break;
        
      case 'image':
        if (background.config.image) {
          const { url, position, size, repeat, opacity, overlay } = background.config.image;
          body.style.backgroundImage = `url(${url})`;
          body.style.backgroundPosition = position;
          body.style.backgroundSize = size;
          body.style.backgroundRepeat = repeat;
          
          if (overlay) {
            body.style.background = `linear-gradient(${overlay}), url(${url})`;
            body.style.backgroundBlendMode = 'overlay';
          }
        }
        break;
        
      case 'animated':
        if (background.config.animation && isAnimated) {
          createAnimatedBackground(background.config.animation);
        }
        break;
    }
  };

  const generatePattern = (pattern: any) => {
    const { type, color, size = 20, opacity = 0.1 } = pattern;
    
    switch (type) {
      case 'dots':
        return `radial-gradient(circle, ${color} 1px, transparent 1px)`;
        
      case 'grid':
        return `
          linear-gradient(${color} 1px, transparent 1px),
          linear-gradient(90deg, ${color} 1px, transparent 1px)
        `;
        
      case 'waves':
        return `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='${encodeURIComponent(color)}' fill-opacity='${opacity}'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`;
        
      case 'geometric':
        return `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='${encodeURIComponent(color)}' fill-opacity='${opacity}' fill-rule='evenodd'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20v20h40V20H20z'/%3E%3C/g%3E%3C/svg%3E")`;
        
      case 'circuit':
        return `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='${encodeURIComponent(color)}' fill-opacity='${opacity}'%3E%3Cpath d='M50 50c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zM10 10c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm60 60c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`;
        
      default:
        return '';
    }
  };

  const createAnimatedBackground = (animation: any) => {
    const canvas = document.createElement('canvas');
    canvas.className = 'background-animation';
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.zIndex = '-1';
    canvas.style.pointerEvents = 'none';
    
    document.body.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Redimensionner le canvas
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Animation selon le type
    switch (animation.type) {
      case 'particles':
        animateParticles(ctx, canvas, animation);
        break;
      case 'waves':
        animateWaves(ctx, canvas, animation);
        break;
      case 'geometric':
        animateGeometric(ctx, canvas, animation);
        break;
      case 'matrix':
        animateMatrix(ctx, canvas, animation);
        break;
    }
  };

  const animateParticles = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement, config: any) => {
    const particles: any[] = [];
    
    // Créer les particules
    for (let i = 0; i < config.density; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * config.speed,
        vy: (Math.random() - 0.5) * config.speed,
        size: Math.random() * 3 + 1,
        color: config.colors[Math.floor(Math.random() * config.colors.length)]
      });
    }
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particles.forEach(particle => {
        // Mettre à jour la position
        particle.x += particle.vx;
        particle.y += particle.vy;
        
        // Rebond sur les bords
        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
        
        // Dessiner la particule
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color + '40'; // Transparence
        ctx.fill();
      });
      
      if (isAnimated) {
        requestAnimationFrame(animate);
      }
    };
    
    animate();
  };

  const animateWaves = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement, config: any) => {
    let time = 0;
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
      config.colors.forEach((color: string, index: number) => {
        gradient.addColorStop(index / (config.colors.length - 1), color + '20');
      });
      
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.moveTo(0, canvas.height);
      
      for (let x = 0; x <= canvas.width; x += 10) {
        const y = canvas.height * 0.7 + Math.sin((x * 0.01) + (time * config.speed * 0.01)) * 50;
        ctx.lineTo(x, y);
      }
      
      ctx.lineTo(canvas.width, canvas.height);
      ctx.closePath();
      ctx.fill();
      
      time++;
      
      if (isAnimated) {
        requestAnimationFrame(animate);
      }
    };
    
    animate();
  };

  const animateGeometric = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement, config: any) => {
    let rotation = 0;
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      
      for (let i = 0; i < config.density; i++) {
        const angle = (i / config.density) * Math.PI * 2 + rotation;
        const radius = 100 + i * 20;
        const x = centerX + Math.cos(angle) * radius;
        const y = centerY + Math.sin(angle) * radius;
        
        ctx.save();
        ctx.translate(x, y);
        ctx.rotate(rotation + i);
        
        ctx.fillStyle = config.colors[i % config.colors.length] + '30';
        ctx.fillRect(-10, -10, 20, 20);
        
        ctx.restore();
      }
      
      rotation += config.speed * 0.01;
      
      if (isAnimated) {
        requestAnimationFrame(animate);
      }
    };
    
    animate();
  };

  const animateMatrix = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement, config: any) => {
    const columns = Math.floor(canvas.width / 20);
    const drops: number[] = new Array(columns).fill(1);
    
    const animate = () => {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      ctx.fillStyle = config.colors[0] || '#00ff00';
      ctx.font = '15px monospace';
      
      for (let i = 0; i < drops.length; i++) {
        const text = String.fromCharCode(Math.random() * 128);
        ctx.fillText(text, i * 20, drops[i] * 20);
        
        if (drops[i] * 20 > canvas.height && Math.random() > 0.975) {
          drops[i] = 0;
        }
        drops[i]++;
      }
      
      if (isAnimated) {
        setTimeout(() => requestAnimationFrame(animate), 100 / config.speed);
      }
    };
    
    animate();
  };

  const setBackground = (background: BackgroundConfig) => {
    setCurrentBackground(background);
    localStorage.setItem('app-background', JSON.stringify(background));
  };

  const toggleAnimation = () => {
    setIsAnimated(!isAnimated);
    if (!isAnimated) {
      applyBackground(currentBackground);
    } else {
      const animations = document.querySelectorAll('.background-animation');
      animations.forEach(el => el.remove());
    }
  };

  return {
    currentBackground,
    setBackground,
    isAnimated,
    toggleAnimation,
    availableBackgrounds: getDefaultBackgrounds()
  };
};

// Arrière-plans par défaut
export const getDefaultBackgrounds = (): BackgroundConfig[] => [
  {
    id: 'default',
    name: 'Défaut Clair',
    type: 'solid',
    preview: '#f8f9fa',
    config: { color: '#f8f9fa' }
  },
  {
    id: 'dark',
    name: 'Sombre',
    type: 'solid',
    preview: '#1a1a1a',
    config: { color: '#1a1a1a' }
  },
  {
    id: 'blue-gradient',
    name: 'Dégradé Bleu',
    type: 'gradient',
    preview: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    config: {
      gradient: {
        type: 'linear',
        direction: '135deg',
        colors: ['#667eea', '#764ba2']
      }
    }
  },
  {
    id: 'sunset',
    name: 'Coucher de Soleil',
    type: 'gradient',
    preview: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)',
    config: {
      gradient: {
        type: 'linear',
        direction: '135deg',
        colors: ['#ff9a9e', '#fecfef', '#fecfef']
      }
    }
  },
  {
    id: 'ocean',
    name: 'Océan',
    type: 'gradient',
    preview: 'linear-gradient(135deg, #2196F3 0%, #21CBF3 100%)',
    config: {
      gradient: {
        type: 'linear',
        direction: '135deg',
        colors: ['#2196F3', '#21CBF3']
      }
    }
  },
  {
    id: 'dots-pattern',
    name: 'Motif Points',
    type: 'pattern',
    preview: '#f0f0f0',
    config: {
      pattern: {
        type: 'dots',
        color: '#e0e0e0',
        backgroundColor: '#f8f9fa',
        size: 20,
        opacity: 0.3
      }
    }
  },
  {
    id: 'grid-pattern',
    name: 'Grille',
    type: 'pattern',
    preview: '#f0f0f0',
    config: {
      pattern: {
        type: 'grid',
        color: '#e0e0e0',
        backgroundColor: '#f8f9fa',
        size: 20,
        opacity: 0.2
      }
    }
  },
  {
    id: 'circuit-pattern',
    name: 'Circuit',
    type: 'pattern',
    preview: '#1a1a1a',
    config: {
      pattern: {
        type: 'circuit',
        color: '#00ff88',
        backgroundColor: '#0a0a0a',
        size: 40,
        opacity: 0.1
      }
    }
  },
  {
    id: 'particles',
    name: 'Particules',
    type: 'animated',
    preview: '#000033',
    config: {
      animation: {
        type: 'particles',
        speed: 1,
        density: 50,
        colors: ['#4fc3f7', '#29b6f6', '#03a9f4']
      }
    }
  },
  {
    id: 'waves',
    name: 'Vagues',
    type: 'animated',
    preview: '#001122',
    config: {
      animation: {
        type: 'waves',
        speed: 2,
        density: 3,
        colors: ['#2196f3', '#03a9f4', '#00bcd4']
      }
    }
  },
  {
    id: 'matrix',
    name: 'Matrix',
    type: 'animated',
    preview: '#000000',
    config: {
      animation: {
        type: 'matrix',
        speed: 3,
        density: 100,
        colors: ['#00ff00']
      }
    }
  }
];
