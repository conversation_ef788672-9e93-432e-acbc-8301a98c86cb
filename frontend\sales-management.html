<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Sales - Gestion Commercial | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #06b6d4 30%, #0891b2 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #06b6d4;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0891b2;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #06b6d4;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #06b6d4;
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #06b6d4;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">📈 Agent Sales - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="openModal('leadModal')">
                <span class="material-icons" style="font-size: 1rem;">add</span>
                Nouveau Lead
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion Commerciale</h1>
            <p class="page-subtitle">Pipeline de ventes et suivi des prospects</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalLeads">0</div>
                <div class="stat-label">Leads Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeLeads">0</div>
                <div class="stat-label">Leads Actifs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="convertedLeads">0</div>
                <div class="stat-label">Conversions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalRevenue">0€</div>
                <div class="stat-label">CA Potentiel</div>
            </div>
        </div>

        <!-- Liste des leads -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Pipeline de Ventes</h2>
                <button class="btn btn-primary" onclick="refreshLeads()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div id="alertContainer"></div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Entreprise</th>
                                <th>Contact</th>
                                <th>Email</th>
                                <th>Téléphone</th>
                                <th>Valeur</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="leadsTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Lead -->
    <div id="leadModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Nouveau Lead</h3>
                <button class="close-btn" onclick="closeModal('leadModal')">&times;</button>
            </div>
            <form id="leadForm">
                <div id="modalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="company">Entreprise *</label>
                        <input type="text" id="company" name="company" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="contactName">Nom du contact *</label>
                        <input type="text" id="contactName" name="contactName" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="email">Email *</label>
                        <input type="email" id="email" name="email" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="phone">Téléphone</label>
                        <input type="tel" id="phone" name="phone" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="value">Valeur estimée (€)</label>
                        <input type="number" id="value" name="value" class="form-input" min="0" step="100">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="status">Statut</label>
                        <select id="status" name="status" class="form-select">
                            <option value="new">Nouveau</option>
                            <option value="contacted">Contacté</option>
                            <option value="qualified">Qualifié</option>
                            <option value="proposal">Proposition</option>
                            <option value="negotiation">Négociation</option>
                            <option value="won">Gagné</option>
                            <option value="lost">Perdu</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="source">Source</label>
                        <select id="source" name="source" class="form-select">
                            <option value="">Sélectionner une source</option>
                            <option value="website">Site web</option>
                            <option value="referral">Référence</option>
                            <option value="social_media">Réseaux sociaux</option>
                            <option value="email_campaign">Campagne email</option>
                            <option value="cold_call">Appel à froid</option>
                            <option value="trade_show">Salon professionnel</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="assignedTo">Assigné à</label>
                        <select id="assignedTo" name="assignedTo" class="form-select">
                            <option value="">Non assigné</option>
                            <option value="jean.dupont">Jean Dupont</option>
                            <option value="marie.martin">Marie Martin</option>
                            <option value="pierre.bernard">Pierre Bernard</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="notes">Notes</label>
                    <textarea id="notes" name="notes" class="form-textarea" placeholder="Notes sur le prospect..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('leadModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveLeadBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let leads = [];
        let editingLeadId = null;
        let isLoading = false;

        // Données de démonstration
        const demoLeads = [
            {
                id: 1,
                company: 'TechCorp Solutions',
                contactName: 'Sophie Dubois',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 89',
                value: 25000,
                status: 'qualified',
                source: 'website',
                assignedTo: 'jean.dupont',
                notes: 'Intéressé par notre solution ERP complète'
            },
            {
                id: 2,
                company: 'InnovateLab',
                contactName: 'Marc Leroy',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 90',
                value: 15000,
                status: 'proposal',
                source: 'referral',
                assignedTo: 'marie.martin',
                notes: 'Demande de proposition pour module CRM'
            },
            {
                id: 3,
                company: 'Digital Dynamics',
                contactName: 'Laura Chen',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 91',
                value: 35000,
                status: 'negotiation',
                source: 'trade_show',
                assignedTo: 'pierre.bernard',
                notes: 'Négociation en cours pour solution complète'
            }
        ];

        function showAlert(message, type = 'error', container = 'alertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
            
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            if (modalId === 'leadModal') {
                document.getElementById('modalTitle').textContent = editingLeadId ? 'Modifier Lead' : 'Nouveau Lead';
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            if (modalId === 'leadModal') {
                document.getElementById('leadForm').reset();
                document.getElementById('modalAlertContainer').innerHTML = '';
                editingLeadId = null;
            }
        }

        function updateStats() {
            const total = leads.length;
            const active = leads.filter(lead => !['won', 'lost'].includes(lead.status)).length;
            const converted = leads.filter(lead => lead.status === 'won').length;
            const totalRevenue = leads.filter(lead => lead.status === 'won').reduce((sum, lead) => sum + (lead.value || 0), 0);

            document.getElementById('totalLeads').textContent = total;
            document.getElementById('activeLeads').textContent = active;
            document.getElementById('convertedLeads').textContent = converted;
            document.getElementById('totalRevenue').textContent = totalRevenue.toLocaleString() + '€';
        }

        function renderLeadsTable() {
            const tbody = document.getElementById('leadsTableBody');
            
            if (leads.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun lead trouvé. Cliquez sur "Nouveau Lead" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = leads.map(lead => {
                const statusBadge = getStatusBadge(lead.status);
                return `
                    <tr>
                        <td>${lead.id}</td>
                        <td>${lead.company}</td>
                        <td>${lead.contactName}</td>
                        <td>${lead.email}</td>
                        <td>${lead.phone || '-'}</td>
                        <td>${lead.value ? lead.value.toLocaleString() + '€' : '-'}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editLead(${lead.id})" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteLead(${lead.id})" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getStatusBadge(status) {
            const badges = {
                'new': '<span class="badge badge-info">Nouveau</span>',
                'contacted': '<span class="badge badge-warning">Contacté</span>',
                'qualified': '<span class="badge badge-warning">Qualifié</span>',
                'proposal': '<span class="badge badge-warning">Proposition</span>',
                'negotiation': '<span class="badge badge-warning">Négociation</span>',
                'won': '<span class="badge badge-success">Gagné</span>',
                'lost': '<span class="badge badge-danger">Perdu</span>'
            };
            return badges[status] || '<span class="badge badge-danger">Inconnu</span>';
        }

        async function loadLeads() {
            try {
                // Tentative de chargement depuis l'API
                const token = localStorage.getItem('access_token');
                if (token) {
                    const response = await fetch('http://localhost:8000/api/agents/sales/leads/', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (response.ok) {
                        leads = await response.json();
                    } else {
                        throw new Error('Erreur API');
                    }
                } else {
                    throw new Error('Pas de token');
                }
            } catch (error) {
                console.log('Chargement des données de démonstration');
                leads = [...demoLeads];
            }
            
            renderLeadsTable();
            updateStats();
        }

        function refreshLeads() {
            loadLeads();
            showAlert('Données actualisées avec succès', 'success');
        }

        function editLead(id) {
            const lead = leads.find(l => l.id === id);
            if (!lead) return;

            editingLeadId = id;
            
            // Remplir le formulaire
            document.getElementById('company').value = lead.company;
            document.getElementById('contactName').value = lead.contactName;
            document.getElementById('email').value = lead.email;
            document.getElementById('phone').value = lead.phone || '';
            document.getElementById('value').value = lead.value || '';
            document.getElementById('status').value = lead.status;
            document.getElementById('source').value = lead.source || '';
            document.getElementById('assignedTo').value = lead.assignedTo || '';
            document.getElementById('notes').value = lead.notes || '';

            openModal('leadModal');
        }

        function deleteLead(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce lead ?')) {
                leads = leads.filter(l => l.id !== id);
                renderLeadsTable();
                updateStats();
                showAlert('Lead supprimé avec succès', 'success');
            }
        }

        // Gestion du formulaire
        document.getElementById('leadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const leadData = {
                company: formData.get('company'),
                contactName: formData.get('contactName'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                value: formData.get('value') ? parseInt(formData.get('value')) : null,
                status: formData.get('status'),
                source: formData.get('source'),
                assignedTo: formData.get('assignedTo'),
                notes: formData.get('notes')
            };

            try {
                if (editingLeadId) {
                    // Modification
                    const index = leads.findIndex(l => l.id === editingLeadId);
                    if (index !== -1) {
                        leads[index] = { ...leads[index], ...leadData };
                        showAlert('Lead modifié avec succès', 'success');
                    }
                } else {
                    // Création
                    const newLead = {
                        id: Math.max(...leads.map(l => l.id), 0) + 1,
                        ...leadData
                    };
                    leads.push(newLead);
                    showAlert('Lead créé avec succès', 'success');
                }

                renderLeadsTable();
                updateStats();
                closeModal('leadModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'modalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadLeads();
            }
        });
    </script>
</body>
</html>
