import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ThemeToggle } from '../components/ui/ThemeToggle';
import { BackgroundSelector } from '../components/ui/BackgroundSelector';
import { NotificationCenter } from '../components/notifications/NotificationCenter';
import { DataExporter } from '../components/export/DataExporter';
import { DashboardCustomizer } from '../components/dashboard/DashboardCustomizer';
import { DraggableWidget, WidgetGrid, useWidgetManager } from '../components/widgets/DraggableWidget';
import { SimpleMetricCard } from '../components/ui/SimpleMetricCard';
import { SimpleChart } from '../components/ui/SimpleChart';
import { UserTestSuite } from '../components/testing/UserTestSuite';

export const FutureFeatures: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<string | null>(null);
  const { widgets, addWidget, removeWidget, updateWidgetPosition, updateWidgetSize } = useWidgetManager();

  const demoData = [
    { name: 'Jan', value: 4000, value2: 2400 },
    { name: 'Fév', value: 3000, value2: 1398 },
    { name: 'Mar', value: 2000, value2: 9800 },
    { name: 'Avr', value: 2780, value2: 3908 },
    { name: 'Mai', value: 1890, value2: 4800 },
    { name: 'Jun', value: 2390, value2: 3800 }
  ];

  const features = [
    {
      id: 'theme',
      title: '🌙 Mode Sombre Intelligent',
      description: 'Basculement automatique selon les préférences système',
      component: <ThemeToggle />,
      status: 'ready'
    },
    {
      id: 'background',
      title: '🎨 Arrière-plans Personnalisés',
      description: 'Changement d\'arrière-plan avec couleurs, dégradés, motifs et animations',
      component: <BackgroundSelector />,
      status: 'ready'
    },
    {
      id: 'notifications',
      title: '🔔 Notifications Temps Réel',
      description: 'Centre de notifications avec actions et filtres',
      component: <NotificationCenter />,
      status: 'ready'
    },
    {
      id: 'export',
      title: '📊 Export Avancé',
      description: 'Export multi-format avec configuration personnalisée',
      component: <DataExporter />,
      status: 'ready'
    },
    {
      id: 'customization',
      title: '🎛️ Personnalisation Dashboard',
      description: 'Layouts personnalisables et widgets configurables',
      component: <DashboardCustomizer />,
      status: 'ready'
    },
    {
      id: 'widgets',
      title: '🧩 Widgets Déplaçables',
      description: 'Interface drag & drop avec redimensionnement',
      component: null,
      status: 'demo'
    },
    {
      id: 'testing',
      title: '🧪 Suite de Tests',
      description: 'Tests automatisés de l\'expérience utilisateur',
      component: null,
      status: 'demo'
    }
  ];

  const addDemoWidget = () => {
    const widgetTypes = [
      {
        title: 'Métrique Revenue',
        component: (
          <SimpleMetricCard
            title="Chiffre d'Affaires"
            value={2847500}
            icon="💰"
            color="green"
            trend="up"
            trendValue={15.2}
          />
        )
      },
      {
        title: 'Graphique Ventes',
        component: (
          <SimpleChart
            title="Évolution des Ventes"
            data={demoData}
            type="line"
            height={200}
            color="#1976d2"
          />
        )
      },
      {
        title: 'Performance KPI',
        component: (
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Objectifs</span>
              <span className="font-semibold text-green-600">94%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{ width: '94%' }}></div>
            </div>
            <div className="text-xs text-gray-500">Excellent performance ce mois</div>
          </div>
        )
      }
    ];

    const randomWidget = widgetTypes[Math.floor(Math.random() * widgetTypes.length)];
    addWidget({
      id: `widget-${Date.now()}`,
      title: randomWidget.title,
      component: randomWidget.component,
      position: {
        x: Math.random() * 300,
        y: Math.random() * 200
      },
      size: ['small', 'medium', 'large'][Math.floor(Math.random() * 3)] as any
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'bg-green-100 text-green-800';
      case 'demo': return 'bg-blue-100 text-blue-800';
      case 'development': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ready': return 'Prêt';
      case 'demo': return 'Démo';
      case 'development': return 'En développement';
      default: return 'Planifié';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <div className="text-2xl">🚀</div>
              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  Améliorations Futures ERP HUB
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Fonctionnalités avancées et innovations
                </p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <BackgroundSelector />
              <ThemeToggle />
              <NotificationCenter />
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Vue d'ensemble */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white mb-8">
            <h2 className="text-3xl font-bold mb-4">
              🎯 Roadmap des Fonctionnalités Avancées
            </h2>
            <p className="text-lg opacity-90 mb-6">
              Découvrez les innovations qui transformeront votre expérience ERP
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white bg-opacity-20 rounded-lg p-4">
                <div className="text-2xl mb-2">✅</div>
                <div className="font-semibold">5 Fonctionnalités Prêtes</div>
                <div className="text-sm opacity-80">Disponibles immédiatement</div>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-4">
                <div className="text-2xl mb-2">🚧</div>
                <div className="font-semibold">2 Démonstrations</div>
                <div className="text-sm opacity-80">Prototypes interactifs</div>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-4">
                <div className="text-2xl mb-2">🎨</div>
                <div className="font-semibold">Interface Moderne</div>
                <div className="text-sm opacity-80">Design system cohérent</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Grille des fonctionnalités */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    {feature.description}
                  </p>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(feature.status)}`}>
                  {getStatusLabel(feature.status)}
                </span>
              </div>

              {feature.component ? (
                <div className="flex justify-center">
                  {feature.component}
                </div>
              ) : (
                <div className="space-y-3">
                  {feature.id === 'widgets' && (
                    <div className="text-center">
                      <button
                        onClick={() => setActiveDemo(activeDemo === 'widgets' ? null : 'widgets')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        {activeDemo === 'widgets' ? 'Fermer la Démo' : '🎮 Lancer la Démo'}
                      </button>
                    </div>
                  )}

                  {feature.id === 'testing' && (
                    <div className="text-center">
                      <button
                        onClick={() => setActiveDemo(activeDemo === 'testing' ? null : 'testing')}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        {activeDemo === 'testing' ? 'Fermer les Tests' : '🧪 Lancer les Tests'}
                      </button>
                    </div>
                  )}
                </div>
              )}
            </motion.div>
          ))}
        </div>

        {/* Zone de démonstration des widgets */}
        {activeDemo === 'widgets' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8 border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                🧩 Démonstration Widgets Déplaçables
              </h3>
              <div className="flex gap-2">
                <button
                  onClick={addDemoWidget}
                  className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors"
                >
                  + Ajouter Widget
                </button>
                <button
                  onClick={() => widgets.forEach(w => removeWidget(w.id))}
                  className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
                >
                  🗑️ Tout Supprimer
                </button>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-900 rounded-lg" style={{ height: '400px' }}>
              <WidgetGrid>
                {widgets.map((widget) => (
                  <DraggableWidget
                    key={widget.id}
                    id={widget.id}
                    title={widget.title}
                    initialPosition={widget.position}
                    size={widget.size}
                    onPositionChange={updateWidgetPosition}
                    onResize={updateWidgetSize}
                    onRemove={removeWidget}
                  >
                    {widget.component}
                  </DraggableWidget>
                ))}
              </WidgetGrid>
            </div>

            <div className="mt-4 text-sm text-gray-600 dark:text-gray-300">
              💡 <strong>Instructions:</strong> Cliquez et glissez les widgets pour les déplacer.
              Survolez un widget pour voir les contrôles de taille et suppression.
            </div>
          </motion.div>
        )}

        {/* Zone de test utilisateur */}
        {activeDemo === 'testing' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-8"
          >
            <UserTestSuite />
          </motion.div>
        )}

        {/* Roadmap future */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            🗺️ Roadmap Futur
          </h3>

          <div className="space-y-4">
            {[
              { phase: 'Phase 1', title: 'Intelligence Artificielle', items: ['Prédictions automatiques', 'Recommandations IA', 'Détection d\'anomalies'] },
              { phase: 'Phase 2', title: 'Collaboration Avancée', items: ['Chat intégré', 'Partage temps réel', 'Workflows collaboratifs'] },
              { phase: 'Phase 3', title: 'Mobile & Offline', items: ['App mobile native', 'Mode hors ligne', 'Synchronisation automatique'] },
              { phase: 'Phase 4', title: 'Intégrations', items: ['API marketplace', 'Connecteurs tiers', 'Webhooks avancés'] }
            ].map((phase, index) => (
              <div key={index} className="border-l-4 border-blue-500 pl-4">
                <div className="flex items-center gap-2 mb-2">
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs font-medium">
                    {phase.phase}
                  </span>
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {phase.title}
                  </h4>
                </div>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  {phase.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center gap-2">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full"></span>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
