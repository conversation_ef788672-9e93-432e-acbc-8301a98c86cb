"""
Vues pour l'agent HR
"""
import logging
from django.utils import timezone
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema

from core.permissions import HRReadPermission, HRWritePermission
from .services import HRService
from .models import (
    Department, Position, Employee, LeaveType, LeaveRequest,
    PerformanceReview, Training, TrainingEnrollment
)
from .serializers import (
    DepartmentSerializer, PositionSerializer, EmployeeSerializer,
    LeaveTypeSerializer, LeaveRequestSerializer, LeaveRequestCreateSerializer,
    PerformanceReviewSerializer, TrainingSerializer, TrainingEnrollmentSerializer,
    HRDashboardSerializer, LeaveRequestActionSerializer,
    PerformanceReviewCreateSerializer, TrainingCreateSerializer, HRInsightSerializer
)

logger = logging.getLogger('agents.hr')


@extend_schema(
    summary="Statut de l'agent HR",
    description="Retourne le statut de l'agent HR"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def hr_status(request):
    """Retourne le statut de l'agent HR"""
    try:
        hr_service = HRService(request.user.tenant)

        return Response({
            'status': 'active',
            'agent': 'hr',
            'message': 'Agent RH opérationnel',
            'capabilities': [
                'employee_management',
                'leave_management',
                'performance_evaluation',
                'training_coordination',
                'recruitment_support',
                'hr_analytics'
            ]
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut HR: {str(e)}")
        return Response({
            'status': 'error',
            'agent': 'hr',
            'message': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Dashboard de l'agent HR",
    description="Retourne les données complètes du dashboard HR"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, HRReadPermission])
def hr_dashboard(request):
    """Retourne les données du dashboard HR"""
    try:
        hr_service = HRService(request.user.tenant)
        dashboard_data = hr_service.get_hr_dashboard()

        return Response(dashboard_data)
    except Exception as e:
        logger.error(f"Erreur lors de la génération du dashboard HR: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Gestion des départements
class DepartmentListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des départements"""
    serializer_class = DepartmentSerializer
    permission_classes = [IsAuthenticated, HRReadPermission]

    def get_queryset(self):
        return Department.objects.filter(tenant=self.request.user.tenant)

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class DepartmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un département"""
    serializer_class = DepartmentSerializer
    permission_classes = [IsAuthenticated, HRWritePermission]

    def get_queryset(self):
        return Department.objects.filter(tenant=self.request.user.tenant)


# Gestion des postes
class PositionListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des postes"""
    serializer_class = PositionSerializer
    permission_classes = [IsAuthenticated, HRReadPermission]

    def get_queryset(self):
        queryset = Position.objects.filter(tenant=self.request.user.tenant)
        department_id = self.request.query_params.get('department')
        if department_id:
            queryset = queryset.filter(department_id=department_id)
        return queryset

    def perform_create(self, serializer):
        # Le tenant sera automatiquement assigné via le département
        serializer.save()


class PositionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un poste"""
    serializer_class = PositionSerializer
    permission_classes = [IsAuthenticated, HRWritePermission]

    def get_queryset(self):
        return Position.objects.filter(tenant=self.request.user.tenant)


# Gestion des employés
class EmployeeListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des employés"""
    serializer_class = EmployeeSerializer
    permission_classes = [IsAuthenticated, HRReadPermission]

    def get_queryset(self):
        queryset = Employee.objects.filter(user__tenant=self.request.user.tenant)

        # Filtres
        department_id = self.request.query_params.get('department')
        if department_id:
            queryset = queryset.filter(position__department_id=department_id)

        employment_status = self.request.query_params.get('status')
        if employment_status:
            queryset = queryset.filter(employment_status=employment_status)

        return queryset


class EmployeeDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un employé"""
    serializer_class = EmployeeSerializer
    permission_classes = [IsAuthenticated, HRWritePermission]

    def get_queryset(self):
        return Employee.objects.filter(user__tenant=self.request.user.tenant)


# Gestion des types de congés
class LeaveTypeListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des types de congés"""
    serializer_class = LeaveTypeSerializer
    permission_classes = [IsAuthenticated, HRReadPermission]

    def get_queryset(self):
        return LeaveType.objects.filter(tenant=self.request.user.tenant)

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


# Gestion des demandes de congés
class LeaveRequestListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des demandes de congés"""
    permission_classes = [IsAuthenticated, HRReadPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return LeaveRequestCreateSerializer
        return LeaveRequestSerializer

    def get_queryset(self):
        queryset = LeaveRequest.objects.filter(employee__user__tenant=self.request.user.tenant)

        # Filtres
        employee_id = self.request.query_params.get('employee')
        if employee_id:
            queryset = queryset.filter(employee_id=employee_id)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        # Si l'utilisateur n'est pas admin RH, il ne peut créer que ses propres demandes
        if hasattr(self.request.user, 'employee_profile'):
            serializer.save(employee=self.request.user.employee_profile)
        else:
            # Admin RH peut créer pour n'importe quel employé
            serializer.save()


class LeaveRequestDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une demande de congé"""
    serializer_class = LeaveRequestSerializer
    permission_classes = [IsAuthenticated, HRReadPermission]

    def get_queryset(self):
        return LeaveRequest.objects.filter(employee__user__tenant=self.request.user.tenant)


class LeaveRequestActionView(APIView):
    """Vue pour approuver/rejeter des demandes de congés"""
    permission_classes = [IsAuthenticated, HRWritePermission]

    @extend_schema(
        summary="Traiter une demande de congé",
        description="Approuve ou rejette une demande de congé",
        request=LeaveRequestActionSerializer
    )
    def post(self, request, leave_request_id):
        """Traite une demande de congé"""
        try:
            serializer = LeaveRequestActionSerializer(data=request.data)
            if serializer.is_valid():
                hr_service = HRService(request.user.tenant)

                result = hr_service.process_leave_request(
                    leave_request_id,
                    serializer.validated_data['action'],
                    request.user,
                    serializer.validated_data.get('comments', '')
                )

                if result['success']:
                    return Response(result, status=status.HTTP_200_OK)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors du traitement de la demande de congé: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Gestion des évaluations de performance
class PerformanceReviewListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des évaluations de performance"""
    serializer_class = PerformanceReviewSerializer
    permission_classes = [IsAuthenticated, HRReadPermission]

    def get_queryset(self):
        queryset = PerformanceReview.objects.filter(employee__user__tenant=self.request.user.tenant)

        # Filtres
        employee_id = self.request.query_params.get('employee')
        if employee_id:
            queryset = queryset.filter(employee_id=employee_id)

        review_type = self.request.query_params.get('type')
        if review_type:
            queryset = queryset.filter(review_type=review_type)

        return queryset.order_by('-review_period_end')


class PerformanceReviewCreateView(APIView):
    """Vue pour créer des évaluations avec l'aide de l'IA"""
    permission_classes = [IsAuthenticated, HRWritePermission]

    @extend_schema(
        summary="Créer une évaluation de performance",
        description="Crée une nouvelle évaluation avec suggestions IA",
        request=PerformanceReviewCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle évaluation"""
        try:
            serializer = PerformanceReviewCreateSerializer(data=request.data)
            if serializer.is_valid():
                hr_service = HRService(request.user.tenant)

                result = hr_service.create_performance_review(
                    serializer.validated_data['employee_id'],
                    request.user,
                    serializer.validated_data
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'évaluation: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Gestion des formations
class TrainingListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des formations"""
    serializer_class = TrainingSerializer
    permission_classes = [IsAuthenticated, HRReadPermission]

    def get_queryset(self):
        queryset = Training.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-start_date')


class TrainingCreateView(APIView):
    """Vue pour créer des formations"""
    permission_classes = [IsAuthenticated, HRWritePermission]

    @extend_schema(
        summary="Créer une formation",
        description="Programme une nouvelle formation",
        request=TrainingCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle formation"""
        try:
            serializer = TrainingCreateSerializer(data=request.data)
            if serializer.is_valid():
                hr_service = HRService(request.user.tenant)

                result = hr_service.schedule_training(serializer.validated_data)

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de la formation: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TrainingEnrollmentView(APIView):
    """Vue pour gérer les inscriptions aux formations"""
    permission_classes = [IsAuthenticated, HRWritePermission]

    @extend_schema(
        summary="Inscrire à une formation",
        description="Inscrit un employé à une formation"
    )
    def post(self, request, training_id, employee_id):
        """Inscrit un employé à une formation"""
        try:
            hr_service = HRService(request.user.tenant)

            result = hr_service.enroll_employee_in_training(training_id, employee_id)

            if result['success']:
                return Response(result, status=status.HTTP_201_CREATED)
            else:
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de l'inscription: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@extend_schema(
    summary="Insights RH",
    description="Retourne les insights et recommandations RH basés sur l'IA"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, HRReadPermission])
def hr_insights(request):
    """Retourne les insights RH"""
    try:
        hr_service = HRService(request.user.tenant)
        insights = hr_service.generate_hr_insights()

        return Response({
            'insights': insights,
            'count': len(insights),
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'insights: {str(e)}")
        return Response(
            {'error': f'Erreur lors de la génération d\'insights: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
