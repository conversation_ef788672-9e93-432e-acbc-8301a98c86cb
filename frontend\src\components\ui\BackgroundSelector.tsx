import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useBackground, BackgroundConfig, BackgroundType } from '../../hooks/useBackground';

export const BackgroundSelector: React.FC = () => {
  const { currentBackground, setBackground, isAnimated, toggleAnimation, availableBackgrounds } = useBackground();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<BackgroundType | 'all'>('all');
  const [customColor, setCustomColor] = useState('#f8f9fa');

  const categories = [
    { key: 'all', label: 'Tous', icon: '🎨' },
    { key: 'solid', label: 'Couleurs', icon: '🎯' },
    { key: 'gradient', label: 'Dégradés', icon: '🌈' },
    { key: 'pattern', label: 'Motifs', icon: '🔷' },
    { key: 'animated', label: 'Animés', icon: '✨' }
  ];

  const filteredBackgrounds = selectedCategory === 'all' 
    ? availableBackgrounds 
    : availableBackgrounds.filter(bg => bg.type === selectedCategory);

  const createCustomBackground = () => {
    const customBg: BackgroundConfig = {
      id: `custom-${Date.now()}`,
      name: 'Couleur Personnalisée',
      type: 'solid',
      preview: customColor,
      config: { color: customColor }
    };
    setBackground(customBg);
  };

  const getBackgroundPreview = (background: BackgroundConfig) => {
    switch (background.type) {
      case 'solid':
        return { backgroundColor: background.config.color };
      case 'gradient':
        if (background.config.gradient) {
          const { type, direction, colors } = background.config.gradient;
          return {
            background: type === 'linear' 
              ? `linear-gradient(${direction || '135deg'}, ${colors.join(', ')})`
              : `radial-gradient(circle, ${colors.join(', ')})`
          };
        }
        break;
      case 'pattern':
        return {
          backgroundColor: background.config.pattern?.backgroundColor || '#f8f9fa',
          backgroundImage: background.preview
        };
      case 'animated':
        return {
          backgroundColor: background.preview,
          position: 'relative' as const
        };
      default:
        return { backgroundColor: background.preview };
    }
  };

  const getTypeIcon = (type: BackgroundType) => {
    switch (type) {
      case 'solid': return '🎯';
      case 'gradient': return '🌈';
      case 'pattern': return '🔷';
      case 'animated': return '✨';
      default: return '🎨';
    }
  };

  return (
    <>
      {/* Bouton d'ouverture */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors shadow-sm"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="Changer l'arrière-plan"
      >
        <span className="text-lg">🎨</span>
        <span className="text-sm font-medium hidden sm:inline text-gray-700 dark:text-gray-300">
          Arrière-plan
        </span>
      </motion.button>

      {/* Modal de sélection */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
              onClick={() => setIsOpen(false)}
            >
              {/* Modal */}
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
                onClick={(e) => e.stopPropagation()}
              >
                {/* En-tête */}
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">🎨</span>
                      <div>
                        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                          Personnaliser l'Arrière-plan
                        </h2>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          Choisissez un style pour votre interface
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => setIsOpen(false)}
                      className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    >
                      ✕
                    </button>
                  </div>

                  {/* Contrôles */}
                  <div className="flex items-center gap-4 mt-4">
                    {/* Catégories */}
                    <div className="flex gap-1">
                      {categories.map(category => (
                        <button
                          key={category.key}
                          onClick={() => setSelectedCategory(category.key as any)}
                          className={`
                            flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-colors
                            ${selectedCategory === category.key
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                            }
                          `}
                        >
                          <span>{category.icon}</span>
                          <span className="hidden sm:inline">{category.label}</span>
                        </button>
                      ))}
                    </div>

                    {/* Toggle animation */}
                    <div className="flex items-center gap-2 ml-auto">
                      <span className="text-sm text-gray-600 dark:text-gray-300">Animations:</span>
                      <button
                        onClick={toggleAnimation}
                        className={`
                          relative w-12 h-6 rounded-full transition-colors
                          ${isAnimated ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'}
                        `}
                      >
                        <motion.div
                          className="absolute top-1 w-4 h-4 bg-white rounded-full shadow-sm"
                          animate={{ x: isAnimated ? 26 : 2 }}
                          transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                        />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Contenu */}
                <div className="p-6 max-h-[60vh] overflow-y-auto">
                  {/* Couleur personnalisée */}
                  {selectedCategory === 'all' || selectedCategory === 'solid' ? (
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Couleur Personnalisée
                      </h3>
                      <div className="flex items-center gap-3">
                        <input
                          type="color"
                          value={customColor}
                          onChange={(e) => setCustomColor(e.target.value)}
                          className="w-12 h-12 rounded-lg border border-gray-300 dark:border-gray-600 cursor-pointer"
                        />
                        <input
                          type="text"
                          value={customColor}
                          onChange={(e) => setCustomColor(e.target.value)}
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="#ffffff"
                        />
                        <button
                          onClick={createCustomBackground}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          Appliquer
                        </button>
                      </div>
                    </div>
                  ) : null}

                  {/* Grille des arrière-plans */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Arrière-plans Prédéfinis
                    </h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                      {filteredBackgrounds.map((background, index) => (
                        <motion.div
                          key={background.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.05 }}
                          className={`
                            relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all
                            ${currentBackground.id === background.id
                              ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800'
                              : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                            }
                          `}
                          onClick={() => setBackground(background)}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          {/* Aperçu */}
                          <div 
                            className="w-full h-20 relative"
                            style={getBackgroundPreview(background)}
                          >
                            {/* Indicateur d'animation */}
                            {background.type === 'animated' && (
                              <div className="absolute top-1 right-1">
                                <motion.div
                                  animate={{ rotate: 360 }}
                                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                                  className="w-4 h-4 text-white opacity-70"
                                >
                                  ✨
                                </motion.div>
                              </div>
                            )}
                            
                            {/* Overlay de sélection */}
                            {currentBackground.id === background.id && (
                              <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
                                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                  <span className="text-white text-sm">✓</span>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Informations */}
                          <div className="p-3 bg-white dark:bg-gray-800">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-sm">{getTypeIcon(background.type)}</span>
                              <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {background.name}
                              </span>
                            </div>
                            <span className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                              {background.type}
                            </span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      Arrière-plan actuel: <strong>{currentBackground.name}</strong>
                    </div>
                    <button
                      onClick={() => setIsOpen(false)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Terminé
                    </button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};
