"""
Services pour l'Agent Purchase
Logique métier pour la gestion des achats
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg
from django.db import transaction
from decimal import Decimal

from core.models import Tenant, User
from agents.models import Agent
from agents.ai_service import ai_service
from .models import (
    Supplier, ProductCategory, PurchaseRequest, PurchaseRequestItem,
    PurchaseOrder, PurchaseOrderItem, GoodsReceipt, GoodsReceiptItem,
    SupplierInvoice
)

logger = logging.getLogger('agents.purchase')


class PurchaseService:
    """
    Service principal pour l'Agent Purchase
    Gère toutes les opérations d'achat
    """

    def __init__(self, tenant: Tenant):
        self.tenant = tenant
        self.purchase_agent = self._get_or_create_purchase_agent()

    def _get_or_create_purchase_agent(self) -> Agent:
        """Récupère ou crée l'agent Purchase pour le tenant"""
        agent, created = Agent.objects.get_or_create(
            tenant=self.tenant,
            agent_type='purchase',
            defaults={
                'name': 'Agent Achats',
                'description': 'Gestion des achats et approvisionnements',
                'ai_enabled': True,
                'ai_model': 'gpt-4',
                'capabilities': [
                    'supplier_management',
                    'purchase_request_processing',
                    'purchase_order_generation',
                    'goods_receipt_management',
                    'invoice_validation',
                    'supplier_performance_analysis',
                    'cost_optimization'
                ]
            }
        )
        if created:
            logger.info(f"Agent Purchase créé pour le tenant {self.tenant.name}")
        return agent

    def get_purchase_dashboard(self) -> Dict[str, Any]:
        """Retourne les données du dashboard Purchase"""

        # Demandes d'achat
        purchase_requests = PurchaseRequest.objects.filter(tenant=self.tenant)
        pending_requests = purchase_requests.filter(status__in=['submitted', 'approved'])

        # Bons de commande
        purchase_orders = PurchaseOrder.objects.filter(tenant=self.tenant)
        active_orders = purchase_orders.exclude(status__in=['received', 'closed', 'cancelled'])

        # Fournisseurs
        suppliers = Supplier.objects.filter(tenant=self.tenant, is_active=True)

        # Factures en attente
        pending_invoices = SupplierInvoice.objects.filter(
            tenant=self.tenant,
            status__in=['received', 'validated']
        )

        # Calculs de métriques
        total_pending_value = pending_requests.aggregate(
            total=Sum('estimated_total')
        )['total'] or Decimal('0.00')

        total_orders_value = active_orders.aggregate(
            total=Sum('total_amount')
        )['total'] or Decimal('0.00')

        # Délais de livraison moyens
        avg_delivery_delay = self._calculate_average_delivery_delay()

        # Performance fournisseurs
        supplier_performance = self._get_supplier_performance_summary()

        return {
            'tenant': self.tenant.name,
            'timestamp': timezone.now().isoformat(),
            'requests': {
                'total': purchase_requests.count(),
                'pending': pending_requests.count(),
                'pending_value': float(total_pending_value)
            },
            'orders': {
                'total': purchase_orders.count(),
                'active': active_orders.count(),
                'active_value': float(total_orders_value)
            },
            'suppliers': {
                'total': suppliers.count(),
                'approved': suppliers.filter(is_approved=True).count(),
                'avg_rating': supplier_performance.get('avg_rating', 0)
            },
            'invoices': {
                'pending': pending_invoices.count(),
                'pending_value': float(pending_invoices.aggregate(
                    total=Sum('total_amount')
                )['total'] or Decimal('0.00'))
            },
            'performance': {
                'avg_delivery_delay': avg_delivery_delay,
                'on_time_delivery_rate': supplier_performance.get('on_time_rate', 0)
            },
            'recent_activities': self._get_recent_activities()
        }

    def _calculate_average_delivery_delay(self) -> float:
        """Calcule le délai de livraison moyen"""
        completed_orders = PurchaseOrder.objects.filter(
            tenant=self.tenant,
            status__in=['received', 'closed'],
            actual_delivery_date__isnull=False
        )

        total_delay = 0
        count = 0

        for order in completed_orders:
            if order.expected_delivery_date and order.actual_delivery_date:
                delay = (order.actual_delivery_date - order.expected_delivery_date).days
                total_delay += delay
                count += 1

        return total_delay / count if count > 0 else 0

    def _get_supplier_performance_summary(self) -> Dict[str, float]:
        """Calcule un résumé des performances fournisseurs"""
        suppliers = Supplier.objects.filter(tenant=self.tenant, is_active=True)

        total_rating = 0
        count = 0
        on_time_deliveries = 0
        total_deliveries = 0

        for supplier in suppliers:
            if supplier.overall_rating:
                total_rating += supplier.overall_rating
                count += 1

            # Calcul du taux de livraison à temps
            supplier_orders = supplier.purchase_orders.filter(
                status__in=['received', 'closed'],
                actual_delivery_date__isnull=False
            )

            for order in supplier_orders:
                total_deliveries += 1
                if order.actual_delivery_date <= order.expected_delivery_date:
                    on_time_deliveries += 1

        avg_rating = total_rating / count if count > 0 else 0
        on_time_rate = (on_time_deliveries / total_deliveries * 100) if total_deliveries > 0 else 0

        return {
            'avg_rating': round(avg_rating, 2),
            'on_time_rate': round(on_time_rate, 2)
        }

    def _get_recent_activities(self) -> List[Dict[str, Any]]:
        """Récupère les activités récentes"""
        activities = []

        # Demandes d'achat récentes
        recent_requests = PurchaseRequest.objects.filter(
            tenant=self.tenant,
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:5]

        for request in recent_requests:
            activities.append({
                'type': 'purchase_request_created',
                'description': f"Nouvelle demande d'achat: {request.title}",
                'date': request.created_at.isoformat(),
                'value': float(request.estimated_total)
            })

        # Bons de commande récents
        recent_orders = PurchaseOrder.objects.filter(
            tenant=self.tenant,
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:3]

        for order in recent_orders:
            activities.append({
                'type': 'purchase_order_created',
                'description': f"Nouveau bon de commande: {order.order_number}",
                'date': order.created_at.isoformat(),
                'value': float(order.total_amount)
            })

        return sorted(activities, key=lambda x: x['date'], reverse=True)[:10]

    def create_purchase_request(self, request_data: Dict[str, Any], requester: User) -> Dict[str, Any]:
        """Crée une nouvelle demande d'achat"""
        try:
            with transaction.atomic():
                # Générer le numéro de demande
                request_number = self._generate_request_number()

                purchase_request = PurchaseRequest.objects.create(
                    tenant=self.tenant,
                    request_number=request_number,
                    requester=requester,
                    title=request_data['title'],
                    description=request_data.get('description', ''),
                    justification=request_data.get('justification', ''),
                    requested_date=request_data.get('requested_date', timezone.now().date()),
                    required_date=request_data['required_date'],
                    priority=request_data.get('priority', 3)
                )

                # Ajouter les lignes de demande
                total_estimated = Decimal('0.00')
                for item_data in request_data.get('items', []):
                    item = PurchaseRequestItem.objects.create(
                        purchase_request=purchase_request,
                        item_name=item_data['item_name'],
                        description=item_data.get('description', ''),
                        category_id=item_data.get('category_id'),
                        quantity=item_data['quantity'],
                        unit_of_measure=item_data.get('unit_of_measure', 'pcs'),
                        estimated_unit_price=item_data.get('estimated_unit_price'),
                        suggested_supplier_id=item_data.get('suggested_supplier_id'),
                        specifications=item_data.get('specifications', {})
                    )
                    total_estimated += item.estimated_total

                # Mettre à jour le total estimé
                purchase_request.estimated_total = total_estimated
                purchase_request.save()

                # Générer des recommandations IA si disponible
                if ai_service.is_available():
                    recommendations = self._generate_purchase_recommendations(purchase_request)
                    if recommendations:
                        # Appliquer les recommandations (assignation d'acheteur, etc.)
                        self._apply_purchase_recommendations(purchase_request, recommendations)

                return {
                    'success': True,
                    'purchase_request': {
                        'id': str(purchase_request.id),
                        'request_number': purchase_request.request_number,
                        'estimated_total': float(purchase_request.estimated_total),
                        'status': purchase_request.status
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_request_number(self) -> str:
        """Génère un numéro de demande d'achat unique"""
        current_year = timezone.now().year
        last_request = PurchaseRequest.objects.filter(
            tenant=self.tenant,
            request_number__startswith=f"DA{current_year}"
        ).order_by('-request_number').first()

        if last_request:
            last_number = int(last_request.request_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"DA{current_year}{new_number:04d}"

    def _generate_purchase_recommendations(self, purchase_request: PurchaseRequest) -> Dict[str, Any]:
        """Génère des recommandations IA pour une demande d'achat"""
        try:
            context = {
                'request': {
                    'title': purchase_request.title,
                    'estimated_total': float(purchase_request.estimated_total),
                    'priority': purchase_request.priority,
                    'required_date': purchase_request.required_date.isoformat()
                },
                'items': [
                    {
                        'name': item.item_name,
                        'quantity': float(item.quantity),
                        'estimated_price': float(item.estimated_unit_price) if item.estimated_unit_price else None,
                        'category': item.category.name if item.category else None
                    }
                    for item in purchase_request.items.all()
                ]
            }

            prompt = f"""
            En tant qu'expert en achats, analyse cette demande d'achat et fournis des recommandations:

            Contexte: {context}

            Fournis des recommandations pour:
            1. Acheteur le plus approprié selon les catégories
            2. Fournisseurs recommandés pour chaque article
            3. Stratégie d'achat (groupage, négociation, etc.)
            4. Alertes sur les délais ou budgets
            5. Optimisations possibles

            Réponds au format JSON avec les clés: buyer_recommendation, supplier_suggestions, strategy, alerts, optimizations
            """

            ai_response = ai_service.generate_response(prompt, "purchase", temperature=0.6)

            if ai_response.success:
                import json
                return json.loads(ai_response.content)

        except Exception as e:
            logger.error(f"Erreur lors de la génération de recommandations: {str(e)}")

        return {}

    def _apply_purchase_recommendations(self, purchase_request: PurchaseRequest, recommendations: Dict[str, Any]):
        """Applique les recommandations IA à une demande d'achat"""
        try:
            # Assignation automatique d'acheteur si recommandé
            buyer_rec = recommendations.get('buyer_recommendation')
            if buyer_rec and 'user_id' in buyer_rec:
                try:
                    buyer = User.objects.get(id=buyer_rec['user_id'], tenant=self.tenant)
                    purchase_request.buyer = buyer
                    purchase_request.save()
                except User.DoesNotExist:
                    pass

            # Log des recommandations pour suivi
            logger.info(f"Recommandations appliquées pour DA {purchase_request.request_number}")

        except Exception as e:
            logger.error(f"Erreur lors de l'application des recommandations: {str(e)}")

    def approve_purchase_request(self, request_id: str, approver: User, comments: str = "") -> Dict[str, Any]:
        """Approuve une demande d'achat"""
        try:
            purchase_request = PurchaseRequest.objects.get(
                id=request_id,
                tenant=self.tenant
            )

            if purchase_request.status != 'submitted':
                return {
                    'success': False,
                    'error': 'La demande doit être soumise pour être approuvée'
                }

            purchase_request.status = 'approved'
            purchase_request.approved_by = approver
            purchase_request.approved_at = timezone.now()
            purchase_request.save()

            # Notification au demandeur (simulation)
            self._notify_request_approval(purchase_request)

            return {
                'success': True,
                'purchase_request': {
                    'id': str(purchase_request.id),
                    'request_number': purchase_request.request_number,
                    'status': purchase_request.status
                }
            }

        except PurchaseRequest.DoesNotExist:
            return {
                'success': False,
                'error': 'Demande d\'achat non trouvée'
            }

    def _notify_request_approval(self, purchase_request: PurchaseRequest):
        """Notifie l'approbation d'une demande d'achat"""
        # Dans un vrai système, cela enverrait un email ou une notification
        logger.info(f"Notification envoyée pour approbation DA {purchase_request.request_number}")

    def generate_purchase_order(self, order_data: Dict[str, Any], buyer: User) -> Dict[str, Any]:
        """Génère un bon de commande"""
        try:
            with transaction.atomic():
                # Générer le numéro de commande
                order_number = self._generate_order_number()

                purchase_order = PurchaseOrder.objects.create(
                    tenant=self.tenant,
                    order_number=order_number,
                    supplier_id=order_data['supplier_id'],
                    purchase_request_id=order_data.get('purchase_request_id'),
                    buyer=buyer,
                    order_date=order_data.get('order_date', timezone.now().date()),
                    expected_delivery_date=order_data['expected_delivery_date'],
                    payment_terms=order_data.get('payment_terms', 30),
                    delivery_address=order_data.get('delivery_address', ''),
                    notes=order_data.get('notes', ''),
                    terms_conditions=order_data.get('terms_conditions', '')
                )

                # Ajouter les lignes de commande
                total_amount = Decimal('0.00')
                for item_data in order_data.get('items', []):
                    item = PurchaseOrderItem.objects.create(
                        purchase_order=purchase_order,
                        request_item_id=item_data.get('request_item_id'),
                        item_name=item_data['item_name'],
                        description=item_data.get('description', ''),
                        supplier_reference=item_data.get('supplier_reference', ''),
                        quantity_ordered=item_data['quantity_ordered'],
                        unit_of_measure=item_data.get('unit_of_measure', 'pcs'),
                        unit_price=item_data['unit_price'],
                        discount_percentage=item_data.get('discount_percentage', 0),
                        expected_delivery_date=item_data.get('expected_delivery_date')
                    )
                    total_amount += item.line_total

                # Calculer les totaux
                purchase_order.subtotal = total_amount
                purchase_order.tax_amount = total_amount * Decimal('0.20')  # TVA 20% par défaut
                purchase_order.total_amount = purchase_order.subtotal + purchase_order.tax_amount
                purchase_order.save()

                return {
                    'success': True,
                    'purchase_order': {
                        'id': str(purchase_order.id),
                        'order_number': purchase_order.order_number,
                        'total_amount': float(purchase_order.total_amount),
                        'status': purchase_order.status
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_order_number(self) -> str:
        """Génère un numéro de bon de commande unique"""
        current_year = timezone.now().year
        last_order = PurchaseOrder.objects.filter(
            tenant=self.tenant,
            order_number__startswith=f"BC{current_year}"
        ).order_by('-order_number').first()

        if last_order:
            last_number = int(last_order.order_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"BC{current_year}{new_number:04d}"

    def create_goods_receipt(self, receipt_data: Dict[str, Any], receiver: User) -> Dict[str, Any]:
        """Crée une réception de marchandises"""
        try:
            with transaction.atomic():
                # Générer le numéro de réception
                receipt_number = self._generate_receipt_number()

                goods_receipt = GoodsReceipt.objects.create(
                    tenant=self.tenant,
                    receipt_number=receipt_number,
                    purchase_order_id=receipt_data['purchase_order_id'],
                    receipt_date=receipt_data.get('receipt_date', timezone.now().date()),
                    delivery_note_number=receipt_data.get('delivery_note_number', ''),
                    received_by=receiver,
                    comments=receipt_data.get('comments', '')
                )

                # Ajouter les lignes de réception
                for item_data in receipt_data.get('items', []):
                    receipt_item = GoodsReceiptItem.objects.create(
                        goods_receipt=goods_receipt,
                        order_item_id=item_data['order_item_id'],
                        quantity_expected=item_data['quantity_expected'],
                        quantity_received=item_data['quantity_received'],
                        quality_status=item_data.get('quality_status', 'pending'),
                        comments=item_data.get('comments', ''),
                        rejection_reason=item_data.get('rejection_reason', '')
                    )

                    # Mettre à jour la quantité reçue sur la ligne de commande
                    order_item = receipt_item.order_item
                    order_item.quantity_received += receipt_item.quantity_accepted
                    order_item.save()

                # Mettre à jour le statut du bon de commande
                self._update_order_status_from_receipts(goods_receipt.purchase_order)

                return {
                    'success': True,
                    'goods_receipt': {
                        'id': str(goods_receipt.id),
                        'receipt_number': goods_receipt.receipt_number,
                        'status': goods_receipt.status
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_receipt_number(self) -> str:
        """Génère un numéro de réception unique"""
        current_year = timezone.now().year
        last_receipt = GoodsReceipt.objects.filter(
            tenant=self.tenant,
            receipt_number__startswith=f"REC{current_year}"
        ).order_by('-receipt_number').first()

        if last_receipt:
            last_number = int(last_receipt.receipt_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"REC{current_year}{new_number:04d}"

    def _update_order_status_from_receipts(self, purchase_order: PurchaseOrder):
        """Met à jour le statut du bon de commande selon les réceptions"""
        total_ordered = sum(item.quantity_ordered for item in purchase_order.items.all())
        total_received = sum(item.quantity_received for item in purchase_order.items.all())

        if total_received == 0:
            # Aucune réception
            return
        elif total_received >= total_ordered:
            # Complètement reçu
            purchase_order.status = 'received'
            purchase_order.actual_delivery_date = timezone.now().date()
        else:
            # Partiellement reçu
            purchase_order.status = 'partially_received'

        purchase_order.save()

    def analyze_supplier_performance(self) -> Dict[str, Any]:
        """Analyse les performances des fournisseurs"""
        suppliers = Supplier.objects.filter(tenant=self.tenant, is_active=True)
        performance_data = []

        for supplier in suppliers:
            # Commandes du fournisseur
            orders = supplier.purchase_orders.filter(
                status__in=['received', 'closed']
            )

            if not orders.exists():
                continue

            # Calculs de performance
            total_orders = orders.count()
            total_value = orders.aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00')

            # Délais de livraison
            delivery_delays = []
            on_time_deliveries = 0

            for order in orders.filter(actual_delivery_date__isnull=False):
                if order.expected_delivery_date:
                    delay = (order.actual_delivery_date - order.expected_delivery_date).days
                    delivery_delays.append(delay)
                    if delay <= 0:
                        on_time_deliveries += 1

            avg_delay = sum(delivery_delays) / len(delivery_delays) if delivery_delays else 0
            on_time_rate = (on_time_deliveries / len(delivery_delays) * 100) if delivery_delays else 0

            # Qualité (basée sur les réceptions)
            quality_issues = 0
            total_receipts = 0

            for order in orders:
                for receipt in order.goods_receipts.all():
                    for item in receipt.items.all():
                        total_receipts += 1
                        if item.quality_status == 'failed':
                            quality_issues += 1

            quality_rate = ((total_receipts - quality_issues) / total_receipts * 100) if total_receipts > 0 else 100

            performance_data.append({
                'supplier': {
                    'id': str(supplier.id),
                    'name': supplier.name,
                    'code': supplier.supplier_code
                },
                'metrics': {
                    'total_orders': total_orders,
                    'total_value': float(total_value),
                    'avg_delivery_delay': round(avg_delay, 1),
                    'on_time_delivery_rate': round(on_time_rate, 1),
                    'quality_rate': round(quality_rate, 1),
                    'overall_rating': supplier.overall_rating or 0
                }
            })

        # Trier par performance globale
        performance_data.sort(key=lambda x: x['metrics']['overall_rating'], reverse=True)

        return {
            'suppliers': performance_data,
            'summary': {
                'total_suppliers': len(performance_data),
                'avg_on_time_rate': sum(s['metrics']['on_time_delivery_rate'] for s in performance_data) / len(performance_data) if performance_data else 0,
                'avg_quality_rate': sum(s['metrics']['quality_rate'] for s in performance_data) / len(performance_data) if performance_data else 0
            }
        }

    def generate_purchase_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights d'achat basés sur les données"""
        insights = []

        # Analyse des demandes en retard
        overdue_requests = PurchaseRequest.objects.filter(
            tenant=self.tenant,
            status__in=['submitted', 'approved'],
            required_date__lt=timezone.now().date()
        )

        if overdue_requests.exists():
            insights.append({
                'type': 'overdue_requests',
                'priority': 'high',
                'title': 'Demandes d\'achat en retard',
                'description': f'{overdue_requests.count()} demandes dépassent leur date requise.',
                'recommendation': 'Traiter en priorité les demandes urgentes et revoir les processus d\'approbation.'
            })

        # Analyse des fournisseurs sous-performants
        poor_suppliers = Supplier.objects.filter(
            tenant=self.tenant,
            is_active=True,
            overall_rating__lt=3.0
        )

        if poor_suppliers.exists():
            insights.append({
                'type': 'poor_suppliers',
                'priority': 'medium',
                'title': 'Fournisseurs sous-performants',
                'description': f'{poor_suppliers.count()} fournisseurs ont une note < 3/5.',
                'recommendation': 'Revoir les contrats et envisager de nouveaux fournisseurs pour ces catégories.'
            })

        # Analyse des commandes en retard de livraison
        late_orders = PurchaseOrder.objects.filter(
            tenant=self.tenant,
            status__in=['sent', 'confirmed'],
            expected_delivery_date__lt=timezone.now().date()
        )

        if late_orders.exists():
            insights.append({
                'type': 'late_deliveries',
                'priority': 'high',
                'title': 'Livraisons en retard',
                'description': f'{late_orders.count()} commandes sont en retard de livraison.',
                'recommendation': 'Contacter les fournisseurs pour obtenir des mises à jour et revoir les délais.'
            })

        # Analyse des factures en attente
        old_invoices = SupplierInvoice.objects.filter(
            tenant=self.tenant,
            status='received',
            received_date__lt=timezone.now().date() - timedelta(days=7)
        )

        if old_invoices.exists():
            insights.append({
                'type': 'pending_invoices',
                'priority': 'medium',
                'title': 'Factures en attente de validation',
                'description': f'{old_invoices.count()} factures attendent validation depuis plus de 7 jours.',
                'recommendation': 'Accélérer le processus de validation pour éviter les pénalités de retard.'
            })

        return insights

    def get_cost_savings_opportunities(self) -> Dict[str, Any]:
        """Identifie les opportunités d'économies"""
        opportunities = []

        # Analyse des achats récurrents
        categories = ProductCategory.objects.filter(tenant=self.tenant)

        for category in categories:
            # Commandes de cette catégorie sur les 6 derniers mois
            recent_orders = PurchaseOrderItem.objects.filter(
                purchase_order__tenant=self.tenant,
                purchase_order__order_date__gte=timezone.now().date() - timedelta(days=180)
            ).filter(
                request_item__category=category
            )

            if recent_orders.count() >= 3:  # Au moins 3 commandes
                total_value = recent_orders.aggregate(total=Sum('line_total'))['total'] or Decimal('0.00')

                if total_value > 10000:  # Seuil significatif
                    opportunities.append({
                        'type': 'volume_discount',
                        'category': category.name,
                        'description': f'Négocier des remises de volume pour {category.name}',
                        'potential_savings': float(total_value * Decimal('0.05')),  # 5% d'économie estimée
                        'recommendation': 'Consolider les achats et négocier un contrat cadre'
                    })

        # Analyse des fournisseurs multiples pour même produit
        duplicate_items = {}
        all_items = PurchaseOrderItem.objects.filter(
            purchase_order__tenant=self.tenant,
            purchase_order__order_date__gte=timezone.now().date() - timedelta(days=90)
        )

        for item in all_items:
            key = item.item_name.lower().strip()
            if key not in duplicate_items:
                duplicate_items[key] = []
            duplicate_items[key].append(item)

        for item_name, items in duplicate_items.items():
            if len(set(item.purchase_order.supplier_id for item in items)) > 1:
                # Même produit acheté chez plusieurs fournisseurs
                prices = [item.unit_price for item in items]
                if max(prices) > min(prices) * Decimal('1.1'):  # Écart > 10%
                    opportunities.append({
                        'type': 'supplier_consolidation',
                        'item': item_name,
                        'description': f'Consolider les achats de {item_name}',
                        'potential_savings': float((max(prices) - min(prices)) * sum(item.quantity_ordered for item in items)),
                        'recommendation': 'Standardiser sur le fournisseur le moins cher'
                    })

        total_savings = sum(opp['potential_savings'] for opp in opportunities)

        return {
            'opportunities': opportunities,
            'total_potential_savings': total_savings,
            'count': len(opportunities)
        }
