"""
Modèles spécifiques à l'Agent Manager
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDModel, Tenant
from agents.models import Agent, AgentTask


class ManagerDecision(UUIDModel, TimeStampedModel):
    """
    Modèle pour les décisions prises par l'Agent Manager
    """
    DECISION_TYPES = [
        ('task_assignment', _('Attribution de tâche')),
        ('resource_allocation', _('Allocation de ressources')),
        ('workflow_optimization', _('Optimisation de workflow')),
        ('conflict_resolution', _('Résolution de conflit')),
        ('performance_adjustment', _('Ajustement de performance')),
    ]
    
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='manager_decisions',
        verbose_name=_("Tenant")
    )
    
    decision_type = models.CharField(_("Type de décision"), max_length=50, choices=DECISION_TYPES)
    context = models.JSONField(_("Contexte"), default=dict)
    decision_data = models.JSONField(_("Données de décision"), default=dict)
    reasoning = models.TextField(_("Raisonnement"), blank=True)
    
    # Agents impliqués
    involved_agents = models.ManyToManyField(
        Agent,
        related_name='manager_decisions',
        verbose_name=_("Agents impliqués")
    )
    
    # Résultats
    implemented = models.BooleanField(_("Implémentée"), default=False)
    success = models.BooleanField(_("Succès"), default=False)
    impact_score = models.FloatField(_("Score d'impact"), null=True, blank=True)
    
    class Meta:
        verbose_name = _("Décision du Manager")
        verbose_name_plural = _("Décisions du Manager")
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.get_decision_type_display()} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"


class SystemMetrics(UUIDModel, TimeStampedModel):
    """
    Modèle pour les métriques système collectées par l'Agent Manager
    """
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='system_metrics',
        verbose_name=_("Tenant")
    )
    
    # Métriques générales
    total_agents = models.PositiveIntegerField(_("Total agents"), default=0)
    active_agents = models.PositiveIntegerField(_("Agents actifs"), default=0)
    total_tasks = models.PositiveIntegerField(_("Total tâches"), default=0)
    pending_tasks = models.PositiveIntegerField(_("Tâches en attente"), default=0)
    running_tasks = models.PositiveIntegerField(_("Tâches en cours"), default=0)
    completed_tasks = models.PositiveIntegerField(_("Tâches terminées"), default=0)
    failed_tasks = models.PositiveIntegerField(_("Tâches échouées"), default=0)
    
    # Métriques de performance
    average_task_duration = models.FloatField(_("Durée moyenne des tâches"), null=True, blank=True)
    system_load = models.FloatField(_("Charge système"), default=0.0)
    success_rate = models.FloatField(_("Taux de réussite"), default=0.0)
    
    # Métriques par agent
    agent_metrics = models.JSONField(_("Métriques par agent"), default=dict)
    
    # Alertes et recommandations
    alerts = models.JSONField(_("Alertes"), default=list)
    recommendations = models.JSONField(_("Recommandations"), default=list)
    
    class Meta:
        verbose_name = _("Métriques système")
        verbose_name_plural = _("Métriques système")
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Métriques {self.tenant.name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
