#!/usr/bin/env python3
"""
Test du déploiement en production pour ERP HUB
Vérifie que tous les composants sont prêts pour la production
"""
import os
import sys
import json
import subprocess
from pathlib import Path

def test_deployment_files():
    """Test de la présence des fichiers de déploiement"""
    print("🔍 TEST DES FICHIERS DE DÉPLOIEMENT")
    print("-" * 50)
    
    required_files = {
        'docker-compose.prod.yml': 'Configuration Docker Compose production',
        '.env.production': 'Variables d\'environnement production',
        'deploy.sh': 'Script de déploiement automatique',
        'DEPLOYMENT.md': 'Guide de déploiement',
        'nginx/nginx.conf': 'Configuration Nginx principale',
        'nginx/conf.d/erp.conf': 'Configuration Nginx ERP',
        'monitoring/prometheus.yml': 'Configuration Prometheus',
        'scripts/backup.sh': 'Script de sauvegarde',
        'backend/Dockerfile': 'Dockerfile backend production',
        'frontend/Dockerfile': 'Dockerfile frontend production',
        'backend/config/settings/production.py': 'Settings Django production'
    }
    
    all_present = True
    for file_path, description in required_files.items():
        if Path(file_path).exists():
            print(f"  ✅ {description}: {file_path}")
        else:
            print(f"  ❌ {description}: {file_path} MANQUANT")
            all_present = False
    
    return all_present

def test_docker_configuration():
    """Test de la configuration Docker"""
    print("\n🐳 TEST CONFIGURATION DOCKER")
    print("-" * 50)
    
    # Vérifier docker-compose.prod.yml
    compose_file = Path('docker-compose.prod.yml')
    if not compose_file.exists():
        print("  ❌ docker-compose.prod.yml manquant")
        return False
    
    with open(compose_file, 'r') as f:
        content = f.read()
    
    required_services = [
        'postgres', 'redis', 'backend', 'frontend', 'nginx',
        'celery_worker', 'celery_beat', 'prometheus', 'grafana'
    ]
    
    all_services = True
    for service in required_services:
        if f'{service}:' in content:
            print(f"  ✅ Service {service} configuré")
        else:
            print(f"  ❌ Service {service} MANQUANT")
            all_services = False
    
    # Vérifier les volumes
    required_volumes = [
        'postgres_data', 'redis_data', 'static_files', 'media_files'
    ]
    
    for volume in required_volumes:
        if volume in content:
            print(f"  ✅ Volume {volume} configuré")
        else:
            print(f"  ❌ Volume {volume} MANQUANT")
            all_services = False
    
    return all_services

def test_environment_variables():
    """Test des variables d'environnement"""
    print("\n🔧 TEST VARIABLES D'ENVIRONNEMENT")
    print("-" * 50)
    
    env_file = Path('.env.production')
    if not env_file.exists():
        print("  ❌ .env.production manquant")
        return False
    
    with open(env_file, 'r') as f:
        content = f.read()
    
    critical_vars = [
        'SECRET_KEY', 'JWT_SECRET_KEY', 'POSTGRES_PASSWORD', 'REDIS_PASSWORD',
        'ALLOWED_HOSTS', 'CORS_ALLOWED_ORIGINS', 'EMAIL_HOST', 'DATABASE_URL'
    ]
    
    all_vars = True
    for var in critical_vars:
        if f'{var}=' in content:
            print(f"  ✅ Variable {var} définie")
        else:
            print(f"  ❌ Variable {var} MANQUANTE")
            all_vars = False
    
    # Vérifier les valeurs par défaut dangereuses
    dangerous_defaults = [
        'your-super-secret-key',
        'your-strong-postgres-password',
        'your-openai-api-key'
    ]
    
    for default in dangerous_defaults:
        if default in content:
            print(f"  ⚠️  ATTENTION: Valeur par défaut détectée: {default}")
            print(f"      Changez cette valeur avant le déploiement!")
    
    return all_vars

def test_security_configuration():
    """Test de la configuration de sécurité"""
    print("\n🛡️  TEST CONFIGURATION SÉCURITÉ")
    print("-" * 50)
    
    # Vérifier les settings de production
    settings_file = Path('backend/config/settings/production.py')
    if not settings_file.exists():
        print("  ❌ Settings production manquants")
        return False
    
    with open(settings_file, 'r') as f:
        content = f.read()
    
    security_settings = [
        'DEBUG = False',
        'SECURE_SSL_REDIRECT = True',
        'SECURE_BROWSER_XSS_FILTER = True',
        'SECURE_CONTENT_TYPE_NOSNIFF = True',
        'SECURE_HSTS_SECONDS',
        'SESSION_COOKIE_SECURE = True',
        'CSRF_COOKIE_SECURE = True'
    ]
    
    all_secure = True
    for setting in security_settings:
        if setting in content:
            print(f"  ✅ {setting}")
        else:
            print(f"  ❌ {setting} MANQUANT")
            all_secure = False
    
    return all_secure

def test_nginx_configuration():
    """Test de la configuration Nginx"""
    print("\n🌐 TEST CONFIGURATION NGINX")
    print("-" * 50)
    
    nginx_conf = Path('nginx/conf.d/erp.conf')
    if not nginx_conf.exists():
        print("  ❌ Configuration Nginx manquante")
        return False
    
    with open(nginx_conf, 'r') as f:
        content = f.read()
    
    nginx_features = [
        'proxy_pass http://backend',
        'proxy_pass http://frontend',
        'gzip on',
        'limit_req zone=',
        'add_header X-Frame-Options',
        'add_header X-Content-Type-Options',
        'ssl_certificate',  # Commenté mais présent
        'location /api/',
        'location /static/',
        'location /media/'
    ]
    
    all_configured = True
    for feature in nginx_features:
        if feature in content:
            print(f"  ✅ {feature}")
        else:
            print(f"  ❌ {feature} MANQUANT")
            all_configured = False
    
    return all_configured

def test_monitoring_configuration():
    """Test de la configuration de monitoring"""
    print("\n📊 TEST CONFIGURATION MONITORING")
    print("-" * 50)
    
    prometheus_conf = Path('monitoring/prometheus.yml')
    if not prometheus_conf.exists():
        print("  ❌ Configuration Prometheus manquante")
        return False
    
    with open(prometheus_conf, 'r') as f:
        content = f.read()
    
    monitoring_targets = [
        'django-backend',
        'nginx-frontend',
        'postgres',
        'redis',
        'prometheus'
    ]
    
    all_monitored = True
    for target in monitoring_targets:
        if target in content:
            print(f"  ✅ Target {target} configuré")
        else:
            print(f"  ❌ Target {target} MANQUANT")
            all_monitored = False
    
    return all_monitored

def test_backup_configuration():
    """Test de la configuration de sauvegarde"""
    print("\n💾 TEST CONFIGURATION SAUVEGARDE")
    print("-" * 50)
    
    backup_script = Path('scripts/backup.sh')
    if not backup_script.exists():
        print("  ❌ Script de sauvegarde manquant")
        return False
    
    # Vérifier que le script est exécutable
    if os.access(backup_script, os.X_OK):
        print("  ✅ Script de sauvegarde exécutable")
    else:
        print("  ❌ Script de sauvegarde non exécutable")
        return False
    
    with open(backup_script, 'r') as f:
        content = f.read()
    
    backup_features = [
        'backup_database()',
        'backup_media()',
        'backup_logs()',
        'backup_config()',
        'cleanup_old_backups()',
        'verify_backup()',
        'pg_dump'
    ]
    
    all_features = True
    for feature in backup_features:
        if feature in content:
            print(f"  ✅ {feature}")
        else:
            print(f"  ❌ {feature} MANQUANT")
            all_features = False
    
    return all_features

def test_deployment_script():
    """Test du script de déploiement"""
    print("\n🚀 TEST SCRIPT DÉPLOIEMENT")
    print("-" * 50)
    
    deploy_script = Path('deploy.sh')
    if not deploy_script.exists():
        print("  ❌ Script de déploiement manquant")
        return False
    
    # Vérifier que le script est exécutable
    if os.access(deploy_script, os.X_OK):
        print("  ✅ Script de déploiement exécutable")
    else:
        print("  ❌ Script de déploiement non exécutable")
        return False
    
    with open(deploy_script, 'r') as f:
        content = f.read()
    
    deploy_functions = [
        'check_prerequisites()',
        'backup_database()',
        'stop_services()',
        'build_images()',
        'start_services()',
        'run_migrations()',
        'health_check()',
        'cleanup()'
    ]
    
    all_functions = True
    for function in deploy_functions:
        if function in content:
            print(f"  ✅ {function}")
        else:
            print(f"  ❌ {function} MANQUANT")
            all_functions = False
    
    return all_functions

def generate_production_checklist():
    """Génère une checklist de déploiement"""
    print("\n📋 CHECKLIST DE DÉPLOIEMENT PRODUCTION")
    print("=" * 60)
    
    checklist = [
        "[ ] Serveur de production configuré (8GB+ RAM, 4+ CPU cores)",
        "[ ] Docker et Docker Compose installés",
        "[ ] Variables d'environnement configurées (.env.production)",
        "[ ] Mots de passe forts générés (SECRET_KEY, POSTGRES_PASSWORD, etc.)",
        "[ ] Domaine configuré et DNS pointant vers le serveur",
        "[ ] Certificats SSL obtenus (Let's Encrypt recommandé)",
        "[ ] Pare-feu configuré (ports 22, 80, 443)",
        "[ ] Sauvegarde automatique programmée (crontab)",
        "[ ] Monitoring configuré (Grafana/Prometheus)",
        "[ ] Tests de charge effectués",
        "[ ] Plan de rollback préparé",
        "[ ] Documentation équipe mise à jour",
        "[ ] Contacts support définis",
        "[ ] Procédures d'urgence documentées"
    ]
    
    for item in checklist:
        print(f"  {item}")
    
    print(f"\n💡 COMMANDES DE DÉPLOIEMENT:")
    print(f"  1. Configuration: cp .env.production .env.local && nano .env.local")
    print(f"  2. Déploiement: ./deploy.sh production")
    print(f"  3. Vérification: curl -f http://localhost:8000/api/health/")
    print(f"  4. Sauvegarde: ./scripts/backup.sh full")

def main():
    """Test principal du déploiement"""
    print("🚀 TEST DE PRÉPARATION DÉPLOIEMENT PRODUCTION")
    print("🏢 SYSTÈME ERP MODULAIRE")
    print("=" * 60)
    
    tests = [
        ("Fichiers de déploiement", test_deployment_files),
        ("Configuration Docker", test_docker_configuration),
        ("Variables d'environnement", test_environment_variables),
        ("Configuration sécurité", test_security_configuration),
        ("Configuration Nginx", test_nginx_configuration),
        ("Configuration monitoring", test_monitoring_configuration),
        ("Configuration sauvegarde", test_backup_configuration),
        ("Script de déploiement", test_deployment_script),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur: {e}")
            results.append((test_name, False))
    
    # Résultats finaux
    print("\n" + "=" * 60)
    print("📊 RÉSULTATS DES TESTS:")
    
    passed_tests = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"   {test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / len(results)) * 100
    print(f"\n🎯 Taux de réussite: {passed_tests}/{len(results)} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("\n🎉 SYSTÈME PRÊT POUR LA PRODUCTION !")
        print("   Tous les composants de déploiement sont configurés.")
        print("   Suivez le guide DEPLOYMENT.md pour déployer.")
        generate_production_checklist()
    else:
        print("\n⚠️  Le système nécessite des ajustements avant la production.")
        print("   Corrigez les éléments marqués comme ÉCHEC.")
    
    return success_rate >= 90

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
