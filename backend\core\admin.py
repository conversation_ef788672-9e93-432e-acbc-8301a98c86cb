"""
Administration Django pour l'application core
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User, Tenant, Role, UserRole


@admin.register(Tenant)
class TenantAdmin(admin.ModelAdmin):
    """Administration des tenants"""
    list_display = ['name', 'slug', 'email', 'is_active', 'max_users', 'created_at']
    list_filter = ['is_active', 'created_at', 'country']
    search_fields = ['name', 'slug', 'email']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (_('Informations générales'), {
            'fields': ('name', 'slug', 'description', 'is_active')
        }),
        (_('Contact'), {
            'fields': ('email', 'phone', 'website')
        }),
        (_('Adresse'), {
            'fields': ('address_line1', 'address_line2', 'city', 'postal_code', 'country')
        }),
        (_('Configuration'), {
            'fields': ('max_users', 'enabled_modules')
        }),
        (_('Métadonnées'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class UserRoleInline(admin.TabularInline):
    """Inline pour les rôles des utilisateurs"""
    model = UserRole
    extra = 0
    readonly_fields = ['created_at']


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Administration des utilisateurs"""
    list_display = ['username', 'email', 'first_name', 'last_name', 'tenant', 'is_active', 'is_tenant_admin']
    list_filter = ['is_active', 'is_tenant_admin', 'tenant', 'date_joined']
    search_fields = ['username', 'first_name', 'last_name', 'email']
    ordering = ['tenant', 'last_name', 'first_name']
    readonly_fields = ['date_joined', 'last_login', 'created_at', 'updated_at']
    inlines = [UserRoleInline]
    
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('Informations personnelles'), {
            'fields': ('first_name', 'last_name', 'email', 'phone', 'avatar', 'birth_date')
        }),
        (_('Tenant et permissions'), {
            'fields': ('tenant', 'is_active', 'is_tenant_admin', 'is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        (_('Préférences'), {
            'fields': ('language', 'timezone', 'theme')
        }),
        (_('Sécurité'), {
            'fields': ('last_login_ip', 'failed_login_attempts', 'account_locked_until'),
            'classes': ('collapse',)
        }),
        (_('Dates importantes'), {
            'fields': ('last_login', 'date_joined', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2', 'tenant'),
        }),
    )
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        # Les administrateurs de tenant ne voient que leurs utilisateurs
        if hasattr(request.user, 'tenant'):
            return qs.filter(tenant=request.user.tenant)
        return qs.none()


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    """Administration des rôles"""
    list_display = ['name', 'tenant', 'is_system_role', 'created_at']
    list_filter = ['is_system_role', 'tenant', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (_('Informations générales'), {
            'fields': ('name', 'description', 'tenant', 'is_system_role')
        }),
        (_('Permissions'), {
            'fields': ('permissions',),
            'description': _('Permissions par module au format JSON')
        }),
        (_('Métadonnées'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        # Les administrateurs de tenant ne voient que leurs rôles
        if hasattr(request.user, 'tenant'):
            return qs.filter(tenant=request.user.tenant)
        return qs.none()


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    """Administration des attributions de rôles"""
    list_display = ['user', 'role', 'is_active', 'assigned_by', 'created_at']
    list_filter = ['is_active', 'role__tenant', 'created_at']
    search_fields = ['user__username', 'user__first_name', 'user__last_name', 'role__name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (_('Attribution'), {
            'fields': ('user', 'role', 'assigned_by', 'is_active')
        }),
        (_('Métadonnées'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        # Les administrateurs de tenant ne voient que leurs attributions
        if hasattr(request.user, 'tenant'):
            return qs.filter(user__tenant=request.user.tenant)
        return qs.none()
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if not request.user.is_superuser and hasattr(request.user, 'tenant'):
            if db_field.name == "user":
                kwargs["queryset"] = User.objects.filter(tenant=request.user.tenant)
            elif db_field.name == "role":
                kwargs["queryset"] = Role.objects.filter(tenant=request.user.tenant)
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
