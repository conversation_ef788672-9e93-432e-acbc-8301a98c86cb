version: '3.8'

services:
  # Base de données PostgreSQL
  db:
    image: postgres:15-alpine
    container_name: erp_hub_db
    environment:
      POSTGRES_DB: erp_hub
      POSTGRES_USER: erp_user
      POSTGRES_PASSWORD: erp_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - erp_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U erp_user -d erp_hub"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Django REST API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: erp_hub_backend
    environment:
      - DEBUG=False
      - DATABASE_URL=******************************************/erp_hub
      - SECRET_KEY=your-secret-key-here
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend
    volumes:
      - ./backend:/app
      - backend_static:/app/static
      - backend_media:/app/media
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - erp_network
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn config.wsgi:application --bind 0.0.0.0:8000"

  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: erp_hub_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - NODE_ENV=production
    volumes:
      - ./frontend:/app
      - frontend_node_modules:/app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - erp_network

  # Redis pour le cache et les tâches asynchrones
  redis:
    image: redis:7-alpine
    container_name: erp_hub_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - erp_network

  # Nginx pour le reverse proxy (production)
  nginx:
    image: nginx:alpine
    container_name: erp_hub_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - backend_static:/var/www/static
      - backend_media:/var/www/media
    depends_on:
      - backend
      - frontend
    networks:
      - erp_network

volumes:
  postgres_data:
  redis_data:
  backend_static:
  backend_media:
  frontend_node_modules:

networks:
  erp_network:
    driver: bridge
