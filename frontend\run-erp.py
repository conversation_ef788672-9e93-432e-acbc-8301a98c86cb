#!/usr/bin/env python3
"""
Serveur HTTP simple pour ERP HUB
Alternative à Node.js en cas de problème
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

PORT = 3000

class ERPHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def do_GET(self):
        # Routes de l'ERP
        routes = {
            '/': 'dashboard-demo.html',
            '/dashboard': 'dashboard-demo.html',
            '/login': 'login-demo.html',
            '/hr': 'hr-management.html',
            '/sales': 'sales-management.html',
            '/purchase': 'purchase-management.html',
            '/logistics': 'logistics-management.html',
            '/stock': 'stock-management.html',
            '/accounting': 'accounting-management.html',
            '/finance': 'finance-management.html',
            '/crm': 'crm-management.html',
            '/bi': 'bi-management.html',
            '/manager': 'manager-management.html'
        }
        
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        print(f"📥 Requête: {self.command} {path}")
        
        if path in routes:
            # Rediriger vers le bon fichier
            self.path = '/' + routes[path]
        
        # Ajouter les headers CORS
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
            super(ERPHandler, self).end_headers()
        
        self.end_headers = end_headers.__get__(self, ERPHandler)
        
        try:
            super().do_GET()
            print(f"✅ Fichier servi: {self.path}")
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

def main():
    try:
        with socketserver.TCPServer(("", PORT), ERPHandler) as httpd:
            print('\n🎉 ===============================================')
            print('🚀 ERP HUB SERVER PYTHON DÉMARRÉ !')
            print('🎉 ===============================================')
            print(f'📍 URL: http://localhost:{PORT}')
            print('')
            print('📋 AGENTS ERP DISPONIBLES:')
            print('🏠 Dashboard: http://localhost:3000/dashboard')
            print('🔐 Login: http://localhost:3000/login')
            print('👥 HR: http://localhost:3000/hr')
            print('📈 Sales: http://localhost:3000/sales')
            print('🛒 Purchase: http://localhost:3000/purchase')
            print('🚚 Logistics: http://localhost:3000/logistics')
            print('📦 Stock: http://localhost:3000/stock')
            print('🏦 Accounting: http://localhost:3000/accounting')
            print('💰 Finance: http://localhost:3000/finance')
            print('🤝 CRM: http://localhost:3000/crm')
            print('📊 BI: http://localhost:3000/bi')
            print('👨‍💼 Manager: http://localhost:3000/manager')
            print('')
            print('✅ AMÉLIORATIONS DATES: 100% COMPLÈTES !')
            print('📅 Formatage français DD/MM/YYYY')
            print('⏰ Horodatage automatique')
            print('🔧 Bibliothèque date-utils.js')
            print('')
            print('🌟 Votre ERP HUB est prêt !')
            print('🎉 ===============================================\n')
            print('🔄 Serveur en écoute... (Ctrl+C pour arrêter)')
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print('\n🛑 Arrêt du serveur...')
        print('✅ Serveur arrêté')
    except OSError as e:
        if e.errno == 98 or 'Address already in use' in str(e):
            print(f'❌ Le port {PORT} est déjà utilisé.')
            print('💡 Essayez de fermer les autres applications ou utilisez un autre port.')
        else:
            print(f'❌ Erreur: {e}')
    except Exception as e:
        print(f'❌ Erreur inattendue: {e}')

if __name__ == '__main__':
    main()
