version: '3.8'

services:
  # Base de données PostgreSQL
  db:
    image: postgres:15-alpine
    container_name: erp_hub_db_dev
    environment:
      POSTGRES_DB: erp_hub_dev
      POSTGRES_USER: erp_user
      POSTGRES_PASSWORD: erp_password
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - erp_network_dev
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U erp_user -d erp_hub_dev"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Django REST API (Développement)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: erp_hub_backend_dev
    environment:
      - DEBUG=True
      - DATABASE_URL=******************************************/erp_hub_dev
      - SECRET_KEY=dev-secret-key-not-for-production
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend,0.0.0.0
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
    volumes:
      - ./backend:/app
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - erp_network_dev
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"
    stdin_open: true
    tty: true

  # Frontend React (Développement avec Hot Reload)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: erp_hub_frontend_dev
    environment:
      - VITE_API_URL=http://localhost:8000
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./frontend:/app
      - frontend_node_modules_dev:/app/node_modules
    ports:
      - "3000:3000"
      - "24678:24678"  # Port pour Vite HMR
    depends_on:
      - backend
    networks:
      - erp_network_dev
    command: npm run dev -- --host 0.0.0.0
    stdin_open: true
    tty: true

  # Redis pour le développement
  redis:
    image: redis:7-alpine
    container_name: erp_hub_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - erp_network_dev

  # Adminer pour la gestion de base de données
  adminer:
    image: adminer
    container_name: erp_hub_adminer
    ports:
      - "8080:8080"
    depends_on:
      - db
    networks:
      - erp_network_dev

volumes:
  postgres_data_dev:
  redis_data_dev:
  frontend_node_modules_dev:

networks:
  erp_network_dev:
    driver: bridge
