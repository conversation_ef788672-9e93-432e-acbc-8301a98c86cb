#!/usr/bin/env python3
"""
Démarrage rapide en mode développement local
"""
import os
import sys
import subprocess
import time
from pathlib import Path

def check_python():
    """Vérifier Python"""
    print("🐍 Vérification de Python...")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ requis")
        return False
    print(f"✅ Python {sys.version.split()[0]}")
    return True

def setup_environment():
    """Configuration de l'environnement"""
    print("🔧 Configuration de l'environnement...")
    
    # Variables d'environnement pour développement
    os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings'
    os.environ['DEBUG'] = 'True'
    os.environ['SECRET_KEY'] = 'dev-secret-key-for-local-testing-only'
    os.environ['DATABASE_URL'] = 'postgresql://erp_user_local:local_test_password_123@localhost:5432/erp_hub_local'
    os.environ['REDIS_URL'] = 'redis://:local_redis_password_123@localhost:6379/0'
    os.environ['ALLOWED_HOSTS'] = 'localhost,127.0.0.1'
    os.environ['CORS_ALLOWED_ORIGINS'] = 'http://localhost:3000'
    
    print("✅ Variables d'environnement configurées")

def install_dependencies():
    """Installation des dépendances"""
    print("📦 Installation des dépendances...")
    
    backend_dir = Path('backend')
    if not backend_dir.exists():
        print("❌ Répertoire backend non trouvé")
        return False
    
    requirements_file = backend_dir / 'requirements.txt'
    if requirements_file.exists():
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
            ], check=True, cwd=backend_dir)
            print("✅ Dépendances installées")
            return True
        except subprocess.CalledProcessError:
            print("⚠️ Erreur d'installation des dépendances")
            return False
    else:
        print("⚠️ requirements.txt non trouvé")
        return False

def run_migrations():
    """Exécution des migrations"""
    print("🔄 Exécution des migrations...")
    
    backend_dir = Path('backend')
    try:
        subprocess.run([
            sys.executable, 'manage.py', 'migrate'
        ], check=True, cwd=backend_dir)
        print("✅ Migrations exécutées")
        return True
    except subprocess.CalledProcessError:
        print("⚠️ Erreur lors des migrations")
        return False

def start_django_server():
    """Démarrage du serveur Django"""
    print("🚀 Démarrage du serveur Django...")
    
    backend_dir = Path('backend')
    try:
        print("🌐 Serveur Django démarré sur http://localhost:8000")
        print("📚 Admin Django: http://localhost:8000/admin")
        print("🔧 API: http://localhost:8000/api")
        print("📖 Documentation: http://localhost:8000/docs")
        print("\n🛑 Appuyez sur Ctrl+C pour arrêter")
        
        subprocess.run([
            sys.executable, 'manage.py', 'runserver', '0.0.0.0:8000'
        ], cwd=backend_dir)
        
    except KeyboardInterrupt:
        print("\n🛑 Serveur arrêté")
    except subprocess.CalledProcessError:
        print("❌ Erreur lors du démarrage du serveur")

def check_database_connection():
    """Vérifier la connexion à la base de données"""
    print("🔍 Vérification de la connexion à la base de données...")
    
    try:
        import psycopg2
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            database='erp_hub_local',
            user='erp_user_local',
            password='local_test_password_123'
        )
        conn.close()
        print("✅ Connexion PostgreSQL OK")
        return True
    except Exception as e:
        print(f"❌ Connexion PostgreSQL échouée: {e}")
        print("💡 Assurez-vous que PostgreSQL est démarré avec Docker:")
        print("   docker-compose -f docker-compose.local.yml up -d postgres")
        return False

def main():
    """Fonction principale"""
    print("🚀 DÉMARRAGE RAPIDE ERP HUB - MODE DÉVELOPPEMENT")
    print("=" * 60)
    
    # Vérifications
    if not check_python():
        return False
    
    if not check_database_connection():
        return False
    
    # Configuration
    setup_environment()
    
    # Installation des dépendances
    if not install_dependencies():
        print("⚠️ Continuons sans installer les dépendances...")
    
    # Migrations
    if not run_migrations():
        print("⚠️ Continuons sans migrations...")
    
    # Démarrage du serveur
    start_django_server()
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt demandé par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur: {e}")
        sys.exit(1)
