@echo off
echo ========================================
echo    🚀 DÉMARRAGE ERP HUB COMPLET
echo ========================================
echo.

echo 📡 Démarrage du backend Django...
start "Django Backend" cmd /k "cd backend && python manage.py runserver 8000"
timeout /t 3 /nobreak >nul

echo 🌐 Démarrage du serveur Node.js...
start "Node.js Frontend" cmd /k "cd frontend && node simple-server.js"
timeout /t 2 /nobreak >nul

echo.
echo ✅ ERP HUB démarré avec succès !
echo.
echo 📊 URLs disponibles :
echo    - Frontend : http://localhost:3000
echo    - Backend Django : http://localhost:8000
echo    - API Health : http://localhost:3000/health
echo.
echo 🔧 Architecture :
echo    - Django Backend (Port 8000) - API REST
echo    - Node.js Frontend (Port 3000) - Interface React
echo    - Proxy automatique vers Django
echo.
echo Appuyez sur une touche pour ouvrir l'application...
pause >nul

start http://localhost:3000
