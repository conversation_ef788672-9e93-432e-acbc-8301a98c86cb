"""
Vues pour l'agent manager
"""
import logging
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema

from agents.models import Agent, AgentTask
from agents.ai_service import ai_service
from .services import AgentManagerService
from .serializers import (
    AgentTaskSerializer, AgentSerializer, SystemMetricsSerializer,
    TaskAssignmentSerializer, AgentCommunicationSerializer,
    MessageBroadcastSerializer
)

logger = logging.getLogger('agents.manager')


@extend_schema(
    summary="Statut de l'agent manager",
    description="Retourne le statut détaillé de l'agent manager et du système"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def manager_status(request):
    """Retourne le statut de l'agent manager"""
    try:
        manager_service = AgentManagerService(request.user.tenant)
        system_status = manager_service.get_system_status()

        return Response({
            'status': 'active',
            'agent': 'manager',
            'message': 'Agent Manager opérationnel',
            'ai_enabled': ai_service.is_available(),
            'system_status': system_status,
            'capabilities': [
                'task_orchestration',
                'resource_management',
                'performance_monitoring',
                'decision_making',
                'workflow_optimization'
            ]
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut: {str(e)}")
        return Response({
            'status': 'error',
            'agent': 'manager',
            'message': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Dashboard de l'agent manager",
    description="Retourne les données complètes du dashboard pour l'agent manager"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def manager_dashboard(request):
    """Retourne les données du dashboard"""
    try:
        manager_service = AgentManagerService(request.user.tenant)

        # Collecte des métriques système
        system_metrics = manager_service.collect_system_metrics()

        # Optimisations de workflows
        workflow_optimizations = manager_service.optimize_workflows()

        # Agents actifs avec leurs métriques
        agents = Agent.objects.filter(tenant=request.user.tenant)
        agents_data = AgentSerializer(agents, many=True).data

        # Tâches récentes
        recent_tasks = AgentTask.objects.filter(
            agent__tenant=request.user.tenant
        ).order_by('-created_at')[:10]
        tasks_data = AgentTaskSerializer(recent_tasks, many=True).data

        return Response({
            'agent': 'manager',
            'tenant': request.user.tenant.name,
            'timestamp': system_metrics.created_at.isoformat(),
            'system_metrics': SystemMetricsSerializer(system_metrics).data,
            'agents': agents_data,
            'recent_tasks': tasks_data,
            'workflow_optimizations': workflow_optimizations,
            'ai_insights': _generate_ai_insights(system_metrics) if ai_service.is_available() else []
        })
    except Exception as e:
        logger.error(f"Erreur lors de la génération du dashboard: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TaskAssignmentView(APIView):
    """Vue pour l'assignation de tâches par l'Agent Manager"""
    permission_classes = [IsAuthenticated]

    @extend_schema(
        summary="Assigner une tâche",
        description="Assigne une tâche à l'agent le plus approprié",
        request=TaskAssignmentSerializer,
        responses={201: AgentTaskSerializer}
    )
    def post(self, request):
        """Assigne une nouvelle tâche"""
        try:
            serializer = TaskAssignmentSerializer(data=request.data)
            if serializer.is_valid():
                manager_service = AgentManagerService(request.user.tenant)

                # Données de la tâche avec l'utilisateur assigneur
                task_data = serializer.validated_data.copy()
                task_data['assigned_by'] = request.user

                # Assignation de la tâche
                task = manager_service.assign_task_to_agent(
                    task_data,
                    task_data.get('target_agent_type')
                )

                return Response(
                    AgentTaskSerializer(task).data,
                    status=status.HTTP_201_CREATED
                )
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Erreur lors de l'assignation de tâche: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AgentCommunicationView(APIView):
    """Vue pour la communication entre agents"""
    permission_classes = [IsAuthenticated]

    @extend_schema(
        summary="Envoyer un message",
        description="Envoie un message à un agent spécifique ou diffuse à tous",
        request=MessageBroadcastSerializer,
        responses={201: AgentCommunicationSerializer(many=True)}
    )
    def post(self, request):
        """Envoie un message aux agents"""
        try:
            serializer = MessageBroadcastSerializer(data=request.data)
            if serializer.is_valid():
                manager_service = AgentManagerService(request.user.tenant)

                target_agents = serializer.validated_data.get('target_agents', [])
                message_type = serializer.validated_data['message_type']
                subject = serializer.validated_data['subject']
                content = serializer.validated_data['content']

                if target_agents:
                    # Envoyer à des agents spécifiques
                    messages = []
                    for agent_type in target_agents:
                        try:
                            message = manager_service.send_message_to_agent(
                                agent_type, message_type, subject, content
                            )
                            messages.append(message)
                        except ValueError as e:
                            logger.warning(f"Agent {agent_type} non trouvé: {str(e)}")
                else:
                    # Diffuser à tous les agents
                    messages = manager_service.broadcast_message(
                        message_type, subject, content
                    )

                return Response(
                    AgentCommunicationSerializer(messages, many=True).data,
                    status=status.HTTP_201_CREATED
                )
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de message: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@extend_schema(
    summary="Métriques système",
    description="Retourne les métriques détaillées du système"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def system_metrics(request):
    """Retourne les métriques système"""
    try:
        manager_service = AgentManagerService(request.user.tenant)
        metrics = manager_service.collect_system_metrics()

        return Response(SystemMetricsSerializer(metrics).data)
    except Exception as e:
        logger.error(f"Erreur lors de la collecte des métriques: {str(e)}")
        return Response(
            {'error': f'Erreur lors de la collecte des métriques: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Optimisations de workflows",
    description="Analyse et retourne les optimisations possibles pour les workflows"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def workflow_optimizations(request):
    """Retourne les optimisations de workflows"""
    try:
        manager_service = AgentManagerService(request.user.tenant)
        optimizations = manager_service.optimize_workflows()

        return Response({
            'optimizations': optimizations,
            'count': len(optimizations),
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse des workflows: {str(e)}")
        return Response(
            {'error': f'Erreur lors de l\'analyse des workflows: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def _generate_ai_insights(system_metrics):
    """Génère des insights IA basés sur les métriques système"""
    try:
        metrics_data = {
            'success_rate': system_metrics.success_rate,
            'system_load': system_metrics.system_load,
            'total_tasks': system_metrics.total_tasks,
            'active_agents': system_metrics.active_agents,
            'alerts': system_metrics.alerts
        }

        insights = ai_service.generate_performance_insights(metrics_data)
        return insights
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'insights IA: {str(e)}")
        return []
