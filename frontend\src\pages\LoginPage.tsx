import React, { useState } from 'react'
import { useN<PERSON><PERSON>, Link } from 'react-router-dom'
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  Container,
} from '@mui/material'
import { 
  Visibility, 
  VisibilityOff, 
  Login as LoginIcon,
  ArrowBack as ArrowBackIcon 
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { useAuthStore } from '../store/authStore'

export const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const { login, loading, error } = useAuthStore()
  
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  })
  
  const [showPassword, setShowPassword] = useState(false)
  const [localError, setLocalError] = useState('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
    // Effacer l'erreur quand l'utilisateur tape
    if (localError) setLocalError('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLocalError('')

    try {
      const success = await login(formData.username, formData.password)
      
      if (success) {
        navigate('/dashboard')
      } else {
        setLocalError('Nom d\'utilisateur ou mot de passe incorrect')
      }
    } catch (err: any) {
      console.error('Erreur de connexion:', err)
      setLocalError('Erreur de connexion. Veuillez réessayer.')
    }
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const displayError = localError || error

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2,
      }}
    >
      <Container maxWidth="sm">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Bouton retour vers l'accueil */}
          <Box sx={{ mb: 3 }}>
            <Button
              component={Link}
              to="/"
              startIcon={<ArrowBackIcon />}
              sx={{
                color: 'white',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              Retour à l'accueil
            </Button>
          </Box>

          <Card
            sx={{
              boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
              borderRadius: 3,
            }}
          >
            <CardContent sx={{ p: 4 }}>
              <Box textAlign="center" mb={4}>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <LoginIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
                </motion.div>
                
                <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
                  Connexion
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Accédez à votre espace ERP HUB
                </Typography>
              </Box>

              {displayError && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {displayError}
                  </Alert>
                </motion.div>
              )}

              <form onSubmit={handleSubmit}>
                <TextField
                  fullWidth
                  label="Nom d'utilisateur"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  margin="normal"
                  required
                  autoComplete="username"
                  autoFocus
                  disabled={loading}
                  sx={{ mb: 2 }}
                />

                <TextField
                  fullWidth
                  label="Mot de passe"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={handleChange}
                  margin="normal"
                  required
                  autoComplete="current-password"
                  disabled={loading}
                  sx={{ mb: 3 }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={togglePasswordVisibility}
                          edge="end"
                          disabled={loading}
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading || !formData.username || !formData.password}
                  sx={{ 
                    py: 1.5, 
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    borderRadius: 2,
                    textTransform: 'none'
                  }}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    'Se connecter'
                  )}
                </Button>
              </form>

              {/* Comptes de démonstration */}
              <Box sx={{ mt: 4, p: 2, bgcolor: 'background.default', borderRadius: 2 }}>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  🔑 Comptes de démonstration
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  <strong>Admin :</strong> admin / admin123
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  <strong>Manager :</strong> manager / test123
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  <strong>RH :</strong> hr_user / test123
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Commercial :</strong> sales_user / test123
                </Typography>
              </Box>

              <Box textAlign="center" mt={3}>
                <Typography variant="body2" color="text.secondary">
                  Pas encore de compte ?{' '}
                  <Button
                    variant="text"
                    size="small"
                    onClick={() => navigate('/register')}
                    disabled={loading}
                    sx={{ textTransform: 'none' }}
                  >
                    S'inscrire
                  </Button>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </motion.div>
      </Container>
    </Box>
  )
}
