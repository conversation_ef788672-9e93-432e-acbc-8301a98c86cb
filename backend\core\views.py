"""
Vues pour l'API REST de l'application core
"""
from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from django.contrib.auth import get_user_model
from django.db import transaction
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

from .models import Tenant, Role, UserRole
from .serializers import (
    UserSerializer, UserCreateSerializer, UserUpdateSerializer,
    UserProfileSerializer, ChangePasswordSerializer, LoginSerializer,
    TenantSerializer, RoleSerializer, UserRoleSerializer
)
from .permissions import IsTenantAdmin, IsSameUserOrTenantAdmin

User = get_user_model()


class CustomTokenObtainPairView(TokenObtainPairView):
    """Vue personnalisée pour l'obtention de tokens JWT"""
    serializer_class = LoginSerializer
    
    @extend_schema(
        summary="Connexion utilisateur",
        description="Authentifie un utilisateur et retourne les tokens JWT",
        responses={200: "Tokens JWT et informations utilisateur"}
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        user = serializer.validated_data['user']
        refresh = RefreshToken.for_user(user)
        
        # Mise à jour de l'IP de dernière connexion
        user.last_login_ip = self.get_client_ip(request)
        user.failed_login_attempts = 0
        user.save(update_fields=['last_login_ip', 'failed_login_attempts'])
        
        return Response({
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user': UserProfileSerializer(user).data
        })
    
    def get_client_ip(self, request):
        """Récupère l'IP du client"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserProfileView(generics.RetrieveUpdateAPIView):
    """Vue pour consulter et modifier le profil utilisateur"""
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        return self.request.user
    
    @extend_schema(
        summary="Profil utilisateur",
        description="Récupère les informations du profil de l'utilisateur connecté"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Mise à jour du profil",
        description="Met à jour les informations du profil utilisateur"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class ChangePasswordView(APIView):
    """Vue pour changer le mot de passe"""
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="Changement de mot de passe",
        description="Permet à un utilisateur de changer son mot de passe",
        request=ChangePasswordSerializer,
        responses={200: "Mot de passe changé avec succès"}
    )
    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            user = request.user
            user.set_password(serializer.validated_data['new_password'])
            user.save()
            return Response({'message': 'Mot de passe changé avec succès.'})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des utilisateurs"""
    permission_classes = [permissions.IsAuthenticated, IsTenantAdmin]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return UserCreateSerializer
        return UserSerializer
    
    def get_queryset(self):
        return User.objects.filter(tenant=self.request.user.tenant)
    
    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)
    
    @extend_schema(
        summary="Liste des utilisateurs",
        description="Récupère la liste des utilisateurs du tenant"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Création d'utilisateur",
        description="Crée un nouvel utilisateur dans le tenant"
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un utilisateur"""
    serializer_class = UserUpdateSerializer
    permission_classes = [permissions.IsAuthenticated, IsSameUserOrTenantAdmin]
    
    def get_queryset(self):
        return User.objects.filter(tenant=self.request.user.tenant)
    
    @extend_schema(
        summary="Détails utilisateur",
        description="Récupère les détails d'un utilisateur"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Mise à jour utilisateur",
        description="Met à jour les informations d'un utilisateur"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)
    
    @extend_schema(
        summary="Suppression utilisateur",
        description="Supprime un utilisateur (désactivation)"
    )
    def delete(self, request, *args, **kwargs):
        user = self.get_object()
        user.is_active = False
        user.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TenantDetailView(generics.RetrieveUpdateAPIView):
    """Vue pour consulter et modifier les informations du tenant"""
    serializer_class = TenantSerializer
    permission_classes = [permissions.IsAuthenticated, IsTenantAdmin]
    
    def get_object(self):
        return self.request.user.tenant
    
    @extend_schema(
        summary="Informations du tenant",
        description="Récupère les informations du tenant de l'utilisateur"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Mise à jour du tenant",
        description="Met à jour les informations du tenant"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class RoleListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des rôles"""
    serializer_class = RoleSerializer
    permission_classes = [permissions.IsAuthenticated, IsTenantAdmin]
    
    def get_queryset(self):
        return Role.objects.filter(tenant=self.request.user.tenant)
    
    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)
    
    @extend_schema(
        summary="Liste des rôles",
        description="Récupère la liste des rôles du tenant"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Création de rôle",
        description="Crée un nouveau rôle dans le tenant"
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class UserRoleManagementView(APIView):
    """Vue pour gérer les rôles des utilisateurs"""
    permission_classes = [permissions.IsAuthenticated, IsTenantAdmin]
    
    @extend_schema(
        summary="Attribution de rôle",
        description="Attribue un rôle à un utilisateur",
        request=UserRoleSerializer,
        responses={201: UserRoleSerializer}
    )
    def post(self, request, user_id):
        try:
            user = User.objects.get(id=user_id, tenant=request.user.tenant)
            role = Role.objects.get(id=request.data['role'], tenant=request.user.tenant)
            
            user_role, created = UserRole.objects.get_or_create(
                user=user,
                role=role,
                defaults={'assigned_by': request.user}
            )
            
            if not created:
                user_role.is_active = True
                user_role.assigned_by = request.user
                user_role.save()
            
            serializer = UserRoleSerializer(user_role)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except (User.DoesNotExist, Role.DoesNotExist):
            return Response(
                {'error': 'Utilisateur ou rôle non trouvé'}, 
                status=status.HTTP_404_NOT_FOUND
            )
    
    @extend_schema(
        summary="Révocation de rôle",
        description="Révoque un rôle d'un utilisateur"
    )
    def delete(self, request, user_id, role_id):
        try:
            user_role = UserRole.objects.get(
                user_id=user_id,
                role_id=role_id,
                user__tenant=request.user.tenant
            )
            user_role.is_active = False
            user_role.save()
            return Response(status=status.HTTP_204_NO_CONTENT)
            
        except UserRole.DoesNotExist:
            return Response(
                {'error': 'Attribution de rôle non trouvée'}, 
                status=status.HTTP_404_NOT_FOUND
            )


@extend_schema(
    summary="Vérification de santé",
    description="Endpoint pour vérifier l'état de l'API"
)
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def health_check(request):
    """Endpoint de vérification de santé"""
    return Response({
        'status': 'healthy',
        'message': 'ERP HUB API is running'
    })
