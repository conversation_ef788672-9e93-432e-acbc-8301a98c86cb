import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  IconButton,
  Alert,
  CircularProgress,
  LinearProgress,
  Divider,
  Badge
} from '@mui/material';
import {
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Campaign as CampaignIcon,
  Support as SupportIcon,
  Insights as InsightsIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Business as BusinessIcon,
  Star as StarIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Add as AddIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { crmService } from '../services/crmService';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`crm-tabpanel-${index}`}
      aria-labelledby={`crm-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const CRMPage = () => {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [dashboardData, setDashboardData] = useState(null);
  const [insights, setInsights] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
    loadInsights();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const data = await crmService.getDashboard();
      setDashboardData(data);
    } catch (err) {
      setError('Erreur lors du chargement du dashboard CRM');
      console.error('Erreur dashboard CRM:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadInsights = async () => {
    try {
      const data = await crmService.getInsights();
      setInsights(data.insights || []);
    } catch (err) {
      console.error('Erreur insights CRM:', err);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const getInsightIcon = (type) => {
    switch (type) {
      case 'critical':
        return <WarningIcon color="error" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'opportunity':
        return <TrendingUpIcon color="success" />;
      default:
        return <InsightsIcon color="info" />;
    }
  };

  const getInsightColor = (type) => {
    switch (type) {
      case 'critical':
        return 'error';
      case 'warning':
        return 'warning';
      case 'opportunity':
        return 'success';
      default:
        return 'info';
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error" action={
          <Button color="inherit" size="small" onClick={loadDashboardData}>
            Réessayer
          </Button>
        }>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* En-tête */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Agent CRM - Relation Client Avancée
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Gestion complète de la relation client avec intelligence artificielle
        </Typography>
      </Box>

      {/* Onglets */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          aria-label="CRM tabs"
        >
          <Tab icon={<InsightsIcon />} label="Dashboard" />
          <Tab icon={<PeopleIcon />} label="Contacts" />
          <Tab icon={<TrendingUpIcon />} label="Opportunités" />
          <Tab icon={<CampaignIcon />} label="Campagnes" />
          <Tab icon={<SupportIcon />} label="Support" />
        </Tabs>
      </Paper>

      {/* Dashboard */}
      <TabPanel value={tabValue} index={0}>
        {dashboardData && (
          <Grid container spacing={3}>
            {/* Métriques principales */}
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      <PeopleIcon />
                    </Avatar>
                    <Typography variant="h6">Contacts</Typography>
                  </Box>
                  <Typography variant="h4" color="primary">
                    {dashboardData.contacts?.total || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {dashboardData.contacts?.prospects || 0} prospects • {dashboardData.contacts?.customers || 0} clients
                  </Typography>
                  <Box mt={2}>
                    <Typography variant="caption" display="block">
                      Score moyen: {Math.round(dashboardData.contacts?.average_score || 0)}
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={dashboardData.contacts?.average_score || 0} 
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                      <TrendingUpIcon />
                    </Avatar>
                    <Typography variant="h6">Opportunités</Typography>
                  </Box>
                  <Typography variant="h4" color="success.main">
                    {dashboardData.opportunities?.total || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {dashboardData.opportunities?.open || 0} ouvertes • {Math.round(dashboardData.opportunities?.win_rate || 0)}% taux de gain
                  </Typography>
                  <Typography variant="caption" display="block" mt={1}>
                    Pipeline: {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(dashboardData.opportunities?.weighted_value || 0)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                      <CampaignIcon />
                    </Avatar>
                    <Typography variant="h6">Campagnes</Typography>
                  </Box>
                  <Typography variant="h4" color="info.main">
                    {dashboardData.campaigns?.active || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {dashboardData.campaigns?.total_leads || 0} leads générés
                  </Typography>
                  <Typography variant="caption" display="block" mt={1}>
                    ROI moyen: {Math.round(dashboardData.campaigns?.average_roi || 0)}%
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                      <SupportIcon />
                    </Avatar>
                    <Typography variant="h6">Support</Typography>
                  </Box>
                  <Typography variant="h4" color="warning.main">
                    {dashboardData.support?.open_tickets || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    tickets ouverts
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <StarIcon fontSize="small" color="action" />
                    <Typography variant="caption" ml={0.5}>
                      Satisfaction: {Math.round(dashboardData.support?.average_satisfaction || 0)}/5
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Alertes et insights */}
            {insights.length > 0 && (
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Insights et Recommandations IA
                    </Typography>
                    <List>
                      {insights.slice(0, 5).map((insight, index) => (
                        <React.Fragment key={index}>
                          <ListItem>
                            <ListItemAvatar>
                              <Avatar sx={{ bgcolor: `${getInsightColor(insight.type)}.main` }}>
                                {getInsightIcon(insight.type)}
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={
                                <Box display="flex" alignItems="center" gap={1}>
                                  <Typography variant="subtitle2">
                                    {insight.title}
                                  </Typography>
                                  <Chip 
                                    label={insight.priority} 
                                    size="small" 
                                    color={insight.priority === 'high' ? 'error' : insight.priority === 'medium' ? 'warning' : 'default'}
                                  />
                                </Box>
                              }
                              secondary={
                                <Box>
                                  <Typography variant="body2" color="text.secondary">
                                    {insight.description}
                                  </Typography>
                                  <Typography variant="caption" color="primary" sx={{ fontWeight: 'medium' }}>
                                    💡 {insight.recommendation}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                          {index < insights.length - 1 && <Divider variant="inset" component="li" />}
                        </React.Fragment>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Activités récentes */}
            {dashboardData.recent_activities && dashboardData.recent_activities.length > 0 && (
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Activités Récentes
                    </Typography>
                    <List dense>
                      {dashboardData.recent_activities.slice(0, 5).map((activity, index) => (
                        <ListItem key={index}>
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                              {activity.type.includes('call') ? <PhoneIcon fontSize="small" /> :
                               activity.type.includes('email') ? <EmailIcon fontSize="small" /> :
                               <BusinessIcon fontSize="small" />}
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={activity.description}
                            secondary={`${activity.contact} • ${activity.user} • ${new Date(activity.date).toLocaleDateString('fr-FR')}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Alertes */}
            {dashboardData.alerts && (
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Alertes
                    </Typography>
                    <Grid container spacing={2}>
                      {dashboardData.alerts.overdue_opportunities > 0 && (
                        <Grid item xs={12}>
                          <Alert severity="warning" icon={<ScheduleIcon />}>
                            {dashboardData.alerts.overdue_opportunities} opportunité(s) en retard
                          </Alert>
                        </Grid>
                      )}
                      {dashboardData.alerts.high_value_prospects > 0 && (
                        <Grid item xs={12}>
                          <Alert severity="info" icon={<StarIcon />}>
                            {dashboardData.alerts.high_value_prospects} prospect(s) à fort potentiel
                          </Alert>
                        </Grid>
                      )}
                      {dashboardData.alerts.unassigned_leads > 0 && (
                        <Grid item xs={12}>
                          <Alert severity="warning">
                            {dashboardData.alerts.unassigned_leads} lead(s) non assigné(s)
                          </Alert>
                        </Grid>
                      )}
                      {dashboardData.alerts.overdue_tickets > 0 && (
                        <Grid item xs={12}>
                          <Alert severity="error">
                            {dashboardData.alerts.overdue_tickets} ticket(s) en retard
                          </Alert>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        )}
      </TabPanel>

      {/* Contacts */}
      <TabPanel value={tabValue} index={1}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5">Gestion des Contacts</Typography>
          <Button variant="contained" startIcon={<AddIcon />}>
            Nouveau Contact
          </Button>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Interface de gestion des contacts en cours de développement...
        </Typography>
      </TabPanel>

      {/* Opportunités */}
      <TabPanel value={tabValue} index={2}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5">Pipeline des Opportunités</Typography>
          <Button variant="contained" startIcon={<AddIcon />}>
            Nouvelle Opportunité
          </Button>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Interface de gestion des opportunités en cours de développement...
        </Typography>
      </TabPanel>

      {/* Campagnes */}
      <TabPanel value={tabValue} index={3}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5">Campagnes Marketing</Typography>
          <Button variant="contained" startIcon={<AddIcon />}>
            Nouvelle Campagne
          </Button>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Interface de gestion des campagnes en cours de développement...
        </Typography>
      </TabPanel>

      {/* Support */}
      <TabPanel value={tabValue} index={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5">Support Client</Typography>
          <Button variant="contained" startIcon={<AddIcon />}>
            Nouveau Ticket
          </Button>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Interface de gestion du support en cours de développement...
        </Typography>
      </TabPanel>

      {/* Bouton de rafraîchissement */}
      <Box position="fixed" bottom={16} right={16}>
        <IconButton
          color="primary"
          onClick={loadDashboardData}
          sx={{
            bgcolor: 'background.paper',
            boxShadow: 2,
            '&:hover': {
              bgcolor: 'background.paper',
              boxShadow: 4,
            },
          }}
        >
          <RefreshIcon />
        </IconButton>
      </Box>
    </Container>
  );
};

export default CRMPage;
