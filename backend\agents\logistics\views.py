"""
Vues pour l'agent Logistics
"""
import logging
from django.utils import timezone
from django.db import models
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema

from core.permissions import LogisticsReadPermission, LogisticsWritePermission
from .services import LogisticsService
from .models import (
    Carrier, CarrierRate, Shipment, ShipmentTracking, Route, DeliveryAttempt
)
from .serializers import (
    CarrierSerializer, CarrierRateSerializer, ShipmentSerializer, ShipmentCreateSerializer,
    ShipmentTrackingSerializer, ShipmentTrackingUpdateSerializer, RouteSerializer,
    RouteCreateSerializer, DeliveryAttemptSerializer, LogisticsDashboardSerializer,
    CarrierPerformanceSerializer, LogisticsInsightSerializer
)

logger = logging.getLogger('agents.logistics')


@extend_schema(
    summary="Statut de l'agent Logistics",
    description="Retourne le statut de l'agent Logistics"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def logistics_status(request):
    """Retourne le statut de l'agent Logistics"""
    try:
        logistics_service = LogisticsService(request.user.tenant)

        return Response({
            'status': 'active',
            'agent': 'logistics',
            'message': 'Agent Logistics opérationnel',
            'capabilities': [
                'carrier_management',
                'shipment_tracking',
                'route_optimization',
                'delivery_management',
                'cost_optimization',
                'performance_analytics',
                'real_time_tracking'
            ]
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut Logistics: {str(e)}")
        return Response({
            'status': 'error',
            'agent': 'logistics',
            'message': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Dashboard de l'agent Logistics",
    description="Retourne les données complètes du dashboard Logistics"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, LogisticsReadPermission])
def logistics_dashboard(request):
    """Retourne les données du dashboard Logistics"""
    try:
        logistics_service = LogisticsService(request.user.tenant)
        dashboard_data = logistics_service.get_logistics_dashboard()

        return Response(dashboard_data)
    except Exception as e:
        logger.error(f"Erreur lors de la génération du dashboard Logistics: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Gestion des transporteurs
class CarrierListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des transporteurs"""
    serializer_class = CarrierSerializer
    permission_classes = [IsAuthenticated, LogisticsReadPermission]

    def get_queryset(self):
        queryset = Carrier.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        carrier_type = self.request.query_params.get('type')
        if carrier_type:
            queryset = queryset.filter(carrier_type=carrier_type)

        service_level = self.request.query_params.get('service_level')
        if service_level:
            queryset = queryset.filter(service_level=service_level)

        is_active = self.request.query_params.get('active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        is_preferred = self.request.query_params.get('preferred')
        if is_preferred is not None:
            queryset = queryset.filter(is_preferred=is_preferred.lower() == 'true')

        # Recherche par nom ou code
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(carrier_code__icontains=search)
            )

        return queryset.order_by('name')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant)


class CarrierDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un transporteur"""
    serializer_class = CarrierSerializer
    permission_classes = [IsAuthenticated, LogisticsWritePermission]

    def get_queryset(self):
        return Carrier.objects.filter(tenant=self.request.user.tenant)


# Gestion des tarifs transporteurs
class CarrierRateListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des tarifs transporteurs"""
    serializer_class = CarrierRateSerializer
    permission_classes = [IsAuthenticated, LogisticsReadPermission]

    def get_queryset(self):
        queryset = CarrierRate.objects.filter(carrier__tenant=self.request.user.tenant)

        # Filtres
        carrier_id = self.request.query_params.get('carrier')
        if carrier_id:
            queryset = queryset.filter(carrier_id=carrier_id)

        rate_type = self.request.query_params.get('rate_type')
        if rate_type:
            queryset = queryset.filter(rate_type=rate_type)

        service_level = self.request.query_params.get('service_level')
        if service_level:
            queryset = queryset.filter(service_level=service_level)

        is_active = self.request.query_params.get('active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('carrier__name', 'rate_name')


class CarrierRateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un tarif"""
    serializer_class = CarrierRateSerializer
    permission_classes = [IsAuthenticated, LogisticsWritePermission]

    def get_queryset(self):
        return CarrierRate.objects.filter(carrier__tenant=self.request.user.tenant)


# Gestion des expéditions
class ShipmentListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des expéditions"""
    permission_classes = [IsAuthenticated, LogisticsReadPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ShipmentCreateSerializer
        return ShipmentSerializer

    def get_queryset(self):
        queryset = Shipment.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        shipment_type = self.request.query_params.get('type')
        if shipment_type:
            queryset = queryset.filter(shipment_type=shipment_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        carrier_id = self.request.query_params.get('carrier')
        if carrier_id:
            queryset = queryset.filter(carrier_id=carrier_id)

        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        # Filtres par date
        date_from = self.request.query_params.get('date_from')
        if date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)

        date_to = self.request.query_params.get('date_to')
        if date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)

        # Recherche
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(shipment_number__icontains=search) |
                models.Q(tracking_number__icontains=search) |
                models.Q(recipient_name__icontains=search) |
                models.Q(customer_reference__icontains=search)
            )

        return queryset.order_by('-created_at')


class ShipmentCreateView(APIView):
    """Vue pour créer des expéditions avec logique métier"""
    permission_classes = [IsAuthenticated, LogisticsWritePermission]

    @extend_schema(
        summary="Créer une expédition",
        description="Crée une nouvelle expédition avec estimation automatique des coûts",
        request=ShipmentCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle expédition"""
        try:
            serializer = ShipmentCreateSerializer(data=request.data)
            if serializer.is_valid():
                logistics_service = LogisticsService(request.user.tenant)

                result = logistics_service.create_shipment(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'expédition: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ShipmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une expédition"""
    serializer_class = ShipmentSerializer
    permission_classes = [IsAuthenticated, LogisticsWritePermission]

    def get_queryset(self):
        return Shipment.objects.filter(tenant=self.request.user.tenant)


# Gestion du suivi des expéditions
class ShipmentTrackingUpdateView(APIView):
    """Vue pour mettre à jour le suivi d'une expédition"""
    permission_classes = [IsAuthenticated, LogisticsWritePermission]

    @extend_schema(
        summary="Mettre à jour le suivi",
        description="Ajoute un événement de suivi à une expédition",
        request=ShipmentTrackingUpdateSerializer
    )
    def post(self, request, shipment_id):
        """Met à jour le suivi d'une expédition"""
        try:
            serializer = ShipmentTrackingUpdateSerializer(data=request.data)
            if serializer.is_valid():
                logistics_service = LogisticsService(request.user.tenant)

                result = logistics_service.update_shipment_tracking(
                    shipment_id,
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour du suivi: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Gestion des itinéraires
class RouteListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des itinéraires"""
    permission_classes = [IsAuthenticated, LogisticsReadPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return RouteCreateSerializer
        return RouteSerializer

    def get_queryset(self):
        queryset = Route.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        carrier_id = self.request.query_params.get('carrier')
        if carrier_id:
            queryset = queryset.filter(carrier_id=carrier_id)

        planned_date = self.request.query_params.get('planned_date')
        if planned_date:
            queryset = queryset.filter(planned_date=planned_date)

        # Recherche
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(route_number__icontains=search) |
                models.Q(route_name__icontains=search) |
                models.Q(driver_name__icontains=search)
            )

        return queryset.order_by('-planned_date', 'route_number')


class RouteCreateView(APIView):
    """Vue pour créer des itinéraires avec logique métier"""
    permission_classes = [IsAuthenticated, LogisticsWritePermission]

    @extend_schema(
        summary="Créer un itinéraire",
        description="Crée un nouvel itinéraire avec optimisation optionnelle",
        request=RouteCreateSerializer
    )
    def post(self, request):
        """Crée un nouvel itinéraire"""
        try:
            serializer = RouteCreateSerializer(data=request.data)
            if serializer.is_valid():
                logistics_service = LogisticsService(request.user.tenant)

                result = logistics_service.create_route(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'itinéraire: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RouteDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un itinéraire"""
    serializer_class = RouteSerializer
    permission_classes = [IsAuthenticated, LogisticsWritePermission]

    def get_queryset(self):
        return Route.objects.filter(tenant=self.request.user.tenant)


# Analyses et rapports
@extend_schema(
    summary="Analyse des performances transporteurs",
    description="Retourne l'analyse détaillée des performances des transporteurs"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, LogisticsReadPermission])
def carrier_performance_analysis(request):
    """Analyse des performances des transporteurs"""
    try:
        logistics_service = LogisticsService(request.user.tenant)
        performance_data = logistics_service.analyze_carrier_performance()

        return Response(performance_data)
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse des performances: {str(e)}")
        return Response({
            'error': f'Erreur lors de l\'analyse: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Insights logistiques IA",
    description="Génère des insights et recommandations basés sur l'IA"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, LogisticsReadPermission])
def logistics_insights(request):
    """Génère des insights logistiques avec IA"""
    try:
        logistics_service = LogisticsService(request.user.tenant)
        insights = logistics_service.generate_logistics_insights()

        return Response({'insights': insights})
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'insights: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération d\'insights: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Vues utilitaires
@extend_schema(
    summary="Expéditions en retard",
    description="Retourne la liste des expéditions en retard"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, LogisticsReadPermission])
def delayed_shipments(request):
    """Retourne les expéditions en retard"""
    try:
        logistics_service = LogisticsService(request.user.tenant)
        delayed = logistics_service._get_delayed_shipments()

        return Response({'delayed_shipments': delayed})
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des expéditions en retard: {str(e)}")
        return Response({
            'error': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Statistiques de livraison",
    description="Retourne les statistiques de performance de livraison"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, LogisticsReadPermission])
def delivery_performance(request):
    """Retourne les performances de livraison"""
    try:
        logistics_service = LogisticsService(request.user.tenant)
        performance = logistics_service._calculate_delivery_performance()

        return Response(performance)
    except Exception as e:
        logger.error(f"Erreur lors du calcul des performances: {str(e)}")
        return Response({
            'error': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Coûts de transport",
    description="Retourne l'analyse des coûts de transport"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, LogisticsReadPermission])
def transport_costs(request):
    """Retourne l'analyse des coûts de transport"""
    try:
        logistics_service = LogisticsService(request.user.tenant)
        costs = logistics_service._calculate_transport_costs()

        return Response(costs)
    except Exception as e:
        logger.error(f"Erreur lors du calcul des coûts: {str(e)}")
        return Response({
            'error': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
