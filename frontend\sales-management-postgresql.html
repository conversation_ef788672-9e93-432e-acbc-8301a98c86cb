<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Sales - Ventes | ERP HUB PostgreSQL</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #3b82f6 30%, #2563eb 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #fde68a;
            color: #92400e;
        }
        
        .alert-info {
            background: #dbeafe;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-connected {
            background: #10b981;
        }
        
        .status-disconnected {
            background: #ef4444;
        }
        
        .nav-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0.5rem;
        }

        .nav-tab {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .nav-tab:hover {
            color: #3b82f6;
            background: #eff6ff;
        }

        .nav-tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
            background: #eff6ff;
        }

        .nav-tab .material-icons {
            font-size: 1.2rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">💼 Agent Sales - ERP HUB</div>
        <div class="nav-buttons">
            <div class="connection-status">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Connexion...</span>
            </div>
            <button class="btn btn-primary" onclick="refreshData()">
                <span class="material-icons" style="font-size: 1rem;">refresh</span>
                Actualiser
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion des Ventes</h1>
            <p class="page-subtitle">Clients, opportunités et commandes - Connecté à PostgreSQL</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalCustomers">0</div>
                <div class="stat-label">Total Clients</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalOpportunities">0</div>
                <div class="stat-label">Opportunités</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalOrders">0</div>
                <div class="stat-label">Commandes</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalRevenue">0€</div>
                <div class="stat-label">Chiffre d'Affaires</div>
            </div>
        </div>

        <!-- Alertes -->
        <div id="alertContainer"></div>

        <!-- Navigation par onglets -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('customers')">
                <span class="material-icons">people</span>
                Clients
            </button>
            <button class="nav-tab" onclick="showTab('opportunities')">
                <span class="material-icons">trending_up</span>
                Opportunités
            </button>
            <button class="nav-tab" onclick="showTab('orders')">
                <span class="material-icons">shopping_cart</span>
                Commandes
            </button>
        </nav>

        <!-- Onglet Clients -->
        <div id="customers" class="tab-content active">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">👥 Clients</h2>
                    <button class="btn btn-primary" onclick="loadCustomers()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Code Client</th>
                                    <th>Entreprise</th>
                                    <th>Contact</th>
                                    <th>Email</th>
                                    <th>Ville</th>
                                    <th>Type</th>
                                    <th>Limite Crédit</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                <tr>
                                    <td colspan="8" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des clients...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Opportunités -->
        <div id="opportunities" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">📈 Opportunités</h2>
                    <button class="btn btn-primary" onclick="loadOpportunities()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Titre</th>
                                    <th>Valeur</th>
                                    <th>Probabilité</th>
                                    <th>Étape</th>
                                    <th>Date Prévue</th>
                                    <th>Source</th>
                                </tr>
                            </thead>
                            <tbody id="opportunitiesTableBody">
                                <tr>
                                    <td colspan="7" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des opportunités...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Commandes -->
        <div id="orders" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">🛒 Commandes</h2>
                    <button class="btn btn-primary" onclick="loadOrders()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>N° Commande</th>
                                    <th>Client</th>
                                    <th>Date Commande</th>
                                    <th>Date Livraison</th>
                                    <th>Montant Total</th>
                                    <th>Statut</th>
                                    <th>Paiement</th>
                                </tr>
                            </thead>
                            <tbody id="ordersTableBody">
                                <tr>
                                    <td colspan="7" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des commandes...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let customers = [];
        let opportunities = [];
        let orders = [];
        let currentTab = 'customers';

        // Configuration API
        const API_BASE_URL = 'http://localhost:5000/api';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
        });

        // Charger toutes les données
        async function loadAllData() {
            await checkConnection();
            await loadCustomers();
            await loadOpportunities();
            await loadOrders();
            updateStats();
        }

        // Vérifier la connexion à l'API
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    updateConnectionStatus(true, data.database_status === 'connected');
                } else {
                    updateConnectionStatus(false, false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateConnectionStatus(false, false);
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(apiConnected, dbConnected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (apiConnected && dbConnected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'PostgreSQL connecté';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Déconnecté';
            }
        }

        // Charger les clients depuis PostgreSQL
        async function loadCustomers() {
            try {
                showAlert('Chargement des clients depuis PostgreSQL...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/customers`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        customers = data.data || [];
                        renderCustomersTable();
                        showAlert(`${customers.length} clients chargés depuis PostgreSQL`, 'success');
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement clients:', error);
                showAlert('Erreur lors du chargement des clients: ' + error.message, 'error');
                customers = [];
                renderCustomersTable();
            }
        }

        // Charger les opportunités depuis PostgreSQL
        async function loadOpportunities() {
            try {
                const response = await fetch(`${API_BASE_URL}/opportunities`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        opportunities = data.data || [];
                        renderOpportunitiesTable();
                        console.log(`${opportunities.length} opportunités chargées depuis PostgreSQL`);
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement opportunités:', error);
                showAlert('Erreur lors du chargement des opportunités: ' + error.message, 'error');
                opportunities = [];
                renderOpportunitiesTable();
            }
        }

        // Charger les commandes depuis PostgreSQL
        async function loadOrders() {
            try {
                const response = await fetch(`${API_BASE_URL}/orders`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        orders = data.data || [];
                        renderOrdersTable();
                        console.log(`${orders.length} commandes chargées depuis PostgreSQL`);
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement commandes:', error);
                showAlert('Erreur lors du chargement des commandes: ' + error.message, 'error');
                orders = [];
                renderOrdersTable();
            }
        }

        // Afficher le tableau des clients
        function renderCustomersTable() {
            const tbody = document.getElementById('customersTableBody');
            
            if (customers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun client trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = customers.map(customer => `
                <tr>
                    <td><strong>${customer.customerCode}</strong></td>
                    <td>${customer.companyName}</td>
                    <td>${customer.contactPerson || 'N/A'}</td>
                    <td>${customer.email || 'N/A'}</td>
                    <td>${customer.city || 'N/A'}</td>
                    <td>${getCustomerTypeBadge(customer.customerType)}</td>
                    <td>${customer.creditLimit ? customer.creditLimit.toLocaleString() + '€' : 'N/A'}</td>
                    <td>${getStatusBadge(customer.status)}</td>
                </tr>
            `).join('');
        }

        // Afficher le tableau des opportunités
        function renderOpportunitiesTable() {
            const tbody = document.getElementById('opportunitiesTableBody');
            
            if (opportunities.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune opportunité trouvée
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = opportunities.map(opp => `
                <tr>
                    <td><strong>${opp.customerName}</strong></td>
                    <td>${opp.title}</td>
                    <td>${opp.value ? opp.value.toLocaleString() + '€' : 'N/A'}</td>
                    <td>${opp.probability}%</td>
                    <td>${getStageBadge(opp.stage)}</td>
                    <td>${formatDate(opp.expectedCloseDate)}</td>
                    <td>${opp.source || 'N/A'}</td>
                </tr>
            `).join('');
        }

        // Afficher le tableau des commandes
        function renderOrdersTable() {
            const tbody = document.getElementById('ordersTableBody');
            
            if (orders.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune commande trouvée
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = orders.map(order => `
                <tr>
                    <td><strong>${order.orderNumber}</strong></td>
                    <td>${order.customerName}</td>
                    <td>${formatDate(order.orderDate)}</td>
                    <td>${formatDate(order.deliveryDate)}</td>
                    <td>${order.totalAmount ? order.totalAmount.toLocaleString() + '€' : 'N/A'}</td>
                    <td>${getOrderStatusBadge(order.status)}</td>
                    <td>${getPaymentStatusBadge(order.paymentStatus)}</td>
                </tr>
            `).join('');
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const totalCustomers = customers.length;
            const totalOpportunities = opportunities.length;
            const totalOrders = orders.length;
            const totalRevenue = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);

            document.getElementById('totalCustomers').textContent = totalCustomers;
            document.getElementById('totalOpportunities').textContent = totalOpportunities;
            document.getElementById('totalOrders').textContent = totalOrders;
            document.getElementById('totalRevenue').textContent = totalRevenue.toLocaleString() + '€';
        }

        // Gestion des onglets
        function showTab(tabName) {
            // Masquer tous les contenus d'onglets
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Désactiver tous les onglets
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Afficher le contenu de l'onglet sélectionné
            document.getElementById(tabName).classList.add('active');
            
            // Activer l'onglet sélectionné
            event.target.classList.add('active');
            
            currentTab = tabName;
        }

        // Fonctions utilitaires
        function getStatusBadge(status) {
            const badges = {
                'active': '<span class="badge badge-success">Actif</span>',
                'inactive': '<span class="badge badge-warning">Inactif</span>',
                'blocked': '<span class="badge badge-danger">Bloqué</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getCustomerTypeBadge(type) {
            const badges = {
                'prospect': '<span class="badge badge-info">Prospect</span>',
                'client': '<span class="badge badge-success">Client</span>',
                'partner': '<span class="badge badge-warning">Partenaire</span>'
            };
            return badges[type] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getStageBadge(stage) {
            const badges = {
                'prospecting': '<span class="badge badge-info">Prospection</span>',
                'qualification': '<span class="badge badge-warning">Qualification</span>',
                'proposal': '<span class="badge badge-warning">Proposition</span>',
                'negotiation': '<span class="badge badge-warning">Négociation</span>',
                'closed_won': '<span class="badge badge-success">Gagné</span>',
                'closed_lost': '<span class="badge badge-danger">Perdu</span>'
            };
            return badges[stage] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getOrderStatusBadge(status) {
            const badges = {
                'pending': '<span class="badge badge-warning">En attente</span>',
                'confirmed': '<span class="badge badge-info">Confirmé</span>',
                'shipped': '<span class="badge badge-warning">Expédié</span>',
                'delivered': '<span class="badge badge-success">Livré</span>',
                'cancelled': '<span class="badge badge-danger">Annulé</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getPaymentStatusBadge(status) {
            const badges = {
                'pending': '<span class="badge badge-warning">En attente</span>',
                'partial': '<span class="badge badge-warning">Partiel</span>',
                'paid': '<span class="badge badge-success">Payé</span>',
                'overdue': '<span class="badge badge-danger">En retard</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            container.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
            
            // Masquer l'alerte après 5 secondes
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function refreshData() {
            loadAllData();
        }
    </script>
</body>
</html>
