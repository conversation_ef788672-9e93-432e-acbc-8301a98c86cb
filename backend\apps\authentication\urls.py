from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

urlpatterns = [
    # Authentification JWT
    path('login/', views.CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('logout/', views.logout_user, name='logout'),
    
    # Gestion des utilisateurs
    path('register/', views.register_user, name='register'),
    path('profile/', views.user_profile, name='user_profile'),
    path('profile/update/', views.update_profile, name='update_profile'),
    
    # Permissions et statut
    path('permissions/', views.user_permissions, name='user_permissions'),
    path('status/', views.auth_status, name='auth_status'),
    
    # Utilitaires
    path('refresh-token/', views.refresh_token, name='refresh_token'),
]
