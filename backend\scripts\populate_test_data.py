#!/usr/bin/env python3
"""
Script pour peupler la base de données avec des données de test
pour l'ERP HUB multi-agents
"""

import os
import sys
import django
from datetime import datetime, timedelta
from decimal import Decimal
import random

# Configuration Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from django.contrib.auth.models import User
from apps.agents.models import *

def create_users():
    """Créer des utilisateurs de test"""
    print("🔧 Création des utilisateurs...")
    
    # Super utilisateur
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='Admin',
            last_name='ERP HUB'
        )
        print("✅ Super utilisateur créé: admin/admin123")
    
    # Utilisateurs de test
    test_users = [
        {'username': 'manager', 'email': '<EMAIL>', 'first_name': 'Jean', 'last_name': 'Manager'},
        {'username': 'hr_user', 'email': '<EMAIL>', 'first_name': 'Marie', 'last_name': 'RH'},
        {'username': 'sales_user', 'email': '<EMAIL>', 'first_name': 'Pierre', 'last_name': 'Commercial'},
        {'username': 'finance_user', 'email': '<EMAIL>', 'first_name': 'Sophie', 'last_name': 'Finance'},
    ]
    
    for user_data in test_users:
        if not User.objects.filter(username=user_data['username']).exists():
            User.objects.create_user(
                password='test123',
                **user_data
            )
            print(f"✅ Utilisateur créé: {user_data['username']}/test123")

def create_agents():
    """Créer les agents avec des données initiales"""
    print("🤖 Création des agents...")
    
    # Agent Manager
    manager, created = ManagerAgent.objects.get_or_create(
        name="Agent Manager Principal",
        defaults={
            'total_users': 10,
            'active_sessions': 5,
            'system_health': {
                'cpu_usage': 45.2,
                'memory_usage': 67.8,
                'disk_usage': 23.1,
                'network_status': 'optimal'
            },
            'performance_metrics': {
                'response_time': 120,
                'throughput': 1500,
                'error_rate': 0.02
            }
        }
    )
    if created:
        print("✅ Agent Manager créé")
    
    # Agent HR
    hr_agent, created = HRAgent.objects.get_or_create(
        name="Agent RH",
        defaults={
            'total_employees': 50,
            'departments': ['IT', 'Sales', 'Finance', 'HR', 'Operations'],
            'pending_requests': 3
        }
    )
    if created:
        print("✅ Agent HR créé")
    
    # Agent Sales
    sales_agent, created = SalesAgent.objects.get_or_create(
        name="Agent Commercial",
        defaults={
            'total_leads': 125,
            'conversion_rate': 15.5,
            'monthly_revenue': Decimal('125000.00')
        }
    )
    if created:
        print("✅ Agent Sales créé")
    
    # Autres agents...
    agents_data = [
        (PurchaseAgent, "Agent Achats", {'total_orders': 45, 'pending_approvals': 8, 'monthly_spending': Decimal('85000.00')}),
        (LogisticsAgent, "Agent Logistique", {'active_shipments': 23, 'delivery_rate': 95.5, 'average_delivery_time': 2.3}),
        (StockAgent, "Agent Stock", {'total_products': 1250, 'low_stock_alerts': 15, 'total_value': Decimal('450000.00')}),
        (AccountingAgent, "Agent Comptabilité", {'total_transactions': 2340, 'monthly_revenue': Decimal('125000.00'), 'monthly_expenses': Decimal('85000.00')}),
        (FinanceAgent, "Agent Finance", {'cash_flow': Decimal('40000.00'), 'profit_margin': 32.1, 'budget_variance': -2.5}),
        (CRMAgent, "Agent CRM", {'total_customers': 340, 'satisfaction_score': 4.2, 'retention_rate': 87.5}),
        (BIAgent, "Agent BI", {'reports_generated': 45, 'data_sources': 8, 'last_analysis': datetime.now()})
    ]
    
    for agent_class, name, defaults in agents_data:
        agent, created = agent_class.objects.get_or_create(
            name=name,
            defaults=defaults
        )
        if created:
            print(f"✅ {name} créé")

def create_employees():
    """Créer des employés de test"""
    print("👥 Création des employés...")
    
    departments = ['IT', 'Sales', 'Finance', 'HR', 'Operations']
    positions = ['Manager', 'Senior', 'Junior', 'Intern', 'Lead']
    
    for i in range(20):
        user = User.objects.create_user(
            username=f'employee_{i+1}',
            email=f'employee{i+1}@erphub.com',
            first_name=f'Employé',
            last_name=f'{i+1}',
            password='emp123'
        )
        
        Employee.objects.create(
            user=user,
            employee_id=f'EMP{str(i+1).zfill(3)}',
            department=random.choice(departments),
            position=random.choice(positions),
            hire_date=datetime.now().date() - timedelta(days=random.randint(30, 1000)),
            salary=Decimal(random.randint(30000, 80000)),
            status='active'
        )
    
    print("✅ 20 employés créés")

def create_leads():
    """Créer des leads de test"""
    print("💼 Création des leads...")
    
    companies = ['TechCorp', 'InnovateLtd', 'FutureSoft', 'DataSystems', 'CloudTech']
    statuses = ['new', 'contacted', 'qualified', 'proposal', 'won', 'lost']
    
    for i in range(30):
        Lead.objects.create(
            name=f'Lead {i+1}',
            email=f'lead{i+1}@{random.choice(companies).lower()}.com',
            phone=f'+33 1 {random.randint(10,99)} {random.randint(10,99)} {random.randint(10,99)} {random.randint(10,99)}',
            company=random.choice(companies),
            status=random.choice(statuses),
            value=Decimal(random.randint(5000, 50000))
        )
    
    print("✅ 30 leads créés")

def create_products():
    """Créer des produits de test"""
    print("📦 Création des produits...")
    
    categories = ['Electronics', 'Software', 'Hardware', 'Services', 'Accessories']
    
    for i in range(50):
        Product.objects.create(
            sku=f'SKU{str(i+1).zfill(4)}',
            name=f'Produit {i+1}',
            category=random.choice(categories),
            quantity=random.randint(0, 100),
            min_quantity=random.randint(5, 20),
            unit_price=Decimal(random.randint(10, 500))
        )
    
    print("✅ 50 produits créés")

def create_transactions():
    """Créer des transactions de test"""
    print("💰 Création des transactions...")
    
    transaction_types = ['income', 'expense', 'transfer']
    
    for i in range(100):
        Transaction.objects.create(
            reference=f'TXN{str(i+1).zfill(6)}',
            type=random.choice(transaction_types),
            amount=Decimal(random.randint(100, 10000)),
            description=f'Transaction de test {i+1}',
            date=datetime.now() - timedelta(days=random.randint(0, 90))
        )
    
    print("✅ 100 transactions créées")

def create_customers():
    """Créer des clients de test"""
    print("🤝 Création des clients...")
    
    companies = ['TechCorp', 'InnovateLtd', 'FutureSoft', 'DataSystems', 'CloudTech']
    statuses = ['prospect', 'active', 'inactive', 'churned']
    
    for i in range(40):
        Customer.objects.create(
            name=f'Client {i+1}',
            email=f'client{i+1}@{random.choice(companies).lower()}.com',
            phone=f'+33 1 {random.randint(10,99)} {random.randint(10,99)} {random.randint(10,99)} {random.randint(10,99)}',
            company=random.choice(companies),
            status=random.choice(statuses),
            lifetime_value=Decimal(random.randint(1000, 100000))
        )
    
    print("✅ 40 clients créés")

def create_notifications():
    """Créer des notifications de test"""
    print("🔔 Création des notifications...")
    
    agents = ['manager', 'hr', 'sales', 'purchase', 'logistics', 'stock', 'accounting', 'finance', 'crm', 'bi']
    priorities = ['low', 'medium', 'high', 'critical']
    
    messages = [
        "Nouveau lead assigné",
        "Stock faible détecté",
        "Commande en attente d'approbation",
        "Rapport mensuel généré",
        "Livraison en retard",
        "Nouveau client enregistré",
        "Facture en attente de paiement"
    ]
    
    for i in range(20):
        AgentNotification.objects.create(
            from_agent=random.choice(agents),
            to_agent=random.choice(agents),
            message=random.choice(messages),
            data={'test': True, 'id': i+1},
            priority=random.choice(priorities),
            read=random.choice([True, False])
        )
    
    print("✅ 20 notifications créées")

def main():
    """Fonction principale"""
    print("🚀 Démarrage du peuplement de la base de données ERP HUB")
    print("=" * 60)
    
    try:
        create_users()
        create_agents()
        create_employees()
        create_leads()
        create_products()
        create_transactions()
        create_customers()
        create_notifications()
        
        print("=" * 60)
        print("🎉 Base de données peuplée avec succès !")
        print("\n📊 Résumé des données créées :")
        print(f"   👤 Utilisateurs: {User.objects.count()}")
        print(f"   👥 Employés: {Employee.objects.count()}")
        print(f"   💼 Leads: {Lead.objects.count()}")
        print(f"   📦 Produits: {Product.objects.count()}")
        print(f"   💰 Transactions: {Transaction.objects.count()}")
        print(f"   🤝 Clients: {Customer.objects.count()}")
        print(f"   🔔 Notifications: {AgentNotification.objects.count()}")
        
        print("\n🔑 Comptes de connexion :")
        print("   Admin: admin / admin123")
        print("   Manager: manager / test123")
        print("   HR: hr_user / test123")
        print("   Sales: sales_user / test123")
        print("   Finance: finance_user / test123")
        
    except Exception as e:
        print(f"❌ Erreur lors du peuplement: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
