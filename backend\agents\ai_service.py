"""
Service d'intégration IA pour les agents ERP HUB
Gère les interactions avec les modèles de langage et l'IA
"""
import logging
import json
import openai
from typing import Dict, List, Optional, Any
from django.conf import settings
from dataclasses import dataclass

logger = logging.getLogger('agents.ai')


@dataclass
class AIResponse:
    """Structure de réponse de l'IA"""
    success: bool
    content: str
    reasoning: str = ""
    confidence: float = 0.0
    tokens_used: int = 0
    error: str = ""


class AIService:
    """
    Service principal pour l'intégration IA
    """
    
    def __init__(self):
        self.api_key = getattr(settings, 'OPENAI_API_KEY', None)
        if self.api_key:
            openai.api_key = self.api_key
        else:
            logger.warning("Clé API OpenAI non configurée")
    
    def is_available(self) -> bool:
        """Vérifie si le service IA est disponible"""
        return bool(self.api_key)
    
    def generate_response(self, prompt: str, agent_type: str = "general", 
                         temperature: float = 0.7, max_tokens: int = 1000) -> AIResponse:
        """
        Génère une réponse IA basée sur un prompt
        """
        if not self.is_available():
            return AIResponse(
                success=False,
                content="",
                error="Service IA non disponible - Clé API manquante"
            )
        
        try:
            # Contexte spécifique à l'agent
            system_prompt = self._get_agent_system_prompt(agent_type)
            
            # Appel à l'API OpenAI
            response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            content = response.choices[0].message.content
            tokens_used = response.usage.total_tokens
            
            return AIResponse(
                success=True,
                content=content,
                tokens_used=tokens_used,
                confidence=0.8  # Score de confiance par défaut
            )
            
        except Exception as e:
            logger.error(f"Erreur lors de l'appel IA: {str(e)}")
            return AIResponse(
                success=False,
                content="",
                error=str(e)
            )
    
    def _get_agent_system_prompt(self, agent_type: str) -> str:
        """Retourne le prompt système spécifique à chaque type d'agent"""
        
        base_prompt = """Tu es un agent intelligent dans un système ERP modulaire. 
        Tu dois répondre de manière professionnelle, précise et orientée business.
        Tes réponses doivent être structurées et actionables."""
        
        agent_prompts = {
            "manager": f"""{base_prompt}
            Tu es l'Agent Manager, responsable de l'orchestration de tous les autres agents.
            Tes responsabilités incluent:
            - Coordination des tâches entre agents
            - Optimisation des workflows
            - Prise de décisions stratégiques
            - Résolution de conflits entre agents
            - Monitoring des performances système""",
            
            "hr": f"""{base_prompt}
            Tu es l'Agent RH, spécialisé dans la gestion des ressources humaines.
            Tes domaines d'expertise incluent:
            - Gestion des employés et recrutement
            - Formation et développement
            - Évaluation des performances
            - Gestion des congés et absences
            - Conformité RH et réglementations""",
            
            "sales": f"""{base_prompt}
            Tu es l'Agent Ventes, expert en processus commerciaux.
            Tes responsabilités incluent:
            - Gestion des opportunités de vente
            - Suivi des prospects et clients
            - Création et gestion des devis
            - Analyse des performances commerciales
            - Stratégies de vente et pricing""",
            
            "purchase": f"""{base_prompt}
            Tu es l'Agent Achats, spécialisé dans la gestion des approvisionnements.
            Tes domaines incluent:
            - Gestion des fournisseurs
            - Négociation et contrats
            - Optimisation des coûts d'achat
            - Gestion des commandes fournisseurs
            - Évaluation et sélection des fournisseurs""",
            
            "logistics": f"""{base_prompt}
            Tu es l'Agent Logistique, expert en chaîne d'approvisionnement.
            Tes responsabilités incluent:
            - Planification des livraisons
            - Optimisation des routes de transport
            - Gestion des entrepôts
            - Coordination avec les transporteurs
            - Suivi des expéditions""",
            
            "stock": f"""{base_prompt}
            Tu es l'Agent Stock, spécialisé dans la gestion des inventaires.
            Tes domaines incluent:
            - Gestion des niveaux de stock
            - Optimisation des réapprovisionnements
            - Prévisions de demande
            - Gestion des mouvements de stock
            - Analyse ABC et rotation des stocks""",
            
            "accounting": f"""{base_prompt}
            Tu es l'Agent Comptabilité, expert en comptabilité générale.
            Tes responsabilités incluent:
            - Saisie et validation des écritures comptables
            - Rapprochements bancaires
            - Gestion des comptes clients et fournisseurs
            - Préparation des états financiers
            - Conformité comptable et fiscale""",
            
            "finance": f"""{base_prompt}
            Tu es l'Agent Finance, spécialisé en gestion financière.
            Tes domaines incluent:
            - Analyse financière et budgétaire
            - Gestion de trésorerie
            - Planification financière
            - Évaluation des investissements
            - Contrôle de gestion et reporting""",
            
            "crm": f"""{base_prompt}
            Tu es l'Agent CRM, expert en relation client.
            Tes responsabilités incluent:
            - Gestion de la base clients
            - Suivi des interactions clients
            - Campagnes marketing et communication
            - Analyse de satisfaction client
            - Fidélisation et rétention client""",
            
            "bi": f"""{base_prompt}
            Tu es l'Agent BI, spécialisé en Business Intelligence.
            Tes domaines incluent:
            - Analyse de données et reporting
            - Création de tableaux de bord
            - Identification de tendances et insights
            - Prévisions et modélisation
            - Support à la prise de décision"""
        }
        
        return agent_prompts.get(agent_type, base_prompt)
    
    def analyze_task_requirements(self, task_description: str) -> Dict[str, Any]:
        """
        Analyse les exigences d'une tâche et recommande l'agent approprié
        """
        prompt = f"""
        Analyse la tâche suivante et détermine:
        1. Le type d'agent le mieux adapté (manager, hr, sales, purchase, logistics, stock, accounting, finance, crm, bi)
        2. La priorité estimée (1-5, 5 étant la plus haute)
        3. La complexité estimée (simple, moyenne, complexe)
        4. Les ressources nécessaires
        5. Le temps estimé de réalisation
        
        Tâche: {task_description}
        
        Réponds au format JSON avec les clés: agent_type, priority, complexity, resources, estimated_time, reasoning
        """
        
        response = self.generate_response(prompt, "manager", temperature=0.3)
        
        if response.success:
            try:
                analysis = json.loads(response.content)
                return analysis
            except json.JSONDecodeError:
                logger.error("Erreur de parsing JSON dans l'analyse de tâche")
                return self._get_default_task_analysis()
        
        return self._get_default_task_analysis()
    
    def _get_default_task_analysis(self) -> Dict[str, Any]:
        """Retourne une analyse par défaut en cas d'erreur"""
        return {
            "agent_type": "manager",
            "priority": 3,
            "complexity": "moyenne",
            "resources": ["agent_disponible"],
            "estimated_time": "1 heure",
            "reasoning": "Analyse par défaut - IA non disponible"
        }
    
    def generate_workflow_optimization(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Génère des recommandations d'optimisation pour un workflow
        """
        prompt = f"""
        Analyse le workflow suivant et propose des optimisations:
        
        Workflow: {json.dumps(workflow_data, indent=2)}
        
        Identifie:
        1. Les goulots d'étranglement potentiels
        2. Les étapes qui peuvent être parallélisées
        3. Les redondances à éliminer
        4. Les améliorations de performance possibles
        5. Les risques et points de défaillance
        
        Réponds au format JSON avec les clés: bottlenecks, parallelization_opportunities, redundancies, performance_improvements, risks, overall_score
        """
        
        response = self.generate_response(prompt, "manager", temperature=0.4)
        
        if response.success:
            try:
                optimization = json.loads(response.content)
                return optimization
            except json.JSONDecodeError:
                logger.error("Erreur de parsing JSON dans l'optimisation de workflow")
                return {"error": "Erreur d'analyse"}
        
        return {"error": "Service IA non disponible"}
    
    def generate_decision_reasoning(self, context: Dict[str, Any], decision_type: str) -> str:
        """
        Génère un raisonnement pour une décision du manager
        """
        prompt = f"""
        En tant qu'Agent Manager, explique le raisonnement derrière cette décision:
        
        Type de décision: {decision_type}
        Contexte: {json.dumps(context, indent=2)}
        
        Fournis une explication claire et structurée de:
        1. Les facteurs considérés
        2. Les alternatives évaluées
        3. Les critères de décision
        4. Les bénéfices attendus
        5. Les risques identifiés
        """
        
        response = self.generate_response(prompt, "manager", temperature=0.6)
        
        if response.success:
            return response.content
        else:
            return f"Décision basée sur les règles métier standard. Contexte: {decision_type}"
    
    def generate_performance_insights(self, metrics_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Génère des insights basés sur les métriques de performance
        """
        prompt = f"""
        Analyse les métriques de performance suivantes et génère des insights actionables:
        
        Métriques: {json.dumps(metrics_data, indent=2)}
        
        Pour chaque insight, fournis:
        1. L'observation (ce qui est remarqué)
        2. L'impact potentiel
        3. La recommandation d'action
        4. La priorité (haute, moyenne, basse)
        
        Réponds au format JSON avec une liste d'objets contenant: observation, impact, recommendation, priority
        """
        
        response = self.generate_response(prompt, "manager", temperature=0.5)
        
        if response.success:
            try:
                insights = json.loads(response.content)
                return insights if isinstance(insights, list) else [insights]
            except json.JSONDecodeError:
                logger.error("Erreur de parsing JSON dans les insights de performance")
                return []
        
        return []


# Instance globale du service IA
ai_service = AIService()
