import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { SimpleMetricCard } from '../../components/ui/SimpleMetricCard';
import { SimpleChart } from '../../components/ui/SimpleChart';

interface AccountingMetrics {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  cashFlow: number;
  accountsReceivable: number;
  accountsPayable: number;
}

interface Transaction {
  id: string;
  date: string;
  description: string;
  category: string;
  amount: number;
  type: 'income' | 'expense';
  status: 'pending' | 'approved' | 'rejected';
  account: string;
}

interface Account {
  id: string;
  name: string;
  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  balance: number;
  lastTransaction: string;
  status: 'active' | 'inactive';
}

export const AccountingDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<AccountingMetrics>({
    totalRevenue: 2847500,
    totalExpenses: 1956000,
    netProfit: 891500,
    cashFlow: 456000,
    accountsReceivable: 1247000,
    accountsPayable: 856000
  });

  const [recentTransactions] = useState<Transaction[]>([
    { id: 'TXN-001', date: '2024-01-27', description: 'Vente produits TechCorp', category: 'Ventes', amount: 125000, type: 'income', status: 'approved', account: 'Compte Clients' },
    { id: 'TXN-002', date: '2024-01-27', description: 'Achat matériel informatique', category: 'Achats', amount: -45000, type: 'expense', status: 'approved', account: 'Fournisseurs' },
    { id: 'TXN-003', date: '2024-01-26', description: 'Salaires janvier', category: 'Personnel', amount: -180000, type: 'expense', status: 'approved', account: 'Charges Personnel' },
    { id: 'TXN-004', date: '2024-01-26', description: 'Prestation services GlobalLtd', category: 'Services', amount: 85000, type: 'income', status: 'pending', account: 'Compte Clients' },
    { id: 'TXN-005', date: '2024-01-25', description: 'Loyer bureaux janvier', category: 'Immobilier', amount: -25000, type: 'expense', status: 'approved', account: 'Charges Locatives' }
  ];

  const [mainAccounts] = useState<Account[]>([
    { id: 'ACC-001', name: 'Compte Clients', type: 'asset', balance: 1247000, lastTransaction: '2024-01-27', status: 'active' },
    { id: 'ACC-002', name: 'Compte Banque', type: 'asset', balance: 456000, lastTransaction: '2024-01-27', status: 'active' },
    { id: 'ACC-003', name: 'Fournisseurs', type: 'liability', balance: -856000, lastTransaction: '2024-01-27', status: 'active' },
    { id: 'ACC-004', name: 'Capital Social', type: 'equity', balance: 500000, lastTransaction: '2024-01-01', status: 'active' },
    { id: 'ACC-005', name: 'Charges Personnel', type: 'expense', balance: -1200000, lastTransaction: '2024-01-26', status: 'active' },
    { id: 'ACC-006', name: 'Ventes Produits', type: 'revenue', balance: 2847500, lastTransaction: '2024-01-27', status: 'active' }
  ];

  const [profitLossData] = useState([
    { name: 'Jan', value: 245000, value2: -180000 },
    { name: 'Fév', value: 280000, value2: -195000 },
    { name: 'Mar', value: 320000, value2: -210000 },
    { name: 'Avr', value: 295000, value2: -205000 },
    { name: 'Mai', value: 350000, value2: -225000 },
    { name: 'Jun', value: 385000, value2: -240000 },
    { name: 'Jul', value: 410000, value2: -250000 }
  ]);

  const [expenseData] = useState([
    { name: 'Personnel', value: 1200 },
    { name: 'Achats', value: 450 },
    { name: 'Marketing', value: 180 },
    { name: 'Immobilier', value: 150 },
    { name: 'IT', value: 120 },
    { name: 'Autres', value: 200 }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const getTransactionStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'asset': return 'text-blue-600';
      case 'liability': return 'text-red-600';
      case 'equity': return 'text-purple-600';
      case 'revenue': return 'text-green-600';
      case 'expense': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'asset': return '💰';
      case 'liability': return '📋';
      case 'equity': return '🏛️';
      case 'revenue': return '📈';
      case 'expense': return '📉';
      default: return '📊';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(Math.abs(amount));
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-3xl">
              📊
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Agent Accounting</h1>
              <p className="text-gray-600 text-lg">Comptabilité générale et analyse financière</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              🔄 Actualiser
            </button>
            <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
              📊 Bilan
            </button>
          </div>
        </div>
      </motion.div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <SimpleMetricCard
          title="Chiffre d'Affaires"
          value={`${(metrics.totalRevenue / 1000000).toFixed(1)}M€`}
          icon="💰"
          color="green"
          trend="up"
          trendValue={15.2}
        />
        <SimpleMetricCard
          title="Charges"
          value={`${(metrics.totalExpenses / 1000000).toFixed(1)}M€`}
          icon="📉"
          color="red"
          trend="up"
          trendValue={8.7}
        />
        <SimpleMetricCard
          title="Résultat Net"
          value={`${(metrics.netProfit / 1000000).toFixed(1)}M€`}
          icon="📈"
          color="blue"
          trend="up"
          trendValue={22.5}
        />
        <SimpleMetricCard
          title="Trésorerie"
          value={`${(metrics.cashFlow / 1000).toFixed(0)}k€`}
          icon="🏦"
          color="purple"
          trend="up"
          trendValue={12.1}
        />
        <SimpleMetricCard
          title="Créances"
          value={`${(metrics.accountsReceivable / 1000000).toFixed(1)}M€`}
          icon="📋"
          color="yellow"
          trend="down"
          trendValue={-3.2}
        />
        <SimpleMetricCard
          title="Dettes"
          value={`${(metrics.accountsPayable / 1000).toFixed(0)}k€`}
          icon="📄"
          color="red"
          trend="down"
          trendValue={-5.8}
        />
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <SimpleChart
            title="Évolution Résultats (Revenus vs Charges)"
            data={profitLossData.map(item => ({
              name: item.name,
              value: item.value / 1000,
              value2: Math.abs(item.value2) / 1000
            }))}
            type="line"
            height={350}
            color="#7C3AED"
          />
        </div>
        <div>
          <SimpleChart
            title="Répartition des Charges (k€)"
            data={expenseData}
            type="bar"
            height={350}
            color="#A855F7"
          />
        </div>
      </div>

      {/* Transactions récentes et comptes principaux */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Transactions récentes */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold mb-4">Transactions Récentes</h3>
          <div className="space-y-4">
            {recentTransactions.map((transaction, index) => (
              <motion.div
                key={transaction.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-purple-600">{transaction.id}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTransactionStatusColor(transaction.status)}`}>
                      {transaction.status}
                    </span>
                  </div>
                  <div className={`font-semibold ${transaction.type === 'income' ? 'text-green-600' : 'text-red-600'}`}>
                    {transaction.type === 'income' ? '+' : ''}{formatCurrency(transaction.amount)}
                  </div>
                </div>
                <div className="text-sm text-gray-600 mb-1">{transaction.description}</div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500">{transaction.category}</span>
                  <span className="text-gray-500">
                    {new Date(transaction.date).toLocaleDateString('fr-FR')}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Comptes principaux */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold mb-4">Plan Comptable Principal</h3>
          <div className="space-y-4">
            {mainAccounts.map((account, index) => (
              <motion.div
                key={account.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="text-2xl">{getAccountTypeIcon(account.type)}</div>
                  <div>
                    <div className="font-semibold">{account.name}</div>
                    <div className={`text-sm ${getAccountTypeColor(account.type)}`}>
                      {account.type.toUpperCase()}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`font-semibold ${account.balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatCurrency(account.balance)}
                  </div>
                  <div className="text-xs text-gray-500">
                    Dernière op: {new Date(account.lastTransaction).toLocaleDateString('fr-FR')}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
