"""
Serializers pour l'Agent CRM
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
from .models import (
    Contact, Opportunity, Campaign, SupportTicket, Interaction,
    CustomerSegment, CRMAnalytics
)

User = get_user_model()


class ContactSerializer(serializers.ModelSerializer):
    """Serializer pour les contacts"""
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    full_name = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = Contact
        fields = [
            'id', 'contact_number', 'first_name', 'last_name', 'full_name', 'display_name',
            'company_name', 'job_title', 'contact_type', 'status', 'lead_source',
            'email', 'phone', 'mobile', 'website',
            'address_line1', 'address_line2', 'city', 'state', 'postal_code', 'country',
            'lead_score', 'customer_value', 'lifetime_value',
            'first_contact_date', 'last_contact_date', 'conversion_date',
            'assigned_to', 'assigned_to_name',
            'email_opt_in', 'sms_opt_in', 'phone_opt_in',
            'industry', 'company_size', 'annual_revenue',
            'tags', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'contact_number', 'full_name', 'display_name', 'created_at', 'updated_at']


class ContactCreateSerializer(serializers.Serializer):
    """Serializer pour la création de contacts"""
    first_name = serializers.CharField(max_length=100)
    last_name = serializers.CharField(max_length=100)
    company_name = serializers.CharField(max_length=200, required=False, allow_blank=True)
    job_title = serializers.CharField(max_length=100, required=False, allow_blank=True)
    contact_type = serializers.ChoiceField(choices=Contact.CONTACT_TYPES)
    status = serializers.ChoiceField(choices=Contact.CONTACT_STATUS, default='active')
    lead_source = serializers.ChoiceField(choices=Contact.LEAD_SOURCES, required=False, allow_blank=True)
    email = serializers.EmailField(required=False, allow_blank=True)
    phone = serializers.CharField(max_length=50, required=False, allow_blank=True)
    mobile = serializers.CharField(max_length=50, required=False, allow_blank=True)
    website = serializers.URLField(required=False, allow_blank=True)
    address_line1 = serializers.CharField(max_length=200, required=False, allow_blank=True)
    address_line2 = serializers.CharField(max_length=200, required=False, allow_blank=True)
    city = serializers.CharField(max_length=100, required=False, allow_blank=True)
    state = serializers.CharField(max_length=100, required=False, allow_blank=True)
    postal_code = serializers.CharField(max_length=20, required=False, allow_blank=True)
    country = serializers.CharField(max_length=100, required=False, allow_blank=True)
    industry = serializers.CharField(max_length=100, required=False, allow_blank=True)
    company_size = serializers.CharField(max_length=50, required=False, allow_blank=True)
    annual_revenue = serializers.DecimalField(max_digits=15, decimal_places=2, required=False, allow_null=True)
    tags = serializers.CharField(max_length=500, required=False, allow_blank=True)
    notes = serializers.CharField(required=False, allow_blank=True)


class OpportunitySerializer(serializers.ModelSerializer):
    """Serializer pour les opportunités"""
    contact_name = serializers.CharField(source='contact.display_name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    is_open = serializers.ReadOnlyField()
    is_won = serializers.ReadOnlyField()
    is_lost = serializers.ReadOnlyField()
    
    class Meta:
        model = Opportunity
        fields = [
            'id', 'opportunity_number', 'name', 'description',
            'contact', 'contact_name', 'opportunity_type', 'stage', 'priority',
            'amount', 'probability', 'weighted_amount',
            'expected_close_date', 'actual_close_date',
            'assigned_to', 'assigned_to_name',
            'competitors', 'competitive_advantage', 'loss_reason',
            'source', 'campaign', 'next_step', 'notes',
            'is_open', 'is_won', 'is_lost',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'opportunity_number', 'weighted_amount', 'is_open', 'is_won', 'is_lost', 'created_at', 'updated_at']


class OpportunityCreateSerializer(serializers.Serializer):
    """Serializer pour la création d'opportunités"""
    contact_id = serializers.UUIDField()
    name = serializers.CharField(max_length=200)
    description = serializers.CharField(required=False, allow_blank=True)
    opportunity_type = serializers.ChoiceField(choices=Opportunity.OPPORTUNITY_TYPES)
    stage = serializers.ChoiceField(choices=Opportunity.OPPORTUNITY_STAGES, default='prospecting')
    priority = serializers.ChoiceField(choices=Opportunity.PRIORITY_LEVELS, default='medium')
    amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    probability = serializers.IntegerField(default=50, min_value=0, max_value=100)
    expected_close_date = serializers.DateField()
    competitors = serializers.CharField(max_length=500, required=False, allow_blank=True)
    competitive_advantage = serializers.CharField(required=False, allow_blank=True)
    source = serializers.CharField(max_length=100, required=False, allow_blank=True)
    campaign = serializers.CharField(max_length=100, required=False, allow_blank=True)
    next_step = serializers.CharField(max_length=200, required=False, allow_blank=True)
    notes = serializers.CharField(required=False, allow_blank=True)


class CampaignSerializer(serializers.ModelSerializer):
    """Serializer pour les campagnes"""
    campaign_manager_name = serializers.CharField(source='campaign_manager.get_full_name', read_only=True)
    delivery_rate = serializers.ReadOnlyField()
    open_rate = serializers.ReadOnlyField()
    click_rate = serializers.ReadOnlyField()
    response_rate = serializers.ReadOnlyField()
    conversion_rate = serializers.ReadOnlyField()
    roi = serializers.ReadOnlyField()
    
    class Meta:
        model = Campaign
        fields = [
            'id', 'campaign_number', 'name', 'description',
            'campaign_type', 'status', 'start_date', 'end_date',
            'budget', 'actual_cost', 'target_audience',
            'expected_leads', 'expected_revenue',
            'total_sent', 'total_delivered', 'total_opened', 'total_clicked',
            'total_responses', 'total_leads', 'total_opportunities', 'actual_revenue',
            'campaign_manager', 'campaign_manager_name',
            'subject', 'content', 'call_to_action', 'segment_criteria',
            'delivery_rate', 'open_rate', 'click_rate', 'response_rate', 'conversion_rate', 'roi',
            'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'campaign_number', 'delivery_rate', 'open_rate', 'click_rate', 
            'response_rate', 'conversion_rate', 'roi', 'created_at', 'updated_at'
        ]


class CampaignCreateSerializer(serializers.Serializer):
    """Serializer pour la création de campagnes"""
    name = serializers.CharField(max_length=200)
    description = serializers.CharField(required=False, allow_blank=True)
    campaign_type = serializers.ChoiceField(choices=Campaign.CAMPAIGN_TYPES)
    status = serializers.ChoiceField(choices=Campaign.CAMPAIGN_STATUS, default='draft')
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    budget = serializers.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    target_audience = serializers.CharField(required=False, allow_blank=True)
    expected_leads = serializers.IntegerField(default=0)
    expected_revenue = serializers.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    subject = serializers.CharField(max_length=200, required=False, allow_blank=True)
    content = serializers.CharField(required=False, allow_blank=True)
    call_to_action = serializers.CharField(max_length=200, required=False, allow_blank=True)
    segment_criteria = serializers.JSONField(default=dict, required=False)
    notes = serializers.CharField(required=False, allow_blank=True)


class SupportTicketSerializer(serializers.ModelSerializer):
    """Serializer pour les tickets de support"""
    contact_name = serializers.CharField(source='contact.display_name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    escalated_to_name = serializers.CharField(source='escalated_to.get_full_name', read_only=True)
    is_open = serializers.ReadOnlyField()
    is_overdue = serializers.ReadOnlyField()
    
    class Meta:
        model = SupportTicket
        fields = [
            'id', 'ticket_number', 'subject', 'description',
            'contact', 'contact_name', 'ticket_type', 'status', 'priority',
            'assigned_to', 'assigned_to_name',
            'due_date', 'first_response_date', 'resolution_date', 'closed_date',
            'response_time_hours', 'resolution_time_hours',
            'satisfaction_rating', 'satisfaction_comment',
            'resolution', 'resolution_category',
            'escalated', 'escalation_reason', 'escalated_to', 'escalated_to_name',
            'tags', 'internal_notes', 'is_open', 'is_overdue',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'ticket_number', 'is_open', 'is_overdue', 
            'response_time_hours', 'resolution_time_hours', 'created_at', 'updated_at'
        ]


class SupportTicketCreateSerializer(serializers.Serializer):
    """Serializer pour la création de tickets de support"""
    contact_id = serializers.UUIDField()
    subject = serializers.CharField(max_length=200)
    description = serializers.CharField()
    ticket_type = serializers.ChoiceField(choices=SupportTicket.TICKET_TYPES)
    status = serializers.ChoiceField(choices=SupportTicket.TICKET_STATUS, default='new')
    priority = serializers.ChoiceField(choices=SupportTicket.PRIORITY_LEVELS, default='medium')
    due_date = serializers.DateTimeField(required=False, allow_null=True)
    tags = serializers.CharField(max_length=500, required=False, allow_blank=True)
    internal_notes = serializers.CharField(required=False, allow_blank=True)


class InteractionSerializer(serializers.ModelSerializer):
    """Serializer pour les interactions"""
    contact_name = serializers.CharField(source='contact.display_name', read_only=True)
    opportunity_name = serializers.CharField(source='opportunity.name', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = Interaction
        fields = [
            'id', 'contact', 'contact_name', 'opportunity', 'opportunity_name',
            'support_ticket', 'interaction_type', 'direction', 'subject', 'description',
            'interaction_date', 'duration_minutes', 'outcome',
            'next_action', 'next_action_date', 'user', 'user_name',
            'location', 'attendees', 'attachments', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class InteractionCreateSerializer(serializers.Serializer):
    """Serializer pour la création d'interactions"""
    contact_id = serializers.UUIDField()
    opportunity_id = serializers.UUIDField(required=False, allow_null=True)
    interaction_type = serializers.ChoiceField(choices=Interaction.INTERACTION_TYPES)
    direction = serializers.ChoiceField(choices=Interaction.INTERACTION_DIRECTIONS)
    subject = serializers.CharField(max_length=200)
    description = serializers.CharField()
    interaction_date = serializers.DateTimeField()
    duration_minutes = serializers.IntegerField(required=False, allow_null=True)
    outcome = serializers.ChoiceField(choices=Interaction.INTERACTION_OUTCOMES, required=False, allow_blank=True)
    next_action = serializers.CharField(max_length=200, required=False, allow_blank=True)
    next_action_date = serializers.DateTimeField(required=False, allow_null=True)
    location = serializers.CharField(max_length=200, required=False, allow_blank=True)
    attendees = serializers.CharField(max_length=500, required=False, allow_blank=True)
    notes = serializers.CharField(required=False, allow_blank=True)


class CustomerSegmentSerializer(serializers.ModelSerializer):
    """Serializer pour les segments clients"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = CustomerSegment
        fields = [
            'id', 'name', 'description', 'segment_type', 'criteria',
            'contact_count', 'total_value', 'average_value',
            'is_active', 'auto_update', 'last_updated',
            'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'contact_count', 'total_value', 'average_value', 'last_updated', 'created_at', 'updated_at']


class CRMAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer pour les analytics CRM"""
    calculated_by_name = serializers.CharField(source='calculated_by.get_full_name', read_only=True)
    variance_from_target = serializers.ReadOnlyField()
    variance_from_previous = serializers.ReadOnlyField()
    performance_vs_target = serializers.ReadOnlyField()
    growth_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = CRMAnalytics
        fields = [
            'id', 'metric_type', 'metric_name', 'period_start', 'period_end',
            'metric_value', 'target_value', 'previous_value',
            'variance_from_target', 'variance_from_previous', 'performance_vs_target', 'growth_rate',
            'calculation_method', 'data_source', 'notes',
            'calculated_by', 'calculated_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'variance_from_target', 'variance_from_previous', 
            'performance_vs_target', 'growth_rate', 'created_at', 'updated_at'
        ]


class CRMDashboardSerializer(serializers.Serializer):
    """Serializer pour le dashboard CRM"""
    tenant = serializers.CharField()
    timestamp = serializers.CharField()
    contacts = serializers.DictField()
    opportunities = serializers.DictField()
    campaigns = serializers.DictField()
    support = serializers.DictField()
    interactions = serializers.DictField()
    performance = serializers.DictField()
    satisfaction = serializers.DictField()
    campaign_metrics = serializers.DictField()
    alerts = serializers.DictField()
    recent_activities = serializers.ListField()


class CRMInsightSerializer(serializers.Serializer):
    """Serializer pour les insights CRM"""
    type = serializers.ChoiceField(choices=['critical', 'warning', 'opportunity', 'info'])
    priority = serializers.ChoiceField(choices=['high', 'medium', 'low'])
    title = serializers.CharField()
    description = serializers.CharField()
    recommendation = serializers.CharField()
    generated_at = serializers.CharField()


class LeadScoreUpdateSerializer(serializers.Serializer):
    """Serializer pour la mise à jour des scores de leads"""
    success = serializers.BooleanField()
    total_contacts = serializers.IntegerField()
    updated_count = serializers.IntegerField()
    error = serializers.CharField(required=False)
