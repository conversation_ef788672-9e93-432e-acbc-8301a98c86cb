<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent CRM - Gestion Clients | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #8b5cf6 30%, #7c3aed 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #8b5cf6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #7c3aed;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .btn-info {
            background: #3b82f6;
            color: white;
        }
        
        .btn-info:hover {
            background: #2563eb;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn-warning:hover {
            background: #d97706;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #8b5cf6;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .star-rating {
            display: inline-flex;
            gap: 0.125rem;
            align-items: center;
        }
        
        .star {
            color: #d1d5db;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .star.filled {
            color: #fbbf24;
        }
        
        .star:hover {
            color: #fbbf24;
        }
        
        .sector-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .sector-tech {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .sector-health {
            background: #dcfce7;
            color: #166534;
        }
        
        .sector-finance {
            background: #fef3c7;
            color: #92400e;
        }
        
        .sector-industry {
            background: #f3e8ff;
            color: #6b21a8;
        }
        
        .sector-commerce {
            background: #fce7f3;
            color: #be185d;
        }
        
        .sector-services {
            background: #e0f2fe;
            color: #0369a1;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #8b5cf6;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .task-priority-high {
            border-left: 4px solid #ef4444;
        }
        
        .task-priority-medium {
            border-left: 4px solid #f59e0b;
        }
        
        .task-priority-low {
            border-left: 4px solid #10b981;
        }
        
        .task-overdue {
            background: #fef2f2;
        }
        
        .task-due-soon {
            background: #fffbeb;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">🤝 Agent CRM - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="openModal('clientModal')">
                <span class="material-icons" style="font-size: 1rem;">add</span>
                Nouveau Client
            </button>
            <button class="btn btn-info" onclick="openModal('interactionModal')">
                <span class="material-icons" style="font-size: 1rem;">chat</span>
                Ajouter Interaction
            </button>
            <button class="btn btn-warning" onclick="openModal('taskModal')">
                <span class="material-icons" style="font-size: 1rem;">task</span>
                Planifier Tâche
            </button>
            <button class="btn btn-success" onclick="generateCRMReport()">
                <span class="material-icons" style="font-size: 1rem;">assessment</span>
                Générer Rapport
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion de la Relation Client</h1>
            <p class="page-subtitle">Clients, interactions et suivi commercial avancé</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalClients">0</div>
                <div class="stat-label">Total Clients</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeClients">0%</div>
                <div class="stat-label">Clients Actifs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgSatisfaction">0★</div>
                <div class="stat-label">Satisfaction Moyenne</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="pendingTasks">0</div>
                <div class="stat-label">Tâches en Cours</div>
            </div>
        </div>

        <!-- Liste des clients -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Base Clients</h2>
                <button class="btn btn-primary" onclick="refreshClients()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div id="alertContainer"></div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Entreprise</th>
                                <th>Secteur</th>
                                <th>Date de création</th>
                                <th>Dernière interaction</th>
                                <th>CA Annuel</th>
                                <th>Contact</th>
                                <th>Satisfaction</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="clientsTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Interactions récentes -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Interactions Récentes</h2>
                <button class="btn btn-primary" onclick="refreshInteractions()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Client</th>
                                <th>Type</th>
                                <th>Durée</th>
                                <th>Résultat</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="interactionsTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Tâches planifiées -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Tâches Planifiées</h2>
                <button class="btn btn-primary" onclick="refreshTasks()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Échéance</th>
                                <th>Client</th>
                                <th>Tâche</th>
                                <th>Priorité</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="tasksTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Client -->
    <div id="clientModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="clientModalTitle">Nouveau Client</h3>
                <button class="close-btn" onclick="closeModal('clientModal')">&times;</button>
            </div>
            <form id="clientForm">
                <div id="clientModalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="clientName">Nom du contact *</label>
                        <input type="text" id="clientName" name="clientName" class="form-input" required placeholder="Prénom Nom">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="clientCompany">Entreprise *</label>
                        <input type="text" id="clientCompany" name="clientCompany" class="form-input" required placeholder="Nom de l'entreprise">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="clientSector">Secteur d'activité *</label>
                        <select id="clientSector" name="clientSector" class="form-select" required>
                            <option value="">Sélectionner un secteur</option>
                            <option value="tech">Technologie</option>
                            <option value="health">Santé</option>
                            <option value="finance">Finance</option>
                            <option value="industry">Industrie</option>
                            <option value="commerce">Commerce</option>
                            <option value="services">Services</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="clientRevenue">CA Annuel (€)</label>
                        <input type="number" id="clientRevenue" name="clientRevenue" class="form-input" min="0" step="1000" placeholder="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="clientEmail">Email *</label>
                        <input type="email" id="clientEmail" name="clientEmail" class="form-input" required placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="clientPhone">Téléphone</label>
                        <input type="tel" id="clientPhone" name="clientPhone" class="form-input" placeholder="+33 1 23 45 67 89">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="clientStatus">Statut *</label>
                        <select id="clientStatus" name="clientStatus" class="form-select" required>
                            <option value="prospect">Prospect</option>
                            <option value="active" selected>Actif</option>
                            <option value="vip">VIP</option>
                            <option value="inactive">Inactif</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="clientSatisfaction">Satisfaction (1-5 étoiles)</label>
                        <div class="star-rating" id="clientSatisfactionRating">
                            <span class="material-icons star" data-rating="1">star_border</span>
                            <span class="material-icons star" data-rating="2">star_border</span>
                            <span class="material-icons star" data-rating="3">star_border</span>
                            <span class="material-icons star" data-rating="4">star_border</span>
                            <span class="material-icons star" data-rating="5">star_border</span>
                        </div>
                        <input type="hidden" id="clientSatisfaction" name="clientSatisfaction" value="0">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="clientNotes">Notes</label>
                    <textarea id="clientNotes" name="clientNotes" class="form-textarea" placeholder="Notes sur le client..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('clientModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveClientBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal Interaction -->
    <div id="interactionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="interactionModalTitle">Nouvelle Interaction</h3>
                <button class="close-btn" onclick="closeModal('interactionModal')">&times;</button>
            </div>
            <form id="interactionForm">
                <div id="interactionModalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="interactionDate">Date *</label>
                        <input type="datetime-local" id="interactionDate" name="interactionDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="interactionClient">Client *</label>
                        <select id="interactionClient" name="interactionClient" class="form-select" required>
                            <option value="">Sélectionner un client</option>
                            <!-- Options chargées dynamiquement -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="interactionType">Type d'interaction *</label>
                        <select id="interactionType" name="interactionType" class="form-select" required>
                            <option value="">Sélectionner un type</option>
                            <option value="call">Appel téléphonique</option>
                            <option value="email">Email</option>
                            <option value="meeting">Réunion</option>
                            <option value="visit">Visite client</option>
                            <option value="support">Support technique</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="interactionDuration">Durée (minutes)</label>
                        <input type="number" id="interactionDuration" name="interactionDuration" class="form-input" min="1" placeholder="30">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="interactionResult">Résultat</label>
                        <select id="interactionResult" name="interactionResult" class="form-select">
                            <option value="">Sélectionner un résultat</option>
                            <option value="success">Succès</option>
                            <option value="follow-up">À suivre</option>
                            <option value="no-answer">Pas de réponse</option>
                            <option value="postponed">Reporté</option>
                            <option value="cancelled">Annulé</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="interactionNotes">Notes de l'interaction *</label>
                    <textarea id="interactionNotes" name="interactionNotes" class="form-textarea" required placeholder="Détails de l'interaction..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('interactionModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveInteractionBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal Tâche -->
    <div id="taskModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="taskModalTitle">Nouvelle Tâche</h3>
                <button class="close-btn" onclick="closeModal('taskModal')">&times;</button>
            </div>
            <form id="taskForm">
                <div id="taskModalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="taskDueDate">Échéance *</label>
                        <input type="datetime-local" id="taskDueDate" name="taskDueDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="taskClient">Client *</label>
                        <select id="taskClient" name="taskClient" class="form-select" required>
                            <option value="">Sélectionner un client</option>
                            <!-- Options chargées dynamiquement -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="taskTitle">Titre de la tâche *</label>
                        <input type="text" id="taskTitle" name="taskTitle" class="form-input" required placeholder="Titre de la tâche">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="taskPriority">Priorité *</label>
                        <select id="taskPriority" name="taskPriority" class="form-select" required>
                            <option value="low">Basse</option>
                            <option value="medium" selected>Moyenne</option>
                            <option value="high">Haute</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="taskStatus">Statut</label>
                        <select id="taskStatus" name="taskStatus" class="form-select">
                            <option value="pending" selected>En attente</option>
                            <option value="in-progress">En cours</option>
                            <option value="completed">Terminée</option>
                            <option value="cancelled">Annulée</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="taskDescription">Description</label>
                    <textarea id="taskDescription" name="taskDescription" class="form-textarea" placeholder="Description détaillée de la tâche..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('taskModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveTaskBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let clients = [];
        let interactions = [];
        let tasks = [];
        let editingClientId = null;
        let editingInteractionId = null;
        let editingTaskId = null;
        let isLoading = false;

        // Fonction utilitaire pour formater les dates
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        }

        // Données de démonstration réalistes - Clients
        const demoClients = [
            {
                id: 'CLI-001',
                name: 'Marie Dubois',
                company: 'TechCorp Solutions',
                sector: 'tech',
                revenue: 2500000,
                email: '<EMAIL>',
                phone: '+33 1 42 86 75 30',
                status: 'active',
                satisfaction: 4.5,
                notes: 'Client stratégique - Contrat de maintenance annuel',
                createdDate: '2024-01-10',
                lastInteractionDate: '2024-01-28T14:30:00'
            },
            {
                id: 'CLI-002',
                name: 'Jean Martin',
                company: 'MedicalPlus Clinique',
                sector: 'health',
                revenue: 1800000,
                email: '<EMAIL>',
                phone: '+33 1 45 67 89 12',
                status: 'vip',
                satisfaction: 4.8,
                notes: 'Client VIP - Équipements médicaux haut de gamme'
            },
            {
                id: 'CLI-003',
                name: 'Sophie Laurent',
                company: 'FinanceGroup SA',
                sector: 'finance',
                revenue: 5200000,
                email: '<EMAIL>',
                phone: '+33 1 56 78 90 23',
                status: 'active',
                satisfaction: 4.2,
                notes: 'Solutions logicielles financières'
            },
            {
                id: 'CLI-004',
                name: 'Pierre Moreau',
                company: 'IndustrialTech',
                sector: 'industry',
                revenue: 3400000,
                email: '<EMAIL>',
                phone: '+33 1 67 89 01 34',
                status: 'active',
                satisfaction: 3.9,
                notes: 'Automatisation industrielle'
            },
            {
                id: 'CLI-005',
                name: 'Lucie Bernard',
                company: 'CommerceMax',
                sector: 'commerce',
                revenue: 950000,
                email: '<EMAIL>',
                phone: '+33 1 78 90 12 45',
                status: 'prospect',
                satisfaction: 3.2,
                notes: 'Prospect intéressé par nos solutions e-commerce'
            },
            {
                id: 'CLI-006',
                name: 'Thomas Petit',
                company: 'ServicesPro',
                sector: 'services',
                revenue: 1200000,
                email: '<EMAIL>',
                phone: '+33 1 89 01 23 56',
                status: 'active',
                satisfaction: 4.1,
                notes: 'Services de conseil en management'
            },
            {
                id: 'CLI-007',
                name: 'Emma Rousseau',
                company: 'HealthCare Plus',
                sector: 'health',
                revenue: 2100000,
                email: '<EMAIL>',
                phone: '+33 1 90 12 34 67',
                status: 'active',
                satisfaction: 4.6,
                notes: 'Équipements hospitaliers - Contrat pluriannuel'
            },
            {
                id: 'CLI-008',
                name: 'Alexandre Durand',
                company: 'TechStartup Innovation',
                sector: 'tech',
                revenue: 450000,
                email: '<EMAIL>',
                phone: '+33 1 01 23 45 78',
                status: 'inactive',
                satisfaction: 2.8,
                notes: 'Ancien client - Contrat non renouvelé'
            }
        ];

        // Données de démonstration réalistes - Interactions
        const demoInteractions = [
            {
                id: 'INT-001',
                date: '2024-01-28T14:30:00',
                clientId: 'CLI-001',
                clientName: 'Marie Dubois (TechCorp Solutions)',
                type: 'meeting',
                duration: 90,
                result: 'success',
                notes: 'Réunion de suivi du projet - Validation des spécifications techniques'
            },
            {
                id: 'INT-002',
                date: '2024-01-26T10:15:00',
                clientId: 'CLI-002',
                clientName: 'Jean Martin (MedicalPlus Clinique)',
                type: 'call',
                duration: 45,
                result: 'follow-up',
                notes: 'Discussion sur les nouveaux équipements - Devis à envoyer'
            },
            {
                id: 'INT-003',
                date: '2024-01-25T16:00:00',
                clientId: 'CLI-003',
                clientName: 'Sophie Laurent (FinanceGroup SA)',
                type: 'email',
                duration: 15,
                result: 'success',
                notes: 'Envoi de la documentation technique - Réponse positive'
            },
            {
                id: 'INT-004',
                date: '2024-01-24T09:30:00',
                clientId: 'CLI-004',
                clientName: 'Pierre Moreau (IndustrialTech)',
                type: 'visit',
                duration: 180,
                result: 'success',
                notes: 'Visite sur site - Installation réussie des équipements'
            },
            {
                id: 'INT-005',
                date: '2024-01-23T11:45:00',
                clientId: 'CLI-005',
                clientName: 'Lucie Bernard (CommerceMax)',
                type: 'call',
                duration: 30,
                result: 'no-answer',
                notes: 'Tentative de contact - Pas de réponse, rappeler demain'
            },
            {
                id: 'INT-006',
                date: '2024-01-22T15:20:00',
                clientId: 'CLI-006',
                clientName: 'Thomas Petit (ServicesPro)',
                type: 'support',
                duration: 60,
                result: 'success',
                notes: 'Support technique - Problème résolu, formation utilisateur'
            },
            {
                id: 'INT-007',
                date: '2024-01-20T13:00:00',
                clientId: 'CLI-007',
                clientName: 'Emma Rousseau (HealthCare Plus)',
                type: 'meeting',
                duration: 120,
                result: 'follow-up',
                notes: 'Présentation des nouvelles solutions - Intérêt confirmé'
            },
            {
                id: 'INT-008',
                date: '2024-01-19T08:45:00',
                clientId: 'CLI-001',
                clientName: 'Marie Dubois (TechCorp Solutions)',
                type: 'email',
                duration: 10,
                result: 'success',
                notes: 'Confirmation de la livraison - Planning respecté'
            },
            {
                id: 'INT-009',
                date: '2024-01-18T14:15:00',
                clientId: 'CLI-003',
                clientName: 'Sophie Laurent (FinanceGroup SA)',
                type: 'call',
                duration: 25,
                result: 'postponed',
                notes: 'Appel reporté à la semaine prochaine - Agenda chargé'
            },
            {
                id: 'INT-010',
                date: '2024-01-17T10:30:00',
                clientId: 'CLI-002',
                clientName: 'Jean Martin (MedicalPlus Clinique)',
                type: 'support',
                duration: 40,
                result: 'success',
                notes: 'Formation équipe médicale - Très satisfaits'
            },
            {
                id: 'INT-011',
                date: '2024-01-16T16:45:00',
                clientId: 'CLI-004',
                clientName: 'Pierre Moreau (IndustrialTech)',
                type: 'email',
                duration: 5,
                result: 'success',
                notes: 'Envoi rapport de maintenance - Accusé de réception'
            },
            {
                id: 'INT-012',
                date: '2024-01-15T12:00:00',
                clientId: 'CLI-006',
                clientName: 'Thomas Petit (ServicesPro)',
                type: 'meeting',
                duration: 75,
                result: 'success',
                notes: 'Réunion bilan trimestriel - Objectifs atteints'
            }
        ];

        // Données de démonstration réalistes - Tâches
        const demoTasks = [
            {
                id: 'TSK-001',
                dueDate: '2024-01-27T09:00:00', // En retard
                clientId: 'CLI-005',
                clientName: 'Lucie Bernard (CommerceMax)',
                title: 'Rappeler pour devis e-commerce',
                priority: 'high',
                status: 'pending',
                description: 'Relancer le prospect pour finaliser le devis solution e-commerce'
            },
            {
                id: 'TSK-002',
                dueDate: '2024-02-02T14:00:00', // Dans 3 jours
                clientId: 'CLI-001',
                clientName: 'Marie Dubois (TechCorp Solutions)',
                title: 'Livraison équipements phase 2',
                priority: 'high',
                status: 'in-progress',
                description: 'Coordonner la livraison des serveurs pour la phase 2 du projet'
            },
            {
                id: 'TSK-003',
                dueDate: '2024-02-03T10:30:00', // Dans 4 jours
                clientId: 'CLI-002',
                clientName: 'Jean Martin (MedicalPlus Clinique)',
                title: 'Formation équipe technique',
                priority: 'medium',
                status: 'pending',
                description: 'Organiser la formation sur les nouveaux équipements médicaux'
            },
            {
                id: 'TSK-004',
                dueDate: '2024-02-05T16:00:00',
                clientId: 'CLI-003',
                clientName: 'Sophie Laurent (FinanceGroup SA)',
                title: 'Présentation solution BI',
                priority: 'medium',
                status: 'pending',
                description: 'Préparer et présenter la solution Business Intelligence'
            },
            {
                id: 'TSK-005',
                dueDate: '2024-02-08T11:00:00',
                clientId: 'CLI-007',
                clientName: 'Emma Rousseau (HealthCare Plus)',
                title: 'Audit sécurité système',
                priority: 'high',
                status: 'pending',
                description: 'Effectuer l\'audit de sécurité du système hospitalier'
            },
            {
                id: 'TSK-006',
                dueDate: '2024-02-10T15:30:00',
                clientId: 'CLI-004',
                clientName: 'Pierre Moreau (IndustrialTech)',
                title: 'Maintenance préventive',
                priority: 'low',
                status: 'pending',
                description: 'Planifier la maintenance préventive des équipements industriels'
            }
        ];

        // Fonctions utilitaires
        function showAlert(message, type = 'error', container = 'alertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';

            if (modalId === 'clientModal') {
                document.getElementById('clientModalTitle').textContent = editingClientId ? 'Modifier Client' : 'Nouveau Client';
                setupStarRating();
            } else if (modalId === 'interactionModal') {
                document.getElementById('interactionModalTitle').textContent = editingInteractionId ? 'Modifier Interaction' : 'Nouvelle Interaction';
                loadClientsInSelect('interactionClient');
                if (!editingInteractionId) {
                    document.getElementById('interactionDate').value = new Date().toISOString().slice(0, 16);
                }
            } else if (modalId === 'taskModal') {
                document.getElementById('taskModalTitle').textContent = editingTaskId ? 'Modifier Tâche' : 'Nouvelle Tâche';
                loadClientsInSelect('taskClient');
                if (!editingTaskId) {
                    // Définir l'échéance à demain par défaut
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    document.getElementById('taskDueDate').value = tomorrow.toISOString().slice(0, 16);
                }
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';

            if (modalId === 'clientModal') {
                document.getElementById('clientForm').reset();
                document.getElementById('clientModalAlertContainer').innerHTML = '';
                editingClientId = null;
                resetStarRating();
            } else if (modalId === 'interactionModal') {
                document.getElementById('interactionForm').reset();
                document.getElementById('interactionModalAlertContainer').innerHTML = '';
                editingInteractionId = null;
            } else if (modalId === 'taskModal') {
                document.getElementById('taskForm').reset();
                document.getElementById('taskModalAlertContainer').innerHTML = '';
                editingTaskId = null;
            }
        }

        function loadClientsInSelect(selectId) {
            const select = document.getElementById(selectId);
            select.innerHTML = '<option value="">Sélectionner un client</option>';

            clients.forEach(client => {
                const option = document.createElement('option');
                option.value = client.id;
                option.textContent = `${client.name} (${client.company})`;
                select.appendChild(option);
            });
        }

        // Système de notation par étoiles
        function setupStarRating() {
            const stars = document.querySelectorAll('#clientSatisfactionRating .star');
            const hiddenInput = document.getElementById('clientSatisfaction');

            stars.forEach((star, index) => {
                star.addEventListener('click', () => {
                    const rating = index + 1;
                    hiddenInput.value = rating;
                    updateStarDisplay(rating);
                });

                star.addEventListener('mouseenter', () => {
                    updateStarDisplay(index + 1, true);
                });
            });

            document.getElementById('clientSatisfactionRating').addEventListener('mouseleave', () => {
                updateStarDisplay(parseInt(hiddenInput.value) || 0);
            });
        }

        function updateStarDisplay(rating, isHover = false) {
            const stars = document.querySelectorAll('#clientSatisfactionRating .star');
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.textContent = 'star';
                    star.classList.add('filled');
                } else {
                    star.textContent = 'star_border';
                    star.classList.remove('filled');
                }
            });
        }

        function resetStarRating() {
            document.getElementById('clientSatisfaction').value = '0';
            updateStarDisplay(0);
        }

        // Calcul des statistiques CRM
        function updateStats() {
            const totalClients = clients.length;
            const activeClients = clients.filter(c => c.status === 'active' || c.status === 'vip').length;
            const activePercentage = totalClients > 0 ? ((activeClients / totalClients) * 100).toFixed(1) : 0;

            const totalSatisfaction = clients.reduce((sum, c) => sum + (c.satisfaction || 0), 0);
            const avgSatisfaction = totalClients > 0 ? (totalSatisfaction / totalClients).toFixed(1) : 0;

            const pendingTasks = tasks.filter(t => t.status === 'pending' || t.status === 'in-progress').length;

            document.getElementById('totalClients').textContent = totalClients;
            document.getElementById('activeClients').textContent = activePercentage + '%';
            document.getElementById('avgSatisfaction').textContent = avgSatisfaction + '★';
            document.getElementById('pendingTasks').textContent = pendingTasks;
        }

        // Obtenir le badge de statut client
        function getClientStatusBadge(status) {
            const badges = {
                'prospect': '<span class="badge badge-info">Prospect</span>',
                'active': '<span class="badge badge-success">Actif</span>',
                'vip': '<span class="badge badge-warning">VIP</span>',
                'inactive': '<span class="badge badge-danger">Inactif</span>'
            };
            return badges[status] || '<span class="badge badge-danger">Inconnu</span>';
        }

        // Obtenir le badge de secteur
        function getSectorBadge(sector) {
            const sectorNames = {
                'tech': 'Technologie',
                'health': 'Santé',
                'finance': 'Finance',
                'industry': 'Industrie',
                'commerce': 'Commerce',
                'services': 'Services'
            };
            const sectorName = sectorNames[sector] || sector;
            return `<span class="sector-badge sector-${sector}">${sectorName}</span>`;
        }

        // Afficher les étoiles de satisfaction
        function renderStars(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<span class="material-icons star filled" style="font-size: 1rem;">star</span>';
                } else {
                    stars += '<span class="material-icons star" style="font-size: 1rem;">star_border</span>';
                }
            }
            return stars;
        }

        // Rendu du tableau des clients
        function renderClientsTable() {
            const tbody = document.getElementById('clientsTableBody');

            if (clients.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun client trouvé. Cliquez sur "Nouveau Client" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = clients.map(client => {
                const statusBadge = getClientStatusBadge(client.status);
                const sectorBadge = getSectorBadge(client.sector);
                const stars = renderStars(client.satisfaction || 0);
                const revenue = client.revenue ? client.revenue.toLocaleString() + '€' : 'N/A';

                return `
                    <tr>
                        <td>${client.id}</td>
                        <td>${client.name}</td>
                        <td>${client.company}</td>
                        <td>${sectorBadge}</td>
                        <td>${revenue}</td>
                        <td>${client.email}<br><small style="color: #6b7280;">${client.phone || 'N/A'}</small></td>
                        <td>${stars}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editClient('${client.id}')" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteClient('${client.id}')" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Rendu du tableau des interactions
        function renderInteractionsTable() {
            const tbody = document.getElementById('interactionsTableBody');

            if (interactions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune interaction trouvée. Cliquez sur "Ajouter Interaction" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            // Trier par date décroissante
            const sortedInteractions = [...interactions].sort((a, b) => new Date(b.date) - new Date(a.date));

            tbody.innerHTML = sortedInteractions.map(interaction => {
                const typeDisplay = getInteractionTypeDisplay(interaction.type);
                const resultDisplay = getInteractionResultDisplay(interaction.result);
                const duration = interaction.duration ? `${interaction.duration} min` : 'N/A';

                return `
                    <tr>
                        <td>${new Date(interaction.date).toLocaleDateString()} ${new Date(interaction.date).toLocaleTimeString()}</td>
                        <td>${interaction.clientName}</td>
                        <td>${typeDisplay}</td>
                        <td>${duration}</td>
                        <td>${resultDisplay}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editInteraction('${interaction.id}')" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteInteraction('${interaction.id}')" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Rendu du tableau des tâches
        function renderTasksTable() {
            const tbody = document.getElementById('tasksTableBody');

            if (tasks.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune tâche trouvée. Cliquez sur "Planifier Tâche" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            // Trier par date d'échéance
            const sortedTasks = [...tasks].sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate));

            tbody.innerHTML = sortedTasks.map(task => {
                const priorityDisplay = getTaskPriorityDisplay(task.priority);
                const statusDisplay = getTaskStatusDisplay(task.status);
                const dueDate = new Date(task.dueDate);
                const now = new Date();
                const isOverdue = dueDate < now && task.status !== 'completed';
                const isDueSoon = dueDate > now && dueDate <= new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);

                let rowClass = '';
                if (isOverdue) rowClass = 'task-overdue';
                else if (isDueSoon) rowClass = 'task-due-soon';

                return `
                    <tr class="${rowClass}">
                        <td>${dueDate.toLocaleDateString()} ${dueDate.toLocaleTimeString()}</td>
                        <td>${task.clientName}</td>
                        <td>${task.title}</td>
                        <td>${priorityDisplay}</td>
                        <td>${statusDisplay}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editTask('${task.id}')" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteTask('${task.id}')" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getInteractionTypeDisplay(type) {
            const types = {
                'call': '<span class="badge badge-info">Appel</span>',
                'email': '<span class="badge badge-success">Email</span>',
                'meeting': '<span class="badge badge-warning">Réunion</span>',
                'visit': '<span class="badge badge-danger">Visite</span>',
                'support': '<span class="badge badge-info">Support</span>'
            };
            return types[type] || '<span class="badge badge-secondary">Inconnu</span>';
        }

        function getInteractionResultDisplay(result) {
            const results = {
                'success': '<span class="badge badge-success">Succès</span>',
                'follow-up': '<span class="badge badge-warning">À suivre</span>',
                'no-answer': '<span class="badge badge-danger">Pas de réponse</span>',
                'postponed': '<span class="badge badge-info">Reporté</span>',
                'cancelled': '<span class="badge badge-danger">Annulé</span>'
            };
            return results[result] || '<span class="badge badge-secondary">N/A</span>';
        }

        function getTaskPriorityDisplay(priority) {
            const priorities = {
                'high': '<span class="badge badge-danger">Haute</span>',
                'medium': '<span class="badge badge-warning">Moyenne</span>',
                'low': '<span class="badge badge-success">Basse</span>'
            };
            return priorities[priority] || '<span class="badge badge-secondary">Inconnu</span>';
        }

        function getTaskStatusDisplay(status) {
            const statuses = {
                'pending': '<span class="badge badge-warning">En attente</span>',
                'in-progress': '<span class="badge badge-info">En cours</span>',
                'completed': '<span class="badge badge-success">Terminée</span>',
                'cancelled': '<span class="badge badge-danger">Annulée</span>'
            };
            return statuses[status] || '<span class="badge badge-secondary">Inconnu</span>';
        }

        // Chargement des données CRM
        async function loadCRMData() {
            try {
                // Tentative de chargement depuis l'API
                const token = localStorage.getItem('access_token');
                if (token) {
                    const response = await fetch('http://localhost:8000/api/agents/crm/clients/', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        clients = data.clients || [];
                        interactions = data.interactions || [];
                        tasks = data.tasks || [];
                    } else {
                        throw new Error('Erreur API');
                    }
                } else {
                    throw new Error('Pas de token');
                }
            } catch (error) {
                console.log('Chargement des données de démonstration');
                clients = [...demoClients];
                interactions = [...demoInteractions];
                tasks = [...demoTasks];
            }

            renderClientsTable();
            renderInteractionsTable();
            renderTasksTable();
            updateStats();
        }

        function refreshClients() {
            loadCRMData();
            showAlert('Données clients actualisées avec succès', 'success');
        }

        function refreshInteractions() {
            loadCRMData();
            showAlert('Données interactions actualisées avec succès', 'success');
        }

        function refreshTasks() {
            loadCRMData();
            showAlert('Données tâches actualisées avec succès', 'success');
        }

        // Fonctions CRUD pour les clients
        function editClient(id) {
            const client = clients.find(c => c.id === id);
            if (!client) return;

            editingClientId = id;

            // Remplir le formulaire
            document.getElementById('clientName').value = client.name;
            document.getElementById('clientCompany').value = client.company;
            document.getElementById('clientSector').value = client.sector;
            document.getElementById('clientRevenue').value = client.revenue || '';
            document.getElementById('clientEmail').value = client.email;
            document.getElementById('clientPhone').value = client.phone || '';
            document.getElementById('clientStatus').value = client.status;
            document.getElementById('clientSatisfaction').value = client.satisfaction || 0;
            document.getElementById('clientNotes').value = client.notes || '';

            openModal('clientModal');
            updateStarDisplay(client.satisfaction || 0);
        }

        function deleteClient(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce client ?')) {
                clients = clients.filter(c => c.id !== id);
                // Supprimer aussi les interactions et tâches liées
                interactions = interactions.filter(i => i.clientId !== id);
                tasks = tasks.filter(t => t.clientId !== id);

                renderClientsTable();
                renderInteractionsTable();
                renderTasksTable();
                updateStats();
                showAlert('Client supprimé avec succès', 'success');
            }
        }

        // Fonctions CRUD pour les interactions
        function editInteraction(id) {
            const interaction = interactions.find(i => i.id === id);
            if (!interaction) return;

            editingInteractionId = id;

            // Remplir le formulaire
            document.getElementById('interactionDate').value = interaction.date.slice(0, 16);
            document.getElementById('interactionClient').value = interaction.clientId;
            document.getElementById('interactionType').value = interaction.type;
            document.getElementById('interactionDuration').value = interaction.duration || '';
            document.getElementById('interactionResult').value = interaction.result || '';
            document.getElementById('interactionNotes').value = interaction.notes;

            openModal('interactionModal');
        }

        function deleteInteraction(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette interaction ?')) {
                interactions = interactions.filter(i => i.id !== id);
                renderInteractionsTable();
                showAlert('Interaction supprimée avec succès', 'success');
            }
        }

        // Fonctions CRUD pour les tâches
        function editTask(id) {
            const task = tasks.find(t => t.id === id);
            if (!task) return;

            editingTaskId = id;

            // Remplir le formulaire
            document.getElementById('taskDueDate').value = task.dueDate.slice(0, 16);
            document.getElementById('taskClient').value = task.clientId;
            document.getElementById('taskTitle').value = task.title;
            document.getElementById('taskPriority').value = task.priority;
            document.getElementById('taskStatus').value = task.status;
            document.getElementById('taskDescription').value = task.description || '';

            openModal('taskModal');
        }

        function deleteTask(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')) {
                tasks = tasks.filter(t => t.id !== id);
                renderTasksTable();
                updateStats();
                showAlert('Tâche supprimée avec succès', 'success');
            }
        }

        // Génération du rapport CRM
        function generateCRMReport() {
            const totalClients = clients.length;
            const activeClients = clients.filter(c => c.status === 'active' || c.status === 'vip').length;
            const prospects = clients.filter(c => c.status === 'prospect').length;
            const conversionRate = prospects > 0 ? ((activeClients / (activeClients + prospects)) * 100).toFixed(1) : 0;

            // Analyse par secteur
            const sectorAnalysis = {};
            clients.forEach(client => {
                if (!sectorAnalysis[client.sector]) {
                    sectorAnalysis[client.sector] = { count: 0, totalSatisfaction: 0, totalRevenue: 0 };
                }
                sectorAnalysis[client.sector].count++;
                sectorAnalysis[client.sector].totalSatisfaction += client.satisfaction || 0;
                sectorAnalysis[client.sector].totalRevenue += client.revenue || 0;
            });

            // Analyse des interactions
            const interactionsByType = {};
            interactions.forEach(interaction => {
                if (!interactionsByType[interaction.type]) {
                    interactionsByType[interaction.type] = 0;
                }
                interactionsByType[interaction.type]++;
            });

            let reportContent = `
                <h2>Rapport CRM - ${new Date().toLocaleDateString()}</h2>

                <h3>Vue d'ensemble</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Métrique</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Valeur</th>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Total clients</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${totalClients}</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Clients actifs</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${activeClients}</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Prospects</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${prospects}</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Taux de conversion</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${conversionRate}%</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Total interactions</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${interactions.length}</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Tâches en cours</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${tasks.filter(t => t.status === 'pending' || t.status === 'in-progress').length}</td>
                    </tr>
                </table>

                <h3>Analyse par Secteur</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Secteur</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Clients</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Satisfaction Moy.</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">CA Total</th>
                    </tr>
            `;

            Object.keys(sectorAnalysis).forEach(sector => {
                const data = sectorAnalysis[sector];
                const avgSatisfaction = data.count > 0 ? (data.totalSatisfaction / data.count).toFixed(1) : 0;
                const sectorNames = {
                    'tech': 'Technologie',
                    'health': 'Santé',
                    'finance': 'Finance',
                    'industry': 'Industrie',
                    'commerce': 'Commerce',
                    'services': 'Services'
                };

                reportContent += `
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">${sectorNames[sector] || sector}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${data.count}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${avgSatisfaction}★</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${data.totalRevenue.toLocaleString()}€</td>
                    </tr>
                `;
            });

            reportContent += `
                </table>

                <h3>Répartition des Interactions</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Type</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Nombre</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Pourcentage</th>
                    </tr>
            `;

            Object.keys(interactionsByType).forEach(type => {
                const count = interactionsByType[type];
                const percentage = ((count / interactions.length) * 100).toFixed(1);
                const typeNames = {
                    'call': 'Appels téléphoniques',
                    'email': 'Emails',
                    'meeting': 'Réunions',
                    'visit': 'Visites clients',
                    'support': 'Support technique'
                };

                reportContent += `
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">${typeNames[type] || type}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${count}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${percentage}%</td>
                    </tr>
                `;
            });

            reportContent += `
                </table>
            `;

            // Ouvrir le rapport dans une nouvelle fenêtre
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Rapport CRM - ERP HUB</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 2rem; }
                        h2 { color: #8b5cf6; margin-bottom: 1rem; }
                        h3 { color: #374151; margin: 1.5rem 0 0.5rem 0; }
                        table { border-collapse: collapse; width: 100%; }
                        th, td { border: 1px solid #e5e7eb; padding: 0.5rem; }
                        th { background: #f9fafb; font-weight: 600; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${reportContent}
                    <div style="margin-top: 2rem; text-align: center;">
                        <button onclick="window.print()" style="padding: 0.5rem 1rem; background: #8b5cf6; color: white; border: none; border-radius: 0.25rem; cursor: pointer;">
                            Imprimer
                        </button>
                    </div>
                </body>
                </html>
            `);
            reportWindow.document.close();
        }

        // Gestionnaires d'événements pour les formulaires
        document.getElementById('clientForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const clientData = {
                name: formData.get('clientName'),
                company: formData.get('clientCompany'),
                sector: formData.get('clientSector'),
                revenue: parseFloat(formData.get('clientRevenue')) || 0,
                email: formData.get('clientEmail'),
                phone: formData.get('clientPhone'),
                status: formData.get('clientStatus'),
                satisfaction: parseFloat(formData.get('clientSatisfaction')) || 0,
                notes: formData.get('clientNotes')
            };

            try {
                if (editingClientId) {
                    // Modification
                    const index = clients.findIndex(c => c.id === editingClientId);
                    if (index !== -1) {
                        clients[index] = { ...clients[index], ...clientData };
                        showAlert('Client modifié avec succès', 'success');
                    }
                } else {
                    // Création
                    const newClient = {
                        id: `CLI-${String(clients.length + 1).padStart(3, '0')}`,
                        ...clientData
                    };
                    clients.push(newClient);
                    showAlert('Client créé avec succès', 'success');
                }

                renderClientsTable();
                updateStats();
                closeModal('clientModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'clientModalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        document.getElementById('interactionForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const clientId = formData.get('interactionClient');
            const client = clients.find(c => c.id === clientId);

            if (!client) {
                showAlert('Client non trouvé', 'error', 'interactionModalAlertContainer');
                isLoading = false;
                return;
            }

            const interactionData = {
                date: formData.get('interactionDate'),
                clientId: clientId,
                clientName: `${client.name} (${client.company})`,
                type: formData.get('interactionType'),
                duration: parseInt(formData.get('interactionDuration')) || 0,
                result: formData.get('interactionResult'),
                notes: formData.get('interactionNotes')
            };

            try {
                if (editingInteractionId) {
                    // Modification
                    const index = interactions.findIndex(i => i.id === editingInteractionId);
                    if (index !== -1) {
                        interactions[index] = { ...interactions[index], ...interactionData };
                        showAlert('Interaction modifiée avec succès', 'success');
                    }
                } else {
                    // Création
                    const newInteraction = {
                        id: `INT-${String(interactions.length + 1).padStart(3, '0')}`,
                        ...interactionData
                    };
                    interactions.push(newInteraction);
                    showAlert('Interaction créée avec succès', 'success');
                }

                renderInteractionsTable();
                closeModal('interactionModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'interactionModalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        document.getElementById('taskForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const clientId = formData.get('taskClient');
            const client = clients.find(c => c.id === clientId);

            if (!client) {
                showAlert('Client non trouvé', 'error', 'taskModalAlertContainer');
                isLoading = false;
                return;
            }

            const taskData = {
                dueDate: formData.get('taskDueDate'),
                clientId: clientId,
                clientName: `${client.name} (${client.company})`,
                title: formData.get('taskTitle'),
                priority: formData.get('taskPriority'),
                status: formData.get('taskStatus'),
                description: formData.get('taskDescription')
            };

            try {
                if (editingTaskId) {
                    // Modification
                    const index = tasks.findIndex(t => t.id === editingTaskId);
                    if (index !== -1) {
                        tasks[index] = { ...tasks[index], ...taskData };
                        showAlert('Tâche modifiée avec succès', 'success');
                    }
                } else {
                    // Création
                    const newTask = {
                        id: `TSK-${String(tasks.length + 1).padStart(3, '0')}`,
                        ...taskData
                    };
                    tasks.push(newTask);
                    showAlert('Tâche créée avec succès', 'success');
                }

                renderTasksTable();
                updateStats();
                closeModal('taskModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'taskModalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadCRMData();
            }
        });
    </script>
</body>
</html>
