<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Logistics - Gestion Logistique | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #f59e0b 30%, #d97706 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #f59e0b;
            color: white;
        }
        
        .btn-primary:hover {
            background: #d97706;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #f59e0b;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #f59e0b;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #f59e0b;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">🚚 Agent Logistics - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="openModal('shipmentModal')">
                <span class="material-icons" style="font-size: 1rem;">add</span>
                Nouvelle Livraison
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion Logistique</h1>
            <p class="page-subtitle">Transport, livraisons et suivi logistique</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalShipments">0</div>
                <div class="stat-label">Expéditions Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="inTransit">0</div>
                <div class="stat-label">En Transit</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="delivered">0</div>
                <div class="stat-label">Livrées</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgDeliveryTime">0j</div>
                <div class="stat-label">Délai Moyen</div>
            </div>
        </div>

        <!-- Liste des expéditions -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Suivi des Expéditions</h2>
                <button class="btn btn-primary" onclick="refreshShipments()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div id="alertContainer"></div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>N° Expédition</th>
                                <th>Destination</th>
                                <th>Transporteur</th>
                                <th>Date Expédition</th>
                                <th>Date Prévue</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="shipmentsTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Expédition -->
    <div id="shipmentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Nouvelle Expédition</h3>
                <button class="close-btn" onclick="closeModal('shipmentModal')">&times;</button>
            </div>
            <form id="shipmentForm">
                <div id="modalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="destination">Destination *</label>
                        <input type="text" id="destination" name="destination" class="form-input" required placeholder="Ville, Pays">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="carrier">Transporteur *</label>
                        <select id="carrier" name="carrier" class="form-select" required>
                            <option value="">Sélectionner un transporteur</option>
                            <option value="DHL Express">DHL Express</option>
                            <option value="FedEx">FedEx</option>
                            <option value="UPS">UPS</option>
                            <option value="Chronopost">Chronopost</option>
                            <option value="Colissimo">Colissimo</option>
                            <option value="Transport Local">Transport Local</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="shipDate">Date d'expédition *</label>
                        <input type="date" id="shipDate" name="shipDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="expectedDate">Date de livraison prévue</label>
                        <input type="date" id="expectedDate" name="expectedDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="trackingNumber">N° de suivi</label>
                        <input type="text" id="trackingNumber" name="trackingNumber" class="form-input" placeholder="Ex: 1Z999AA1234567890">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="weight">Poids (kg)</label>
                        <input type="number" id="weight" name="weight" class="form-input" min="0" step="0.1">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="cost">Coût transport (€)</label>
                        <input type="number" id="cost" name="cost" class="form-input" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="status">Statut</label>
                        <select id="status" name="status" class="form-select">
                            <option value="preparing">En préparation</option>
                            <option value="shipped" selected>Expédiée</option>
                            <option value="in_transit">En transit</option>
                            <option value="delivered">Livrée</option>
                            <option value="delayed">Retardée</option>
                            <option value="returned">Retournée</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="products">Produits expédiés</label>
                    <textarea id="products" name="products" class="form-textarea" placeholder="Description des produits expédiés..."></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label" for="notes">Notes</label>
                    <textarea id="notes" name="notes" class="form-textarea" placeholder="Notes sur l'expédition..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('shipmentModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveShipmentBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let shipments = [];
        let editingShipmentId = null;
        let isLoading = false;

        // Données de démonstration réalistes
        const demoShipments = [
            {
                id: 'EXP-2024-001',
                destination: 'Lyon, France',
                carrier: 'DHL Express',
                shipDate: '2024-01-15',
                expectedDate: '2024-01-17',
                actualDate: '2024-01-17',
                trackingNumber: '1Z999AA1234567890',
                weight: 15.5,
                cost: 45.80,
                status: 'delivered',
                products: 'Ordinateurs portables Dell (x10)',
                notes: 'Livraison express, signature requise'
            },
            {
                id: 'EXP-2024-002',
                destination: 'Marseille, France',
                carrier: 'Chronopost',
                shipDate: '2024-01-20',
                expectedDate: '2024-01-22',
                actualDate: null,
                trackingNumber: 'CP123456789FR',
                weight: 8.2,
                cost: 28.50,
                status: 'in_transit',
                products: 'Fournitures de bureau',
                notes: 'Livraison standard, point relais'
            },
            {
                id: 'EXP-2024-003',
                destination: 'Toulouse, France',
                carrier: 'UPS',
                shipDate: '2024-01-22',
                expectedDate: '2024-01-25',
                actualDate: null,
                trackingNumber: 'UPS987654321',
                weight: 25.0,
                cost: 65.00,
                status: 'shipped',
                products: 'Pièces industrielles',
                notes: 'Matériel fragile, manipulation avec précaution'
            },
            {
                id: 'EXP-2024-004',
                destination: 'Bordeaux, France',
                carrier: 'FedEx',
                shipDate: '2024-01-18',
                expectedDate: '2024-01-20',
                actualDate: '2024-01-19',
                trackingNumber: 'FX456789123',
                weight: 12.8,
                cost: 38.90,
                status: 'delivered',
                products: 'Écrans et accessoires informatiques',
                notes: 'Livraison anticipée, client satisfait'
            },
            {
                id: 'EXP-2024-005',
                destination: 'Lille, France',
                carrier: 'Colissimo',
                shipDate: '2024-01-25',
                expectedDate: '2024-01-28',
                actualDate: null,
                trackingNumber: 'COL789123456',
                weight: 5.5,
                cost: 18.20,
                status: 'preparing',
                products: 'Mobilier de bureau (chaises)',
                notes: 'En attente d\'emballage spécialisé'
            },
            {
                id: 'EXP-2024-006',
                destination: 'Nantes, France',
                carrier: 'Transport Local',
                shipDate: '2024-01-12',
                expectedDate: '2024-01-15',
                actualDate: '2024-01-16',
                trackingNumber: 'TL202401001',
                weight: 45.0,
                cost: 120.00,
                status: 'delivered',
                products: 'Serveur Dell PowerEdge',
                notes: 'Transport spécialisé, installation sur site'
            },
            {
                id: 'EXP-2024-007',
                destination: 'Strasbourg, France',
                carrier: 'DHL Express',
                shipDate: '2024-01-28',
                expectedDate: '2024-01-30',
                actualDate: null,
                trackingNumber: 'DHL567890123',
                weight: 18.7,
                cost: 52.30,
                status: 'delayed',
                products: 'Matières premières aluminium',
                notes: 'Retard dû aux conditions météorologiques'
            },
            {
                id: 'EXP-2024-008',
                destination: 'Rennes, France',
                carrier: 'Chronopost',
                shipDate: '2024-01-26',
                expectedDate: '2024-01-29',
                actualDate: null,
                trackingNumber: 'CP890123456FR',
                weight: 3.2,
                cost: 15.80,
                status: 'in_transit',
                products: 'Licences logicielles Microsoft',
                notes: 'Envoi dématérialisé avec support physique'
            },
            {
                id: 'EXP-2024-009',
                destination: 'Nice, France',
                carrier: 'UPS',
                shipDate: '2024-01-10',
                expectedDate: '2024-01-13',
                actualDate: '2024-01-15',
                trackingNumber: 'UPS345678901',
                weight: 22.5,
                cost: 58.70,
                status: 'returned',
                products: 'Emballages personnalisés',
                notes: 'Retour expéditeur - adresse incorrecte'
            },
            {
                id: 'EXP-2024-010',
                destination: 'Montpellier, France',
                carrier: 'FedEx',
                shipDate: '2024-01-30',
                expectedDate: '2024-02-02',
                actualDate: null,
                trackingNumber: 'FX234567890',
                weight: 9.8,
                cost: 32.40,
                status: 'shipped',
                products: 'Outillage spécialisé',
                notes: 'Livraison express demandée par le client'
            }
        ];

        function showAlert(message, type = 'error', container = 'alertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            if (modalId === 'shipmentModal') {
                document.getElementById('modalTitle').textContent = editingShipmentId ? 'Modifier Expédition' : 'Nouvelle Expédition';
                // Définir la date d'aujourd'hui par défaut
                if (!editingShipmentId) {
                    document.getElementById('shipDate').value = new Date().toISOString().split('T')[0];
                }
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            if (modalId === 'shipmentModal') {
                document.getElementById('shipmentForm').reset();
                document.getElementById('modalAlertContainer').innerHTML = '';
                editingShipmentId = null;
            }
        }

        function updateStats() {
            const total = shipments.length;
            const inTransit = shipments.filter(s => ['shipped', 'in_transit'].includes(s.status)).length;
            const delivered = shipments.filter(s => s.status === 'delivered').length;

            // Calcul du délai moyen de livraison
            const deliveredShipments = shipments.filter(s => s.status === 'delivered' && s.actualDate);
            let avgDeliveryTime = 0;
            if (deliveredShipments.length > 0) {
                const totalDays = deliveredShipments.reduce((sum, s) => {
                    const shipDate = new Date(s.shipDate);
                    const deliveryDate = new Date(s.actualDate);
                    const diffTime = Math.abs(deliveryDate - shipDate);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    return sum + diffDays;
                }, 0);
                avgDeliveryTime = Math.round(totalDays / deliveredShipments.length);
            }

            document.getElementById('totalShipments').textContent = total;
            document.getElementById('inTransit').textContent = inTransit;
            document.getElementById('delivered').textContent = delivered;
            document.getElementById('avgDeliveryTime').textContent = avgDeliveryTime + 'j';
        }

        function renderShipmentsTable() {
            const tbody = document.getElementById('shipmentsTableBody');

            if (shipments.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune expédition trouvée. Cliquez sur "Nouvelle Livraison" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = shipments.map(shipment => {
                const statusBadge = getStatusBadge(shipment.status);
                return `
                    <tr>
                        <td>${shipment.id}</td>
                        <td>${shipment.destination}</td>
                        <td>${shipment.carrier}</td>
                        <td>${new Date(shipment.shipDate).toLocaleDateString()}</td>
                        <td>${shipment.expectedDate ? new Date(shipment.expectedDate).toLocaleDateString() : '-'}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editShipment('${shipment.id}')" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteShipment('${shipment.id}')" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getStatusBadge(status) {
            const badges = {
                'preparing': '<span class="badge badge-info">En préparation</span>',
                'shipped': '<span class="badge badge-warning">Expédiée</span>',
                'in_transit': '<span class="badge badge-warning">En transit</span>',
                'delivered': '<span class="badge badge-success">Livrée</span>',
                'delayed': '<span class="badge badge-danger">Retardée</span>',
                'returned': '<span class="badge badge-danger">Retournée</span>'
            };
            return badges[status] || '<span class="badge badge-danger">Inconnu</span>';
        }

        async function loadShipments() {
            try {
                // Tentative de chargement depuis l'API
                const token = localStorage.getItem('access_token');
                if (token) {
                    const response = await fetch('http://localhost:8000/api/agents/logistics/shipments/', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        shipments = await response.json();
                    } else {
                        throw new Error('Erreur API');
                    }
                } else {
                    throw new Error('Pas de token');
                }
            } catch (error) {
                console.log('Chargement des données de démonstration');
                shipments = [...demoShipments];
            }

            renderShipmentsTable();
            updateStats();
        }

        function refreshShipments() {
            loadShipments();
            showAlert('Données actualisées avec succès', 'success');
        }

        function editShipment(id) {
            const shipment = shipments.find(s => s.id === id);
            if (!shipment) return;

            editingShipmentId = id;

            // Remplir le formulaire
            document.getElementById('destination').value = shipment.destination;
            document.getElementById('carrier').value = shipment.carrier;
            document.getElementById('shipDate').value = shipment.shipDate;
            document.getElementById('expectedDate').value = shipment.expectedDate || '';
            document.getElementById('trackingNumber').value = shipment.trackingNumber || '';
            document.getElementById('weight').value = shipment.weight || '';
            document.getElementById('cost').value = shipment.cost || '';
            document.getElementById('status').value = shipment.status;
            document.getElementById('products').value = shipment.products || '';
            document.getElementById('notes').value = shipment.notes || '';

            openModal('shipmentModal');
        }

        function deleteShipment(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette expédition ?')) {
                shipments = shipments.filter(s => s.id !== id);
                renderShipmentsTable();
                updateStats();
                showAlert('Expédition supprimée avec succès', 'success');
            }
        }

        // Gestion du formulaire
        document.getElementById('shipmentForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const shipmentData = {
                destination: formData.get('destination'),
                carrier: formData.get('carrier'),
                shipDate: formData.get('shipDate'),
                expectedDate: formData.get('expectedDate'),
                trackingNumber: formData.get('trackingNumber'),
                weight: formData.get('weight') ? parseFloat(formData.get('weight')) : null,
                cost: formData.get('cost') ? parseFloat(formData.get('cost')) : null,
                status: formData.get('status'),
                products: formData.get('products'),
                notes: formData.get('notes')
            };

            try {
                if (editingShipmentId) {
                    // Modification
                    const index = shipments.findIndex(s => s.id === editingShipmentId);
                    if (index !== -1) {
                        shipments[index] = { ...shipments[index], ...shipmentData };
                        showAlert('Expédition modifiée avec succès', 'success');
                    }
                } else {
                    // Création
                    const newShipment = {
                        id: `EXP-2024-${String(shipments.length + 1).padStart(3, '0')}`,
                        ...shipmentData
                    };
                    shipments.push(newShipment);
                    showAlert('Expédition créée avec succès', 'success');
                }

                renderShipmentsTable();
                updateStats();
                closeModal('shipmentModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'modalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadShipments();
            }
        });
    </script>
</body>
</html>
