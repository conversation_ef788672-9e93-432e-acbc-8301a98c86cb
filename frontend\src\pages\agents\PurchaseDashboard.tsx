import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { SimpleMetricCard } from '../../components/ui/SimpleMetricCard';
import { SimpleChart } from '../../components/ui/SimpleChart';

interface PurchaseMetrics {
  totalOrders: number;
  totalValue: number;
  pendingOrders: number;
  averageDeliveryTime: number;
  supplierCount: number;
  savingsThisMonth: number;
}

interface Supplier {
  id: string;
  name: string;
  rating: number;
  orders: number;
  totalValue: number;
  deliveryTime: number;
  status: 'excellent' | 'good' | 'warning';
}

interface PurchaseOrder {
  id: string;
  supplier: string;
  items: string;
  value: number;
  status: 'pending' | 'approved' | 'delivered' | 'cancelled';
  orderDate: string;
  expectedDelivery: string;
  urgency: 'low' | 'medium' | 'high';
}

export const PurchaseDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PurchaseMetrics>({
    totalOrders: 156,
    totalValue: 1247500,
    pendingOrders: 23,
    averageDeliveryTime: 7.5,
    supplierCount: 45,
    savingsThisMonth: 85000
  });

  const [topSuppliers] = useState<Supplier[]>([
    { id: '1', name: 'TechSupply Pro', rating: 4.8, orders: 28, totalValue: 450000, deliveryTime: 5, status: 'excellent' },
    { id: '2', name: 'Global Materials', rating: 4.5, orders: 22, totalValue: 320000, deliveryTime: 7, status: 'good' },
    { id: '3', name: 'Express Components', rating: 4.2, orders: 18, totalValue: 280000, deliveryTime: 6, status: 'good' },
    { id: '4', name: 'Budget Supplies', rating: 3.8, orders: 15, totalValue: 180000, deliveryTime: 12, status: 'warning' },
    { id: '5', name: 'Premium Parts', rating: 4.9, orders: 12, totalValue: 380000, deliveryTime: 4, status: 'excellent' }
  ]);

  const [recentOrders] = useState<PurchaseOrder[]>([
    { id: 'PO-001', supplier: 'TechSupply Pro', items: 'Processeurs Intel (x50)', value: 125000, status: 'pending', orderDate: '2024-01-27', expectedDelivery: '2024-02-03', urgency: 'high' },
    { id: 'PO-002', supplier: 'Global Materials', items: 'Écrans Dell (x25)', value: 87500, status: 'approved', orderDate: '2024-01-26', expectedDelivery: '2024-02-05', urgency: 'medium' },
    { id: 'PO-003', supplier: 'Express Components', items: 'Câbles réseau (x200)', value: 15000, status: 'delivered', orderDate: '2024-01-25', expectedDelivery: '2024-01-30', urgency: 'low' },
    { id: 'PO-004', supplier: 'Premium Parts', items: 'Serveurs HP (x5)', value: 95000, status: 'pending', orderDate: '2024-01-24', expectedDelivery: '2024-02-01', urgency: 'high' },
    { id: 'PO-005', supplier: 'Budget Supplies', items: 'Fournitures bureau', value: 8500, status: 'approved', orderDate: '2024-01-23', expectedDelivery: '2024-02-10', urgency: 'low' }
  ]);

  const [purchaseData] = useState([
    { name: 'Jan', value: 980000, value2: 850000 },
    { name: 'Fév', value: 1120000, value2: 950000 },
    { name: 'Mar', value: 1050000, value2: 920000 },
    { name: 'Avr', value: 1180000, value2: 1000000 },
    { name: 'Mai', value: 1350000, value2: 1150000 },
    { name: 'Jun', value: 1280000, value2: 1100000 },
    { name: 'Jul', value: 1247500, value2: 1080000 }
  ]);

  const [supplierData] = useState([
    { name: 'TechSupply', value: 450 },
    { name: 'Global Mat.', value: 320 },
    { name: 'Express', value: 280 },
    { name: 'Premium', value: 380 },
    { name: 'Budget', value: 180 },
    { name: 'Autres', value: 240 }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getSupplierStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center text-3xl">
              🛒
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Agent Purchase</h1>
              <p className="text-gray-600 text-lg">Gestion des achats et approvisionnements</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              🔄 Actualiser
            </button>
            <button className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
              📊 Rapport
            </button>
          </div>
        </div>
      </motion.div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <SimpleMetricCard
          title="Commandes Total"
          value={metrics.totalOrders}
          icon="📋"
          color="blue"
          trend="up"
          trendValue={8.2}
        />
        <SimpleMetricCard
          title="Valeur Achats"
          value={`${(metrics.totalValue / 1000000).toFixed(1)}M€`}
          icon="💰"
          color="green"
          trend="up"
          trendValue={12.5}
        />
        <SimpleMetricCard
          title="En Attente"
          value={metrics.pendingOrders}
          icon="⏳"
          color="yellow"
          trend="down"
          trendValue={-5.3}
        />
        <SimpleMetricCard
          title="Délai Moyen"
          value={metrics.averageDeliveryTime}
          unit="j"
          icon="🚚"
          color="purple"
          trend="down"
          trendValue={-8.1}
        />
        <SimpleMetricCard
          title="Fournisseurs"
          value={metrics.supplierCount}
          icon="🏢"
          color="blue"
          trend="up"
          trendValue={3.2}
        />
        <SimpleMetricCard
          title="Économies"
          value={`${(metrics.savingsThisMonth / 1000).toFixed(0)}k€`}
          icon="💎"
          color="green"
          trend="up"
          trendValue={25.7}
        />
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <SimpleChart
            title="Évolution des Achats (Budget vs Réalisé)"
            data={purchaseData}
            type="line"
            height={350}
            color="#EA580C"
          />
        </div>
        <div>
          <SimpleChart
            title="Répartition par Fournisseur (k€)"
            data={supplierData}
            type="bar"
            height={350}
            color="#FB923C"
          />
        </div>
      </div>

      {/* Top fournisseurs et commandes récentes */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top fournisseurs */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold mb-4">Top Fournisseurs</h3>
          <div className="space-y-4">
            {topSuppliers.map((supplier, index) => (
              <motion.div
                key={supplier.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-semibold">
                    {supplier.name.charAt(0)}
                  </div>
                  <div>
                    <div className="font-semibold">{supplier.name}</div>
                    <div className="text-sm text-gray-600">
                      {supplier.orders} commandes • {supplier.deliveryTime}j délai
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{(supplier.totalValue / 1000).toFixed(0)}k€</div>
                  <div className={`text-sm ${getSupplierStatusColor(supplier.status)}`}>
                    ⭐ {supplier.rating}/5
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Commandes récentes */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold mb-4">Commandes Récentes</h3>
          <div className="space-y-4">
            {recentOrders.map((order, index) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-blue-600">{order.id}</span>
                    <div className={`w-2 h-2 rounded-full ${getUrgencyColor(order.urgency)}`}></div>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                    {order.status}
                  </span>
                </div>
                <div className="text-sm text-gray-600 mb-1">{order.supplier}</div>
                <div className="text-sm mb-2">{order.items}</div>
                <div className="flex justify-between items-center text-sm">
                  <span className="font-semibold">{order.value.toLocaleString('fr-FR')}€</span>
                  <span className="text-gray-500">
                    Livraison: {new Date(order.expectedDelivery).toLocaleDateString('fr-FR')}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
