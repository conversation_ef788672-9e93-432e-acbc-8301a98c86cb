"""
Serializers pour l'Agent Purchase
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
from .models import (
    Supplier, ProductCategory, PurchaseRequest, PurchaseRequestItem,
    PurchaseOrder, PurchaseOrderItem, GoodsReceipt, GoodsReceiptItem,
    SupplierInvoice
)

User = get_user_model()


class SupplierSerializer(serializers.ModelSerializer):
    """Serializer pour les fournisseurs"""
    buyer_name = serializers.CharField(source='buyer.get_full_name', read_only=True)
    overall_rating = serializers.ReadOnlyField()
    
    class Meta:
        model = Supplier
        fields = [
            'id', 'supplier_code', 'supplier_type', 'name', 'contact_person',
            'email', 'phone', 'website', 'address_line1', 'address_line2',
            'city', 'postal_code', 'country', 'payment_terms', 'currency',
            'quality_rating', 'delivery_rating', 'service_rating', 'overall_rating',
            'buyer', 'buyer_name', 'is_active', 'is_approved', 'bank_name',
            'bank_account', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'supplier_code', 'created_at', 'updated_at']


class ProductCategorySerializer(serializers.ModelSerializer):
    """Serializer pour les catégories de produits"""
    buyer_name = serializers.CharField(source='buyer.get_full_name', read_only=True)
    
    class Meta:
        model = ProductCategory
        fields = [
            'id', 'name', 'code', 'description', 'parent_category',
            'buyer', 'buyer_name', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class PurchaseRequestItemSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes de demande d'achat"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    suggested_supplier_name = serializers.CharField(source='suggested_supplier.name', read_only=True)
    
    class Meta:
        model = PurchaseRequestItem
        fields = [
            'id', 'item_name', 'description', 'category', 'category_name',
            'quantity', 'unit_of_measure', 'estimated_unit_price', 'estimated_total',
            'suggested_supplier', 'suggested_supplier_name', 'specifications',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'estimated_total', 'created_at', 'updated_at']


class PurchaseRequestSerializer(serializers.ModelSerializer):
    """Serializer pour les demandes d'achat"""
    requester_name = serializers.CharField(source='requester.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    buyer_name = serializers.CharField(source='buyer.get_full_name', read_only=True)
    items = PurchaseRequestItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = PurchaseRequest
        fields = [
            'id', 'request_number', 'requester', 'requester_name', 'title',
            'description', 'justification', 'requested_date', 'required_date',
            'priority', 'status', 'estimated_total', 'approved_by', 'approved_by_name',
            'approved_at', 'rejection_reason', 'buyer', 'buyer_name', 'items',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'request_number', 'estimated_total', 'created_at', 'updated_at']


class PurchaseRequestCreateSerializer(serializers.Serializer):
    """Serializer pour la création de demandes d'achat"""
    title = serializers.CharField(max_length=200)
    description = serializers.CharField(required=False, allow_blank=True)
    justification = serializers.CharField(required=False, allow_blank=True)
    requested_date = serializers.DateField(required=False)
    required_date = serializers.DateField()
    priority = serializers.IntegerField(min_value=1, max_value=5, default=3)
    items = serializers.ListField(
        child=serializers.DictField(),
        min_length=1,
        help_text="Liste des articles à acheter"
    )
    
    def validate_items(self, value):
        """Valide les lignes de demande"""
        for item in value:
            required_fields = ['item_name', 'quantity']
            for field in required_fields:
                if field not in item:
                    raise serializers.ValidationError(f"Le champ '{field}' est requis pour chaque ligne")
            
            # Validation des types
            try:
                float(item['quantity'])
                if 'estimated_unit_price' in item and item['estimated_unit_price']:
                    float(item['estimated_unit_price'])
            except (ValueError, TypeError):
                raise serializers.ValidationError("Les valeurs numériques doivent être valides")
        
        return value


class PurchaseOrderItemSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes de bon de commande"""
    request_item_name = serializers.CharField(source='request_item.item_name', read_only=True)
    quantity_pending = serializers.ReadOnlyField()
    is_fully_received = serializers.ReadOnlyField()
    
    class Meta:
        model = PurchaseOrderItem
        fields = [
            'id', 'request_item', 'request_item_name', 'item_name', 'description',
            'supplier_reference', 'quantity_ordered', 'quantity_received',
            'unit_of_measure', 'unit_price', 'discount_percentage', 'line_total',
            'expected_delivery_date', 'quantity_pending', 'is_fully_received',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'line_total', 'quantity_received', 'created_at', 'updated_at']


class PurchaseOrderSerializer(serializers.ModelSerializer):
    """Serializer pour les bons de commande"""
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    buyer_name = serializers.CharField(source='buyer.get_full_name', read_only=True)
    purchase_request_number = serializers.CharField(source='purchase_request.request_number', read_only=True)
    items = PurchaseOrderItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = PurchaseOrder
        fields = [
            'id', 'order_number', 'supplier', 'supplier_name', 'purchase_request',
            'purchase_request_number', 'buyer', 'buyer_name', 'order_date',
            'expected_delivery_date', 'actual_delivery_date', 'status',
            'subtotal', 'tax_amount', 'total_amount', 'payment_terms',
            'delivery_address', 'notes', 'terms_conditions', 'items',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'order_number', 'subtotal', 'tax_amount', 'total_amount', 'created_at', 'updated_at']


class PurchaseOrderCreateSerializer(serializers.Serializer):
    """Serializer pour la création de bons de commande"""
    supplier_id = serializers.UUIDField()
    purchase_request_id = serializers.UUIDField(required=False)
    order_date = serializers.DateField(required=False)
    expected_delivery_date = serializers.DateField()
    payment_terms = serializers.IntegerField(default=30)
    delivery_address = serializers.CharField(required=False, allow_blank=True)
    notes = serializers.CharField(required=False, allow_blank=True)
    terms_conditions = serializers.CharField(required=False, allow_blank=True)
    items = serializers.ListField(
        child=serializers.DictField(),
        min_length=1,
        help_text="Liste des lignes de commande"
    )
    
    def validate_items(self, value):
        """Valide les lignes de commande"""
        for item in value:
            required_fields = ['item_name', 'quantity_ordered', 'unit_price']
            for field in required_fields:
                if field not in item:
                    raise serializers.ValidationError(f"Le champ '{field}' est requis pour chaque ligne")
            
            # Validation des types
            try:
                float(item['quantity_ordered'])
                float(item['unit_price'])
                if 'discount_percentage' in item:
                    discount = float(item['discount_percentage'])
                    if not 0 <= discount <= 100:
                        raise serializers.ValidationError("La remise doit être entre 0 et 100%")
            except (ValueError, TypeError):
                raise serializers.ValidationError("Les valeurs numériques doivent être valides")
        
        return value


class GoodsReceiptItemSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes de réception"""
    order_item_name = serializers.CharField(source='order_item.item_name', read_only=True)
    
    class Meta:
        model = GoodsReceiptItem
        fields = [
            'id', 'order_item', 'order_item_name', 'quantity_expected',
            'quantity_received', 'quantity_accepted', 'quantity_rejected',
            'quality_status', 'comments', 'rejection_reason',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class GoodsReceiptSerializer(serializers.ModelSerializer):
    """Serializer pour les réceptions de marchandises"""
    purchase_order_number = serializers.CharField(source='purchase_order.order_number', read_only=True)
    received_by_name = serializers.CharField(source='received_by.get_full_name', read_only=True)
    items = GoodsReceiptItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = GoodsReceipt
        fields = [
            'id', 'receipt_number', 'purchase_order', 'purchase_order_number',
            'receipt_date', 'delivery_note_number', 'received_by', 'received_by_name',
            'status', 'quality_check_passed', 'comments', 'quality_notes',
            'items', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'receipt_number', 'created_at', 'updated_at']


class SupplierInvoiceSerializer(serializers.ModelSerializer):
    """Serializer pour les factures fournisseurs"""
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    purchase_order_number = serializers.CharField(source='purchase_order.order_number', read_only=True)
    validated_by_name = serializers.CharField(source='validated_by.get_full_name', read_only=True)
    
    class Meta:
        model = SupplierInvoice
        fields = [
            'id', 'invoice_number', 'supplier_invoice_number', 'supplier',
            'supplier_name', 'purchase_order', 'purchase_order_number',
            'invoice_date', 'due_date', 'received_date', 'subtotal',
            'tax_amount', 'total_amount', 'status', 'validated_by',
            'validated_by_name', 'validated_at', 'payment_date',
            'payment_reference', 'comments', 'dispute_reason',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'invoice_number', 'created_at', 'updated_at']


class PurchaseDashboardSerializer(serializers.Serializer):
    """Serializer pour le dashboard Purchase"""
    tenant = serializers.CharField()
    timestamp = serializers.CharField()
    requests = serializers.DictField()
    orders = serializers.DictField()
    suppliers = serializers.DictField()
    invoices = serializers.DictField()
    performance = serializers.DictField()
    recent_activities = serializers.ListField()


class PurchaseRequestApprovalSerializer(serializers.Serializer):
    """Serializer pour l'approbation de demandes d'achat"""
    action = serializers.ChoiceField(choices=['approve', 'reject'])
    comments = serializers.CharField(required=False, allow_blank=True)


class SupplierPerformanceSerializer(serializers.Serializer):
    """Serializer pour les performances fournisseurs"""
    suppliers = serializers.ListField()
    summary = serializers.DictField()


class PurchaseInsightSerializer(serializers.Serializer):
    """Serializer pour les insights d'achat"""
    type = serializers.CharField()
    priority = serializers.ChoiceField(choices=['high', 'medium', 'low'])
    title = serializers.CharField()
    description = serializers.CharField()
    recommendation = serializers.CharField()


class CostSavingsSerializer(serializers.Serializer):
    """Serializer pour les opportunités d'économies"""
    opportunities = serializers.ListField()
    total_potential_savings = serializers.FloatField()
    count = serializers.IntegerField()


class GoodsReceiptCreateSerializer(serializers.Serializer):
    """Serializer pour la création de réceptions"""
    purchase_order_id = serializers.UUIDField()
    receipt_date = serializers.DateField(required=False)
    delivery_note_number = serializers.CharField(required=False, allow_blank=True)
    comments = serializers.CharField(required=False, allow_blank=True)
    items = serializers.ListField(
        child=serializers.DictField(),
        min_length=1,
        help_text="Liste des lignes de réception"
    )
    
    def validate_items(self, value):
        """Valide les lignes de réception"""
        for item in value:
            required_fields = ['order_item_id', 'quantity_expected', 'quantity_received']
            for field in required_fields:
                if field not in item:
                    raise serializers.ValidationError(f"Le champ '{field}' est requis pour chaque ligne")
            
            # Validation des quantités
            try:
                expected = float(item['quantity_expected'])
                received = float(item['quantity_received'])
                if received < 0:
                    raise serializers.ValidationError("La quantité reçue ne peut pas être négative")
                if received > expected * 1.1:  # Tolérance de 10%
                    raise serializers.ValidationError("La quantité reçue ne peut pas dépasser significativement la quantité attendue")
            except (ValueError, TypeError):
                raise serializers.ValidationError("Les quantités doivent être des valeurs numériques valides")
        
        return value
