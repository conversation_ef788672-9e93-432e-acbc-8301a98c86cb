#!/bin/bash

# Script de sauvegarde automatique pour ERP HUB
# Usage: ./backup.sh [type] [retention_days]

set -e

# Configuration
BACKUP_TYPE=${1:-full}
RETENTION_DAYS=${2:-30}
BACKUP_DIR="/backups"
PROJECT_NAME="erp-hub"
DATE=$(date +%Y%m%d_%H%M%S)

# Couleurs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Création du répertoire de sauvegarde
create_backup_dir() {
    mkdir -p "$BACKUP_DIR"/{database,media,logs,config}
    log "Répertoires de sauvegarde créés"
}

# Sauvegarde de la base de données
backup_database() {
    log "Sauvegarde de la base de données..."
    
    local backup_file="$BACKUP_DIR/database/db_backup_$DATE.sql"
    local compressed_file="$backup_file.gz"
    
    # Sauvegarde PostgreSQL
    docker exec "${PROJECT_NAME}_postgres_prod" pg_dump \
        -U erp_user_prod \
        -h localhost \
        -p 5432 \
        --verbose \
        --clean \
        --no-owner \
        --no-privileges \
        erp_hub_prod > "$backup_file"
    
    # Compression
    gzip "$backup_file"
    
    log "✅ Base de données sauvegardée: $compressed_file"
    echo "$compressed_file"
}

# Sauvegarde des fichiers media
backup_media() {
    log "Sauvegarde des fichiers media..."
    
    local backup_file="$BACKUP_DIR/media/media_backup_$DATE.tar.gz"
    
    # Sauvegarde du volume media
    docker run --rm \
        -v "${PROJECT_NAME}_media_files:/data" \
        -v "$BACKUP_DIR/media:/backup" \
        alpine:latest \
        tar czf "/backup/media_backup_$DATE.tar.gz" -C /data .
    
    log "✅ Fichiers media sauvegardés: $backup_file"
    echo "$backup_file"
}

# Sauvegarde des logs
backup_logs() {
    log "Sauvegarde des logs..."
    
    local backup_file="$BACKUP_DIR/logs/logs_backup_$DATE.tar.gz"
    
    # Sauvegarde des logs
    if [ -d "./logs" ]; then
        tar czf "$backup_file" -C . logs/
        log "✅ Logs sauvegardés: $backup_file"
        echo "$backup_file"
    else
        warning "Répertoire logs non trouvé"
    fi
}

# Sauvegarde de la configuration
backup_config() {
    log "Sauvegarde de la configuration..."
    
    local backup_file="$BACKUP_DIR/config/config_backup_$DATE.tar.gz"
    
    # Fichiers de configuration à sauvegarder
    local config_files=(
        "docker-compose.prod.yml"
        "nginx/"
        "monitoring/"
        ".env.production"
    )
    
    # Créer l'archive
    tar czf "$backup_file" "${config_files[@]}" 2>/dev/null || true
    
    log "✅ Configuration sauvegardée: $backup_file"
    echo "$backup_file"
}

# Sauvegarde complète
full_backup() {
    log "🚀 Début de la sauvegarde complète"
    
    create_backup_dir
    
    local db_backup=$(backup_database)
    local media_backup=$(backup_media)
    local logs_backup=$(backup_logs)
    local config_backup=$(backup_config)
    
    # Créer un fichier de métadonnées
    local metadata_file="$BACKUP_DIR/backup_metadata_$DATE.json"
    cat > "$metadata_file" << EOF
{
    "timestamp": "$(date -Iseconds)",
    "type": "full",
    "files": {
        "database": "$(basename "$db_backup")",
        "media": "$(basename "$media_backup")",
        "logs": "$(basename "$logs_backup")",
        "config": "$(basename "$config_backup")"
    },
    "size": {
        "database": "$(du -h "$db_backup" | cut -f1)",
        "media": "$(du -h "$media_backup" | cut -f1)",
        "logs": "$(du -h "$logs_backup" | cut -f1)",
        "config": "$(du -h "$config_backup" | cut -f1)"
    }
}
EOF
    
    log "✅ Métadonnées créées: $metadata_file"
    log "🎉 Sauvegarde complète terminée"
}

# Sauvegarde incrémentale (base de données uniquement)
incremental_backup() {
    log "🚀 Début de la sauvegarde incrémentale"
    
    create_backup_dir
    backup_database
    
    log "🎉 Sauvegarde incrémentale terminée"
}

# Nettoyage des anciennes sauvegardes
cleanup_old_backups() {
    log "Nettoyage des sauvegardes anciennes (> $RETENTION_DAYS jours)..."
    
    # Supprimer les fichiers plus anciens que RETENTION_DAYS
    find "$BACKUP_DIR" -type f -mtime +$RETENTION_DAYS -delete
    
    # Supprimer les répertoires vides
    find "$BACKUP_DIR" -type d -empty -delete
    
    log "✅ Nettoyage terminé"
}

# Vérification de l'intégrité
verify_backup() {
    local backup_file=$1
    
    if [ -f "$backup_file" ]; then
        if [[ "$backup_file" == *.gz ]]; then
            if gzip -t "$backup_file"; then
                log "✅ Intégrité vérifiée: $(basename "$backup_file")"
                return 0
            else
                error "❌ Fichier corrompu: $(basename "$backup_file")"
                return 1
            fi
        else
            log "✅ Fichier présent: $(basename "$backup_file")"
            return 0
        fi
    else
        error "❌ Fichier manquant: $(basename "$backup_file")"
        return 1
    fi
}

# Upload vers S3 (optionnel)
upload_to_s3() {
    if [ -n "$AWS_S3_BACKUP_BUCKET" ]; then
        log "Upload vers S3..."
        
        aws s3 sync "$BACKUP_DIR" "s3://$AWS_S3_BACKUP_BUCKET/erp-hub-backups/" \
            --exclude "*" \
            --include "*_$DATE.*" \
            --storage-class STANDARD_IA
        
        log "✅ Upload S3 terminé"
    fi
}

# Notification (optionnel)
send_notification() {
    local status=$1
    local message=$2
    
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"ERP HUB Backup $status: $message\"}" \
            "$SLACK_WEBHOOK_URL"
    fi
    
    if [ -n "$EMAIL_NOTIFICATION" ]; then
        echo "$message" | mail -s "ERP HUB Backup $status" "$EMAIL_NOTIFICATION"
    fi
}

# Fonction principale
main() {
    log "🚀 Début de la sauvegarde ERP HUB"
    log "Type: $BACKUP_TYPE, Rétention: $RETENTION_DAYS jours"
    
    case "$BACKUP_TYPE" in
        "full")
            full_backup
            ;;
        "incremental")
            incremental_backup
            ;;
        "database")
            create_backup_dir
            backup_database
            ;;
        "media")
            create_backup_dir
            backup_media
            ;;
        *)
            error "Type de sauvegarde non supporté: $BACKUP_TYPE"
            ;;
    esac
    
    cleanup_old_backups
    upload_to_s3
    
    send_notification "SUCCESS" "Sauvegarde $BACKUP_TYPE terminée avec succès"
    
    log "🎉 Sauvegarde terminée avec succès"
}

# Gestion des erreurs
trap 'send_notification "FAILED" "Sauvegarde échouée"; error "Sauvegarde interrompue"' INT TERM ERR

# Exécution
main "$@"
