<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Stock - Inventaire | ERP HUB PostgreSQL</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #8b5cf6 30%, #7c3aed 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #8b5cf6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #7c3aed;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #8b5cf6;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-size: 0.875rem; /* Réduction de la taille de police */
        }

        .data-table th,
        .data-table td {
            padding: 0.5rem; /* Réduction du padding de 1rem à 0.5rem */
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            line-height: 1.25; /* Réduction de la hauteur de ligne */
            vertical-align: middle;
        }

        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 0.8125rem; /* Police encore plus petite pour les en-têtes */
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .data-table tbody tr {
            height: 2.5rem; /* Hauteur fixe pour les lignes */
        }
        
        .badge {
            display: inline-block;
            padding: 0.125rem 0.5rem; /* Réduction du padding des badges */
            border-radius: 0.75rem;
            font-size: 0.6875rem; /* Police plus petite pour les badges */
            font-weight: 600;
            line-height: 1.2;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #8b5cf6;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #fde68a;
            color: #92400e;
        }
        
        .alert-info {
            background: #dbeafe;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-connected {
            background: #10b981;
        }
        
        .status-disconnected {
            background: #ef4444;
        }
        
        .nav-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0.5rem;
        }

        .nav-tab {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .nav-tab:hover {
            color: #8b5cf6;
            background: #faf5ff;
        }

        .nav-tab.active {
            color: #8b5cf6;
            border-bottom-color: #8b5cf6;
            background: #faf5ff;
        }

        .nav-tab .material-icons {
            font-size: 1.2rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .stock-level {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stock-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .stock-high {
            background: #10b981;
        }

        .stock-medium {
            background: #f59e0b;
        }

        .stock-low {
            background: #ef4444;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">📦 Agent Stock - ERP HUB</div>
        <div class="nav-buttons">
            <div class="connection-status">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Connexion...</span>
            </div>
            <button class="btn btn-primary" onclick="refreshData()">
                <span class="material-icons" style="font-size: 1rem;">refresh</span>
                Actualiser
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion des Stocks</h1>
            <p class="page-subtitle">Produits et inventaire - Connecté à PostgreSQL</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalProducts">0</div>
                <div class="stat-label">Total Produits</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalInventoryItems">0</div>
                <div class="stat-label">Articles en Stock</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalStockValue">0€</div>
                <div class="stat-label">Valeur du Stock</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="lowStockItems">0</div>
                <div class="stat-label">Stock Faible</div>
            </div>
        </div>

        <!-- Alertes -->
        <div id="alertContainer"></div>

        <!-- Navigation par onglets -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('products')">
                <span class="material-icons">inventory_2</span>
                Produits
            </button>
            <button class="nav-tab" onclick="showTab('inventory')">
                <span class="material-icons">warehouse</span>
                Inventaire
            </button>
        </nav>

        <!-- Onglet Produits -->
        <div id="products" class="tab-content active">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">📦 Produits</h2>
                    <button class="btn btn-primary" onclick="loadProducts()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Code Produit</th>
                                    <th>Nom</th>
                                    <th>Description</th>
                                    <th>Catégorie</th>
                                    <th>Marque</th>
                                    <th>Prix Coût</th>
                                    <th>Prix Vente</th>
                                    <th>Stock Min</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <tr>
                                    <td colspan="9" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des produits...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Inventaire -->
        <div id="inventory" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">🏪 Inventaire</h2>
                    <button class="btn btn-primary" onclick="loadInventory()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Code Produit</th>
                                    <th>Nom Produit</th>
                                    <th>Entrepôt</th>
                                    <th>Emplacement</th>
                                    <th>Qté en Stock</th>
                                    <th>Qté Réservée</th>
                                    <th>Qté Disponible</th>
                                    <th>Niveau Stock</th>
                                </tr>
                            </thead>
                            <tbody id="inventoryTableBody">
                                <tr>
                                    <td colspan="8" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement de l'inventaire...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let products = [];
        let inventory = [];
        let currentTab = 'products';

        // Configuration API
        const API_BASE_URL = 'http://localhost:5000/api';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
        });

        // Charger toutes les données
        async function loadAllData() {
            await checkConnection();
            await loadProducts();
            await loadInventory();
            updateStats();
        }

        // Vérifier la connexion à l'API
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    updateConnectionStatus(true, data.database_status === 'connected');
                } else {
                    updateConnectionStatus(false, false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateConnectionStatus(false, false);
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(apiConnected, dbConnected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (apiConnected && dbConnected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'PostgreSQL connecté';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Déconnecté';
            }
        }

        // Charger les produits depuis PostgreSQL
        async function loadProducts() {
            try {
                showAlert('Chargement des produits depuis PostgreSQL...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/products`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        products = data.data || [];
                        renderProductsTable();
                        showAlert(`${products.length} produits chargés depuis PostgreSQL`, 'success');
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement produits:', error);
                showAlert('Erreur lors du chargement des produits: ' + error.message, 'error');
                products = [];
                renderProductsTable();
            }
        }

        // Charger l'inventaire depuis PostgreSQL
        async function loadInventory() {
            try {
                const response = await fetch(`${API_BASE_URL}/inventory`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        inventory = data.data || [];
                        renderInventoryTable();
                        console.log(`${inventory.length} articles d'inventaire chargés depuis PostgreSQL`);
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement inventaire:', error);
                showAlert('Erreur lors du chargement de l\'inventaire: ' + error.message, 'error');
                inventory = [];
                renderInventoryTable();
            }
        }

        // Afficher le tableau des produits
        function renderProductsTable() {
            const tbody = document.getElementById('productsTableBody');
            
            if (products.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun produit trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = products.map(product => `
                <tr>
                    <td><strong>${product.productCode}</strong></td>
                    <td>${product.name}</td>
                    <td>${product.description || 'N/A'}</td>
                    <td>${product.category || 'N/A'}</td>
                    <td>${product.brand || 'N/A'}</td>
                    <td>${product.costPrice ? product.costPrice.toLocaleString() + '€' : 'N/A'}</td>
                    <td>${product.sellingPrice ? product.sellingPrice.toLocaleString() + '€' : 'N/A'}</td>
                    <td>${product.minStockLevel || 0}</td>
                    <td>${getStatusBadge(product.status)}</td>
                </tr>
            `).join('');
        }

        // Afficher le tableau de l'inventaire
        function renderInventoryTable() {
            const tbody = document.getElementById('inventoryTableBody');
            
            if (inventory.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun article d'inventaire trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = inventory.map(item => `
                <tr>
                    <td><strong>${item.productCode}</strong></td>
                    <td>${item.productName}</td>
                    <td>${item.warehouseName}</td>
                    <td>${item.location || 'N/A'}</td>
                    <td>${item.quantityOnHand}</td>
                    <td>${item.quantityReserved}</td>
                    <td>${item.quantityAvailable}</td>
                    <td>${getStockLevelIndicator(item.quantityAvailable, getMinStockForProduct(item.productId))}</td>
                </tr>
            `).join('');
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const totalProducts = products.length;
            const totalInventoryItems = inventory.length;
            const totalStockValue = inventory.reduce((sum, item) => {
                const product = products.find(p => p.id === item.productId);
                const costPrice = product ? product.costPrice : 0;
                return sum + (item.quantityOnHand * costPrice);
            }, 0);
            
            const lowStockItems = inventory.filter(item => {
                const minStock = getMinStockForProduct(item.productId);
                return item.quantityAvailable <= minStock;
            }).length;

            document.getElementById('totalProducts').textContent = totalProducts;
            document.getElementById('totalInventoryItems').textContent = totalInventoryItems;
            document.getElementById('totalStockValue').textContent = totalStockValue.toLocaleString() + '€';
            document.getElementById('lowStockItems').textContent = lowStockItems;
        }

        // Gestion des onglets
        function showTab(tabName) {
            // Masquer tous les contenus d'onglets
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Désactiver tous les onglets
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Afficher le contenu de l'onglet sélectionné
            document.getElementById(tabName).classList.add('active');
            
            // Activer l'onglet sélectionné
            event.target.classList.add('active');
            
            currentTab = tabName;
        }

        // Fonctions utilitaires
        function getStatusBadge(status) {
            const badges = {
                'active': '<span class="badge badge-success">Actif</span>',
                'inactive': '<span class="badge badge-warning">Inactif</span>',
                'discontinued': '<span class="badge badge-danger">Arrêté</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getMinStockForProduct(productId) {
            const product = products.find(p => p.id === productId);
            return product ? product.minStockLevel : 0;
        }

        function getStockLevelIndicator(currentStock, minStock) {
            let indicatorClass, levelText;
            
            if (currentStock <= minStock) {
                indicatorClass = 'stock-low';
                levelText = 'Faible';
            } else if (currentStock <= minStock * 2) {
                indicatorClass = 'stock-medium';
                levelText = 'Moyen';
            } else {
                indicatorClass = 'stock-high';
                levelText = 'Bon';
            }

            return `
                <div class="stock-level">
                    <div class="stock-indicator ${indicatorClass}"></div>
                    <span>${levelText}</span>
                </div>
            `;
        }

        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            container.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
            
            // Masquer l'alerte après 5 secondes
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function refreshData() {
            loadAllData();
        }
    </script>
</body>
</html>
