import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  LinearProgress,
  Button,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  TrendingUp,
  AttachMoney,
  People,
  ShoppingCart,
  Target,
  Star,
  Refresh,
  Download,
  FilterList,
  Phone,
  Email
} from '@mui/icons-material';
import { MetricCard } from '../../components/ui/MetricCard';
import { AnimatedChart } from '../../components/ui/AnimatedChart';
import { StatusIndicator } from '../../components/ui/StatusIndicator';

interface SalesMetrics {
  totalRevenue: number;
  monthlyTarget: number;
  dealsWon: number;
  conversionRate: number;
  averageDealSize: number;
  activePipeline: number;
}

interface SalesRep {
  id: string;
  name: string;
  avatar: string;
  revenue: number;
  target: number;
  deals: number;
  performance: number;
  status: 'excellent' | 'good' | 'needs_attention';
}

interface Deal {
  id: string;
  client: string;
  value: number;
  stage: string;
  probability: number;
  closeDate: string;
  rep: string;
}

export const SalesDashboard: React.FC = () => {
  const [salesMetrics, setSalesMetrics] = useState<SalesMetrics>({
    totalRevenue: 2847500,
    monthlyTarget: 3000000,
    dealsWon: 47,
    conversionRate: 23.5,
    averageDealSize: 60500,
    activePipeline: 1250000
  });

  const [salesReps] = useState<SalesRep[]>([
    { id: '1', name: 'Marie Dubois', avatar: '👩‍💼', revenue: 485000, target: 500000, deals: 12, performance: 97, status: 'excellent' },
    { id: '2', name: 'Pierre Martin', avatar: '👨‍💼', revenue: 420000, target: 450000, deals: 9, performance: 93, status: 'excellent' },
    { id: '3', name: 'Sophie Laurent', avatar: '👩‍💼', revenue: 380000, target: 400000, deals: 8, performance: 95, status: 'excellent' },
    { id: '4', name: 'Thomas Bernard', avatar: '👨‍💼', revenue: 320000, target: 400000, deals: 6, performance: 80, status: 'good' },
    { id: '5', name: 'Julie Moreau', avatar: '👩‍💼', revenue: 280000, target: 350000, deals: 5, performance: 80, status: 'good' },
    { id: '6', name: 'Antoine Petit', avatar: '👨‍💼', revenue: 180000, target: 300000, deals: 3, performance: 60, status: 'needs_attention' }
  ]);

  const [topDeals] = useState<Deal[]>([
    { id: '1', client: 'TechCorp SA', value: 125000, stage: 'Négociation', probability: 80, closeDate: '2024-02-15', rep: 'Marie Dubois' },
    { id: '2', client: 'InnovateLtd', value: 95000, stage: 'Proposition', probability: 65, closeDate: '2024-02-20', rep: 'Pierre Martin' },
    { id: '3', client: 'GlobalSystems', value: 87000, stage: 'Qualification', probability: 45, closeDate: '2024-03-01', rep: 'Sophie Laurent' },
    { id: '4', client: 'StartupXYZ', value: 75000, stage: 'Négociation', probability: 70, closeDate: '2024-02-25', rep: 'Thomas Bernard' },
    { id: '5', client: 'Enterprise Co', value: 68000, stage: 'Démonstration', probability: 55, closeDate: '2024-03-10', rep: 'Julie Moreau' }
  ]);

  const [revenueData] = useState([
    { name: 'Jan', value: 2200000, target: 2500000 },
    { name: 'Fév', value: 2450000, target: 2500000 },
    { name: 'Mar', value: 2680000, target: 2700000 },
    { name: 'Avr', value: 2520000, target: 2800000 },
    { name: 'Mai', value: 2750000, target: 2800000 },
    { name: 'Jun', value: 2890000, target: 2900000 },
    { name: 'Jul', value: 2847500, target: 3000000 }
  ]);

  const [pipelineData] = useState([
    { name: 'Prospection', value: 450000 },
    { name: 'Qualification', value: 320000 },
    { name: 'Démonstration', value: 280000 },
    { name: 'Proposition', value: 200000 },
    { name: 'Négociation', value: 150000 },
    { name: 'Signature', value: 80000 }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getPerformanceColor = (performance: number) => {
    if (performance >= 90) return 'success';
    if (performance >= 75) return 'warning';
    return 'error';
  };

  const getStageColor = (stage: string) => {
    const colors: { [key: string]: string } = {
      'Prospection': '#9e9e9e',
      'Qualification': '#2196f3',
      'Démonstration': '#ff9800',
      'Proposition': '#9c27b0',
      'Négociation': '#f44336',
      'Signature': '#4caf50'
    };
    return colors[stage] || '#9e9e9e';
  };

  const targetAchievement = (salesMetrics.totalRevenue / salesMetrics.monthlyTarget) * 100;

  return (
    <Box sx={{ p: 3 }}>
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
              <TrendingUp fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={700} color="primary">
                Agent Sales
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Gestion commerciale et suivi des ventes
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={1}>
            <Tooltip title="Actualiser les données">
              <IconButton onClick={handleRefresh} disabled={isLoading}>
                <Refresh />
              </IconButton>
            </Tooltip>
            <Button variant="outlined" startIcon={<FilterList />}>
              Filtres
            </Button>
            <Button variant="contained" startIcon={<Download />}>
              Rapport
            </Button>
          </Box>
        </Box>
      </motion.div>

      {/* Métriques principales */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="CA Réalisé"
            value={salesMetrics.totalRevenue}
            icon={<AttachMoney />}
            color="primary"
            trend="up"
            trendValue={15.2}
            format="currency"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Objectif Mensuel"
            value={salesMetrics.monthlyTarget}
            icon={<Target />}
            color="secondary"
            subtitle={`${targetAchievement.toFixed(1)}% atteint`}
            format="currency"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Affaires Gagnées"
            value={salesMetrics.dealsWon}
            icon={<Star />}
            color="success"
            trend="up"
            trendValue={8.7}
            format="number"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Taux Conversion"
            value={salesMetrics.conversionRate}
            unit="%"
            icon={<TrendingUp />}
            color="info"
            trend="up"
            trendValue={3.2}
            format="percentage"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Panier Moyen"
            value={salesMetrics.averageDealSize}
            icon={<ShoppingCart />}
            color="warning"
            trend="up"
            trendValue={12.1}
            format="currency"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Pipeline Actif"
            value={salesMetrics.activePipeline}
            icon={<People />}
            color="success"
            trend="up"
            trendValue={5.8}
            format="currency"
            isLoading={isLoading}
          />
        </Grid>
      </Grid>

      {/* Performance des commerciaux */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" fontWeight={600} mb={3}>
              Performance de l'Équipe Commerciale
            </Typography>
            
            <Grid container spacing={2}>
              {salesReps.map((rep, index) => (
                <Grid item xs={12} sm={6} md={4} key={rep.id}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Card 
                      variant="outlined"
                      sx={{ 
                        height: '100%',
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: 3
                        }
                      }}
                    >
                      <CardContent sx={{ p: 2 }}>
                        <Box display="flex" alignItems="center" gap={2} mb={2}>
                          <Typography variant="h4">{rep.avatar}</Typography>
                          <Box flex={1}>
                            <Typography variant="subtitle2" fontWeight={600}>
                              {rep.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {rep.deals} affaires en cours
                            </Typography>
                          </Box>
                          <Box display="flex" gap={0.5}>
                            <IconButton size="small" color="primary">
                              <Phone fontSize="small" />
                            </IconButton>
                            <IconButton size="small" color="primary">
                              <Email fontSize="small" />
                            </IconButton>
                          </Box>
                        </Box>
                        
                        <Box mb={2}>
                          <Box display="flex" justifyContent="space-between" mb={1}>
                            <Typography variant="caption">CA / Objectif</Typography>
                            <Typography variant="caption" fontWeight={600}>
                              {formatCurrency(rep.revenue)} / {formatCurrency(rep.target)}
                            </Typography>
                          </Box>
                          <LinearProgress 
                            variant="determinate" 
                            value={(rep.revenue / rep.target) * 100}
                            sx={{
                              height: 6,
                              borderRadius: 3,
                              backgroundColor: '#f0f0f0',
                              '& .MuiLinearProgress-bar': {
                                borderRadius: 3,
                                backgroundColor: getPerformanceColor(rep.performance) === 'success' ? '#4caf50' : 
                                                getPerformanceColor(rep.performance) === 'warning' ? '#ff9800' : '#f44336'
                              }
                            }}
                          />
                        </Box>
                        
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="caption" color="text.secondary">
                            Performance: {rep.performance}%
                          </Typography>
                          <Chip
                            label={rep.status === 'excellent' ? 'Excellent' : 
                                  rep.status === 'good' ? 'Bon' : 'À améliorer'}
                            color={getPerformanceColor(rep.performance) as any}
                            size="small"
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </motion.div>

      {/* Graphiques */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} lg={8}>
          <AnimatedChart
            title="Évolution du Chiffre d'Affaires"
            subtitle="Réalisé vs Objectif (en millions €)"
            data={revenueData.map(item => ({
              name: item.name,
              value: item.value / 1000000,
              value2: item.target / 1000000
            }))}
            type="area"
            height={350}
            color="#1976d2"
            secondaryColor="#42a5f5"
            isLoading={isLoading}
          />
        </Grid>
        <Grid item xs={12} lg={4}>
          <AnimatedChart
            title="Pipeline par Étape"
            subtitle="Répartition des opportunités"
            data={pipelineData.map(item => ({
              name: item.name,
              value: item.value / 1000
            }))}
            type="pie"
            height={350}
            isLoading={isLoading}
          />
        </Grid>
      </Grid>

      {/* Top deals */}
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight={600} mb={3}>
            Principales Opportunités
          </Typography>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Client</TableCell>
                  <TableCell align="right">Valeur</TableCell>
                  <TableCell>Étape</TableCell>
                  <TableCell align="center">Probabilité</TableCell>
                  <TableCell>Date Prévue</TableCell>
                  <TableCell>Commercial</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {topDeals.map((deal) => (
                  <TableRow key={deal.id} hover>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight={600}>
                        {deal.client}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight={600}>
                        {formatCurrency(deal.value)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={deal.stage}
                        size="small"
                        sx={{
                          backgroundColor: getStageColor(deal.stage),
                          color: 'white'
                        }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
                        <LinearProgress
                          variant="determinate"
                          value={deal.probability}
                          sx={{ width: 60, height: 6, borderRadius: 3 }}
                        />
                        <Typography variant="caption">
                          {deal.probability}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(deal.closeDate).toLocaleDateString('fr-FR')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {deal.rep}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};
