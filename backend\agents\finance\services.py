"""
Services pour l'Agent Finance
Logique métier pour la trésorerie et les analyses financières
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg, F
from django.db import transaction
from decimal import Decimal

from core.models import Tenant, User
from agents.models import Agent
from agents.ai_service import ai_service
from .models import (
    Bank, BankAccount, BankTransaction, CashFlowForecast, CashFlowForecastLine,
    Investment, Loan, FinancialRatio, TreasuryReport
)

logger = logging.getLogger('agents.finance')


class FinanceService:
    """
    Service principal pour l'Agent Finance
    Gère toutes les opérations de trésorerie et analyses financières
    """

    def __init__(self, tenant: Tenant):
        self.tenant = tenant
        self.finance_agent = self._get_or_create_finance_agent()

    def _get_or_create_finance_agent(self) -> Agent:
        """Récupère ou crée l'agent Finance pour le tenant"""
        agent, created = Agent.objects.get_or_create(
            tenant=self.tenant,
            agent_type='finance',
            defaults={
                'name': 'Agent Finance',
                'description': 'Trésorerie et analyses financières',
                'ai_enabled': True,
                'ai_model': 'gpt-4',
                'capabilities': [
                    'treasury_management',
                    'cash_flow_forecasting',
                    'investment_portfolio',
                    'loan_management',
                    'financial_ratios',
                    'risk_analysis',
                    'liquidity_management',
                    'financial_reporting'
                ]
            }
        )
        if created:
            logger.info(f"Agent Finance créé pour le tenant {self.tenant.name}")
        return agent

    def get_finance_dashboard(self) -> Dict[str, Any]:
        """Retourne les données du dashboard Finance"""

        # Comptes bancaires
        bank_accounts = BankAccount.objects.filter(tenant=self.tenant, status='active')

        # Transactions récentes
        recent_transactions = BankTransaction.objects.filter(
            bank_account__tenant=self.tenant,
            transaction_date__gte=timezone.now().date() - timedelta(days=30)
        )

        # Investissements
        investments = Investment.objects.filter(tenant=self.tenant, status='active')

        # Emprunts
        loans = Loan.objects.filter(tenant=self.tenant, status='active')

        # Prévisions de trésorerie
        forecasts = CashFlowForecast.objects.filter(tenant=self.tenant, status='active')

        # Calculs de trésorerie
        treasury_position = self._calculate_treasury_position()

        # Analyse de liquidité
        liquidity_analysis = self._analyze_liquidity()

        # Ratios financiers récents
        recent_ratios = self._get_recent_financial_ratios()

        # Alertes financières
        financial_alerts = self._get_financial_alerts()

        return {
            'tenant': self.tenant.name,
            'timestamp': timezone.now().isoformat(),
            'treasury_position': treasury_position,
            'bank_accounts': {
                'total': bank_accounts.count(),
                'active': bank_accounts.filter(status='active').count(),
                'total_balance': float(sum(account.current_balance for account in bank_accounts)),
                'available_balance': float(sum(account.available_balance for account in bank_accounts)),
                'overdrawn_accounts': bank_accounts.filter(current_balance__lt=0).count()
            },
            'transactions': {
                'total_recent': recent_transactions.count(),
                'pending': recent_transactions.filter(status='pending').count(),
                'processed': recent_transactions.filter(status='processed').count(),
                'total_inflows': float(recent_transactions.filter(
                    transaction_type__in=['credit', 'transfer_in', 'interest']
                ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')),
                'total_outflows': float(recent_transactions.filter(
                    transaction_type__in=['debit', 'transfer_out', 'fee', 'check', 'card_payment', 'direct_debit']
                ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00'))
            },
            'investments': {
                'total': investments.count(),
                'total_value': float(sum(inv.current_value for inv in investments)),
                'total_gain_loss': float(sum(inv.gain_loss for inv in investments)),
                'profitable_count': investments.filter(current_value__gt=F('initial_amount')).count(),
                'by_type': self._get_investments_by_type()
            },
            'loans': {
                'total': loans.count(),
                'total_principal': float(sum(loan.principal_amount for loan in loans)),
                'total_outstanding': float(sum(loan.outstanding_balance for loan in loans)),
                'total_paid': float(sum(loan.total_paid for loan in loans)),
                'by_type': self._get_loans_by_type()
            },
            'forecasts': {
                'total': forecasts.count(),
                'active': forecasts.filter(status='active').count(),
                'latest_forecast': self._get_latest_forecast_summary()
            },
            'liquidity': liquidity_analysis,
            'ratios': recent_ratios,
            'alerts': financial_alerts,
            'recent_activities': self._get_recent_activities()
        }

    def _calculate_treasury_position(self) -> Dict[str, Any]:
        """Calcule la position de trésorerie actuelle"""
        bank_accounts = BankAccount.objects.filter(tenant=self.tenant, status='active')

        total_cash = sum(account.current_balance for account in bank_accounts)
        available_cash = sum(account.available_balance for account in bank_accounts)

        # Calcul par devise
        cash_by_currency = {}
        for account in bank_accounts:
            currency = account.currency
            if currency not in cash_by_currency:
                cash_by_currency[currency] = {
                    'current_balance': Decimal('0.00'),
                    'available_balance': Decimal('0.00'),
                    'accounts_count': 0
                }
            cash_by_currency[currency]['current_balance'] += account.current_balance
            cash_by_currency[currency]['available_balance'] += account.available_balance
            cash_by_currency[currency]['accounts_count'] += 1

        # Convertir en float pour JSON
        for currency_data in cash_by_currency.values():
            currency_data['current_balance'] = float(currency_data['current_balance'])
            currency_data['available_balance'] = float(currency_data['available_balance'])

        return {
            'total_cash': float(total_cash),
            'available_cash': float(available_cash),
            'cash_by_currency': cash_by_currency,
            'calculation_date': timezone.now().date().isoformat()
        }

    def _analyze_liquidity(self) -> Dict[str, Any]:
        """Analyse la liquidité de l'entreprise"""
        # Position de trésorerie
        treasury_position = self._calculate_treasury_position()

        # Flux de trésorerie des 30 derniers jours
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)

        transactions = BankTransaction.objects.filter(
            bank_account__tenant=self.tenant,
            transaction_date__gte=start_date,
            transaction_date__lte=end_date,
            status='processed'
        )

        inflows = transactions.filter(
            transaction_type__in=['credit', 'transfer_in', 'interest']
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        outflows = transactions.filter(
            transaction_type__in=['debit', 'transfer_out', 'fee', 'check', 'card_payment', 'direct_debit']
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        net_cash_flow = inflows - outflows

        # Ratio de liquidité (approximation)
        current_cash = treasury_position['total_cash']
        monthly_outflows = float(outflows)

        # Nombre de jours de trésorerie disponible
        if monthly_outflows > 0:
            cash_runway_days = (current_cash / monthly_outflows) * 30
        else:
            cash_runway_days = float('inf')

        # Évaluation du niveau de liquidité
        if cash_runway_days >= 90:
            liquidity_level = 'excellent'
        elif cash_runway_days >= 60:
            liquidity_level = 'good'
        elif cash_runway_days >= 30:
            liquidity_level = 'adequate'
        elif cash_runway_days >= 15:
            liquidity_level = 'low'
        else:
            liquidity_level = 'critical'

        return {
            'current_cash': current_cash,
            'monthly_inflows': float(inflows),
            'monthly_outflows': monthly_outflows,
            'net_cash_flow': float(net_cash_flow),
            'cash_runway_days': min(cash_runway_days, 999),  # Limiter pour JSON
            'liquidity_level': liquidity_level,
            'analysis_period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            }
        }

    def _get_investments_by_type(self) -> Dict[str, Any]:
        """Répartition des investissements par type"""
        investments = Investment.objects.filter(tenant=self.tenant, status='active')

        by_type = {}
        for investment in investments:
            inv_type = investment.investment_type
            if inv_type not in by_type:
                by_type[inv_type] = {
                    'count': 0,
                    'total_value': Decimal('0.00'),
                    'total_gain_loss': Decimal('0.00')
                }
            by_type[inv_type]['count'] += 1
            by_type[inv_type]['total_value'] += investment.current_value
            by_type[inv_type]['total_gain_loss'] += investment.gain_loss

        # Convertir en float pour JSON
        for type_data in by_type.values():
            type_data['total_value'] = float(type_data['total_value'])
            type_data['total_gain_loss'] = float(type_data['total_gain_loss'])

        return by_type

    def _get_loans_by_type(self) -> Dict[str, Any]:
        """Répartition des emprunts par type"""
        loans = Loan.objects.filter(tenant=self.tenant, status='active')

        by_type = {}
        for loan in loans:
            loan_type = loan.loan_type
            if loan_type not in by_type:
                by_type[loan_type] = {
                    'count': 0,
                    'total_principal': Decimal('0.00'),
                    'total_outstanding': Decimal('0.00')
                }
            by_type[loan_type]['count'] += 1
            by_type[loan_type]['total_principal'] += loan.principal_amount
            by_type[loan_type]['total_outstanding'] += loan.outstanding_balance

        # Convertir en float pour JSON
        for type_data in by_type.values():
            type_data['total_principal'] = float(type_data['total_principal'])
            type_data['total_outstanding'] = float(type_data['total_outstanding'])

        return by_type

    def _get_latest_forecast_summary(self) -> Optional[Dict[str, Any]]:
        """Résumé de la dernière prévision de trésorerie"""
        latest_forecast = CashFlowForecast.objects.filter(
            tenant=self.tenant,
            status='active'
        ).order_by('-start_date').first()

        if not latest_forecast:
            return None

        return {
            'name': latest_forecast.name,
            'period': f"{latest_forecast.start_date} - {latest_forecast.end_date}",
            'opening_balance': float(latest_forecast.opening_balance),
            'total_inflows': float(latest_forecast.total_inflows),
            'total_outflows': float(latest_forecast.total_outflows),
            'closing_balance': float(latest_forecast.closing_balance),
            'net_cash_flow': float(latest_forecast.net_cash_flow)
        }

    def _get_recent_financial_ratios(self) -> List[Dict[str, Any]]:
        """Ratios financiers récents"""
        recent_ratios = FinancialRatio.objects.filter(
            tenant=self.tenant,
            calculation_date__gte=timezone.now().date() - timedelta(days=90)
        ).order_by('-calculation_date')[:10]

        ratios_data = []
        for ratio in recent_ratios:
            ratios_data.append({
                'name': ratio.ratio_name,
                'category': ratio.category,
                'value': float(ratio.ratio_value),
                'calculation_date': ratio.calculation_date.isoformat(),
                'variance_from_target': float(ratio.variance_from_target) if ratio.variance_from_target else None
            })

        return ratios_data

    def _get_financial_alerts(self) -> Dict[str, int]:
        """Alertes financières"""
        alerts = {
            'low_cash_accounts': 0,
            'overdue_loans': 0,
            'underperforming_investments': 0,
            'covenant_breaches': 0,
            'pending_transactions': 0
        }

        # Comptes avec trésorerie faible
        low_cash_threshold = Decimal('10000.00')  # Seuil configurable
        alerts['low_cash_accounts'] = BankAccount.objects.filter(
            tenant=self.tenant,
            status='active',
            current_balance__lt=low_cash_threshold
        ).count()

        # Emprunts en retard (approximation)
        alerts['overdue_loans'] = Loan.objects.filter(
            tenant=self.tenant,
            status='active',
            maturity_date__lt=timezone.now().date()
        ).count()

        # Investissements sous-performants
        alerts['underperforming_investments'] = Investment.objects.filter(
            tenant=self.tenant,
            status='active',
            current_value__lt=F('initial_amount') * Decimal('0.9')  # Perte > 10%
        ).count()

        # Transactions en attente
        alerts['pending_transactions'] = BankTransaction.objects.filter(
            bank_account__tenant=self.tenant,
            status='pending',
            transaction_date__lt=timezone.now().date()
        ).count()

        return alerts

    def _get_recent_activities(self) -> List[Dict[str, Any]]:
        """Activités financières récentes"""
        activities = []

        # Transactions récentes
        recent_transactions = BankTransaction.objects.filter(
            bank_account__tenant=self.tenant,
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:10]

        for transaction in recent_transactions:
            activities.append({
                'type': f'transaction_{transaction.transaction_type}',
                'description': f"{transaction.transaction_type.title()}: {transaction.description}",
                'date': transaction.transaction_date.isoformat(),
                'amount': float(transaction.amount),
                'currency': transaction.currency,
                'account': transaction.bank_account.account_name,
                'status': transaction.status
            })

        return activities

    def create_bank_transaction(self, transaction_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée une nouvelle transaction bancaire"""
        try:
            with transaction.atomic():
                # Récupérer le compte bancaire
                bank_account = BankAccount.objects.get(
                    id=transaction_data['bank_account_id'],
                    tenant=self.tenant
                )

                # Créer la transaction
                bank_transaction = BankTransaction.objects.create(
                    bank_account=bank_account,
                    transaction_type=transaction_data['transaction_type'],
                    amount=transaction_data['amount'],
                    currency=transaction_data.get('currency', bank_account.currency),
                    transaction_date=transaction_data['transaction_date'],
                    value_date=transaction_data.get('value_date', transaction_data['transaction_date']),
                    description=transaction_data['description'],
                    counterpart_name=transaction_data.get('counterpart_name', ''),
                    counterpart_account=transaction_data.get('counterpart_account', ''),
                    category=transaction_data.get('category', ''),
                    reference=transaction_data.get('reference', ''),
                    source='manual'
                )

                # Traiter automatiquement si demandé
                if transaction_data.get('auto_process', False):
                    bank_transaction.status = 'processed'
                    bank_transaction.save()

                # Générer des recommandations IA si disponible
                if ai_service.is_available():
                    recommendations = self._generate_transaction_recommendations(bank_transaction)
                    if recommendations:
                        self._apply_transaction_recommendations(bank_transaction, recommendations)

                return {
                    'success': True,
                    'transaction': {
                        'id': str(bank_transaction.id),
                        'transaction_id': bank_transaction.transaction_id,
                        'status': bank_transaction.status,
                        'amount': float(bank_transaction.amount),
                        'balance_after': float(bank_account.current_balance)
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_transaction_recommendations(self, transaction: BankTransaction) -> Dict[str, Any]:
        """Génère des recommandations IA pour la transaction"""
        try:
            # Analyser le contexte de la transaction
            context = {
                'transaction': {
                    'type': transaction.transaction_type,
                    'amount': float(transaction.amount),
                    'description': transaction.description,
                    'counterpart': transaction.counterpart_name,
                    'account_balance': float(transaction.bank_account.current_balance)
                },
                'account': {
                    'name': transaction.bank_account.account_name,
                    'type': transaction.bank_account.account_type,
                    'currency': transaction.bank_account.currency,
                    'overdraft_limit': float(transaction.bank_account.overdraft_limit)
                }
            }

            prompt = f"""
            En tant qu'expert financier, analyse cette transaction bancaire et fournis des recommandations:

            Contexte: {context}

            Fournis des recommandations pour:
            1. Catégorisation automatique
            2. Détection d'anomalies
            3. Optimisation de trésorerie
            4. Alertes de risque

            Réponds au format JSON avec les clés: categorization, anomaly_detection, treasury_optimization, risk_alerts
            """

            ai_response = ai_service.generate_response(prompt, "finance", temperature=0.6)

            if ai_response.success:
                import json
                return json.loads(ai_response.content)

        except Exception as e:
            logger.error(f"Erreur lors de la génération de recommandations: {str(e)}")

        return {}

    def _apply_transaction_recommendations(self, transaction: BankTransaction, recommendations: Dict[str, Any]):
        """Applique les recommandations IA"""
        try:
            # Log des recommandations pour suivi
            logger.info(f"Recommandations transaction {transaction.transaction_id}: {recommendations}")

            # Appliquer la catégorisation automatique
            if 'categorization' in recommendations and recommendations['categorization']:
                suggested_category = recommendations['categorization'].get('suggested_category')
                if suggested_category:
                    transaction.category = suggested_category
                    transaction.save(update_fields=['category'])

        except Exception as e:
            logger.error(f"Erreur lors de l'application des recommandations: {str(e)}")

    def create_cash_flow_forecast(self, forecast_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée une nouvelle prévision de trésorerie"""
        try:
            with transaction.atomic():
                # Créer la prévision
                forecast = CashFlowForecast.objects.create(
                    tenant=self.tenant,
                    name=forecast_data['name'],
                    forecast_type=forecast_data['forecast_type'],
                    start_date=forecast_data['start_date'],
                    end_date=forecast_data['end_date'],
                    opening_balance=forecast_data.get('opening_balance', Decimal('0.00')),
                    created_by=user,
                    description=forecast_data.get('description', ''),
                    assumptions=forecast_data.get('assumptions', ''),
                    notes=forecast_data.get('notes', '')
                )

                # Ajouter les lignes de prévision
                for i, line_data in enumerate(forecast_data.get('lines', []), 1):
                    CashFlowForecastLine.objects.create(
                        forecast=forecast,
                        line_number=i,
                        description=line_data['description'],
                        flow_type=line_data['flow_type'],
                        category=line_data['category'],
                        amount=line_data['amount'],
                        expected_date=line_data['expected_date'],
                        probability=line_data.get('probability', Decimal('100.00')),
                        confidence_level=line_data.get('confidence_level', 'medium'),
                        reference_document=line_data.get('reference_document', ''),
                        customer_supplier=line_data.get('customer_supplier', ''),
                        is_recurring=line_data.get('is_recurring', False),
                        recurrence_pattern=line_data.get('recurrence_pattern', ''),
                        notes=line_data.get('notes', '')
                    )

                # Calculer les totaux
                forecast.calculate_totals()

                return {
                    'success': True,
                    'forecast': {
                        'id': str(forecast.id),
                        'name': forecast.name,
                        'status': forecast.status,
                        'total_inflows': float(forecast.total_inflows),
                        'total_outflows': float(forecast.total_outflows),
                        'net_cash_flow': float(forecast.net_cash_flow),
                        'closing_balance': float(forecast.closing_balance)
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def create_investment(self, investment_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée un nouvel investissement"""
        try:
            with transaction.atomic():
                # Générer le code d'investissement
                investment_code = self._generate_investment_code()

                investment = Investment.objects.create(
                    tenant=self.tenant,
                    investment_code=investment_code,
                    name=investment_data['name'],
                    investment_type=investment_data['investment_type'],
                    initial_amount=investment_data['initial_amount'],
                    current_value=investment_data.get('current_value', investment_data['initial_amount']),
                    currency=investment_data.get('currency', 'EUR'),
                    purchase_date=investment_data['purchase_date'],
                    maturity_date=investment_data.get('maturity_date'),
                    expected_return_rate=investment_data.get('expected_return_rate'),
                    risk_level=investment_data.get('risk_level', 3),
                    portfolio_manager=user,
                    description=investment_data.get('description', ''),
                    notes=investment_data.get('notes', '')
                )

                return {
                    'success': True,
                    'investment': {
                        'id': str(investment.id),
                        'investment_code': investment.investment_code,
                        'name': investment.name,
                        'initial_amount': float(investment.initial_amount),
                        'current_value': float(investment.current_value),
                        'gain_loss': float(investment.gain_loss),
                        'gain_loss_percentage': float(investment.gain_loss_percentage)
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_investment_code(self) -> str:
        """Génère un code d'investissement unique"""
        current_year = timezone.now().year
        last_investment = Investment.objects.filter(
            tenant=self.tenant,
            investment_code__startswith=f"INV{current_year}"
        ).order_by('-investment_code').first()

        if last_investment:
            last_number = int(last_investment.investment_code[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"INV{current_year}{new_number:04d}"

    def create_loan(self, loan_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée un nouvel emprunt"""
        try:
            with transaction.atomic():
                # Générer le numéro de prêt
                loan_number = self._generate_loan_number()

                loan = Loan.objects.create(
                    tenant=self.tenant,
                    loan_number=loan_number,
                    loan_name=loan_data['loan_name'],
                    loan_type=loan_data['loan_type'],
                    lender_name=loan_data['lender_name'],
                    lender_contact=loan_data.get('lender_contact', ''),
                    principal_amount=loan_data['principal_amount'],
                    outstanding_balance=loan_data['principal_amount'],  # Initialement égal au principal
                    currency=loan_data.get('currency', 'EUR'),
                    interest_rate=loan_data['interest_rate'],
                    interest_type=loan_data.get('interest_type', 'fixed'),
                    disbursement_date=loan_data['disbursement_date'],
                    maturity_date=loan_data['maturity_date'],
                    first_payment_date=loan_data['first_payment_date'],
                    repayment_frequency=loan_data['repayment_frequency'],
                    monthly_payment=loan_data.get('monthly_payment'),
                    collateral_description=loan_data.get('collateral_description', ''),
                    collateral_value=loan_data.get('collateral_value'),
                    loan_officer=user,
                    purpose=loan_data.get('purpose', ''),
                    terms_conditions=loan_data.get('terms_conditions', ''),
                    notes=loan_data.get('notes', '')
                )

                return {
                    'success': True,
                    'loan': {
                        'id': str(loan.id),
                        'loan_number': loan.loan_number,
                        'loan_name': loan.loan_name,
                        'principal_amount': float(loan.principal_amount),
                        'outstanding_balance': float(loan.outstanding_balance),
                        'interest_rate': float(loan.interest_rate),
                        'maturity_date': loan.maturity_date.isoformat()
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_loan_number(self) -> str:
        """Génère un numéro de prêt unique"""
        current_year = timezone.now().year
        last_loan = Loan.objects.filter(
            tenant=self.tenant,
            loan_number__startswith=f"LOAN{current_year}"
        ).order_by('-loan_number').first()

        if last_loan:
            last_number = int(last_loan.loan_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"LOAN{current_year}{new_number:04d}"

    def calculate_financial_ratios(self, period_start: str, period_end: str, user: User) -> Dict[str, Any]:
        """Calcule les ratios financiers pour une période donnée"""
        try:
            # Récupérer les données financières de base
            treasury_position = self._calculate_treasury_position()

            # Calculer les ratios de liquidité
            liquidity_ratios = self._calculate_liquidity_ratios(treasury_position)

            # Calculer les ratios d'endettement
            leverage_ratios = self._calculate_leverage_ratios()

            # Calculer les ratios d'efficacité
            efficiency_ratios = self._calculate_efficiency_ratios(period_start, period_end)

            # Sauvegarder les ratios calculés
            calculation_date = timezone.now().date()
            saved_ratios = []

            all_ratios = {
                **liquidity_ratios,
                **leverage_ratios,
                **efficiency_ratios
            }

            for ratio_code, ratio_data in all_ratios.items():
                financial_ratio = FinancialRatio.objects.create(
                    tenant=self.tenant,
                    ratio_name=ratio_data['name'],
                    ratio_code=ratio_code,
                    category=ratio_data['category'],
                    calculation_date=calculation_date,
                    period_start=period_start,
                    period_end=period_end,
                    ratio_value=ratio_data['value'],
                    numerator=ratio_data['numerator'],
                    denominator=ratio_data['denominator'],
                    calculation_method=ratio_data.get('method', ''),
                    interpretation=ratio_data.get('interpretation', ''),
                    calculated_by=user
                )
                saved_ratios.append({
                    'id': str(financial_ratio.id),
                    'name': financial_ratio.ratio_name,
                    'value': float(financial_ratio.ratio_value),
                    'category': financial_ratio.category
                })

            return {
                'success': True,
                'ratios': saved_ratios,
                'calculation_date': calculation_date.isoformat(),
                'period': f"{period_start} - {period_end}"
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _calculate_liquidity_ratios(self, treasury_position: Dict[str, Any]) -> Dict[str, Any]:
        """Calcule les ratios de liquidité"""
        total_cash = Decimal(str(treasury_position['total_cash']))

        # Ratio de liquidité immédiate (approximation)
        # En l'absence de données comptables complètes, on utilise la trésorerie disponible
        current_ratio = {
            'name': 'Ratio de liquidité générale',
            'category': 'liquidity',
            'value': Decimal('1.0'),  # Valeur par défaut
            'numerator': total_cash,
            'denominator': total_cash,
            'method': 'Trésorerie disponible / Trésorerie disponible (approximation)',
            'interpretation': 'Capacité à honorer les engagements à court terme'
        }

        # Ratio de trésorerie
        cash_ratio = {
            'name': 'Ratio de trésorerie',
            'category': 'liquidity',
            'value': total_cash,
            'numerator': total_cash,
            'denominator': Decimal('1.0'),
            'method': 'Trésorerie et équivalents de trésorerie',
            'interpretation': 'Liquidités immédiatement disponibles'
        }

        return {
            'current_ratio': current_ratio,
            'cash_ratio': cash_ratio
        }

    def _calculate_leverage_ratios(self) -> Dict[str, Any]:
        """Calcule les ratios d'endettement"""
        # Total des emprunts actifs
        total_debt = Loan.objects.filter(
            tenant=self.tenant,
            status='active'
        ).aggregate(total=Sum('outstanding_balance'))['total'] or Decimal('0.00')

        # Approximation des capitaux propres (trésorerie - dettes)
        total_cash = sum(
            account.current_balance
            for account in BankAccount.objects.filter(tenant=self.tenant, status='active')
        )
        equity_approximation = total_cash - total_debt

        # Ratio d'endettement
        if equity_approximation > 0:
            debt_to_equity = total_debt / equity_approximation
        else:
            debt_to_equity = Decimal('0.00')

        debt_to_equity_ratio = {
            'name': 'Ratio d\'endettement',
            'category': 'leverage',
            'value': debt_to_equity,
            'numerator': total_debt,
            'denominator': equity_approximation,
            'method': 'Total des dettes / Capitaux propres (approximation)',
            'interpretation': 'Niveau d\'endettement par rapport aux capitaux propres'
        }

        return {
            'debt_to_equity': debt_to_equity_ratio
        }

    def _calculate_efficiency_ratios(self, period_start: str, period_end: str) -> Dict[str, Any]:
        """Calcule les ratios d'efficacité"""
        # Flux de trésorerie sur la période
        transactions = BankTransaction.objects.filter(
            bank_account__tenant=self.tenant,
            transaction_date__gte=period_start,
            transaction_date__lte=period_end,
            status='processed'
        )

        total_inflows = transactions.filter(
            transaction_type__in=['credit', 'transfer_in', 'interest']
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        total_outflows = transactions.filter(
            transaction_type__in=['debit', 'transfer_out', 'fee', 'check', 'card_payment', 'direct_debit']
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # Ratio de rotation de trésorerie
        if total_outflows > 0:
            cash_turnover = total_inflows / total_outflows
        else:
            cash_turnover = Decimal('0.00')

        cash_turnover_ratio = {
            'name': 'Rotation de trésorerie',
            'category': 'efficiency',
            'value': cash_turnover,
            'numerator': total_inflows,
            'denominator': total_outflows,
            'method': 'Total des entrées / Total des sorties',
            'interpretation': 'Efficacité de la gestion des flux de trésorerie'
        }

        return {
            'cash_turnover': cash_turnover_ratio
        }

    def generate_treasury_report(self, report_type: str, period_start: str, period_end: str, user: User) -> Dict[str, Any]:
        """Génère un rapport de trésorerie"""
        try:
            # Créer l'enregistrement du rapport
            report = TreasuryReport.objects.create(
                tenant=self.tenant,
                report_name=f"Rapport {report_type} - {period_start} à {period_end}",
                report_type=report_type,
                report_date=timezone.now().date(),
                period_start=period_start,
                period_end=period_end,
                generated_by=user
            )

            # Générer les données selon le type de rapport
            if report_type == 'daily_cash':
                report_data = self._generate_daily_cash_report(period_start, period_end)
            elif report_type == 'cash_flow':
                report_data = self._generate_cash_flow_report(period_start, period_end)
            elif report_type == 'investment_portfolio':
                report_data = self._generate_investment_portfolio_report()
            elif report_type == 'loan_portfolio':
                report_data = self._generate_loan_portfolio_report()
            elif report_type == 'financial_ratios':
                report_data = self._generate_financial_ratios_report(period_start, period_end)
            elif report_type == 'liquidity_analysis':
                report_data = self._generate_liquidity_analysis_report()
            else:
                report_data = {'error': f'Type de rapport non supporté: {report_type}'}

            # Sauvegarder les données du rapport
            report.report_data = report_data
            report.status = 'completed'
            report.save()

            return {
                'success': True,
                'report': {
                    'id': str(report.id),
                    'name': report.report_name,
                    'type': report.report_type,
                    'status': report.status,
                    'data': report_data
                }
            }

        except Exception as e:
            # Marquer le rapport comme en erreur
            if 'report' in locals():
                report.status = 'error'
                report.error_message = str(e)
                report.save()

            return {
                'success': False,
                'error': str(e)
            }

    def _generate_daily_cash_report(self, period_start: str, period_end: str) -> Dict[str, Any]:
        """Génère un rapport de position de trésorerie quotidienne"""
        bank_accounts = BankAccount.objects.filter(tenant=self.tenant, status='active')

        accounts_data = []
        total_balance = Decimal('0.00')

        for account in bank_accounts:
            account_data = {
                'account_name': account.account_name,
                'account_number': account.account_number,
                'bank_name': account.bank.name,
                'currency': account.currency,
                'current_balance': float(account.current_balance),
                'available_balance': float(account.available_balance),
                'overdraft_limit': float(account.overdraft_limit)
            }
            accounts_data.append(account_data)
            total_balance += account.current_balance

        return {
            'report_type': 'daily_cash',
            'period': f"{period_start} - {period_end}",
            'total_balance': float(total_balance),
            'accounts': accounts_data,
            'generation_date': timezone.now().isoformat()
        }

    def _generate_cash_flow_report(self, period_start: str, period_end: str) -> Dict[str, Any]:
        """Génère un tableau de flux de trésorerie"""
        transactions = BankTransaction.objects.filter(
            bank_account__tenant=self.tenant,
            transaction_date__gte=period_start,
            transaction_date__lte=period_end,
            status='processed'
        )

        # Catégoriser les flux
        inflows = transactions.filter(
            transaction_type__in=['credit', 'transfer_in', 'interest']
        )
        outflows = transactions.filter(
            transaction_type__in=['debit', 'transfer_out', 'fee', 'check', 'card_payment', 'direct_debit']
        )

        # Calculer les totaux
        total_inflows = inflows.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        total_outflows = outflows.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        net_cash_flow = total_inflows - total_outflows

        # Détail par catégorie
        inflows_by_category = {}
        for transaction in inflows:
            category = transaction.category or 'Non catégorisé'
            if category not in inflows_by_category:
                inflows_by_category[category] = Decimal('0.00')
            inflows_by_category[category] += transaction.amount

        outflows_by_category = {}
        for transaction in outflows:
            category = transaction.category or 'Non catégorisé'
            if category not in outflows_by_category:
                outflows_by_category[category] = Decimal('0.00')
            outflows_by_category[category] += transaction.amount

        # Convertir en float pour JSON
        for category in inflows_by_category:
            inflows_by_category[category] = float(inflows_by_category[category])
        for category in outflows_by_category:
            outflows_by_category[category] = float(outflows_by_category[category])

        return {
            'report_type': 'cash_flow',
            'period': f"{period_start} - {period_end}",
            'summary': {
                'total_inflows': float(total_inflows),
                'total_outflows': float(total_outflows),
                'net_cash_flow': float(net_cash_flow)
            },
            'inflows_by_category': inflows_by_category,
            'outflows_by_category': outflows_by_category,
            'generation_date': timezone.now().isoformat()
        }

    def _generate_investment_portfolio_report(self) -> Dict[str, Any]:
        """Génère un rapport de portefeuille d'investissements"""
        investments = Investment.objects.filter(tenant=self.tenant, status='active')

        portfolio_data = []
        total_value = Decimal('0.00')
        total_gain_loss = Decimal('0.00')

        for investment in investments:
            investment_data = {
                'name': investment.name,
                'code': investment.investment_code,
                'type': investment.investment_type,
                'initial_amount': float(investment.initial_amount),
                'current_value': float(investment.current_value),
                'gain_loss': float(investment.gain_loss),
                'gain_loss_percentage': float(investment.gain_loss_percentage),
                'risk_level': investment.risk_level,
                'purchase_date': investment.purchase_date.isoformat()
            }
            portfolio_data.append(investment_data)
            total_value += investment.current_value
            total_gain_loss += investment.gain_loss

        return {
            'report_type': 'investment_portfolio',
            'summary': {
                'total_investments': len(portfolio_data),
                'total_value': float(total_value),
                'total_gain_loss': float(total_gain_loss),
                'overall_performance': float((total_gain_loss / sum(inv.initial_amount for inv in investments)) * 100) if investments else 0
            },
            'investments': portfolio_data,
            'generation_date': timezone.now().isoformat()
        }

    def _generate_loan_portfolio_report(self) -> Dict[str, Any]:
        """Génère un rapport de portefeuille de crédits"""
        loans = Loan.objects.filter(tenant=self.tenant, status='active')

        portfolio_data = []
        total_principal = Decimal('0.00')
        total_outstanding = Decimal('0.00')

        for loan in loans:
            loan_data = {
                'name': loan.loan_name,
                'number': loan.loan_number,
                'type': loan.loan_type,
                'lender': loan.lender_name,
                'principal_amount': float(loan.principal_amount),
                'outstanding_balance': float(loan.outstanding_balance),
                'interest_rate': float(loan.interest_rate),
                'maturity_date': loan.maturity_date.isoformat(),
                'payment_progress': float(loan.payment_progress_percentage)
            }
            portfolio_data.append(loan_data)
            total_principal += loan.principal_amount
            total_outstanding += loan.outstanding_balance

        return {
            'report_type': 'loan_portfolio',
            'summary': {
                'total_loans': len(portfolio_data),
                'total_principal': float(total_principal),
                'total_outstanding': float(total_outstanding),
                'total_paid': float(total_principal - total_outstanding),
                'average_progress': float((total_principal - total_outstanding) / total_principal * 100) if total_principal > 0 else 0
            },
            'loans': portfolio_data,
            'generation_date': timezone.now().isoformat()
        }

    def _generate_financial_ratios_report(self, period_start: str, period_end: str) -> Dict[str, Any]:
        """Génère un rapport de ratios financiers"""
        ratios = FinancialRatio.objects.filter(
            tenant=self.tenant,
            calculation_date__gte=period_start,
            calculation_date__lte=period_end
        ).order_by('-calculation_date')

        ratios_data = []
        for ratio in ratios:
            ratio_data = {
                'name': ratio.ratio_name,
                'category': ratio.category,
                'value': float(ratio.ratio_value),
                'calculation_date': ratio.calculation_date.isoformat(),
                'interpretation': ratio.interpretation
            }
            ratios_data.append(ratio_data)

        return {
            'report_type': 'financial_ratios',
            'period': f"{period_start} - {period_end}",
            'ratios': ratios_data,
            'generation_date': timezone.now().isoformat()
        }

    def _generate_liquidity_analysis_report(self) -> Dict[str, Any]:
        """Génère un rapport d'analyse de liquidité"""
        liquidity_analysis = self._analyze_liquidity()
        treasury_position = self._calculate_treasury_position()

        return {
            'report_type': 'liquidity_analysis',
            'treasury_position': treasury_position,
            'liquidity_analysis': liquidity_analysis,
            'generation_date': timezone.now().isoformat()
        }

    def generate_finance_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights financiers avec IA"""
        insights = []

        try:
            # Analyser la position de trésorerie
            treasury_insights = self._analyze_treasury_insights()
            insights.extend(treasury_insights)

            # Analyser les investissements
            investment_insights = self._analyze_investment_insights()
            insights.extend(investment_insights)

            # Analyser les emprunts
            loan_insights = self._analyze_loan_insights()
            insights.extend(loan_insights)

            # Générer des insights IA si disponible
            if ai_service.is_available():
                ai_insights = self._generate_ai_insights()
                insights.extend(ai_insights)

        except Exception as e:
            logger.error(f"Erreur lors de la génération d'insights: {str(e)}")
            insights.append({
                'type': 'error',
                'priority': 'high',
                'title': 'Erreur de génération d\'insights',
                'description': f'Impossible de générer les insights: {str(e)}',
                'recommendation': 'Vérifier la configuration du système',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _analyze_treasury_insights(self) -> List[Dict[str, Any]]:
        """Analyse les insights de trésorerie"""
        insights = []

        # Analyser la liquidité
        liquidity_analysis = self._analyze_liquidity()

        if liquidity_analysis['liquidity_level'] == 'critical':
            insights.append({
                'type': 'critical',
                'priority': 'high',
                'title': 'Liquidité critique',
                'description': f"Trésorerie disponible pour seulement {liquidity_analysis['cash_runway_days']:.0f} jours",
                'recommendation': 'Négocier des lignes de crédit ou accélérer les encaissements',
                'generated_at': timezone.now().isoformat()
            })
        elif liquidity_analysis['liquidity_level'] == 'low':
            insights.append({
                'type': 'warning',
                'priority': 'medium',
                'title': 'Liquidité faible',
                'description': f"Trésorerie disponible pour {liquidity_analysis['cash_runway_days']:.0f} jours",
                'recommendation': 'Surveiller les flux de trésorerie et prévoir des financements',
                'generated_at': timezone.now().isoformat()
            })

        # Analyser les comptes à découvert
        overdrawn_accounts = BankAccount.objects.filter(
            tenant=self.tenant,
            status='active',
            current_balance__lt=0
        ).count()

        if overdrawn_accounts > 0:
            insights.append({
                'type': 'warning',
                'priority': 'medium',
                'title': 'Comptes à découvert',
                'description': f"{overdrawn_accounts} compte(s) bancaire(s) à découvert",
                'recommendation': 'Rééquilibrer les comptes ou négocier des découverts autorisés',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _analyze_investment_insights(self) -> List[Dict[str, Any]]:
        """Analyse les insights d'investissement"""
        insights = []

        # Analyser les investissements sous-performants
        underperforming = Investment.objects.filter(
            tenant=self.tenant,
            status='active',
            current_value__lt=F('initial_amount') * Decimal('0.9')  # Perte > 10%
        )

        if underperforming.exists():
            total_loss = sum(inv.gain_loss for inv in underperforming)
            insights.append({
                'type': 'warning',
                'priority': 'medium',
                'title': 'Investissements sous-performants',
                'description': f"{underperforming.count()} investissement(s) avec perte > 10% (total: {total_loss:.2f}€)",
                'recommendation': 'Revoir la stratégie d\'investissement et considérer la diversification',
                'generated_at': timezone.now().isoformat()
            })

        # Analyser la concentration des risques
        high_risk_investments = Investment.objects.filter(
            tenant=self.tenant,
            status='active',
            risk_level__gte=4
        )

        if high_risk_investments.count() > 0:
            total_high_risk_value = sum(inv.current_value for inv in high_risk_investments)
            total_portfolio_value = sum(
                inv.current_value for inv in Investment.objects.filter(tenant=self.tenant, status='active')
            )

            if total_portfolio_value > 0:
                risk_concentration = (total_high_risk_value / total_portfolio_value) * 100

                if risk_concentration > 30:  # Plus de 30% en investissements à haut risque
                    insights.append({
                        'type': 'warning',
                        'priority': 'medium',
                        'title': 'Concentration de risque élevée',
                        'description': f"{risk_concentration:.1f}% du portefeuille en investissements à haut risque",
                        'recommendation': 'Diversifier le portefeuille avec des investissements moins risqués',
                        'generated_at': timezone.now().isoformat()
                    })

        return insights

    def _analyze_loan_insights(self) -> List[Dict[str, Any]]:
        """Analyse les insights d'emprunt"""
        insights = []

        # Analyser les échéances proches
        upcoming_maturity = Loan.objects.filter(
            tenant=self.tenant,
            status='active',
            maturity_date__lte=timezone.now().date() + timedelta(days=90)
        )

        if upcoming_maturity.exists():
            total_amount = sum(loan.outstanding_balance for loan in upcoming_maturity)
            insights.append({
                'type': 'warning',
                'priority': 'high',
                'title': 'Échéances de prêts proches',
                'description': f"{upcoming_maturity.count()} prêt(s) arrivent à échéance dans les 90 jours (total: {total_amount:.2f}€)",
                'recommendation': 'Préparer le refinancement ou le remboursement des prêts',
                'generated_at': timezone.now().isoformat()
            })

        # Analyser le ratio d'endettement
        total_debt = sum(
            loan.outstanding_balance
            for loan in Loan.objects.filter(tenant=self.tenant, status='active')
        )
        total_cash = sum(
            account.current_balance
            for account in BankAccount.objects.filter(tenant=self.tenant, status='active')
        )

        if total_cash > 0:
            debt_ratio = (total_debt / total_cash) * 100

            if debt_ratio > 80:  # Ratio d'endettement > 80%
                insights.append({
                    'type': 'warning',
                    'priority': 'medium',
                    'title': 'Ratio d\'endettement élevé',
                    'description': f"Ratio d'endettement de {debt_ratio:.1f}%",
                    'recommendation': 'Réduire l\'endettement ou augmenter les capitaux propres',
                    'generated_at': timezone.now().isoformat()
                })

        return insights

    def _generate_ai_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights avec IA"""
        try:
            # Préparer le contexte financier
            dashboard_data = self.get_finance_dashboard()

            prompt = f"""
            En tant qu'expert financier, analyse cette situation financière et fournis 3-5 insights stratégiques:

            Données financières: {dashboard_data}

            Fournis des insights sur:
            1. Optimisation de trésorerie
            2. Gestion des risques financiers
            3. Opportunités d'investissement
            4. Stratégie de financement
            5. Performance financière

            Pour chaque insight, fournis:
            - type: 'opportunity', 'warning', 'critical', 'info'
            - priority: 'high', 'medium', 'low'
            - title: titre court
            - description: description détaillée
            - recommendation: action recommandée

            Réponds au format JSON avec une liste d'insights.
            """

            ai_response = ai_service.generate_response(prompt, "finance", temperature=0.7)

            if ai_response.success:
                import json
                ai_insights_data = json.loads(ai_response.content)

                # Ajouter la date de génération
                for insight in ai_insights_data:
                    insight['generated_at'] = timezone.now().isoformat()

                return ai_insights_data

        except Exception as e:
            logger.error(f"Erreur lors de la génération d'insights IA: {str(e)}")

        return []