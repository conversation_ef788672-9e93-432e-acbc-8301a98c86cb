version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: erp_postgres_local
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-erp_hub_local}
      POSTGRES_USER: ${POSTGRES_USER:-erp_user_local}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-local_test_password_123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - erp_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-erp_user_local} -d ${POSTGRES_DB:-erp_hub_local}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Cache Redis
  redis:
    image: redis:7-alpine
    container_name: erp_redis_local
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-local_redis_password_123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - erp_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Django
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: erp_backend_local
    restart: unless-stopped
    environment:
      - DJANGO_SETTINGS_MODULE=config.settings
      - DATABASE_URL=postgresql://${POSTGRES_USER:-erp_user_local}:${POSTGRES_PASSWORD:-local_test_password_123}@postgres:5432/${POSTGRES_DB:-erp_hub_local}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-local_redis_password_123}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-local-test-secret-key-for-development-only-2024}
      - DEBUG=True
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0,backend
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
    volumes:
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - erp_network

  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - VITE_API_URL=http://localhost:8000/api
        - VITE_APP_NAME=ERP HUB - Test Local
        - VITE_APP_VERSION=1.0.0-local
    container_name: erp_frontend_local
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - erp_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  erp_network:
    driver: bridge
