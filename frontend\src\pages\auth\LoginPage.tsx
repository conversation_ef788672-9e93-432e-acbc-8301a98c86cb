import React, { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import { useAuthStore } from '@/store/authStore'
import Button from '@/components/ui/Button'
import type { LoginCredentials } from '@/types/auth'

interface LoginFormData {
  username: string
  password: string
  tenant_slug?: string
}

const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { login, error, clearError } = useAuthStore()
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>()

  const from = location.state?.from?.pathname || '/dashboard'

  const onSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true)
      clearError()
      
      await login(data as LoginCredentials)
      
      toast.success('Connexion réussie!')
      navigate(from, { replace: true })
    } catch (error: any) {
      toast.error(error.message || 'Erreur de connexion')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* En-tête */}
      <div>
        <div className="lg:hidden mb-8">
          <h1 className="text-3xl font-bold text-gray-900">ERP HUB</h1>
        </div>
        <h2 className="text-3xl font-bold text-gray-900">Connexion</h2>
        <p className="mt-2 text-sm text-gray-600">
          Connectez-vous à votre compte pour accéder au système ERP
        </p>
      </div>

      {/* Formulaire */}
      <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
        {/* Tenant (optionnel) */}
        <div>
          <label htmlFor="tenant_slug" className="form-label">
            Organisation (optionnel)
          </label>
          <input
            id="tenant_slug"
            type="text"
            className="form-input"
            placeholder="Slug de votre organisation"
            {...register('tenant_slug')}
          />
          <p className="mt-1 text-xs text-gray-500">
            Laissez vide si votre nom d'utilisateur contient déjà l'organisation
          </p>
        </div>

        {/* Nom d'utilisateur */}
        <div>
          <label htmlFor="username" className="form-label">
            Nom d'utilisateur
          </label>
          <input
            id="username"
            type="text"
            autoComplete="username"
            required
            className="form-input"
            placeholder="Votre nom d'utilisateur"
            {...register('username', {
              required: 'Le nom d\'utilisateur est requis',
            })}
          />
          {errors.username && (
            <p className="form-error">{errors.username.message}</p>
          )}
        </div>

        {/* Mot de passe */}
        <div>
          <label htmlFor="password" className="form-label">
            Mot de passe
          </label>
          <input
            id="password"
            type="password"
            autoComplete="current-password"
            required
            className="form-input"
            placeholder="Votre mot de passe"
            {...register('password', {
              required: 'Le mot de passe est requis',
            })}
          />
          {errors.password && (
            <p className="form-error">{errors.password.message}</p>
          )}
        </div>

        {/* Erreur globale */}
        {error && (
          <div className="rounded-md bg-danger-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-danger-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-danger-800">
                  Erreur de connexion
                </h3>
                <div className="mt-2 text-sm text-danger-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bouton de connexion */}
        <div>
          <Button
            type="submit"
            variant="primary"
            size="lg"
            fullWidth
            loading={isLoading}
          >
            Se connecter
          </Button>
        </div>
      </form>

      {/* Informations supplémentaires */}
      <div className="text-center">
        <p className="text-sm text-gray-600">
          Besoin d'aide ? Contactez votre administrateur système
        </p>
      </div>
    </div>
  )
}

export default LoginPage
