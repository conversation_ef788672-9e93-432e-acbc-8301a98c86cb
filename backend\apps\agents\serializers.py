from rest_framework import serializers
from .models import *

class ManagerAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ManagerAgent
        fields = '__all__'

class HRAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = HRAgent
        fields = '__all__'

class EmployeeSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = Employee
        fields = '__all__'

class SalesAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesAgent
        fields = '__all__'

class LeadSerializer(serializers.ModelSerializer):
    class Meta:
        model = Lead
        fields = '__all__'

class PurchaseAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = PurchaseAgent
        fields = '__all__'

class PurchaseOrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = PurchaseOrder
        fields = '__all__'

class LogisticsAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = LogisticsAgent
        fields = '__all__'

class ShipmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Shipment
        fields = '__all__'

class StockAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = StockAgent
        fields = '__all__'

class ProductSerializer(serializers.ModelSerializer):
    is_low_stock = serializers.SerializerMethodField()
    
    def get_is_low_stock(self, obj):
        return obj.quantity <= obj.min_quantity
    
    class Meta:
        model = Product
        fields = '__all__'

class AccountingAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountingAgent
        fields = '__all__'

class TransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = '__all__'

class FinanceAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = FinanceAgent
        fields = '__all__'

class CRMAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = CRMAgent
        fields = '__all__'

class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = '__all__'

class BIAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = BIAgent
        fields = '__all__'

class RealtimeMetricSerializer(serializers.ModelSerializer):
    class Meta:
        model = RealtimeMetric
        fields = '__all__'

class AgentNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = AgentNotification
        fields = '__all__'
