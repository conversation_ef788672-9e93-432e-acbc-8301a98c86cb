"""
Services pour l'Agent Sales
Logique métier pour la gestion des ventes
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg
from django.db import transaction
from decimal import Decimal

from core.models import Tenant, User
from agents.models import Agent
from agents.ai_service import ai_service
from .models import Customer, Product, Opportunity, Quote, QuoteItem

logger = logging.getLogger('agents.sales')


class SalesService:
    """
    Service principal pour l'Agent Sales
    Gère toutes les opérations de vente
    """
    
    def __init__(self, tenant: Tenant):
        self.tenant = tenant
        self.sales_agent = self._get_or_create_sales_agent()
    
    def _get_or_create_sales_agent(self) -> Agent:
        """Récupère ou crée l'agent Sales pour le tenant"""
        agent, created = Agent.objects.get_or_create(
            tenant=self.tenant,
            agent_type='sales',
            defaults={
                'name': 'Agent Ventes',
                'description': 'Gestion des ventes et opportunités commerciales',
                'ai_enabled': True,
                'ai_model': 'gpt-4',
                'capabilities': [
                    'opportunity_management',
                    'quote_generation',
                    'customer_relationship',
                    'sales_forecasting',
                    'pipeline_analysis',
                    'pricing_optimization'
                ]
            }
        )
        if created:
            logger.info(f"Agent Sales créé pour le tenant {self.tenant.name}")
        return agent
    
    def get_sales_dashboard(self) -> Dict[str, Any]:
        """Retourne les données du dashboard Sales"""
        
        # Opportunités
        opportunities = Opportunity.objects.filter(tenant=self.tenant)
        open_opportunities = opportunities.exclude(stage__in=['won', 'lost'])
        
        # Devis
        quotes = Quote.objects.filter(tenant=self.tenant)
        pending_quotes = quotes.filter(status__in=['draft', 'sent'])
        
        # Clients
        customers = Customer.objects.filter(tenant=self.tenant, is_active=True)
        
        # Calculs de métriques
        total_pipeline_value = open_opportunities.aggregate(
            total=Sum('estimated_value')
        )['total'] or Decimal('0.00')
        
        weighted_pipeline = sum(
            opp.weighted_value for opp in open_opportunities
        )
        
        # Taux de conversion (opportunités gagnées vs total)
        won_opportunities = opportunities.filter(stage='won').count()
        total_opportunities = opportunities.count()
        conversion_rate = (won_opportunities / total_opportunities * 100) if total_opportunities > 0 else 0
        
        # Revenus du mois
        current_month = timezone.now().replace(day=1)
        monthly_revenue = opportunities.filter(
            stage='won',
            actual_close_date__gte=current_month
        ).aggregate(total=Sum('estimated_value'))['total'] or Decimal('0.00')
        
        return {
            'tenant': self.tenant.name,
            'timestamp': timezone.now().isoformat(),
            'pipeline': {
                'total_value': float(total_pipeline_value),
                'weighted_value': float(weighted_pipeline),
                'open_opportunities': open_opportunities.count(),
                'conversion_rate': round(conversion_rate, 2)
            },
            'quotes': {
                'total': quotes.count(),
                'pending': pending_quotes.count(),
                'pending_value': float(pending_quotes.aggregate(
                    total=Sum('total_amount')
                )['total'] or Decimal('0.00'))
            },
            'customers': {
                'total': customers.count(),
                'new_this_month': customers.filter(
                    created_at__gte=current_month
                ).count()
            },
            'revenue': {
                'monthly': float(monthly_revenue),
                'target': 100000.0,  # Objectif par défaut
                'achievement': float(monthly_revenue / 100000 * 100) if monthly_revenue > 0 else 0
            },
            'recent_activities': self._get_recent_activities()
        }
    
    def _get_recent_activities(self) -> List[Dict[str, Any]]:
        """Récupère les activités récentes"""
        activities = []
        
        # Opportunités récentes
        recent_opportunities = Opportunity.objects.filter(
            tenant=self.tenant,
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:5]
        
        for opp in recent_opportunities:
            activities.append({
                'type': 'opportunity_created',
                'description': f"Nouvelle opportunité: {opp.title}",
                'date': opp.created_at.isoformat(),
                'value': float(opp.estimated_value)
            })
        
        # Devis récents
        recent_quotes = Quote.objects.filter(
            tenant=self.tenant,
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:3]
        
        for quote in recent_quotes:
            activities.append({
                'type': 'quote_created',
                'description': f"Nouveau devis: {quote.quote_number}",
                'date': quote.created_at.isoformat(),
                'value': float(quote.total_amount)
            })
        
        return sorted(activities, key=lambda x: x['date'], reverse=True)[:10]
    
    def create_opportunity(self, opportunity_data: Dict[str, Any], sales_rep: User) -> Dict[str, Any]:
        """Crée une nouvelle opportunité"""
        try:
            with transaction.atomic():
                opportunity = Opportunity.objects.create(
                    tenant=self.tenant,
                    title=opportunity_data['title'],
                    description=opportunity_data.get('description', ''),
                    customer_id=opportunity_data['customer_id'],
                    sales_rep=sales_rep,
                    estimated_value=opportunity_data['estimated_value'],
                    probability=opportunity_data.get('probability', 50),
                    stage=opportunity_data.get('stage', 'lead'),
                    priority=opportunity_data.get('priority', 3),
                    expected_close_date=opportunity_data['expected_close_date']
                )
                
                # Générer des recommandations IA si disponible
                if ai_service.is_available():
                    recommendations = self._generate_opportunity_recommendations(opportunity)
                    if recommendations:
                        opportunity.next_action = recommendations.get('next_action', '')
                        opportunity.save()
                
                return {
                    'success': True,
                    'opportunity': {
                        'id': str(opportunity.id),
                        'title': opportunity.title,
                        'estimated_value': float(opportunity.estimated_value),
                        'stage': opportunity.stage
                    }
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_opportunity_recommendations(self, opportunity: Opportunity) -> Dict[str, Any]:
        """Génère des recommandations IA pour une opportunité"""
        try:
            context = {
                'opportunity': {
                    'title': opportunity.title,
                    'stage': opportunity.stage,
                    'estimated_value': float(opportunity.estimated_value),
                    'probability': opportunity.probability,
                    'customer_type': opportunity.customer.customer_type
                },
                'customer': {
                    'name': opportunity.customer.name,
                    'type': opportunity.customer.customer_type,
                    'credit_limit': float(opportunity.customer.credit_limit)
                }
            }
            
            prompt = f"""
            En tant qu'expert commercial, analyse cette opportunité et fournis des recommandations:
            
            Contexte: {context}
            
            Fournis des recommandations pour:
            1. Prochaine action à entreprendre
            2. Stratégie de vente adaptée
            3. Points d'attention
            4. Estimation de probabilité de succès
            
            Réponds au format JSON avec les clés: next_action, strategy, attention_points, success_probability
            """
            
            ai_response = ai_service.generate_response(prompt, "sales", temperature=0.6)
            
            if ai_response.success:
                import json
                return json.loads(ai_response.content)
                
        except Exception as e:
            logger.error(f"Erreur lors de la génération de recommandations: {str(e)}")
        
        return {}
    
    def generate_quote(self, quote_data: Dict[str, Any], sales_rep: User) -> Dict[str, Any]:
        """Génère un nouveau devis"""
        try:
            with transaction.atomic():
                # Générer le numéro de devis
                quote_number = self._generate_quote_number()
                
                quote = Quote.objects.create(
                    tenant=self.tenant,
                    quote_number=quote_number,
                    customer_id=quote_data['customer_id'],
                    opportunity_id=quote_data.get('opportunity_id'),
                    sales_rep=sales_rep,
                    quote_date=quote_data.get('quote_date', timezone.now().date()),
                    valid_until=quote_data['valid_until'],
                    notes=quote_data.get('notes', ''),
                    terms_conditions=quote_data.get('terms_conditions', '')
                )
                
                # Ajouter les lignes de devis
                total_amount = Decimal('0.00')
                for item_data in quote_data.get('items', []):
                    item = QuoteItem.objects.create(
                        quote=quote,
                        product_id=item_data['product_id'],
                        description=item_data.get('description', ''),
                        quantity=item_data['quantity'],
                        unit_price=item_data['unit_price'],
                        discount_percentage=item_data.get('discount_percentage', 0)
                    )
                    total_amount += item.line_total
                
                # Calculer les totaux
                quote.subtotal = total_amount
                quote.tax_amount = total_amount * Decimal('0.20')  # TVA 20% par défaut
                quote.total_amount = quote.subtotal + quote.tax_amount
                quote.save()
                
                return {
                    'success': True,
                    'quote': {
                        'id': str(quote.id),
                        'quote_number': quote.quote_number,
                        'total_amount': float(quote.total_amount),
                        'status': quote.status
                    }
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_quote_number(self) -> str:
        """Génère un numéro de devis unique"""
        current_year = timezone.now().year
        last_quote = Quote.objects.filter(
            tenant=self.tenant,
            quote_number__startswith=f"DEV{current_year}"
        ).order_by('-quote_number').first()
        
        if last_quote:
            last_number = int(last_quote.quote_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1
        
        return f"DEV{current_year}{new_number:04d}"
    
    def update_opportunity_stage(self, opportunity_id: str, new_stage: str, 
                               notes: str = "") -> Dict[str, Any]:
        """Met à jour l'étape d'une opportunité"""
        try:
            opportunity = Opportunity.objects.get(
                id=opportunity_id,
                tenant=self.tenant
            )
            
            old_stage = opportunity.stage
            opportunity.stage = new_stage
            
            # Si l'opportunité est gagnée ou perdue, définir la date de clôture
            if new_stage in ['won', 'lost']:
                opportunity.actual_close_date = timezone.now().date()
            
            opportunity.save()
            
            # Log de l'activité
            logger.info(f"Opportunité {opportunity.title} passée de {old_stage} à {new_stage}")
            
            return {
                'success': True,
                'opportunity': {
                    'id': str(opportunity.id),
                    'title': opportunity.title,
                    'old_stage': old_stage,
                    'new_stage': new_stage
                }
            }
            
        except Opportunity.DoesNotExist:
            return {
                'success': False,
                'error': 'Opportunité non trouvée'
            }
    
    def analyze_sales_performance(self) -> Dict[str, Any]:
        """Analyse les performances de vente"""
        current_month = timezone.now().replace(day=1)
        last_month = (current_month - timedelta(days=1)).replace(day=1)
        
        # Métriques du mois actuel
        current_month_opps = Opportunity.objects.filter(
            tenant=self.tenant,
            created_at__gte=current_month
        )
        
        # Métriques du mois précédent
        last_month_opps = Opportunity.objects.filter(
            tenant=self.tenant,
            created_at__gte=last_month,
            created_at__lt=current_month
        )
        
        # Calculs
        current_revenue = current_month_opps.filter(stage='won').aggregate(
            total=Sum('estimated_value')
        )['total'] or Decimal('0.00')
        
        last_revenue = last_month_opps.filter(stage='won').aggregate(
            total=Sum('estimated_value')
        )['total'] or Decimal('0.00')
        
        # Évolution
        revenue_growth = 0
        if last_revenue > 0:
            revenue_growth = ((current_revenue - last_revenue) / last_revenue) * 100
        
        # Performance par commercial
        sales_rep_performance = []
        sales_reps = User.objects.filter(
            tenant=self.tenant,
            opportunities__isnull=False
        ).distinct()
        
        for rep in sales_reps:
            rep_revenue = rep.opportunities.filter(
                stage='won',
                actual_close_date__gte=current_month
            ).aggregate(total=Sum('estimated_value'))['total'] or Decimal('0.00')
            
            rep_opportunities = rep.opportunities.filter(
                created_at__gte=current_month
            ).count()
            
            sales_rep_performance.append({
                'name': rep.get_full_name(),
                'revenue': float(rep_revenue),
                'opportunities': rep_opportunities
            })
        
        return {
            'period': {
                'current_month': current_month.strftime('%Y-%m'),
                'revenue': float(current_revenue),
                'opportunities': current_month_opps.count(),
                'won_opportunities': current_month_opps.filter(stage='won').count()
            },
            'growth': {
                'revenue_growth_percentage': round(revenue_growth, 2),
                'opportunity_growth': current_month_opps.count() - last_month_opps.count()
            },
            'sales_rep_performance': sorted(
                sales_rep_performance, 
                key=lambda x: x['revenue'], 
                reverse=True
            )
        }
    
    def generate_sales_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights de vente basés sur les données"""
        insights = []
        
        # Analyse du pipeline
        opportunities = Opportunity.objects.filter(tenant=self.tenant)
        stalled_opportunities = opportunities.filter(
            stage__in=['qualified', 'proposal', 'negotiation'],
            last_contact_date__lt=timezone.now().date() - timedelta(days=14)
        )
        
        if stalled_opportunities.exists():
            insights.append({
                'type': 'stalled_opportunities',
                'priority': 'high',
                'title': 'Opportunités en stagnation',
                'description': f'{stalled_opportunities.count()} opportunités sans contact depuis 2 semaines.',
                'recommendation': 'Relancer les prospects et planifier des actions de suivi.'
            })
        
        # Analyse des devis
        expired_quotes = Quote.objects.filter(
            tenant=self.tenant,
            status='sent',
            valid_until__lt=timezone.now().date()
        )
        
        if expired_quotes.exists():
            insights.append({
                'type': 'expired_quotes',
                'priority': 'medium',
                'title': 'Devis expirés',
                'description': f'{expired_quotes.count()} devis ont expiré sans réponse.',
                'recommendation': 'Contacter les clients pour renouveler les devis ou comprendre les blocages.'
            })
        
        # Analyse des performances
        low_probability_opps = opportunities.filter(
            stage__in=['qualified', 'proposal'],
            probability__lt=30
        )
        
        if low_probability_opps.exists():
            insights.append({
                'type': 'low_probability',
                'priority': 'low',
                'title': 'Opportunités à faible probabilité',
                'description': f'{low_probability_opps.count()} opportunités ont une probabilité < 30%.',
                'recommendation': 'Requalifier ces opportunités ou ajuster la stratégie commerciale.'
            })
        
        return insights
