import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'
import { 
  ChartBarIcon, 
  CogIcon, 
  ExclamationTriangleIcon,
  LightBulbIcon,
  PlayIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline'

import { agentsApi, type ManagerDashboard, type TaskAssignment, type MessageBroadcast } from '@/services/api/agentsApi'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import Button from '@/components/ui/Button'

const ManagerPage: React.FC = () => {
  const queryClient = useQueryClient()
  const [selectedTab, setSelectedTab] = useState<'overview' | 'tasks' | 'communication' | 'optimization'>('overview')

  // Récupération des données du dashboard
  const { data: dashboard, isLoading, error, refetch } = useQuery<ManagerDashboard>(
    'manager-dashboard',
    agentsApi.getManagerDashboard,
    {
      refetchInterval: 30000, // Actualisation toutes les 30 secondes
      staleTime: 10000,
    }
  )

  // Mutation pour assigner une tâche
  const assignTaskMutation = useMutation(agentsApi.assignTask, {
    onSuccess: () => {
      toast.success('Tâche assignée avec succès')
      queryClient.invalidateQueries('manager-dashboard')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de l\'assignation de la tâche')
    }
  })

  // Mutation pour envoyer un message
  const sendMessageMutation = useMutation(agentsApi.sendMessage, {
    onSuccess: () => {
      toast.success('Message envoyé avec succès')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de l\'envoi du message')
    }
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Erreur de chargement</h3>
        <p className="mt-1 text-sm text-gray-500">
          Impossible de charger les données de l'Agent Manager
        </p>
        <div className="mt-6">
          <Button onClick={() => refetch()}>Réessayer</Button>
        </div>
      </div>
    )
  }

  if (!dashboard) {
    return null
  }

  const tabs = [
    { id: 'overview', name: 'Vue d\'ensemble', icon: ChartBarIcon },
    { id: 'tasks', name: 'Gestion des tâches', icon: PlayIcon },
    { id: 'communication', name: 'Communication', icon: PaperAirplaneIcon },
    { id: 'optimization', name: 'Optimisation', icon: CogIcon },
  ]

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Agent Manager</h1>
            <p className="mt-2 text-indigo-100">
              Orchestrateur central du système ERP - {dashboard.tenant}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">
              {dashboard.system_metrics.active_agents}/{dashboard.system_metrics.total_agents}
            </div>
            <div className="text-sm text-indigo-100">Agents actifs</div>
          </div>
        </div>
      </div>

      {/* Métriques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Taux de réussite"
          value={`${dashboard.system_metrics.success_rate.toFixed(1)}%`}
          color="green"
          trend={dashboard.system_metrics.success_rate >= 90 ? 'up' : 'down'}
        />
        <MetricCard
          title="Charge système"
          value={`${dashboard.system_metrics.system_load.toFixed(1)}%`}
          color={dashboard.system_metrics.system_load > 80 ? 'red' : 'blue'}
          trend={dashboard.system_metrics.system_load <= 70 ? 'up' : 'down'}
        />
        <MetricCard
          title="Tâches en cours"
          value={dashboard.system_metrics.running_tasks.toString()}
          color="yellow"
        />
        <MetricCard
          title="Tâches en attente"
          value={dashboard.system_metrics.pending_tasks.toString()}
          color="gray"
        />
      </div>

      {/* Alertes */}
      {dashboard.system_metrics.alerts.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Alertes système ({dashboard.system_metrics.alerts.length})
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc list-inside space-y-1">
                  {dashboard.system_metrics.alerts.map((alert, index) => (
                    <li key={index}>{alert.message}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation par onglets */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`
                  flex items-center py-2 px-1 border-b-2 font-medium text-sm
                  ${selectedTab === tab.id
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Contenu des onglets */}
      <div className="mt-6">
        {selectedTab === 'overview' && (
          <OverviewTab dashboard={dashboard} />
        )}
        {selectedTab === 'tasks' && (
          <TasksTab 
            dashboard={dashboard} 
            onAssignTask={(taskData) => assignTaskMutation.mutate(taskData)}
            isAssigning={assignTaskMutation.isLoading}
          />
        )}
        {selectedTab === 'communication' && (
          <CommunicationTab 
            onSendMessage={(messageData) => sendMessageMutation.mutate(messageData)}
            isSending={sendMessageMutation.isLoading}
          />
        )}
        {selectedTab === 'optimization' && (
          <OptimizationTab dashboard={dashboard} />
        )}
      </div>
    </div>
  )
}

// Composant pour les métriques
interface MetricCardProps {
  title: string
  value: string
  color: 'green' | 'red' | 'blue' | 'yellow' | 'gray'
  trend?: 'up' | 'down'
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, color, trend }) => {
  const colorClasses = {
    green: 'bg-green-50 text-green-700 border-green-200',
    red: 'bg-red-50 text-red-700 border-red-200',
    blue: 'bg-blue-50 text-blue-700 border-blue-200',
    yellow: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    gray: 'bg-gray-50 text-gray-700 border-gray-200',
  }

  return (
    <div className={`card border ${colorClasses[color]}`}>
      <div className="card-body">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium opacity-75">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
          </div>
          {trend && (
            <div className={`text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
              {trend === 'up' ? '↗' : '↘'}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Onglet Vue d'ensemble
const OverviewTab: React.FC<{ dashboard: ManagerDashboard }> = ({ dashboard }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Agents */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">État des Agents</h3>
        </div>
        <div className="card-body">
          <div className="space-y-3">
            {dashboard.agents.map((agent) => (
              <div key={agent.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    agent.status === 'active' ? 'bg-green-400' : 
                    agent.status === 'error' ? 'bg-red-400' : 'bg-gray-400'
                  }`} />
                  <div>
                    <p className="font-medium">{agent.name}</p>
                    <p className="text-sm text-gray-500">{agent.agent_type}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{agent.success_rate.toFixed(1)}%</p>
                  <p className="text-xs text-gray-500">{agent.total_tasks} tâches</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Insights IA */}
      {dashboard.ai_insights.length > 0 && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold flex items-center">
              <LightBulbIcon className="h-5 w-5 mr-2 text-yellow-500" />
              Insights IA
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              {dashboard.ai_insights.map((insight, index) => (
                <div key={index} className="border-l-4 border-yellow-400 pl-4">
                  <p className="font-medium text-gray-900">{insight.observation}</p>
                  <p className="text-sm text-gray-600 mt-1">{insight.recommendation}</p>
                  <div className="flex items-center mt-2 space-x-2">
                    <span className={`badge ${
                      insight.priority === 'haute' ? 'badge-danger' :
                      insight.priority === 'moyenne' ? 'badge-warning' : 'badge-primary'
                    }`}>
                      {insight.priority}
                    </span>
                    <span className="text-xs text-gray-500">
                      Confiance: {(insight.confidence * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Onglet Gestion des tâches
const TasksTab: React.FC<{
  dashboard: ManagerDashboard
  onAssignTask: (taskData: TaskAssignment) => void
  isAssigning: boolean
}> = ({ dashboard, onAssignTask, isAssigning }) => {
  const [showTaskForm, setShowTaskForm] = useState(false)

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Gestion des Tâches</h3>
        <Button
          onClick={() => setShowTaskForm(true)}
          variant="primary"
        >
          Assigner une tâche
        </Button>
      </div>

      {/* Tâches récentes */}
      <div className="card">
        <div className="card-header">
          <h4 className="font-medium">Tâches Récentes</h4>
        </div>
        <div className="card-body">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tâche
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Agent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priorité
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Créée le
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {dashboard.recent_tasks.map((task) => (
                  <tr key={task.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{task.title}</div>
                        <div className="text-sm text-gray-500">{task.task_type}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {task.agent_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`badge ${
                        task.status === 'completed' ? 'badge-success' :
                        task.status === 'failed' ? 'badge-danger' :
                        task.status === 'running' ? 'badge-warning' : 'badge-primary'
                      }`}>
                        {task.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {task.priority}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(task.created_at).toLocaleDateString('fr-FR')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Formulaire d'assignation de tâche */}
      {showTaskForm && (
        <TaskAssignmentForm
          onSubmit={(taskData) => {
            onAssignTask(taskData)
            setShowTaskForm(false)
          }}
          onCancel={() => setShowTaskForm(false)}
          isLoading={isAssigning}
          agents={dashboard.agents}
        />
      )}
    </div>
  )
}

// Autres composants d'onglets (CommunicationTab, OptimizationTab) et TaskAssignmentForm
// seraient implémentés de manière similaire...

const CommunicationTab: React.FC<{
  onSendMessage: (messageData: MessageBroadcast) => void
  isSending: boolean
}> = ({ onSendMessage, isSending }) => {
  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Communication Inter-Agents</h3>
      </div>
      <div className="card-body">
        <p className="text-gray-600">Interface de communication en cours de développement...</p>
      </div>
    </div>
  )
}

const OptimizationTab: React.FC<{ dashboard: ManagerDashboard }> = ({ dashboard }) => {
  return (
    <div className="space-y-6">
      {/* Optimisations de workflows */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Optimisations de Workflows</h3>
        </div>
        <div className="card-body">
          {dashboard.workflow_optimizations.length > 0 ? (
            <div className="space-y-4">
              {dashboard.workflow_optimizations.map((optimization, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium text-gray-900">{optimization.workflow_name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{optimization.recommendation}</p>
                    </div>
                    <span className={`badge ${
                      optimization.priority === 'haute' ? 'badge-danger' :
                      optimization.priority === 'moyenne' ? 'badge-warning' : 'badge-primary'
                    }`}>
                      {optimization.priority}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">Aucune optimisation détectée pour le moment.</p>
          )}
        </div>
      </div>
    </div>
  )
}

const TaskAssignmentForm: React.FC<{
  onSubmit: (taskData: TaskAssignment) => void
  onCancel: () => void
  isLoading: boolean
  agents: any[]
}> = ({ onSubmit, onCancel, isLoading, agents }) => {
  const [formData, setFormData] = useState<TaskAssignment>({
    title: '',
    description: '',
    task_type: '',
    priority: 3,
    target_agent_type: '',
    input_data: {}
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Assigner une nouvelle tâche</h3>
      </div>
      <div className="card-body">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="form-label">Titre de la tâche</label>
            <input
              type="text"
              className="form-input"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              required
            />
          </div>
          
          <div>
            <label className="form-label">Description</label>
            <textarea
              className="form-input"
              rows={3}
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="form-label">Type de tâche</label>
              <input
                type="text"
                className="form-input"
                value={formData.task_type}
                onChange={(e) => setFormData({ ...formData, task_type: e.target.value })}
                required
              />
            </div>
            
            <div>
              <label className="form-label">Priorité</label>
              <select
                className="form-input"
                value={formData.priority}
                onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) })}
              >
                <option value={1}>Très basse</option>
                <option value={2}>Basse</option>
                <option value={3}>Normale</option>
                <option value={4}>Haute</option>
                <option value={5}>Critique</option>
              </select>
            </div>
          </div>
          
          <div>
            <label className="form-label">Agent cible (optionnel)</label>
            <select
              className="form-input"
              value={formData.target_agent_type}
              onChange={(e) => setFormData({ ...formData, target_agent_type: e.target.value })}
            >
              <option value="">Sélection automatique</option>
              {agents.map((agent) => (
                <option key={agent.agent_type} value={agent.agent_type}>
                  {agent.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isLoading}
            >
              Assigner la tâche
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default ManagerPage
