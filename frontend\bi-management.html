<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent BI - Business Intelligence | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #6366f1 30%, #4f46e5 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #6366f1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4f46e5;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-info {
            background: #3b82f6;
            color: white;
        }
        
        .btn-info:hover {
            background: #2563eb;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn-warning:hover {
            background: #d97706;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #6366f1;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .stat-trend {
            font-size: 0.75rem;
            margin-top: 0.5rem;
        }
        
        .trend-up {
            color: #10b981;
        }
        
        .trend-down {
            color: #ef4444;
        }
        
        .trend-neutral {
            color: #6b7280;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .dashboard-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .tab-button {
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            color: #6366f1;
            border-bottom-color: #6366f1;
        }
        
        .tab-button:hover {
            color: #4f46e5;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .kpi-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            position: relative;
        }
        
        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .kpi-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
        }
        
        .kpi-status {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
        }
        
        .status-good {
            background: #10b981;
        }
        
        .status-warning {
            background: #f59e0b;
        }
        
        .status-danger {
            background: #ef4444;
        }
        
        .kpi-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .kpi-change {
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .chart-container {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 1.5rem;
        }
        
        .chart-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .simple-chart {
            height: 200px;
            position: relative;
            background: linear-gradient(to top, #f3f4f6 0%, #f9fafb 100%);
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .chart-bar {
            position: absolute;
            bottom: 0;
            background: linear-gradient(to top, #6366f1, #8b5cf6);
            border-radius: 0.25rem 0.25rem 0 0;
            transition: all 0.3s;
        }
        
        .chart-line {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .chart-point {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #6366f1;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .filter-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .filter-select {
            padding: 0.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            background: white;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .dashboard-tabs {
                flex-wrap: wrap;
            }
            
            .tab-button {
                padding: 0.75rem 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">📊 Agent BI - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="generateBIReport()">
                <span class="material-icons" style="font-size: 1rem;">assessment</span>
                Générer Rapport
            </button>
            <button class="btn btn-info" onclick="openModal('alertsModal')">
                <span class="material-icons" style="font-size: 1rem;">notifications</span>
                Configurer Alertes
            </button>
            <button class="btn btn-warning" onclick="exportData()">
                <span class="material-icons" style="font-size: 1rem;">download</span>
                Exporter Données
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Business Intelligence</h1>
            <p class="page-subtitle">Tableaux de bord et analyses avancées</p>
        </div>

        <!-- Statistiques globales -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalRevenue">0€</div>
                <div class="stat-label">CA Total</div>
                <div class="stat-trend trend-up" id="revenueTrend">+0% vs mois dernier</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="monthlyGrowth">0%</div>
                <div class="stat-label">Croissance Mensuelle</div>
                <div class="stat-trend trend-neutral" id="growthTrend">Stable</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="averageMargin">0%</div>
                <div class="stat-label">Marge Moyenne</div>
                <div class="stat-trend trend-up" id="marginTrend">+0% vs objectif</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="performanceScore">0/100</div>
                <div class="stat-label">Score Performance</div>
                <div class="stat-trend trend-good" id="scoreTrend">Excellent</div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="filter-controls">
            <select class="filter-select" id="periodFilter" onchange="updateDashboard()">
                <option value="7">7 derniers jours</option>
                <option value="30" selected>30 derniers jours</option>
                <option value="90">3 derniers mois</option>
                <option value="365">Année complète</option>
            </select>
            <select class="filter-select" id="departmentFilter" onchange="updateDashboard()">
                <option value="all">Tous les départements</option>
                <option value="sales">Commercial</option>
                <option value="finance">Finance</option>
                <option value="hr">Ressources Humaines</option>
                <option value="operations">Opérations</option>
            </select>
        </div>

        <!-- Onglets des tableaux de bord -->
        <div class="dashboard-tabs">
            <button class="tab-button active" onclick="switchTab('sales')">
                <span class="material-icons" style="font-size: 1rem; margin-right: 0.5rem;">trending_up</span>
                Ventes
            </button>
            <button class="tab-button" onclick="switchTab('finance')">
                <span class="material-icons" style="font-size: 1rem; margin-right: 0.5rem;">account_balance</span>
                Finance
            </button>
            <button class="tab-button" onclick="switchTab('hr')">
                <span class="material-icons" style="font-size: 1rem; margin-right: 0.5rem;">people</span>
                RH
            </button>
            <button class="tab-button" onclick="switchTab('operations')">
                <span class="material-icons" style="font-size: 1rem; margin-right: 0.5rem;">settings</span>
                Opérations
            </button>
        </div>

        <!-- Contenu des onglets -->
        <div id="salesTab" class="tab-content active">
            <div class="kpi-grid">
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Chiffre d'Affaires</div>
                        <div class="kpi-status status-good"></div>
                    </div>
                    <div class="kpi-value" id="salesRevenue">0€</div>
                    <div class="kpi-change trend-up">
                        <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                        <span id="salesRevenueChange">+0%</span>
                    </div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Taux de Conversion</div>
                        <div class="kpi-status status-warning"></div>
                    </div>
                    <div class="kpi-value" id="conversionRate">0%</div>
                    <div class="kpi-change trend-neutral">
                        <span class="material-icons" style="font-size: 1rem;">remove</span>
                        <span id="conversionChange">Stable</span>
                    </div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Panier Moyen</div>
                        <div class="kpi-status status-good"></div>
                    </div>
                    <div class="kpi-value" id="averageOrder">0€</div>
                    <div class="kpi-change trend-up">
                        <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                        <span id="orderChange">+0%</span>
                    </div>
                </div>
            </div>
            <div class="chart-container">
                <div class="chart-title">Évolution des Ventes (30 jours)</div>
                <div class="simple-chart" id="salesChart"></div>
            </div>
        </div>

        <div id="financeTab" class="tab-content">
            <div class="kpi-grid">
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Trésorerie</div>
                        <div class="kpi-status status-good"></div>
                    </div>
                    <div class="kpi-value" id="cashFlow">0€</div>
                    <div class="kpi-change trend-up">
                        <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                        <span id="cashFlowChange">+0%</span>
                    </div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Marge Brute</div>
                        <div class="kpi-status status-good"></div>
                    </div>
                    <div class="kpi-value" id="grossMargin">0%</div>
                    <div class="kpi-change trend-up">
                        <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                        <span id="marginChange">+0%</span>
                    </div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">ROI</div>
                        <div class="kpi-status status-warning"></div>
                    </div>
                    <div class="kpi-value" id="roi">0%</div>
                    <div class="kpi-change trend-neutral">
                        <span class="material-icons" style="font-size: 1rem;">remove</span>
                        <span id="roiChange">Stable</span>
                    </div>
                </div>
            </div>
            <div class="chart-container">
                <div class="chart-title">Évolution Trésorerie (30 jours)</div>
                <div class="simple-chart" id="financeChart"></div>
            </div>
        </div>

        <div id="hrTab" class="tab-content">
            <div class="kpi-grid">
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Effectif Total</div>
                        <div class="kpi-status status-good"></div>
                    </div>
                    <div class="kpi-value" id="totalEmployees">0</div>
                    <div class="kpi-change trend-up">
                        <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                        <span id="employeesChange">+0</span>
                    </div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Taux de Satisfaction</div>
                        <div class="kpi-status status-good"></div>
                    </div>
                    <div class="kpi-value" id="satisfactionRate">0%</div>
                    <div class="kpi-change trend-up">
                        <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                        <span id="satisfactionChange">+0%</span>
                    </div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Productivité</div>
                        <div class="kpi-status status-warning"></div>
                    </div>
                    <div class="kpi-value" id="productivity">0%</div>
                    <div class="kpi-change trend-neutral">
                        <span class="material-icons" style="font-size: 1rem;">remove</span>
                        <span id="productivityChange">Stable</span>
                    </div>
                </div>
            </div>
            <div class="chart-container">
                <div class="chart-title">Évolution Effectif (30 jours)</div>
                <div class="simple-chart" id="hrChart"></div>
            </div>
        </div>

        <div id="operationsTab" class="tab-content">
            <div class="kpi-grid">
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Rotation Stock</div>
                        <div class="kpi-status status-good"></div>
                    </div>
                    <div class="kpi-value" id="stockTurnover">0x</div>
                    <div class="kpi-change trend-up">
                        <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                        <span id="stockChange">+0%</span>
                    </div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Délai Livraison</div>
                        <div class="kpi-status status-warning"></div>
                    </div>
                    <div class="kpi-value" id="deliveryTime">0j</div>
                    <div class="kpi-change trend-down">
                        <span class="material-icons" style="font-size: 1rem;">trending_down</span>
                        <span id="deliveryChange">+0j</span>
                    </div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-header">
                        <div class="kpi-title">Efficacité</div>
                        <div class="kpi-status status-good"></div>
                    </div>
                    <div class="kpi-value" id="efficiency">0%</div>
                    <div class="kpi-change trend-up">
                        <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                        <span id="efficiencyChange">+0%</span>
                    </div>
                </div>
            </div>
            <div class="chart-container">
                <div class="chart-title">Performance Opérationnelle (30 jours)</div>
                <div class="simple-chart" id="operationsChart"></div>
            </div>
        </div>
    </main>

    <!-- Modal Configuration Alertes -->
    <div id="alertsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Configuration des Alertes</h3>
                <button class="close-btn" onclick="closeModal('alertsModal')">&times;</button>
            </div>
            <div id="alertsModalAlertContainer"></div>
            <div style="margin-bottom: 1.5rem;">
                <h4 style="margin-bottom: 1rem; color: #374151;">Seuils d'Alerte</h4>
                <div style="display: grid; gap: 1rem;">
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">CA Mensuel Minimum (€)</label>
                        <input type="number" id="revenueThreshold" value="50000" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Taux de Conversion Minimum (%)</label>
                        <input type="number" id="conversionThreshold" value="15" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Trésorerie Minimum (€)</label>
                        <input type="number" id="cashThreshold" value="10000" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Délai Livraison Maximum (jours)</label>
                        <input type="number" id="deliveryThreshold" value="7" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                </div>
            </div>
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button class="btn btn-secondary" onclick="closeModal('alertsModal')">
                    Annuler
                </button>
                <button class="btn btn-success" onclick="saveAlerts()">
                    <span class="material-icons" style="font-size: 1rem;">save</span>
                    Enregistrer
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentTab = 'sales';
        let currentPeriod = 30;
        let currentDepartment = 'all';
        let isLoading = false;

        // Données de démonstration agrégées depuis les autres agents
        const demoData = {
            // Données Sales (depuis sales-management.html)
            sales: {
                totalRevenue: 63600, // Somme des deals fermés
                prospects: 10,
                converted: 3,
                conversionRate: 30,
                averageOrderValue: 21200,
                monthlyGrowth: 15.2
            },

            // Données Finance (depuis finance-management.html)
            finance: {
                totalTreasury: 240000, // Somme des comptes bancaires
                monthlyVariation: 13375,
                availableBudget: 133700,
                monthlyROI: 5.6,
                grossMargin: 42.5
            },

            // Données HR (depuis hr-management.html)
            hr: {
                totalEmployees: 10,
                averageSalary: 4500,
                satisfactionRate: 87,
                productivity: 92,
                newHires: 2
            },

            // Données Operations (Stock + Logistics)
            operations: {
                totalProducts: 10,
                stockValue: 89500,
                stockTurnover: 4.2,
                averageDeliveryTime: 5.8,
                efficiency: 89,
                deliveredOrders: 10
            },

            // Données CRM (depuis crm-management.html)
            crm: {
                totalClients: 8,
                activeClients: 6,
                clientSatisfaction: 4.0,
                totalInteractions: 12,
                pendingTasks: 5
            }
        };

        // Données historiques simulées pour les graphiques (30 jours)
        const historicalData = {
            sales: [
                45000, 47000, 49000, 51000, 48000, 52000, 54000, 56000, 53000, 57000,
                59000, 61000, 58000, 62000, 64000, 66000, 63000, 67000, 69000, 71000,
                68000, 72000, 74000, 76000, 73000, 77000, 79000, 81000, 78000, 63600
            ],
            finance: [
                220000, 225000, 230000, 235000, 232000, 238000, 242000, 245000, 241000, 248000,
                252000, 255000, 251000, 258000, 262000, 265000, 261000, 268000, 272000, 275000,
                271000, 278000, 282000, 285000, 281000, 288000, 292000, 295000, 291000, 240000
            ],
            hr: [
                8, 8, 8, 9, 9, 9, 9, 9, 10, 10,
                10, 10, 10, 10, 10, 10, 10, 10, 10, 10,
                10, 10, 10, 10, 10, 10, 10, 10, 10, 10
            ],
            operations: [
                85, 87, 89, 91, 88, 92, 94, 96, 93, 97,
                89, 91, 88, 92, 94, 96, 93, 97, 89, 91,
                88, 92, 94, 96, 93, 97, 89, 91, 88, 89
            ]
        };

        // Fonctions utilitaires
        function showAlert(message, type = 'error', container = 'alertsModalAlertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function switchTab(tabName) {
            // Désactiver tous les onglets
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Activer l'onglet sélectionné
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');

            currentTab = tabName;
            updateTabContent();
        }

        function updateDashboard() {
            currentPeriod = parseInt(document.getElementById('periodFilter').value);
            currentDepartment = document.getElementById('departmentFilter').value;

            updateGlobalStats();
            updateTabContent();
            updateCharts();
        }

        function updateGlobalStats() {
            // Calcul des statistiques globales
            const totalRevenue = demoData.sales.totalRevenue;
            const monthlyGrowth = demoData.sales.monthlyGrowth;
            const averageMargin = demoData.finance.grossMargin;

            // Score de performance global (moyenne pondérée)
            const performanceScore = Math.round(
                (demoData.sales.conversionRate * 0.3 +
                 demoData.finance.monthlyROI * 10 * 0.2 +
                 demoData.hr.satisfactionRate * 0.2 +
                 demoData.operations.efficiency * 0.3)
            );

            document.getElementById('totalRevenue').textContent = totalRevenue.toLocaleString() + '€';
            document.getElementById('monthlyGrowth').textContent = monthlyGrowth.toFixed(1) + '%';
            document.getElementById('averageMargin').textContent = averageMargin.toFixed(1) + '%';
            document.getElementById('performanceScore').textContent = performanceScore + '/100';

            // Mise à jour des tendances
            document.getElementById('revenueTrend').textContent = `+${monthlyGrowth.toFixed(1)}% vs mois dernier`;
            document.getElementById('growthTrend').textContent = monthlyGrowth > 10 ? 'Excellente' : monthlyGrowth > 5 ? 'Bonne' : 'Stable';
            document.getElementById('marginTrend').textContent = `+${(averageMargin - 40).toFixed(1)}% vs objectif`;
            document.getElementById('scoreTrend').textContent = performanceScore > 80 ? 'Excellent' : performanceScore > 60 ? 'Bon' : 'À améliorer';
        }

        function updateTabContent() {
            switch(currentTab) {
                case 'sales':
                    updateSalesTab();
                    break;
                case 'finance':
                    updateFinanceTab();
                    break;
                case 'hr':
                    updateHRTab();
                    break;
                case 'operations':
                    updateOperationsTab();
                    break;
            }
        }

        function updateSalesTab() {
            const data = demoData.sales;

            document.getElementById('salesRevenue').textContent = data.totalRevenue.toLocaleString() + '€';
            document.getElementById('conversionRate').textContent = data.conversionRate + '%';
            document.getElementById('averageOrder').textContent = data.averageOrderValue.toLocaleString() + '€';

            document.getElementById('salesRevenueChange').textContent = `+${data.monthlyGrowth.toFixed(1)}%`;
            document.getElementById('conversionChange').textContent = 'Stable';
            document.getElementById('orderChange').textContent = '****%';
        }

        function updateFinanceTab() {
            const data = demoData.finance;

            document.getElementById('cashFlow').textContent = data.totalTreasury.toLocaleString() + '€';
            document.getElementById('grossMargin').textContent = data.grossMargin.toFixed(1) + '%';
            document.getElementById('roi').textContent = data.monthlyROI.toFixed(1) + '%';

            document.getElementById('cashFlowChange').textContent = '****%';
            document.getElementById('marginChange').textContent = '****%';
            document.getElementById('roiChange').textContent = 'Stable';
        }

        function updateHRTab() {
            const data = demoData.hr;

            document.getElementById('totalEmployees').textContent = data.totalEmployees;
            document.getElementById('satisfactionRate').textContent = data.satisfactionRate + '%';
            document.getElementById('productivity').textContent = data.productivity + '%';

            document.getElementById('employeesChange').textContent = `+${data.newHires}`;
            document.getElementById('satisfactionChange').textContent = '+3%';
            document.getElementById('productivityChange').textContent = 'Stable';
        }

        function updateOperationsTab() {
            const data = demoData.operations;

            document.getElementById('stockTurnover').textContent = data.stockTurnover.toFixed(1) + 'x';
            document.getElementById('deliveryTime').textContent = data.averageDeliveryTime.toFixed(1) + 'j';
            document.getElementById('efficiency').textContent = data.efficiency + '%';

            document.getElementById('stockChange').textContent = '+12%';
            document.getElementById('deliveryChange').textContent = '+0.8j';
            document.getElementById('efficiencyChange').textContent = '+5%';
        }

        function updateCharts() {
            updateSalesChart();
            updateFinanceChart();
            updateHRChart();
            updateOperationsChart();
        }

        function updateSalesChart() {
            const container = document.getElementById('salesChart');
            container.innerHTML = '';

            const data = historicalData.sales.slice(-currentPeriod);
            const maxValue = Math.max(...data);
            const minValue = Math.min(...data);
            const range = maxValue - minValue;

            data.forEach((value, index) => {
                const height = range > 0 ? ((value - minValue) / range) * 80 + 10 : 50;
                const left = (index / (data.length - 1)) * 90 + 5;

                const bar = document.createElement('div');
                bar.className = 'chart-bar';
                bar.style.left = left + '%';
                bar.style.width = '2%';
                bar.style.height = height + '%';
                bar.title = `${value.toLocaleString()}€`;

                container.appendChild(bar);
            });
        }

        function updateFinanceChart() {
            const container = document.getElementById('financeChart');
            container.innerHTML = '';

            const data = historicalData.finance.slice(-currentPeriod);
            const maxValue = Math.max(...data);
            const minValue = Math.min(...data);
            const range = maxValue - minValue;

            data.forEach((value, index) => {
                const height = range > 0 ? ((value - minValue) / range) * 80 + 10 : 50;
                const left = (index / (data.length - 1)) * 90 + 5;

                const point = document.createElement('div');
                point.className = 'chart-point';
                point.style.left = left + '%';
                point.style.bottom = height + '%';
                point.title = `${value.toLocaleString()}€`;

                container.appendChild(point);
            });
        }

        function updateHRChart() {
            const container = document.getElementById('hrChart');
            container.innerHTML = '';

            const data = historicalData.hr.slice(-currentPeriod);
            const maxValue = Math.max(...data);
            const minValue = Math.min(...data);
            const range = maxValue - minValue || 1;

            data.forEach((value, index) => {
                const height = ((value - minValue) / range) * 80 + 10;
                const left = (index / (data.length - 1)) * 90 + 5;

                const bar = document.createElement('div');
                bar.className = 'chart-bar';
                bar.style.left = left + '%';
                bar.style.width = '2%';
                bar.style.height = height + '%';
                bar.title = `${value} employés`;

                container.appendChild(bar);
            });
        }

        function updateOperationsChart() {
            const container = document.getElementById('operationsChart');
            container.innerHTML = '';

            const data = historicalData.operations.slice(-currentPeriod);
            const maxValue = Math.max(...data);
            const minValue = Math.min(...data);
            const range = maxValue - minValue;

            data.forEach((value, index) => {
                const height = range > 0 ? ((value - minValue) / range) * 80 + 10 : 50;
                const left = (index / (data.length - 1)) * 90 + 5;

                const point = document.createElement('div');
                point.className = 'chart-point';
                point.style.left = left + '%';
                point.style.bottom = height + '%';
                point.title = `${value}% efficacité`;

                container.appendChild(point);
            });
        }

        // Génération du rapport BI complet
        function generateBIReport() {
            const reportData = {
                date: new Date().toLocaleDateString(),
                period: currentPeriod,
                globalStats: {
                    totalRevenue: demoData.sales.totalRevenue,
                    monthlyGrowth: demoData.sales.monthlyGrowth,
                    averageMargin: demoData.finance.grossMargin,
                    performanceScore: Math.round(
                        (demoData.sales.conversionRate * 0.3 +
                         demoData.finance.monthlyROI * 10 * 0.2 +
                         demoData.hr.satisfactionRate * 0.2 +
                         demoData.operations.efficiency * 0.3)
                    )
                },
                kpis: {
                    sales: {
                        revenue: demoData.sales.totalRevenue,
                        conversion: demoData.sales.conversionRate,
                        averageOrder: demoData.sales.averageOrderValue
                    },
                    finance: {
                        treasury: demoData.finance.totalTreasury,
                        margin: demoData.finance.grossMargin,
                        roi: demoData.finance.monthlyROI
                    },
                    hr: {
                        employees: demoData.hr.totalEmployees,
                        satisfaction: demoData.hr.satisfactionRate,
                        productivity: demoData.hr.productivity
                    },
                    operations: {
                        stockTurnover: demoData.operations.stockTurnover,
                        deliveryTime: demoData.operations.averageDeliveryTime,
                        efficiency: demoData.operations.efficiency
                    }
                }
            };

            let reportContent = `
                <h2>Rapport Business Intelligence - ${reportData.date}</h2>
                <p style="color: #6b7280; margin-bottom: 2rem;">Période d'analyse : ${reportData.period} derniers jours</p>

                <h3>Vue d'Ensemble</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: left;">Métrique</th>
                        <th style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">Valeur</th>
                        <th style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">Statut</th>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb;">Chiffre d'Affaires Total</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.globalStats.totalRevenue.toLocaleString()}€</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">✓ Excellent</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb;">Croissance Mensuelle</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.globalStats.monthlyGrowth.toFixed(1)}%</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">✓ Excellent</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb;">Marge Moyenne</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.globalStats.averageMargin.toFixed(1)}%</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">✓ Bon</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb;">Score Performance Global</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.globalStats.performanceScore}/100</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">✓ Excellent</td>
                    </tr>
                </table>

                <h3>Analyse Détaillée par Département</h3>

                <h4>📈 Commercial</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">KPI</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Valeur</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Objectif</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Écart</th>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Chiffre d'Affaires</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.sales.revenue.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">50 000€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">+${((reportData.kpis.sales.revenue - 50000) / 50000 * 100).toFixed(1)}%</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Taux de Conversion</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.sales.conversion}%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">25%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #f59e0b;">+${(reportData.kpis.sales.conversion - 25).toFixed(1)}%</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Panier Moyen</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.sales.averageOrder.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">20 000€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">+${((reportData.kpis.sales.averageOrder - 20000) / 20000 * 100).toFixed(1)}%</td>
                    </tr>
                </table>

                <h4>💰 Finance</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">KPI</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Valeur</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Objectif</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Écart</th>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Trésorerie</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.finance.treasury.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">200 000€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">+${((reportData.kpis.finance.treasury - 200000) / 200000 * 100).toFixed(1)}%</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Marge Brute</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.finance.margin.toFixed(1)}%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">40%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">+${(reportData.kpis.finance.margin - 40).toFixed(1)}%</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">ROI</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.finance.roi.toFixed(1)}%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">5%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">+${(reportData.kpis.finance.roi - 5).toFixed(1)}%</td>
                    </tr>
                </table>

                <h4>👥 Ressources Humaines</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">KPI</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Valeur</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Objectif</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Écart</th>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Effectif Total</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.hr.employees}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">12</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #f59e0b;">${reportData.kpis.hr.employees - 12}</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Satisfaction</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.hr.satisfaction}%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">85%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">+${reportData.kpis.hr.satisfaction - 85}%</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Productivité</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.hr.productivity}%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">90%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">+${reportData.kpis.hr.productivity - 90}%</td>
                    </tr>
                </table>

                <h4>⚙️ Opérations</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">KPI</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Valeur</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Objectif</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Écart</th>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Rotation Stock</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.operations.stockTurnover.toFixed(1)}x</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">4x</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">+${(reportData.kpis.operations.stockTurnover - 4).toFixed(1)}x</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Délai Livraison</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.operations.deliveryTime.toFixed(1)}j</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">5j</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #f59e0b;">+${(reportData.kpis.operations.deliveryTime - 5).toFixed(1)}j</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">Efficacité</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${reportData.kpis.operations.efficiency}%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">90%</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #f59e0b;">${reportData.kpis.operations.efficiency - 90}%</td>
                    </tr>
                </table>

                <h3>Recommandations</h3>
                <ul style="margin: 1rem 0; padding-left: 2rem;">
                    <li style="margin-bottom: 0.5rem;">✅ <strong>Commercial :</strong> Excellent performance, maintenir la dynamique de croissance</li>
                    <li style="margin-bottom: 0.5rem;">✅ <strong>Finance :</strong> Trésorerie solide, optimiser les investissements</li>
                    <li style="margin-bottom: 0.5rem;">⚠️ <strong>RH :</strong> Recruter 2 employés supplémentaires pour atteindre l'objectif</li>
                    <li style="margin-bottom: 0.5rem;">⚠️ <strong>Opérations :</strong> Améliorer les délais de livraison et l'efficacité</li>
                </ul>
            `;

            // Ouvrir le rapport dans une nouvelle fenêtre
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Rapport BI - ERP HUB</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 2rem; line-height: 1.6; }
                        h2 { color: #6366f1; margin-bottom: 1rem; }
                        h3 { color: #374151; margin: 2rem 0 1rem 0; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem; }
                        h4 { color: #4b5563; margin: 1.5rem 0 0.5rem 0; }
                        table { border-collapse: collapse; width: 100%; margin: 1rem 0; }
                        th, td { border: 1px solid #e5e7eb; padding: 0.5rem; }
                        th { background: #f9fafb; font-weight: 600; }
                        ul { margin: 1rem 0; }
                        li { margin-bottom: 0.5rem; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${reportContent}
                    <div style="margin-top: 3rem; text-align: center; border-top: 1px solid #e5e7eb; padding-top: 2rem;">
                        <button onclick="window.print()" style="padding: 0.75rem 1.5rem; background: #6366f1; color: white; border: none; border-radius: 0.5rem; cursor: pointer; font-size: 1rem;">
                            📄 Imprimer le Rapport
                        </button>
                    </div>
                </body>
                </html>
            `);
            reportWindow.document.close();
        }

        // Export des données
        function exportData() {
            const exportData = {
                timestamp: new Date().toISOString(),
                period: currentPeriod,
                department: currentDepartment,
                data: demoData,
                historicalData: historicalData
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `erp-bi-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showAlert('Données exportées avec succès', 'success');
        }

        // Sauvegarde des alertes
        function saveAlerts() {
            const thresholds = {
                revenue: document.getElementById('revenueThreshold').value,
                conversion: document.getElementById('conversionThreshold').value,
                cash: document.getElementById('cashThreshold').value,
                delivery: document.getElementById('deliveryThreshold').value
            };

            localStorage.setItem('biAlertThresholds', JSON.stringify(thresholds));
            showAlert('Seuils d\'alerte sauvegardés avec succès', 'success');
            closeModal('alertsModal');
        }

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                updateGlobalStats();
                updateTabContent();
                updateCharts();

                // Charger les seuils d'alerte sauvegardés
                const savedThresholds = localStorage.getItem('biAlertThresholds');
                if (savedThresholds) {
                    const thresholds = JSON.parse(savedThresholds);
                    document.getElementById('revenueThreshold').value = thresholds.revenue || 50000;
                    document.getElementById('conversionThreshold').value = thresholds.conversion || 15;
                    document.getElementById('cashThreshold').value = thresholds.cash || 10000;
                    document.getElementById('deliveryThreshold').value = thresholds.delivery || 7;
                }
            }
        });
    </script>
</body>
</html>
