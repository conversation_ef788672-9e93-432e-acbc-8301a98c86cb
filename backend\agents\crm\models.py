"""
Modèles pour l'Agent CRM - Relation Client Avancée
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal

from core.models import TimeStampedModel, UUIDModel, Tenant, User


class Contact(UUIDModel, TimeStampedModel):
    """Modèle pour les contacts (prospects, clients, partenaires)"""

    CONTACT_TYPES = [
        ('prospect', _('Prospect')),
        ('customer', _('Client')),
        ('partner', _('Partenaire')),
        ('supplier', _('Fournisseur')),
        ('lead', _('Lead')),
        ('former_customer', _('Ancien client')),
    ]

    CONTACT_STATUS = [
        ('active', _('Actif')),
        ('inactive', _('Inactif')),
        ('qualified', _('Qualifié')),
        ('unqualified', _('Non qualifié')),
        ('converted', _('Converti')),
        ('lost', _('Perdu')),
        ('blacklisted', _('Liste noire')),
    ]

    LEAD_SOURCES = [
        ('website', _('Site web')),
        ('social_media', _('Réseaux sociaux')),
        ('email_campaign', _('Campagne email')),
        ('referral', _('Recommandation')),
        ('cold_call', _('Appel à froid')),
        ('trade_show', _('Salon professionnel')),
        ('advertising', _('Publicité')),
        ('partner', _('Partenaire')),
        ('direct', _('Direct')),
        ('other', _('Autre')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='contacts',
        verbose_name=_("Tenant")
    )

    # Informations de base
    contact_number = models.CharField(_("Numéro de contact"), max_length=50, unique=True)
    first_name = models.CharField(_("Prénom"), max_length=100)
    last_name = models.CharField(_("Nom"), max_length=100)
    company_name = models.CharField(_("Nom de l'entreprise"), max_length=200, blank=True)
    job_title = models.CharField(_("Poste"), max_length=100, blank=True)

    # Type et statut
    contact_type = models.CharField(_("Type de contact"), max_length=20, choices=CONTACT_TYPES)
    status = models.CharField(_("Statut"), max_length=15, choices=CONTACT_STATUS, default='active')
    lead_source = models.CharField(_("Source du lead"), max_length=20, choices=LEAD_SOURCES, blank=True)

    # Coordonnées
    email = models.EmailField(_("Email"), blank=True)
    phone = models.CharField(_("Téléphone"), max_length=50, blank=True)
    mobile = models.CharField(_("Mobile"), max_length=50, blank=True)
    website = models.URLField(_("Site web"), blank=True)

    # Adresse
    address_line1 = models.CharField(_("Adresse ligne 1"), max_length=200, blank=True)
    address_line2 = models.CharField(_("Adresse ligne 2"), max_length=200, blank=True)
    city = models.CharField(_("Ville"), max_length=100, blank=True)
    state = models.CharField(_("État/Région"), max_length=100, blank=True)
    postal_code = models.CharField(_("Code postal"), max_length=20, blank=True)
    country = models.CharField(_("Pays"), max_length=100, blank=True)

    # Scoring et segmentation
    lead_score = models.PositiveIntegerField(
        _("Score du lead"),
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    customer_value = models.DecimalField(
        _("Valeur client"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    lifetime_value = models.DecimalField(
        _("Valeur vie client"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Dates importantes
    first_contact_date = models.DateField(_("Date du premier contact"), null=True, blank=True)
    last_contact_date = models.DateField(_("Date du dernier contact"), null=True, blank=True)
    conversion_date = models.DateField(_("Date de conversion"), null=True, blank=True)

    # Responsable
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_contacts',
        verbose_name=_("Assigné à")
    )

    # Préférences de communication
    email_opt_in = models.BooleanField(_("Accepte les emails"), default=True)
    sms_opt_in = models.BooleanField(_("Accepte les SMS"), default=False)
    phone_opt_in = models.BooleanField(_("Accepte les appels"), default=True)

    # Informations complémentaires
    industry = models.CharField(_("Secteur d'activité"), max_length=100, blank=True)
    company_size = models.CharField(_("Taille de l'entreprise"), max_length=50, blank=True)
    annual_revenue = models.DecimalField(
        _("Chiffre d'affaires annuel"),
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Tags et notes
    tags = models.CharField(_("Tags"), max_length=500, blank=True, help_text=_("Séparés par des virgules"))
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Contact")
        verbose_name_plural = _("Contacts")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['contact_type', 'status']),
            models.Index(fields=['email']),
            models.Index(fields=['lead_score']),
        ]

    def __str__(self):
        if self.company_name:
            return f"{self.first_name} {self.last_name} ({self.company_name})"
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def display_name(self):
        if self.company_name:
            return f"{self.full_name} - {self.company_name}"
        return self.full_name

    def save(self, *args, **kwargs):
        if not self.contact_number:
            self.contact_number = self._generate_contact_number()
        super().save(*args, **kwargs)

    def _generate_contact_number(self):
        """Génère un numéro de contact unique"""
        current_year = timezone.now().year
        last_contact = Contact.objects.filter(
            tenant=self.tenant,
            contact_number__startswith=f"CNT{current_year}"
        ).order_by('-contact_number').first()

        if last_contact:
            last_number = int(last_contact.contact_number[-6:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"CNT{current_year}{new_number:06d}"


class Opportunity(UUIDModel, TimeStampedModel):
    """Modèle pour les opportunités commerciales"""

    OPPORTUNITY_STAGES = [
        ('prospecting', _('Prospection')),
        ('qualification', _('Qualification')),
        ('needs_analysis', _('Analyse des besoins')),
        ('proposal', _('Proposition')),
        ('negotiation', _('Négociation')),
        ('closed_won', _('Gagné')),
        ('closed_lost', _('Perdu')),
        ('on_hold', _('En attente')),
    ]

    OPPORTUNITY_TYPES = [
        ('new_business', _('Nouvelle affaire')),
        ('existing_business', _('Affaire existante')),
        ('upsell', _('Montée en gamme')),
        ('cross_sell', _('Vente croisée')),
        ('renewal', _('Renouvellement')),
    ]

    PRIORITY_LEVELS = [
        ('low', _('Faible')),
        ('medium', _('Moyenne')),
        ('high', _('Élevée')),
        ('urgent', _('Urgente')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='opportunities',
        verbose_name=_("Tenant")
    )

    contact = models.ForeignKey(
        Contact,
        on_delete=models.CASCADE,
        related_name='opportunities',
        verbose_name=_("Contact")
    )

    # Informations de base
    opportunity_number = models.CharField(_("Numéro d'opportunité"), max_length=50, unique=True)
    name = models.CharField(_("Nom de l'opportunité"), max_length=200)
    description = models.TextField(_("Description"), blank=True)

    # Type et étape
    opportunity_type = models.CharField(_("Type d'opportunité"), max_length=20, choices=OPPORTUNITY_TYPES)
    stage = models.CharField(_("Étape"), max_length=20, choices=OPPORTUNITY_STAGES, default='prospecting')
    priority = models.CharField(_("Priorité"), max_length=10, choices=PRIORITY_LEVELS, default='medium')

    # Valeurs financières
    amount = models.DecimalField(
        _("Montant"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    probability = models.PositiveIntegerField(
        _("Probabilité (%)"),
        default=50,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    weighted_amount = models.DecimalField(
        _("Montant pondéré"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Dates
    expected_close_date = models.DateField(_("Date de clôture prévue"))
    actual_close_date = models.DateField(_("Date de clôture réelle"), null=True, blank=True)

    # Responsable
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_opportunities',
        verbose_name=_("Assigné à")
    )

    # Concurrence
    competitors = models.CharField(_("Concurrents"), max_length=500, blank=True)
    competitive_advantage = models.TextField(_("Avantage concurrentiel"), blank=True)

    # Raison de perte
    loss_reason = models.CharField(_("Raison de la perte"), max_length=200, blank=True)

    # Informations complémentaires
    source = models.CharField(_("Source"), max_length=100, blank=True)
    campaign = models.CharField(_("Campagne"), max_length=100, blank=True)
    next_step = models.CharField(_("Prochaine étape"), max_length=200, blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Opportunité")
        verbose_name_plural = _("Opportunités")
        ordering = ['-expected_close_date', '-amount']
        indexes = [
            models.Index(fields=['stage', 'expected_close_date']),
            models.Index(fields=['amount']),
            models.Index(fields=['probability']),
        ]

    def __str__(self):
        return f"{self.name} - {self.contact.display_name}"

    @property
    def is_open(self):
        return self.stage not in ['closed_won', 'closed_lost']

    @property
    def is_won(self):
        return self.stage == 'closed_won'

    @property
    def is_lost(self):
        return self.stage == 'closed_lost'

    def save(self, *args, **kwargs):
        if not self.opportunity_number:
            self.opportunity_number = self._generate_opportunity_number()

        # Calculer le montant pondéré
        self.weighted_amount = (self.amount * self.probability) / 100

        super().save(*args, **kwargs)

    def _generate_opportunity_number(self):
        """Génère un numéro d'opportunité unique"""
        current_year = timezone.now().year
        last_opportunity = Opportunity.objects.filter(
            tenant=self.tenant,
            opportunity_number__startswith=f"OPP{current_year}"
        ).order_by('-opportunity_number').first()

        if last_opportunity:
            last_number = int(last_opportunity.opportunity_number[-6:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"OPP{current_year}{new_number:06d}"


class Campaign(UUIDModel, TimeStampedModel):
    """Modèle pour les campagnes marketing"""

    CAMPAIGN_TYPES = [
        ('email', _('Email')),
        ('sms', _('SMS')),
        ('social_media', _('Réseaux sociaux')),
        ('advertising', _('Publicité')),
        ('direct_mail', _('Courrier direct')),
        ('webinar', _('Webinaire')),
        ('event', _('Événement')),
        ('content', _('Contenu')),
        ('referral', _('Parrainage')),
        ('other', _('Autre')),
    ]

    CAMPAIGN_STATUS = [
        ('draft', _('Brouillon')),
        ('scheduled', _('Programmée')),
        ('active', _('Active')),
        ('paused', _('En pause')),
        ('completed', _('Terminée')),
        ('cancelled', _('Annulée')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='campaigns',
        verbose_name=_("Tenant")
    )

    # Informations de base
    campaign_number = models.CharField(_("Numéro de campagne"), max_length=50, unique=True)
    name = models.CharField(_("Nom de la campagne"), max_length=200)
    description = models.TextField(_("Description"), blank=True)

    # Type et statut
    campaign_type = models.CharField(_("Type de campagne"), max_length=20, choices=CAMPAIGN_TYPES)
    status = models.CharField(_("Statut"), max_length=15, choices=CAMPAIGN_STATUS, default='draft')

    # Dates
    start_date = models.DateField(_("Date de début"))
    end_date = models.DateField(_("Date de fin"))

    # Budget et coûts
    budget = models.DecimalField(
        _("Budget"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    actual_cost = models.DecimalField(
        _("Coût réel"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Objectifs
    target_audience = models.TextField(_("Audience cible"), blank=True)
    expected_leads = models.PositiveIntegerField(_("Leads attendus"), default=0)
    expected_revenue = models.DecimalField(
        _("Revenus attendus"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Résultats
    total_sent = models.PositiveIntegerField(_("Total envoyé"), default=0)
    total_delivered = models.PositiveIntegerField(_("Total livré"), default=0)
    total_opened = models.PositiveIntegerField(_("Total ouvert"), default=0)
    total_clicked = models.PositiveIntegerField(_("Total cliqué"), default=0)
    total_responses = models.PositiveIntegerField(_("Total réponses"), default=0)
    total_leads = models.PositiveIntegerField(_("Total leads"), default=0)
    total_opportunities = models.PositiveIntegerField(_("Total opportunités"), default=0)
    actual_revenue = models.DecimalField(
        _("Revenus réels"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Responsable
    campaign_manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_campaigns',
        verbose_name=_("Responsable de campagne")
    )

    # Contenu
    subject = models.CharField(_("Sujet"), max_length=200, blank=True)
    content = models.TextField(_("Contenu"), blank=True)
    call_to_action = models.CharField(_("Appel à l'action"), max_length=200, blank=True)

    # Segmentation
    segment_criteria = models.JSONField(_("Critères de segmentation"), default=dict, blank=True)

    # Informations complémentaires
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Campagne")
        verbose_name_plural = _("Campagnes")
        ordering = ['-start_date']
        indexes = [
            models.Index(fields=['campaign_type', 'status']),
            models.Index(fields=['start_date', 'end_date']),
        ]

    def __str__(self):
        return f"{self.name} ({self.campaign_type})"

    @property
    def delivery_rate(self):
        """Taux de livraison"""
        if self.total_sent > 0:
            return (self.total_delivered / self.total_sent) * 100
        return 0

    @property
    def open_rate(self):
        """Taux d'ouverture"""
        if self.total_delivered > 0:
            return (self.total_opened / self.total_delivered) * 100
        return 0

    @property
    def click_rate(self):
        """Taux de clic"""
        if self.total_opened > 0:
            return (self.total_clicked / self.total_opened) * 100
        return 0

    @property
    def response_rate(self):
        """Taux de réponse"""
        if self.total_delivered > 0:
            return (self.total_responses / self.total_delivered) * 100
        return 0

    @property
    def conversion_rate(self):
        """Taux de conversion en leads"""
        if self.total_delivered > 0:
            return (self.total_leads / self.total_delivered) * 100
        return 0

    @property
    def roi(self):
        """Retour sur investissement"""
        if self.actual_cost > 0:
            return ((self.actual_revenue - self.actual_cost) / self.actual_cost) * 100
        return 0

    def save(self, *args, **kwargs):
        if not self.campaign_number:
            self.campaign_number = self._generate_campaign_number()
        super().save(*args, **kwargs)

    def _generate_campaign_number(self):
        """Génère un numéro de campagne unique"""
        current_year = timezone.now().year
        last_campaign = Campaign.objects.filter(
            tenant=self.tenant,
            campaign_number__startswith=f"CAM{current_year}"
        ).order_by('-campaign_number').first()

        if last_campaign:
            last_number = int(last_campaign.campaign_number[-6:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"CAM{current_year}{new_number:06d}"


class SupportTicket(UUIDModel, TimeStampedModel):
    """Modèle pour les tickets de support client"""

    TICKET_TYPES = [
        ('question', _('Question')),
        ('bug_report', _('Rapport de bug')),
        ('feature_request', _('Demande de fonctionnalité')),
        ('complaint', _('Réclamation')),
        ('refund_request', _('Demande de remboursement')),
        ('technical_support', _('Support technique')),
        ('billing_inquiry', _('Question de facturation')),
        ('account_issue', _('Problème de compte')),
        ('other', _('Autre')),
    ]

    TICKET_STATUS = [
        ('new', _('Nouveau')),
        ('open', _('Ouvert')),
        ('in_progress', _('En cours')),
        ('pending', _('En attente')),
        ('resolved', _('Résolu')),
        ('closed', _('Fermé')),
        ('cancelled', _('Annulé')),
    ]

    PRIORITY_LEVELS = [
        ('low', _('Faible')),
        ('medium', _('Moyenne')),
        ('high', _('Élevée')),
        ('urgent', _('Urgente')),
        ('critical', _('Critique')),
    ]

    SATISFACTION_RATINGS = [
        (1, _('Très insatisfait')),
        (2, _('Insatisfait')),
        (3, _('Neutre')),
        (4, _('Satisfait')),
        (5, _('Très satisfait')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='support_tickets',
        verbose_name=_("Tenant")
    )

    contact = models.ForeignKey(
        Contact,
        on_delete=models.CASCADE,
        related_name='support_tickets',
        verbose_name=_("Contact")
    )

    # Informations de base
    ticket_number = models.CharField(_("Numéro de ticket"), max_length=50, unique=True)
    subject = models.CharField(_("Sujet"), max_length=200)
    description = models.TextField(_("Description"))

    # Type et statut
    ticket_type = models.CharField(_("Type de ticket"), max_length=20, choices=TICKET_TYPES)
    status = models.CharField(_("Statut"), max_length=15, choices=TICKET_STATUS, default='new')
    priority = models.CharField(_("Priorité"), max_length=10, choices=PRIORITY_LEVELS, default='medium')

    # Assignation
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_tickets',
        verbose_name=_("Assigné à")
    )

    # Dates importantes
    due_date = models.DateTimeField(_("Date d'échéance"), null=True, blank=True)
    first_response_date = models.DateTimeField(_("Date de première réponse"), null=True, blank=True)
    resolution_date = models.DateTimeField(_("Date de résolution"), null=True, blank=True)
    closed_date = models.DateTimeField(_("Date de fermeture"), null=True, blank=True)

    # Métriques de temps
    response_time_hours = models.DecimalField(
        _("Temps de réponse (heures)"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    resolution_time_hours = models.DecimalField(
        _("Temps de résolution (heures)"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Satisfaction client
    satisfaction_rating = models.PositiveIntegerField(
        _("Note de satisfaction"),
        choices=SATISFACTION_RATINGS,
        null=True,
        blank=True
    )
    satisfaction_comment = models.TextField(_("Commentaire de satisfaction"), blank=True)

    # Résolution
    resolution = models.TextField(_("Résolution"), blank=True)
    resolution_category = models.CharField(_("Catégorie de résolution"), max_length=100, blank=True)

    # Escalade
    escalated = models.BooleanField(_("Escaladé"), default=False)
    escalation_reason = models.CharField(_("Raison de l'escalade"), max_length=200, blank=True)
    escalated_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='escalated_tickets',
        verbose_name=_("Escaladé vers")
    )

    # Informations complémentaires
    tags = models.CharField(_("Tags"), max_length=500, blank=True)
    internal_notes = models.TextField(_("Notes internes"), blank=True)

    class Meta:
        verbose_name = _("Ticket de support")
        verbose_name_plural = _("Tickets de support")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['ticket_type']),
            models.Index(fields=['assigned_to', 'status']),
        ]

    def __str__(self):
        return f"{self.ticket_number} - {self.subject}"

    @property
    def is_open(self):
        return self.status not in ['resolved', 'closed', 'cancelled']

    @property
    def is_overdue(self):
        if self.due_date and self.is_open:
            return timezone.now() > self.due_date
        return False

    def save(self, *args, **kwargs):
        if not self.ticket_number:
            self.ticket_number = self._generate_ticket_number()
        super().save(*args, **kwargs)

    def _generate_ticket_number(self):
        """Génère un numéro de ticket unique"""
        current_year = timezone.now().year
        last_ticket = SupportTicket.objects.filter(
            tenant=self.tenant,
            ticket_number__startswith=f"TKT{current_year}"
        ).order_by('-ticket_number').first()

        if last_ticket:
            last_number = int(last_ticket.ticket_number[-6:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"TKT{current_year}{new_number:06d}"


class Interaction(UUIDModel, TimeStampedModel):
    """Modèle pour les interactions avec les contacts"""

    INTERACTION_TYPES = [
        ('call', _('Appel téléphonique')),
        ('email', _('Email')),
        ('meeting', _('Réunion')),
        ('demo', _('Démonstration')),
        ('proposal', _('Proposition')),
        ('follow_up', _('Suivi')),
        ('support', _('Support')),
        ('social_media', _('Réseaux sociaux')),
        ('chat', _('Chat')),
        ('sms', _('SMS')),
        ('letter', _('Courrier')),
        ('other', _('Autre')),
    ]

    INTERACTION_DIRECTIONS = [
        ('inbound', _('Entrant')),
        ('outbound', _('Sortant')),
    ]

    INTERACTION_OUTCOMES = [
        ('successful', _('Réussi')),
        ('no_answer', _('Pas de réponse')),
        ('busy', _('Occupé')),
        ('voicemail', _('Messagerie')),
        ('meeting_scheduled', _('Réunion programmée')),
        ('follow_up_required', _('Suivi requis')),
        ('not_interested', _('Pas intéressé')),
        ('interested', _('Intéressé')),
        ('proposal_requested', _('Proposition demandée')),
        ('closed_won', _('Affaire gagnée')),
        ('closed_lost', _('Affaire perdue')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='interactions',
        verbose_name=_("Tenant")
    )

    contact = models.ForeignKey(
        Contact,
        on_delete=models.CASCADE,
        related_name='interactions',
        verbose_name=_("Contact")
    )

    opportunity = models.ForeignKey(
        Opportunity,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='interactions',
        verbose_name=_("Opportunité")
    )

    support_ticket = models.ForeignKey(
        SupportTicket,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='interactions',
        verbose_name=_("Ticket de support")
    )

    # Informations de base
    interaction_type = models.CharField(_("Type d'interaction"), max_length=20, choices=INTERACTION_TYPES)
    direction = models.CharField(_("Direction"), max_length=10, choices=INTERACTION_DIRECTIONS)
    subject = models.CharField(_("Sujet"), max_length=200)
    description = models.TextField(_("Description"))

    # Timing
    interaction_date = models.DateTimeField(_("Date d'interaction"))
    duration_minutes = models.PositiveIntegerField(_("Durée (minutes)"), null=True, blank=True)

    # Résultat
    outcome = models.CharField(_("Résultat"), max_length=20, choices=INTERACTION_OUTCOMES, blank=True)
    next_action = models.CharField(_("Prochaine action"), max_length=200, blank=True)
    next_action_date = models.DateTimeField(_("Date de la prochaine action"), null=True, blank=True)

    # Responsable
    user = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='interactions',
        verbose_name=_("Utilisateur")
    )

    # Informations complémentaires
    location = models.CharField(_("Lieu"), max_length=200, blank=True)
    attendees = models.CharField(_("Participants"), max_length=500, blank=True)
    attachments = models.CharField(_("Pièces jointes"), max_length=500, blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Interaction")
        verbose_name_plural = _("Interactions")
        ordering = ['-interaction_date']
        indexes = [
            models.Index(fields=['interaction_type', 'interaction_date']),
            models.Index(fields=['contact', 'interaction_date']),
            models.Index(fields=['opportunity', 'interaction_date']),
        ]

    def __str__(self):
        return f"{self.interaction_type} - {self.contact.display_name} ({self.interaction_date.strftime('%d/%m/%Y')})"


class CustomerSegment(UUIDModel, TimeStampedModel):
    """Modèle pour la segmentation client"""

    SEGMENT_TYPES = [
        ('demographic', _('Démographique')),
        ('geographic', _('Géographique')),
        ('behavioral', _('Comportemental')),
        ('psychographic', _('Psychographique')),
        ('value_based', _('Basé sur la valeur')),
        ('lifecycle', _('Cycle de vie')),
        ('custom', _('Personnalisé')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='customer_segments',
        verbose_name=_("Tenant")
    )

    # Informations de base
    name = models.CharField(_("Nom du segment"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    segment_type = models.CharField(_("Type de segment"), max_length=20, choices=SEGMENT_TYPES)

    # Critères de segmentation (JSON)
    criteria = models.JSONField(_("Critères"), default=dict)

    # Statistiques
    contact_count = models.PositiveIntegerField(_("Nombre de contacts"), default=0)
    total_value = models.DecimalField(
        _("Valeur totale"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )
    average_value = models.DecimalField(
        _("Valeur moyenne"),
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)
    auto_update = models.BooleanField(_("Mise à jour automatique"), default=True)
    last_updated = models.DateTimeField(_("Dernière mise à jour"), null=True, blank=True)

    # Responsable
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_segments',
        verbose_name=_("Créé par")
    )

    class Meta:
        verbose_name = _("Segment client")
        verbose_name_plural = _("Segments clients")
        unique_together = ['tenant', 'name']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.contact_count} contacts)"

    def update_statistics(self):
        """Met à jour les statistiques du segment"""
        # Cette méthode sera implémentée dans le service
        pass


class CRMAnalytics(UUIDModel, TimeStampedModel):
    """Modèle pour les analytics CRM"""

    METRIC_TYPES = [
        ('lead_conversion', _('Conversion de leads')),
        ('sales_performance', _('Performance des ventes')),
        ('customer_satisfaction', _('Satisfaction client')),
        ('campaign_effectiveness', _('Efficacité des campagnes')),
        ('support_metrics', _('Métriques de support')),
        ('pipeline_health', _('Santé du pipeline')),
        ('customer_lifetime_value', _('Valeur vie client')),
        ('churn_rate', _('Taux de désabonnement')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='crm_analytics',
        verbose_name=_("Tenant")
    )

    # Informations de base
    metric_type = models.CharField(_("Type de métrique"), max_length=30, choices=METRIC_TYPES)
    metric_name = models.CharField(_("Nom de la métrique"), max_length=200)

    # Période
    period_start = models.DateField(_("Début de période"))
    period_end = models.DateField(_("Fin de période"))

    # Valeurs
    metric_value = models.DecimalField(
        _("Valeur de la métrique"),
        max_digits=15,
        decimal_places=4
    )
    target_value = models.DecimalField(
        _("Valeur cible"),
        max_digits=15,
        decimal_places=4,
        null=True,
        blank=True
    )
    previous_value = models.DecimalField(
        _("Valeur précédente"),
        max_digits=15,
        decimal_places=4,
        null=True,
        blank=True
    )

    # Métadonnées
    calculation_method = models.TextField(_("Méthode de calcul"), blank=True)
    data_source = models.CharField(_("Source des données"), max_length=100, blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    # Responsable
    calculated_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='calculated_crm_analytics',
        verbose_name=_("Calculé par")
    )

    class Meta:
        verbose_name = _("Analytics CRM")
        verbose_name_plural = _("Analytics CRM")
        unique_together = ['tenant', 'metric_type', 'metric_name', 'period_start', 'period_end']
        ordering = ['-period_end', 'metric_type']

    def __str__(self):
        return f"{self.metric_name} ({self.period_start} - {self.period_end}): {self.metric_value}"

    @property
    def variance_from_target(self):
        """Écart par rapport à la cible"""
        if self.target_value:
            return self.metric_value - self.target_value
        return None

    @property
    def variance_from_previous(self):
        """Écart par rapport à la période précédente"""
        if self.previous_value:
            return self.metric_value - self.previous_value
        return None

    @property
    def performance_vs_target(self):
        """Performance par rapport à la cible (%)"""
        if self.target_value and self.target_value != 0:
            return ((self.metric_value - self.target_value) / self.target_value) * 100
        return None

    @property
    def growth_rate(self):
        """Taux de croissance par rapport à la période précédente (%)"""
        if self.previous_value and self.previous_value != 0:
            return ((self.metric_value - self.previous_value) / self.previous_value) * 100
        return None
