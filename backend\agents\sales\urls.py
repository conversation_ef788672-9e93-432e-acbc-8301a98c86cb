"""
URLs pour l'agent Sales
"""
from django.urls import path
from . import views

app_name = 'sales'

urlpatterns = [
    # Statut et dashboard
    path('status/', views.sales_status, name='status'),
    path('dashboard/', views.sales_dashboard, name='dashboard'),
    path('performance/', views.sales_performance, name='performance'),
    path('insights/', views.sales_insights, name='insights'),

    # Gestion des clients
    path('customers/', views.CustomerListCreateView.as_view(), name='customer_list_create'),
    path('customers/<uuid:pk>/', views.CustomerDetailView.as_view(), name='customer_detail'),

    # Gestion des produits
    path('products/', views.ProductListCreateView.as_view(), name='product_list_create'),
    path('products/<uuid:pk>/', views.ProductDetailView.as_view(), name='product_detail'),

    # Gestion des opportunités
    path('opportunities/', views.OpportunityListCreateView.as_view(), name='opportunity_list_create'),
    path('opportunities/create/', views.OpportunityCreateView.as_view(), name='opportunity_create'),
    path('opportunities/<uuid:pk>/', views.OpportunityDetailView.as_view(), name='opportunity_detail'),
    path('opportunities/<uuid:opportunity_id>/stage/', views.OpportunityStageUpdateView.as_view(), name='opportunity_stage_update'),

    # Gestion des devis
    path('quotes/', views.QuoteListCreateView.as_view(), name='quote_list_create'),
    path('quotes/create/', views.QuoteCreateView.as_view(), name='quote_create'),
    path('quotes/<uuid:pk>/', views.QuoteDetailView.as_view(), name='quote_detail'),
    path('quotes/<uuid:quote_id>/status/', views.QuoteStatusUpdateView.as_view(), name='quote_status_update'),
]
