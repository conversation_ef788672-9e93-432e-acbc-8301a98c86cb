# 🗄️ GESTIONNAIRE INTERACTIF DE BASE POSTGRESQL
# Interface simple pour gérer votre base ERP HUB

import subprocess
import json
from datetime import datetime

def execute_sql(sql_command):
    """Exécuter une commande SQL"""
    try:
        cmd = [
            'docker', 'exec', 'erp_postgres', 
            'psql', '-U', 'erp_admin', '-d', 'erp_hub', 
            '-c', sql_command
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            return {'success': True, 'data': result.stdout}
        else:
            return {'success': False, 'error': result.stderr}
            
    except Exception as e:
        return {'success': False, 'error': str(e)}

def show_tables():
    """Afficher toutes les tables"""
    print("\n📊 TABLES DISPONIBLES")
    print("=" * 50)
    
    result = execute_sql("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;")
    
    if result['success']:
        lines = result['data'].strip().split('\n')[2:-1]  # Enlever header et footer
        for i, line in enumerate(lines, 1):
            table_name = line.strip()
            if table_name and table_name != '-':
                print(f"{i:2d}. {table_name}")
    else:
        print(f"❌ Erreur: {result['error']}")

def show_table_data(table_name):
    """Afficher les données d'une table"""
    print(f"\n📋 DONNÉES DE LA TABLE: {table_name}")
    print("=" * 60)
    
    # Compter les enregistrements
    count_result = execute_sql(f"SELECT COUNT(*) FROM {table_name};")
    if count_result['success']:
        count = count_result['data'].strip().split('\n')[2].strip()
        print(f"📊 Nombre d'enregistrements: {count}")
    
    # Afficher les données (limité à 10)
    result = execute_sql(f"SELECT * FROM {table_name} LIMIT 10;")
    
    if result['success']:
        print("\n📄 Aperçu (10 premiers enregistrements):")
        print(result['data'])
    else:
        print(f"❌ Erreur: {result['error']}")

def add_employee():
    """Ajouter un nouvel employé"""
    print("\n➕ AJOUTER UN NOUVEL EMPLOYÉ")
    print("=" * 40)
    
    try:
        # Générer un ID unique
        count_result = execute_sql("SELECT COUNT(*) FROM employees;")
        if count_result['success']:
            count = int(count_result['data'].strip().split('\n')[2].strip())
            emp_id = f"EMP{count+1:03d}"
            emp_number = f"E{count+1:03d}"
        else:
            emp_id = "EMP999"
            emp_number = "E999"
        
        print(f"🆔 ID généré: {emp_id}")
        
        # Collecter les informations
        first_name = input("👤 Prénom: ").strip()
        last_name = input("👤 Nom: ").strip()
        email = input("📧 Email: ").strip()
        position = input("💼 Poste: ").strip()
        department = input("🏢 Département: ").strip()
        salary = input("💰 Salaire: ").strip()
        
        if not all([first_name, last_name, email]):
            print("❌ Les champs prénom, nom et email sont obligatoires")
            return
        
        # Construire la requête SQL
        sql = f"""
        INSERT INTO employees (
            id, employee_number, first_name, last_name, 
            email, position, department, salary, status
        ) VALUES (
            '{emp_id}', '{emp_number}', '{first_name}', '{last_name}',
            '{email}', '{position}', '{department}', {salary or 'NULL'}, 'active'
        );
        """
        
        result = execute_sql(sql)
        
        if result['success']:
            print(f"✅ Employé {first_name} {last_name} ajouté avec succès!")
            print(f"🆔 ID: {emp_id}")
        else:
            print(f"❌ Erreur: {result['error']}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def update_salary():
    """Modifier le salaire d'un employé"""
    print("\n💰 MODIFIER LE SALAIRE D'UN EMPLOYÉ")
    print("=" * 45)
    
    # Afficher les employés
    result = execute_sql("SELECT employee_number, first_name, last_name, salary FROM employees ORDER BY last_name;")
    
    if result['success']:
        print("👥 Employés disponibles:")
        print(result['data'])
        
        emp_number = input("\n🆔 Numéro d'employé (ex: E001): ").strip()
        new_salary = input("💰 Nouveau salaire: ").strip()
        
        if emp_number and new_salary:
            sql = f"UPDATE employees SET salary = {new_salary} WHERE employee_number = '{emp_number}';"
            
            update_result = execute_sql(sql)
            
            if update_result['success']:
                print(f"✅ Salaire mis à jour pour l'employé {emp_number}")
            else:
                print(f"❌ Erreur: {update_result['error']}")
        else:
            print("❌ Numéro d'employé et salaire requis")
    else:
        print(f"❌ Erreur: {result['error']}")

def backup_database():
    """Créer une sauvegarde de la base"""
    print("\n💾 SAUVEGARDE DE LA BASE DE DONNÉES")
    print("=" * 45)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"backup_erp_hub_{timestamp}.sql"
    
    try:
        cmd = [
            'docker', 'exec', 'erp_postgres',
            'pg_dump', '-U', 'erp_admin', 'erp_hub'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(result.stdout)
            
            print(f"✅ Sauvegarde créée: {backup_file}")
            print(f"📊 Taille: {len(result.stdout)} caractères")
        else:
            print(f"❌ Erreur: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def show_statistics():
    """Afficher les statistiques de la base"""
    print("\n📊 STATISTIQUES DE LA BASE ERP HUB")
    print("=" * 50)
    
    tables = [
        ('employees', 'Employés'),
        ('customers', 'Clients'),
        ('products', 'Produits'),
        ('orders', 'Commandes'),
        ('suppliers', 'Fournisseurs'),
        ('budgets', 'Budgets'),
        ('contacts', 'Contacts CRM'),
        ('kpis', 'Indicateurs BI')
    ]
    
    for table, description in tables:
        result = execute_sql(f"SELECT COUNT(*) FROM {table};")
        if result['success']:
            count = result['data'].strip().split('\n')[2].strip()
            print(f"📋 {description:15} : {count:>3} enregistrements")

def main_menu():
    """Menu principal"""
    while True:
        print("\n" + "="*60)
        print("🗄️ GESTIONNAIRE DE BASE POSTGRESQL - ERP HUB")
        print("="*60)
        print("1. 📊 Voir toutes les tables")
        print("2. 📋 Voir les données d'une table")
        print("3. ➕ Ajouter un employé")
        print("4. 💰 Modifier un salaire")
        print("5. 📊 Statistiques générales")
        print("6. 💾 Sauvegarder la base")
        print("7. 🔧 Commande SQL personnalisée")
        print("8. ❌ Quitter")
        print("="*60)
        
        choice = input("🎯 Votre choix (1-8): ").strip()
        
        if choice == '1':
            show_tables()
            
        elif choice == '2':
            show_tables()
            table_name = input("\n📋 Nom de la table: ").strip()
            if table_name:
                show_table_data(table_name)
                
        elif choice == '3':
            add_employee()
            
        elif choice == '4':
            update_salary()
            
        elif choice == '5':
            show_statistics()
            
        elif choice == '6':
            backup_database()
            
        elif choice == '7':
            print("\n🔧 COMMANDE SQL PERSONNALISÉE")
            print("=" * 40)
            print("⚠️ Attention: Soyez prudent avec les commandes de modification")
            sql = input("SQL> ").strip()
            
            if sql:
                result = execute_sql(sql)
                if result['success']:
                    print("✅ Résultat:")
                    print(result['data'])
                else:
                    print(f"❌ Erreur: {result['error']}")
                    
        elif choice == '8':
            print("\n👋 Au revoir!")
            break
            
        else:
            print("❌ Choix invalide. Veuillez choisir entre 1 et 8.")
        
        input("\n⏸️ Appuyez sur Entrée pour continuer...")

if __name__ == "__main__":
    print("🚀 Démarrage du gestionnaire de base PostgreSQL...")
    
    # Vérifier la connexion
    result = execute_sql("SELECT 1;")
    
    if result['success']:
        print("✅ Connexion PostgreSQL réussie")
        main_menu()
    else:
        print("❌ Impossible de se connecter à PostgreSQL")
        print("💡 Assurez-vous que le conteneur erp_postgres est démarré")
        print("🔧 Commande: docker ps | grep erp_postgres")
