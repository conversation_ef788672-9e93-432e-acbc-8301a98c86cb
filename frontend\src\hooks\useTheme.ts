import { useState, useEffect } from 'react';

export type ThemeMode = 'light' | 'dark' | 'auto';

interface ThemeState {
  mode: ThemeMode;
  isDark: boolean;
}

export const useTheme = () => {
  const [themeState, setThemeState] = useState<ThemeState>(() => {
    const savedMode = localStorage.getItem('theme-mode') as ThemeMode || 'auto';
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    return {
      mode: savedMode,
      isDark: savedMode === 'dark' || (savedMode === 'auto' && systemPrefersDark)
    };
  });

  // Écouter les changements de préférence système
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      if (themeState.mode === 'auto') {
        setThemeState(prev => ({ ...prev, isDark: e.matches }));
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [themeState.mode]);

  // Appliquer le thème au document
  useEffect(() => {
    const root = document.documentElement;
    
    if (themeState.isDark) {
      root.classList.add('dark');
      root.style.colorScheme = 'dark';
    } else {
      root.classList.remove('dark');
      root.style.colorScheme = 'light';
    }
  }, [themeState.isDark]);

  const setThemeMode = (mode: ThemeMode) => {
    localStorage.setItem('theme-mode', mode);
    
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const isDark = mode === 'dark' || (mode === 'auto' && systemPrefersDark);
    
    setThemeState({ mode, isDark });
  };

  const toggleTheme = () => {
    const newMode = themeState.mode === 'light' ? 'dark' : 
                   themeState.mode === 'dark' ? 'auto' : 'light';
    setThemeMode(newMode);
  };

  return {
    mode: themeState.mode,
    isDark: themeState.isDark,
    setThemeMode,
    toggleTheme
  };
};
