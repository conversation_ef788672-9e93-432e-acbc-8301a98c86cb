"""
URLs pour l'agent BI
"""
from django.urls import path
from . import views

app_name = 'bi'

urlpatterns = [
    # Statut et dashboard
    path('status/', views.bi_status, name='status'),
    path('dashboard/', views.bi_dashboard, name='dashboard'),

    # KPIs
    path('kpis/', views.KPIListCreateView.as_view(), name='kpi-list'),
    path('kpis/create/', views.KPICreateView.as_view(), name='kpi-create'),
    path('kpis/<uuid:pk>/', views.KPIDetailView.as_view(), name='kpi-detail'),
    path('kpis/<uuid:kpi_id>/calculate/', views.calculate_kpi, name='kpi-calculate'),

    # Alertes
    path('alerts/', views.AlertListCreateView.as_view(), name='alert-list'),
    path('alerts/<uuid:pk>/', views.AlertDetailView.as_view(), name='alert-detail'),

    # Tâches d'analyse
    path('analysis-jobs/', views.AnalysisJobListCreateView.as_view(), name='analysis-job-list'),
    path('analysis-jobs/<uuid:pk>/', views.AnalysisJobDetailView.as_view(), name='analysis-job-detail'),

    # Analytics et insights
    path('insights/', views.bi_insights, name='insights'),
]
