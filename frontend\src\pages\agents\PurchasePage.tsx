import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'
import { 
  BuildingOfficeIcon, 
  DocumentTextIcon, 
  TruckIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

import { purchaseApi, type PurchaseDashboard, type PurchaseRequest, type Supplier, type PurchaseOrder } from '@/services/api/agentsApi'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import Button from '@/components/ui/Button'

const PurchasePage: React.FC = () => {
  const queryClient = useQueryClient()
  const [selectedTab, setSelectedTab] = useState<'overview' | 'requests' | 'suppliers' | 'orders'>('overview')

  // Récupération des données du dashboard
  const { data: dashboard, isLoading, error, refetch } = useQuery<PurchaseDashboard>(
    'purchase-dashboard',
    purchaseApi.getDashboard,
    {
      refetchInterval: 60000, // Actualisation toutes les minutes
      staleTime: 30000,
    }
  )

  // Récupération des demandes d'achat en attente
  const { data: pendingRequests } = useQuery<PurchaseRequest[]>(
    'pending-requests',
    () => purchaseApi.getPurchaseRequests({ status: 'submitted' }),
    {
      enabled: selectedTab === 'requests',
    }
  )

  // Récupération des fournisseurs
  const { data: suppliers } = useQuery<Supplier[]>(
    'suppliers',
    () => purchaseApi.getSuppliers({ active: true }),
    {
      enabled: selectedTab === 'suppliers',
    }
  )

  // Récupération des bons de commande
  const { data: orders } = useQuery<PurchaseOrder[]>(
    'purchase-orders',
    () => purchaseApi.getPurchaseOrders({ status: 'sent,confirmed' }),
    {
      enabled: selectedTab === 'orders',
    }
  )

  // Mutation pour approuver les demandes d'achat
  const approveRequestMutation = useMutation(
    ({ id, action, comments }: { id: string; action: 'approve' | 'reject'; comments?: string }) =>
      purchaseApi.approvePurchaseRequest(id, action, comments),
    {
      onSuccess: () => {
        toast.success('Demande d\'achat traitée avec succès')
        queryClient.invalidateQueries('pending-requests')
        queryClient.invalidateQueries('purchase-dashboard')
      },
      onError: (error: any) => {
        toast.error(error.message || 'Erreur lors du traitement de la demande')
      }
    }
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Erreur de chargement</h3>
        <p className="mt-1 text-sm text-gray-500">
          Impossible de charger les données d'achat
        </p>
        <div className="mt-6">
          <Button onClick={() => refetch()}>Réessayer</Button>
        </div>
      </div>
    )
  }

  if (!dashboard) {
    return null
  }

  const tabs = [
    { id: 'overview', name: 'Vue d\'ensemble', icon: ChartBarIcon },
    { id: 'requests', name: 'Demandes', icon: DocumentTextIcon },
    { id: 'suppliers', name: 'Fournisseurs', icon: BuildingOfficeIcon },
    { id: 'orders', name: 'Commandes', icon: TruckIcon },
  ]

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Agent Achats</h1>
            <p className="mt-2 text-purple-100">
              Gestion des achats et approvisionnements - {dashboard.tenant}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">
              {dashboard.requests.pending}
            </div>
            <div className="text-sm text-purple-100">Demandes en attente</div>
          </div>
        </div>
      </div>

      {/* Métriques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Demandes en attente"
          value={dashboard.requests.pending.toString()}
          color="orange"
          icon={DocumentTextIcon}
        />
        <MetricCard
          title="Commandes actives"
          value={dashboard.orders.active.toString()}
          color="blue"
          icon={TruckIcon}
        />
        <MetricCard
          title="Fournisseurs approuvés"
          value={dashboard.suppliers.approved.toString()}
          color="green"
          icon={BuildingOfficeIcon}
        />
        <MetricCard
          title="Note fournisseurs"
          value={`${dashboard.suppliers.avg_rating.toFixed(1)}/5`}
          color="purple"
          icon={ChartBarIcon}
        />
      </div>

      {/* Performance des livraisons */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Performance des Livraisons</h3>
          <span className={`text-sm font-medium ${
            dashboard.performance.on_time_delivery_rate >= 90 ? 'text-green-600' : 'text-orange-600'
          }`}>
            {dashboard.performance.on_time_delivery_rate.toFixed(1)}% à temps
          </span>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600">Délai moyen</p>
            <p className="text-2xl font-bold text-gray-900">
              {dashboard.performance.avg_delivery_delay > 0 ? '+' : ''}{dashboard.performance.avg_delivery_delay.toFixed(1)} jours
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Taux de ponctualité</p>
            <div className="w-full bg-gray-200 rounded-full h-3 mt-2">
              <div 
                className={`h-3 rounded-full transition-all duration-300 ${
                  dashboard.performance.on_time_delivery_rate >= 90 ? 'bg-green-500' : 'bg-orange-500'
                }`}
                style={{ width: `${dashboard.performance.on_time_delivery_rate}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Actions en attente */}
      {dashboard.requests.pending > 0 && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex">
            <ClockIcon className="h-5 w-5 text-orange-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-orange-800">
                Actions requises
              </h3>
              <div className="mt-2 text-sm text-orange-700">
                <p>
                  {dashboard.requests.pending} demande(s) d'achat en attente d'approbation
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation par onglets */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`
                  flex items-center py-2 px-1 border-b-2 font-medium text-sm
                  ${selectedTab === tab.id
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Contenu des onglets */}
      <div className="mt-6">
        {selectedTab === 'overview' && (
          <OverviewTab dashboard={dashboard} />
        )}
        {selectedTab === 'requests' && (
          <RequestsTab 
            requests={pendingRequests} 
            onProcessRequest={(id, action, comments) => 
              approveRequestMutation.mutate({ id, action, comments })
            }
            isProcessing={approveRequestMutation.isLoading}
          />
        )}
        {selectedTab === 'suppliers' && (
          <SuppliersTab suppliers={suppliers} />
        )}
        {selectedTab === 'orders' && (
          <OrdersTab orders={orders} />
        )}
      </div>
    </div>
  )
}

// Composant pour les métriques
interface MetricCardProps {
  title: string
  value: string
  color: 'blue' | 'green' | 'red' | 'yellow' | 'orange' | 'purple'
  icon: React.ComponentType<{ className?: string }>
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, color, icon: Icon }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-700 border-blue-200',
    green: 'bg-green-50 text-green-700 border-green-200',
    red: 'bg-red-50 text-red-700 border-red-200',
    yellow: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    orange: 'bg-orange-50 text-orange-700 border-orange-200',
    purple: 'bg-purple-50 text-purple-700 border-purple-200',
  }

  return (
    <div className={`card border ${colorClasses[color]}`}>
      <div className="card-body">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-8 w-8" />
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium opacity-75">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Onglet Vue d'ensemble
const OverviewTab: React.FC<{ dashboard: PurchaseDashboard }> = ({ dashboard }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Statistiques des achats */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Statistiques des Achats</h3>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Demandes totales</span>
              <span className="font-semibold">{dashboard.requests.total}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Valeur en attente</span>
              <span className="font-semibold">{(dashboard.requests.pending_value / 1000).toFixed(0)}k €</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Commandes actives</span>
              <span className="font-semibold">{dashboard.orders.active}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Valeur commandes</span>
              <span className="font-semibold">{(dashboard.orders.active_value / 1000).toFixed(0)}k €</span>
            </div>
          </div>
        </div>
      </div>

      {/* Activités récentes */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Activités Récentes</h3>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            {dashboard.recent_activities.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.type === 'purchase_request_created' ? 'bg-orange-400' : 'bg-blue-400'
                }`} />
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <div className="flex justify-between items-center mt-1">
                    <p className="text-xs text-gray-500">
                      {new Date(activity.date).toLocaleDateString('fr-FR')}
                    </p>
                    <p className="text-xs font-medium text-purple-600">
                      {(activity.value / 1000).toFixed(0)}k €
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Onglet Demandes
const RequestsTab: React.FC<{
  requests?: PurchaseRequest[]
  onProcessRequest: (id: string, action: 'approve' | 'reject', comments?: string) => void
  isProcessing: boolean
}> = ({ requests, onProcessRequest, isProcessing }) => {
  if (!requests) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Demandes d'Achat en Attente</h3>
      </div>
      <div className="card-body">
        {requests.length === 0 ? (
          <p className="text-gray-500 text-center py-8">Aucune demande d'achat en attente</p>
        ) : (
          <div className="space-y-4">
            {requests.map((request) => (
              <div key={request.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{request.title}</h4>
                    <p className="text-sm text-gray-600">Demandeur: {request.requester_name}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>Montant: {(request.estimated_total / 1000).toFixed(1)}k €</span>
                      <span>Requis le: {new Date(request.required_date).toLocaleDateString('fr-FR')}</span>
                      <span className={`badge ${
                        request.priority >= 4 ? 'badge-danger' : 
                        request.priority >= 3 ? 'badge-warning' : 'badge-secondary'
                      }`}>
                        Priorité {request.priority}
                      </span>
                    </div>
                    {request.justification && (
                      <p className="text-sm text-gray-600 mt-2">
                        <strong>Justification:</strong> {request.justification}
                      </p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="success"
                      onClick={() => onProcessRequest(request.id, 'approve')}
                      disabled={isProcessing}
                      leftIcon={<CheckCircleIcon className="h-4 w-4" />}
                    >
                      Approuver
                    </Button>
                    <Button
                      size="sm"
                      variant="danger"
                      onClick={() => onProcessRequest(request.id, 'reject')}
                      disabled={isProcessing}
                      leftIcon={<XCircleIcon className="h-4 w-4" />}
                    >
                      Rejeter
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// Onglet Fournisseurs
const SuppliersTab: React.FC<{ suppliers?: Supplier[] }> = ({ suppliers }) => {
  if (!suppliers) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Liste des Fournisseurs</h3>
      </div>
      <div className="card-body">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fournisseur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Note globale
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {suppliers.map((supplier) => (
                <tr key={supplier.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{supplier.name}</div>
                      <div className="text-sm text-gray-500">{supplier.supplier_code}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="badge badge-secondary">
                      {supplier.supplier_type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div>{supplier.email}</div>
                      <div className="text-gray-500">{supplier.phone}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {supplier.overall_rating ? `${supplier.overall_rating.toFixed(1)}/5` : 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`badge ${
                      supplier.is_approved ? 'badge-success' : 'badge-warning'
                    }`}>
                      {supplier.is_approved ? 'Approuvé' : 'En attente'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

// Onglet Commandes
const OrdersTab: React.FC<{ orders?: PurchaseOrder[] }> = ({ orders }) => {
  if (!orders) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Bons de Commande Actifs</h3>
      </div>
      <div className="card-body">
        {orders.length === 0 ? (
          <p className="text-gray-500 text-center py-8">Aucun bon de commande actif</p>
        ) : (
          <div className="space-y-4">
            {orders.map((order) => (
              <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{order.order_number}</h4>
                    <p className="text-sm text-gray-600">{order.supplier_name}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>Montant: {(order.total_amount / 1000).toFixed(1)}k €</span>
                      <span>Livraison prévue: {new Date(order.expected_delivery_date).toLocaleDateString('fr-FR')}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`badge ${
                      order.status === 'confirmed' ? 'badge-success' : 
                      order.status === 'sent' ? 'badge-warning' : 'badge-secondary'
                    }`}>
                      {order.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default PurchasePage
