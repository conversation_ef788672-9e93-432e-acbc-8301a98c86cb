import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../hooks/useTheme';

export const ThemeToggle: React.FC = () => {
  const { mode, isDark, toggleTheme } = useTheme();

  const getIcon = () => {
    switch (mode) {
      case 'light': return '☀️';
      case 'dark': return '🌙';
      case 'auto': return '🔄';
      default: return '☀️';
    }
  };

  const getLabel = () => {
    switch (mode) {
      case 'light': return 'Mode Clair';
      case 'dark': return 'Mode Sombre';
      case 'auto': return 'Mode Auto';
      default: return 'Mode Clair';
    }
  };

  return (
    <motion.button
      onClick={toggleTheme}
      className={`
        relative flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300
        ${isDark 
          ? 'bg-gray-800 text-white hover:bg-gray-700' 
          : 'bg-white text-gray-800 hover:bg-gray-100'
        }
        border border-gray-300 dark:border-gray-600
        shadow-sm hover:shadow-md
      `}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      title={`Basculer vers le thème suivant (actuellement: ${getLabel()})`}
    >
      <motion.span
        key={mode}
        initial={{ rotate: -180, opacity: 0 }}
        animate={{ rotate: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="text-lg"
      >
        {getIcon()}
      </motion.span>
      
      <span className="text-sm font-medium hidden sm:inline">
        {getLabel()}
      </span>

      {/* Indicateur de mode auto */}
      {mode === 'auto' && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"
        />
      )}
    </motion.button>
  );
};
