<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent HR - Gestion RH | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }

        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #8b5cf6 30%, #a855f7 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #8b5cf6;
            color: white;
        }

        .btn-primary:hover {
            background: #7c3aed;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-header {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #8b5cf6;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }

        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }

        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }

        .section-content {
            padding: 1.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }

        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }

        .close-btn:hover {
            color: #374151;
        }

        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #8b5cf6;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }

        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }

            .main-content {
                padding: 1rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">👥 Agent HR - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="openModal('employeeModal')">
                <span class="material-icons" style="font-size: 1rem;">add</span>
                Nouvel Employé
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion des Ressources Humaines</h1>
            <p class="page-subtitle">Administration du personnel et gestion RH</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalEmployees">0</div>
                <div class="stat-label">Employés Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeEmployees">0</div>
                <div class="stat-label">Employés Actifs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="newHires">0</div>
                <div class="stat-label">Nouvelles Embauches</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgSalary">0€</div>
                <div class="stat-label">Salaire Moyen</div>
            </div>
        </div>

        <!-- Liste des employés -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Liste des Employés</h2>
                <button class="btn btn-primary" onclick="refreshEmployees()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div id="alertContainer"></div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nom Complet</th>
                                <th>Email</th>
                                <th>Poste</th>
                                <th>Département</th>
                                <th>Salaire</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="employeesTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Employé -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Nouvel Employé</h3>
                <button class="close-btn" onclick="closeModal('employeeModal')">&times;</button>
            </div>
            <form id="employeeForm">
                <div id="modalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="firstName">Prénom *</label>
                        <input type="text" id="firstName" name="firstName" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="lastName">Nom *</label>
                        <input type="text" id="lastName" name="lastName" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="email">Email *</label>
                        <input type="email" id="email" name="email" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="phone">Téléphone</label>
                        <input type="tel" id="phone" name="phone" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="position">Poste *</label>
                        <input type="text" id="position" name="position" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="department">Département *</label>
                        <select id="department" name="department" class="form-select" required>
                            <option value="">Sélectionner un département</option>
                            <option value="IT">Informatique</option>
                            <option value="HR">Ressources Humaines</option>
                            <option value="Sales">Commercial</option>
                            <option value="Marketing">Marketing</option>
                            <option value="Finance">Finance</option>
                            <option value="Operations">Opérations</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="salary">Salaire (€)</label>
                        <input type="number" id="salary" name="salary" class="form-input" min="0" step="100">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="hireDate">Date d'embauche</label>
                        <input type="date" id="hireDate" name="hireDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="status">Statut</label>
                        <select id="status" name="status" class="form-select">
                            <option value="active">Actif</option>
                            <option value="inactive">Inactif</option>
                            <option value="on_leave">En congé</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="notes">Notes</label>
                    <textarea id="notes" name="notes" class="form-textarea" placeholder="Notes additionnelles..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('employeeModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveEmployeeBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let employees = [];
        let editingEmployeeId = null;
        let isLoading = false;

        // Données de démonstration réalistes
        const demoEmployees = [
            {
                id: 1,
                firstName: 'Jean',
                lastName: 'Dupont',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 89',
                position: 'Développeur Senior',
                department: 'IT',
                salary: 55000,
                hireDate: '2023-01-15',
                status: 'active',
                notes: 'Excellent développeur avec expertise en React et Node.js'
            },
            {
                id: 2,
                firstName: 'Marie',
                lastName: 'Martin',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 90',
                position: 'Responsable RH',
                department: 'HR',
                salary: 48000,
                hireDate: '2022-06-01',
                status: 'active',
                notes: 'Gère les recrutements et formations, 8 ans d\'expérience'
            },
            {
                id: 3,
                firstName: 'Pierre',
                lastName: 'Bernard',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 91',
                position: 'Commercial Senior',
                department: 'Sales',
                salary: 42000,
                hireDate: '2023-03-10',
                status: 'active',
                notes: 'Spécialisé dans les ventes B2B, excellent relationnel'
            },
            {
                id: 4,
                firstName: 'Sophie',
                lastName: 'Dubois',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 92',
                position: 'Directrice Marketing',
                department: 'Marketing',
                salary: 62000,
                hireDate: '2021-09-15',
                status: 'active',
                notes: 'Stratégie digitale et communication, MBA Marketing'
            },
            {
                id: 5,
                firstName: 'Thomas',
                lastName: 'Leroy',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 93',
                position: 'Comptable',
                department: 'Finance',
                salary: 38000,
                hireDate: '2023-07-01',
                status: 'active',
                notes: 'Diplômé DCG, spécialisé en comptabilité analytique'
            },
            {
                id: 6,
                firstName: 'Laura',
                lastName: 'Chen',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 94',
                position: 'Chef de Projet',
                department: 'Operations',
                salary: 45000,
                hireDate: '2022-11-20',
                status: 'active',
                notes: 'Gestion de projets agiles, certification PMP'
            },
            {
                id: 7,
                firstName: 'Alexandre',
                lastName: 'Moreau',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 95',
                position: 'Développeur Junior',
                department: 'IT',
                salary: 32000,
                hireDate: '2024-01-08',
                status: 'active',
                notes: 'Fraîchement diplômé, motivé et apprend rapidement'
            },
            {
                id: 8,
                firstName: 'Camille',
                lastName: 'Rousseau',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 96',
                position: 'Assistante RH',
                department: 'HR',
                salary: 28000,
                hireDate: '2023-05-15',
                status: 'on_leave',
                notes: 'En congé maternité, retour prévu en mars 2024'
            },
            {
                id: 9,
                firstName: 'Nicolas',
                lastName: 'Petit',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 97',
                position: 'Responsable Logistique',
                department: 'Operations',
                salary: 41000,
                hireDate: '2022-02-14',
                status: 'active',
                notes: 'Optimisation des flux, réduction des coûts de 15%'
            },
            {
                id: 10,
                firstName: 'Émilie',
                lastName: 'Garcia',
                email: '<EMAIL>',
                phone: '+33 1 23 45 67 98',
                position: 'Analyste Financier',
                department: 'Finance',
                salary: 44000,
                hireDate: '2023-09-01',
                status: 'active',
                notes: 'Analyse des performances, reporting mensuel'
            }
        ];

        function showAlert(message, type = 'error', container = 'alertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            if (modalId === 'employeeModal') {
                document.getElementById('modalTitle').textContent = editingEmployeeId ? 'Modifier Employé' : 'Nouvel Employé';
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            if (modalId === 'employeeModal') {
                document.getElementById('employeeForm').reset();
                document.getElementById('modalAlertContainer').innerHTML = '';
                editingEmployeeId = null;
            }
        }

        function updateStats() {
            const total = employees.length;
            const active = employees.filter(emp => emp.status === 'active').length;
            const newHires = employees.filter(emp => {
                const hireDate = new Date(emp.hireDate);
                const threeMonthsAgo = new Date();
                threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
                return hireDate >= threeMonthsAgo;
            }).length;
            const avgSalary = total > 0 ? Math.round(employees.reduce((sum, emp) => sum + (emp.salary || 0), 0) / total) : 0;

            document.getElementById('totalEmployees').textContent = total;
            document.getElementById('activeEmployees').textContent = active;
            document.getElementById('newHires').textContent = newHires;
            document.getElementById('avgSalary').textContent = avgSalary.toLocaleString() + '€';
        }

        function renderEmployeesTable() {
            const tbody = document.getElementById('employeesTableBody');

            if (employees.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun employé trouvé. Cliquez sur "Nouvel Employé" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = employees.map(employee => {
                const statusBadge = getStatusBadge(employee.status);
                return `
                    <tr>
                        <td>${employee.id}</td>
                        <td>${employee.firstName} ${employee.lastName}</td>
                        <td>${employee.email}</td>
                        <td>${employee.position}</td>
                        <td>${employee.department}</td>
                        <td>${employee.salary ? employee.salary.toLocaleString() + '€' : '-'}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editEmployee(${employee.id})" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteEmployee(${employee.id})" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getStatusBadge(status) {
            const badges = {
                'active': '<span class="badge badge-success">Actif</span>',
                'inactive': '<span class="badge badge-danger">Inactif</span>',
                'on_leave': '<span class="badge badge-warning">En congé</span>'
            };
            return badges[status] || '<span class="badge badge-danger">Inconnu</span>';
        }

        async function loadEmployees() {
            try {
                // Tentative de chargement depuis l'API
                const token = localStorage.getItem('access_token');
                if (token) {
                    const response = await fetch('http://localhost:8000/api/agents/hr/employees/', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        employees = await response.json();
                    } else {
                        throw new Error('Erreur API');
                    }
                } else {
                    throw new Error('Pas de token');
                }
            } catch (error) {
                console.log('Chargement des données de démonstration');
                employees = [...demoEmployees];
            }

            renderEmployeesTable();
            updateStats();
        }

        function refreshEmployees() {
            loadEmployees();
            showAlert('Données actualisées avec succès', 'success');
        }

        function editEmployee(id) {
            const employee = employees.find(emp => emp.id === id);
            if (!employee) return;

            editingEmployeeId = id;

            // Remplir le formulaire
            document.getElementById('firstName').value = employee.firstName;
            document.getElementById('lastName').value = employee.lastName;
            document.getElementById('email').value = employee.email;
            document.getElementById('phone').value = employee.phone || '';
            document.getElementById('position').value = employee.position;
            document.getElementById('department').value = employee.department;
            document.getElementById('salary').value = employee.salary || '';
            document.getElementById('hireDate').value = employee.hireDate;
            document.getElementById('status').value = employee.status;
            document.getElementById('notes').value = employee.notes || '';

            openModal('employeeModal');
        }

        function deleteEmployee(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cet employé ?')) {
                employees = employees.filter(emp => emp.id !== id);
                renderEmployeesTable();
                updateStats();
                showAlert('Employé supprimé avec succès', 'success');
            }
        }

        // Gestion du formulaire
        document.getElementById('employeeForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const employeeData = {
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                position: formData.get('position'),
                department: formData.get('department'),
                salary: formData.get('salary') ? parseInt(formData.get('salary')) : null,
                hireDate: formData.get('hireDate'),
                status: formData.get('status'),
                notes: formData.get('notes')
            };

            try {
                if (editingEmployeeId) {
                    // Modification
                    const index = employees.findIndex(emp => emp.id === editingEmployeeId);
                    if (index !== -1) {
                        employees[index] = { ...employees[index], ...employeeData };
                        showAlert('Employé modifié avec succès', 'success');
                    }
                } else {
                    // Création
                    const newEmployee = {
                        id: Math.max(...employees.map(emp => emp.id), 0) + 1,
                        ...employeeData
                    };
                    employees.push(newEmployee);
                    showAlert('Employé créé avec succès', 'success');
                }

                renderEmployeesTable();
                updateStats();
                closeModal('employeeModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'modalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadEmployees();
            }
        });
    </script>
</body>
</html>
