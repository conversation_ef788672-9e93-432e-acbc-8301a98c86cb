"""
Vues pour l'agent BI
"""
import logging
from django.db import models
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema

from core.permissions import BIReadPermission, BIWritePermission
from .services import BIService
from .models import DataSource, Dataset, Report, Dashboard, KPI, Alert, AnalysisJob
from .serializers import (
    DataSourceSerializer, DatasetSerializer, ReportSerializer,
    DashboardSerializer, KPISerializer, KPICreateSerializer,
    AlertSerializer, AnalysisJobSerializer, BIDashboardSerializer,
    BIInsightSerializer, KPICalculationSerializer
)

logger = logging.getLogger('agents.bi')


@extend_schema(
    summary="Statut de l'agent BI",
    description="Retourne le statut de l'agent BI"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def bi_status(request):
    """Retourne le statut de l'agent BI"""
    try:
        bi_service = BIService(request.user.tenant)

        return Response({
            'status': 'active',
            'agent': 'bi',
            'message': 'Agent BI opérationnel',
            'capabilities': [
                'data_analysis',
                'report_generation',
                'dashboard_creation',
                'kpi_monitoring',
                'alert_management',
                'predictive_analytics',
                'data_visualization',
                'business_insights'
            ]
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut BI: {str(e)}")
        return Response({
            'status': 'error',
            'agent': 'bi',
            'message': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Dashboard de l'agent BI",
    description="Retourne les données complètes du dashboard BI"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, BIReadPermission])
def bi_dashboard(request):
    """Retourne les données du dashboard BI"""
    try:
        bi_service = BIService(request.user.tenant)
        dashboard_data = bi_service.get_bi_dashboard()

        return Response(dashboard_data)
    except Exception as e:
        logger.error(f"Erreur lors de la génération du dashboard BI: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Gestion des KPIs
class KPIListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des KPIs"""
    serializer_class = KPISerializer
    permission_classes = [IsAuthenticated, BIReadPermission]

    def get_queryset(self):
        queryset = KPI.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        kpi_type = self.request.query_params.get('type')
        if kpi_type:
            queryset = queryset.filter(kpi_type=kpi_type)

        calculation_method = self.request.query_params.get('method')
        if calculation_method:
            queryset = queryset.filter(calculation_method=calculation_method)

        is_active = self.request.query_params.get('active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        dataset_id = self.request.query_params.get('dataset')
        if dataset_id:
            queryset = queryset.filter(dataset_id=dataset_id)

        # Recherche par nom
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(description__icontains=search)
            )

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant, created_by=self.request.user)


class KPIDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer un KPI"""
    serializer_class = KPISerializer
    permission_classes = [IsAuthenticated, BIWritePermission]

    def get_queryset(self):
        return KPI.objects.filter(tenant=self.request.user.tenant)


class KPICreateView(APIView):
    """Vue pour créer des KPIs avec logique métier"""
    permission_classes = [IsAuthenticated, BIWritePermission]

    @extend_schema(
        summary="Créer un KPI",
        description="Crée un nouveau KPI avec configuration de calcul",
        request=KPICreateSerializer
    )
    def post(self, request):
        """Crée un nouveau KPI"""
        try:
            serializer = KPICreateSerializer(data=request.data)
            if serializer.is_valid():
                bi_service = BIService(request.user.tenant)

                result = bi_service.create_kpi(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création du KPI: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@extend_schema(
    summary="Calculer un KPI",
    description="Calcule la valeur d'un KPI spécifique"
)
@api_view(['POST'])
@permission_classes([IsAuthenticated, BIWritePermission])
def calculate_kpi(request, kpi_id):
    """Calcule la valeur d'un KPI"""
    try:
        bi_service = BIService(request.user.tenant)
        result = bi_service.calculate_kpi(kpi_id)

        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Erreur lors du calcul du KPI: {str(e)}")
        return Response({
            'error': f'Erreur interne: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Gestion des alertes
class AlertListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des alertes"""
    serializer_class = AlertSerializer
    permission_classes = [IsAuthenticated, BIReadPermission]

    def get_queryset(self):
        queryset = Alert.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        alert_type = self.request.query_params.get('type')
        if alert_type:
            queryset = queryset.filter(alert_type=alert_type)

        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        kpi_id = self.request.query_params.get('kpi')
        if kpi_id:
            queryset = queryset.filter(kpi_id=kpi_id)

        # Recherche par nom
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(description__icontains=search)
            )

        return queryset.order_by('-last_triggered', '-created_at')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant, created_by=self.request.user)


class AlertDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une alerte"""
    serializer_class = AlertSerializer
    permission_classes = [IsAuthenticated, BIWritePermission]

    def get_queryset(self):
        return Alert.objects.filter(tenant=self.request.user.tenant)


# Gestion des tâches d'analyse
class AnalysisJobListCreateView(generics.ListCreateAPIView):
    """Vue pour lister et créer des tâches d'analyse"""
    serializer_class = AnalysisJobSerializer
    permission_classes = [IsAuthenticated, BIReadPermission]

    def get_queryset(self):
        queryset = AnalysisJob.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        job_type = self.request.query_params.get('type')
        if job_type:
            queryset = queryset.filter(job_type=job_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        # Recherche par nom
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(description__icontains=search)
            )

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.user.tenant, created_by=self.request.user)


class AnalysisJobDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vue pour consulter, modifier et supprimer une tâche d'analyse"""
    serializer_class = AnalysisJobSerializer
    permission_classes = [IsAuthenticated, BIWritePermission]

    def get_queryset(self):
        return AnalysisJob.objects.filter(tenant=self.request.user.tenant)


# Insights et analytics
@extend_schema(
    summary="Insights BI IA",
    description="Génère des insights et recommandations BI basés sur l'IA"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, BIReadPermission])
def bi_insights(request):
    """Génère des insights BI avec IA"""
    try:
        bi_service = BIService(request.user.tenant)
        insights = bi_service.generate_bi_insights()

        return Response({'insights': insights})
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'insights BI: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération d\'insights: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)