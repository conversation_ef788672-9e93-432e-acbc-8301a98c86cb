import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { SimpleMetricCard } from '../../components/ui/SimpleMetricCard';
import { SimpleChart } from '../../components/ui/SimpleChart';

interface BIMetrics {
  totalDataPoints: number;
  reportsGenerated: number;
  dataAccuracy: number;
  processingTime: number;
  activeQueries: number;
  storageUsed: number;
}

interface Report {
  id: string;
  name: string;
  type: 'financial' | 'sales' | 'operational' | 'hr' | 'custom';
  lastGenerated: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  status: 'active' | 'scheduled' | 'error' | 'completed';
  size: number;
  views: number;
}

interface DataSource {
  id: string;
  name: string;
  type: 'database' | 'api' | 'file' | 'stream';
  status: 'connected' | 'disconnected' | 'error' | 'syncing';
  lastSync: string;
  recordCount: number;
  healthScore: number;
}

interface Insight {
  id: string;
  title: string;
  description: string;
  type: 'trend' | 'anomaly' | 'prediction' | 'recommendation';
  priority: 'high' | 'medium' | 'low';
  confidence: number;
  impact: string;
  date: string;
}

export const BIDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<BIMetrics>({
    totalDataPoints: 15847000,
    reportsGenerated: 342,
    dataAccuracy: 98.7,
    processingTime: 2.3,
    activeQueries: 28,
    storageUsed: 2.4
  });

  const [activeReports] = useState<Report[]>([
    { id: 'RPT-001', name: 'Rapport Financier Mensuel', type: 'financial', lastGenerated: '2024-01-27', frequency: 'monthly', status: 'completed', size: 2.5, views: 145 },
    { id: 'RPT-002', name: 'Performance Ventes Hebdo', type: 'sales', lastGenerated: '2024-01-26', frequency: 'weekly', status: 'active', size: 1.8, views: 89 },
    { id: 'RPT-003', name: 'KPI Opérationnels', type: 'operational', lastGenerated: '2024-01-27', frequency: 'daily', status: 'scheduled', size: 0.9, views: 234 },
    { id: 'RPT-004', name: 'Analyse RH Trimestrielle', type: 'hr', lastGenerated: '2024-01-15', frequency: 'quarterly', status: 'error', size: 3.2, views: 67 },
    { id: 'RPT-005', name: 'Dashboard Exécutif', type: 'custom', lastGenerated: '2024-01-27', frequency: 'daily', status: 'completed', size: 1.2, views: 312 }
  ];

  const [dataSources] = useState<DataSource[]>([
    { id: 'DS-001', name: 'Base ERP Principale', type: 'database', status: 'connected', lastSync: '2024-01-27 14:30', recordCount: 2847000, healthScore: 98 },
    { id: 'DS-002', name: 'API Comptabilité', type: 'api', status: 'syncing', lastSync: '2024-01-27 14:25', recordCount: 456000, healthScore: 95 },
    { id: 'DS-003', name: 'Fichiers Ventes CSV', type: 'file', status: 'connected', lastSync: '2024-01-27 12:00', recordCount: 125000, healthScore: 92 },
    { id: 'DS-004', name: 'Stream Analytics', type: 'stream', status: 'error', lastSync: '2024-01-27 10:15', recordCount: 0, healthScore: 45 },
    { id: 'DS-005', name: 'Base CRM', type: 'database', status: 'connected', lastSync: '2024-01-27 14:20', recordCount: 89000, healthScore: 97 }
  ];

  const [insights] = useState<Insight[]>([
    { id: 'INS-001', title: 'Augmentation des ventes Q1', description: 'Les ventes ont augmenté de 23% par rapport au trimestre précédent', type: 'trend', priority: 'high', confidence: 94, impact: '+23% revenus', date: '2024-01-27' },
    { id: 'INS-002', title: 'Anomalie stock produit A', description: 'Détection d\'une baisse inhabituelle du stock', type: 'anomaly', priority: 'medium', confidence: 87, impact: 'Risque rupture', date: '2024-01-26' },
    { id: 'INS-003', title: 'Prédiction demande février', description: 'Augmentation prévue de 15% de la demande le mois prochain', type: 'prediction', priority: 'medium', confidence: 82, impact: '+15% demande', date: '2024-01-25' },
    { id: 'INS-004', title: 'Optimisation processus achat', description: 'Recommandation d\'automatisation pour réduire les délais', type: 'recommendation', priority: 'low', confidence: 76, impact: '-30% délais', date: '2024-01-24' }
  ];

  const [performanceData] = useState([
    { name: '00h', value: 1200, value2: 980 },
    { name: '04h', value: 800, value2: 650 },
    { name: '08h', value: 2400, value2: 2100 },
    { name: '12h', value: 3200, value2: 2800 },
    { name: '16h', value: 2800, value2: 2400 },
    { name: '20h', value: 1600, value2: 1300 },
    { name: '24h', value: 900, value2: 750 }
  ]);

  const [dataVolumeData] = useState([
    { name: 'ERP', value: 2847 },
    { name: 'CRM', value: 456 },
    { name: 'Compta', value: 234 },
    { name: 'Ventes', value: 125 },
    { name: 'RH', value: 89 },
    { name: 'Autres', value: 156 }
  ]);

  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const getReportStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'active': return 'bg-blue-100 text-blue-800';
      case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDataSourceStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'syncing': return 'bg-blue-100 text-blue-800';
      case 'disconnected': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getInsightPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getInsightTypeIcon = (type: string) => {
    switch (type) {
      case 'trend': return '📈';
      case 'anomaly': return '⚠️';
      case 'prediction': return '🔮';
      case 'recommendation': return '💡';
      default: return '📊';
    }
  };

  const getDataSourceIcon = (type: string) => {
    switch (type) {
      case 'database': return '🗄️';
      case 'api': return '🔌';
      case 'file': return '📁';
      case 'stream': return '🌊';
      default: return '📊';
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center text-3xl">
              📈
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Agent BI</h1>
              <p className="text-gray-600 text-lg">Business Intelligence et analyse de données</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              🔄 Actualiser
            </button>
            <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
              📊 Nouveau Rapport
            </button>
          </div>
        </div>
      </motion.div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <SimpleMetricCard
          title="Points de Données"
          value={`${(metrics.totalDataPoints / 1000000).toFixed(1)}M`}
          icon="📊"
          color="blue"
          trend="up"
          trendValue={12.5}
        />
        <SimpleMetricCard
          title="Rapports Générés"
          value={metrics.reportsGenerated}
          icon="📋"
          color="green"
          trend="up"
          trendValue={8.3}
        />
        <SimpleMetricCard
          title="Précision Données"
          value={metrics.dataAccuracy}
          unit="%"
          icon="🎯"
          color="purple"
          trend="up"
          trendValue={1.2}
        />
        <SimpleMetricCard
          title="Temps Traitement"
          value={metrics.processingTime}
          unit="s"
          icon="⚡"
          color="yellow"
          trend="down"
          trendValue={-15.7}
        />
        <SimpleMetricCard
          title="Requêtes Actives"
          value={metrics.activeQueries}
          icon="🔍"
          color="blue"
          trend="neutral"
          trendValue={0}
        />
        <SimpleMetricCard
          title="Stockage Utilisé"
          value={metrics.storageUsed}
          unit="TB"
          icon="💾"
          color="red"
          trend="up"
          trendValue={18.9}
        />
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <SimpleChart
            title="Performance Système (Requêtes vs Réponses)"
            data={performanceData}
            type="line"
            height={350}
            color="#4F46E5"
          />
        </div>
        <div>
          <SimpleChart
            title="Volume de Données par Source (k records)"
            data={dataVolumeData}
            type="bar"
            height={350}
            color="#6366F1"
          />
        </div>
      </div>

      {/* Rapports et sources de données */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Rapports actifs */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold mb-4">Rapports Actifs</h3>
          <div className="space-y-4">
            {activeReports.map((report, index) => (
              <motion.div
                key={report.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="font-semibold text-indigo-600">{report.name}</div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getReportStatusColor(report.status)}`}>
                    {report.status}
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">Type: {report.type}</div>
                    <div className="text-gray-600">Fréquence: {report.frequency}</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Taille: {report.size}MB</div>
                    <div className="text-gray-600">Vues: {report.views}</div>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  Dernière génération: {new Date(report.lastGenerated).toLocaleDateString('fr-FR')}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Sources de données */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold mb-4">Sources de Données</h3>
          <div className="space-y-4">
            {dataSources.map((source, index) => (
              <motion.div
                key={source.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="text-2xl">{getDataSourceIcon(source.type)}</div>
                  <div>
                    <div className="font-semibold">{source.name}</div>
                    <div className="text-sm text-gray-600">
                      {source.recordCount.toLocaleString('fr-FR')} enregistrements
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDataSourceStatusColor(source.status)}`}>
                    {source.status}
                  </span>
                  <div className={`text-sm font-semibold mt-1 ${getHealthScoreColor(source.healthScore)}`}>
                    Santé: {source.healthScore}%
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Insights IA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="bg-white rounded-xl shadow-lg p-6"
      >
        <h3 className="text-xl font-semibold mb-4">Insights Intelligence Artificielle</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {insights.map((insight, index) => (
            <motion.div
              key={insight.id}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{getInsightTypeIcon(insight.type)}</span>
                  <span className="font-semibold">{insight.title}</span>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getInsightPriorityColor(insight.priority)}`}>
                  {insight.priority}
                </span>
              </div>
              <div className="text-sm text-gray-600 mb-3">{insight.description}</div>
              <div className="flex justify-between items-center text-sm">
                <div className="flex items-center gap-4">
                  <span className="text-gray-500">Confiance: {insight.confidence}%</span>
                  <span className="font-semibold text-indigo-600">{insight.impact}</span>
                </div>
                <span className="text-gray-500">
                  {new Date(insight.date).toLocaleDateString('fr-FR')}
                </span>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};
