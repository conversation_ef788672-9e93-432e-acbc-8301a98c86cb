"""
Permissions personnalisées pour ERP HUB
"""
from rest_framework import permissions


class IsTenantAdmin(permissions.BasePermission):
    """
    Permission qui vérifie si l'utilisateur est administrateur de son tenant
    """
    message = "Vous devez être administrateur du tenant pour effectuer cette action."
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.is_tenant_admin
        )


class IsSameUserOrTenantAdmin(permissions.BasePermission):
    """
    Permission qui autorise l'utilisateur à modifier ses propres données
    ou un administrateur du tenant à modifier les données de ses utilisateurs
    """
    message = "Vous ne pouvez modifier que vos propres données ou être administrateur du tenant."
    
    def has_object_permission(self, request, view, obj):
        # L'utilisateur peut modifier ses propres données
        if request.user == obj:
            return True
        
        # Un administrateur du tenant peut modifier les données de ses utilisateurs
        if (request.user.is_tenant_admin and 
            hasattr(obj, 'tenant') and 
            obj.tenant == request.user.tenant):
            return True
        
        return False


class HasModulePermission(permissions.BasePermission):
    """
    Permission qui vérifie si l'utilisateur a les permissions nécessaires
    pour un module spécifique
    """
    
    def __init__(self, module_name, required_permission='read'):
        self.module_name = module_name
        self.required_permission = required_permission
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # Les super utilisateurs ont tous les droits
        if request.user.is_superuser:
            return True
        
        # Vérifier les permissions via les rôles
        user_roles = request.user.user_roles.filter(is_active=True)
        
        for user_role in user_roles:
            role_permissions = user_role.role.permissions.get(self.module_name, [])
            if self.required_permission in role_permissions:
                return True
        
        return False


class IsSameTenant(permissions.BasePermission):
    """
    Permission qui vérifie si l'objet appartient au même tenant que l'utilisateur
    """
    message = "Vous ne pouvez accéder qu'aux données de votre tenant."
    
    def has_object_permission(self, request, view, obj):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # Vérifier si l'objet a un attribut tenant
        if hasattr(obj, 'tenant'):
            return obj.tenant == request.user.tenant
        
        # Vérifier si l'objet a un attribut user avec un tenant
        if hasattr(obj, 'user') and hasattr(obj.user, 'tenant'):
            return obj.user.tenant == request.user.tenant
        
        return True  # Par défaut, autoriser si pas de tenant défini


def get_module_permission(module_name, permission_type='read'):
    """
    Factory function pour créer des permissions de module
    """
    class ModulePermission(HasModulePermission):
        def __init__(self):
            super().__init__(module_name, permission_type)
    
    return ModulePermission


# Permissions prédéfinies pour chaque module
class HRReadPermission(HasModulePermission):
    def __init__(self):
        super().__init__('hr', 'read')


class HRWritePermission(HasModulePermission):
    def __init__(self):
        super().__init__('hr', 'create')


class SalesReadPermission(HasModulePermission):
    def __init__(self):
        super().__init__('sales', 'read')


class SalesWritePermission(HasModulePermission):
    def __init__(self):
        super().__init__('sales', 'create')


class PurchaseReadPermission(HasModulePermission):
    def __init__(self):
        super().__init__('purchase', 'read')


class PurchaseWritePermission(HasModulePermission):
    def __init__(self):
        super().__init__('purchase', 'create')


class LogisticsReadPermission(HasModulePermission):
    def __init__(self):
        super().__init__('logistics', 'read')


class LogisticsWritePermission(HasModulePermission):
    def __init__(self):
        super().__init__('logistics', 'create')


class StockReadPermission(HasModulePermission):
    def __init__(self):
        super().__init__('stock', 'read')


class StockWritePermission(HasModulePermission):
    def __init__(self):
        super().__init__('stock', 'create')


class AccountingReadPermission(HasModulePermission):
    def __init__(self):
        super().__init__('accounting', 'read')


class AccountingWritePermission(HasModulePermission):
    def __init__(self):
        super().__init__('accounting', 'create')


class FinanceReadPermission(HasModulePermission):
    def __init__(self):
        super().__init__('finance', 'read')


class FinanceWritePermission(HasModulePermission):
    def __init__(self):
        super().__init__('finance', 'create')


class CRMReadPermission(HasModulePermission):
    def __init__(self):
        super().__init__('crm', 'read')


class CRMWritePermission(HasModulePermission):
    def __init__(self):
        super().__init__('crm', 'create')


class BIReadPermission(HasModulePermission):
    def __init__(self):
        super().__init__('bi', 'read')


class BIWritePermission(HasModulePermission):
    def __init__(self):
        super().__init__('bi', 'create')
