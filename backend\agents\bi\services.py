"""
Services pour l'Agent BI
Logique métier pour la business intelligence et le reporting
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg, F, Max, Min
from django.db import transaction, connection
from decimal import Decimal
import json

from core.models import Tenant, User
from agents.models import Agent
from agents.ai_service import ai_service
from .models import (
    DataSource, Dataset, Report, Dashboard, DashboardReport, KPI,
    Alert, AnalysisJob, BIAnalytics, UserActivity
)

logger = logging.getLogger('agents.bi')


class BIService:
    """
    Service principal pour l'Agent BI
    Gère toutes les opérations de business intelligence
    """

    def __init__(self, tenant: Tenant):
        self.tenant = tenant
        self.bi_agent = self._get_or_create_bi_agent()

    def _get_or_create_bi_agent(self) -> Agent:
        """Récupère ou crée l'agent BI pour le tenant"""
        agent, created = Agent.objects.get_or_create(
            tenant=self.tenant,
            agent_type='bi',
            defaults={
                'name': 'Agent BI',
                'description': 'Business Intelligence et Reporting',
                'ai_enabled': True,
                'ai_model': 'gpt-4',
                'capabilities': [
                    'data_analysis',
                    'report_generation',
                    'dashboard_creation',
                    'kpi_monitoring',
                    'alert_management',
                    'predictive_analytics',
                    'data_visualization',
                    'business_insights'
                ]
            }
        )
        if created:
            logger.info(f"Agent BI créé pour le tenant {self.tenant.name}")
        return agent

    def get_bi_dashboard(self) -> Dict[str, Any]:
        """Retourne les données du dashboard BI"""

        # Sources de données
        data_sources = DataSource.objects.filter(tenant=self.tenant)

        # Datasets
        datasets = Dataset.objects.filter(tenant=self.tenant)

        # Rapports
        reports = Report.objects.filter(tenant=self.tenant)

        # Tableaux de bord
        dashboards = Dashboard.objects.filter(tenant=self.tenant)

        # KPIs
        kpis = KPI.objects.filter(tenant=self.tenant)

        # Alertes
        alerts = Alert.objects.filter(tenant=self.tenant)

        # Tâches d'analyse
        analysis_jobs = AnalysisJob.objects.filter(tenant=self.tenant)

        # Activités utilisateur récentes
        recent_activities = UserActivity.objects.filter(
            tenant=self.tenant,
            created_at__gte=timezone.now() - timedelta(days=30)
        )

        # Métriques de performance
        performance_metrics = self._calculate_performance_metrics()

        # Qualité des données
        data_quality_metrics = self._calculate_data_quality_metrics()

        # Utilisation du système
        usage_metrics = self._calculate_usage_metrics()

        # Alertes actives
        active_alerts = self._get_active_alerts()

        return {
            'tenant': self.tenant.name,
            'timestamp': timezone.now().isoformat(),
            'data_sources': {
                'total': data_sources.count(),
                'connected': data_sources.filter(connection_status='connected').count(),
                'disconnected': data_sources.filter(connection_status='disconnected').count(),
                'error': data_sources.filter(connection_status='error').count(),
                'by_type': self._get_data_sources_by_type(),
                'last_refresh': self._get_last_data_refresh()
            },
            'datasets': {
                'total': datasets.count(),
                'active': datasets.filter(is_active=True).count(),
                'total_rows': datasets.aggregate(total=Sum('row_count'))['total'] or 0,
                'total_size_mb': float(datasets.aggregate(total=Sum('data_size_mb'))['total'] or Decimal('0.00')),
                'by_type': self._get_datasets_by_type(),
                'cached': datasets.filter(cache_enabled=True).count()
            },
            'reports': {
                'total': reports.count(),
                'published': reports.filter(status='published').count(),
                'draft': reports.filter(status='draft').count(),
                'scheduled': reports.filter(is_scheduled=True).count(),
                'by_type': self._get_reports_by_type(),
                'most_viewed': self._get_most_viewed_reports()
            },
            'dashboards': {
                'total': dashboards.count(),
                'active': dashboards.filter(is_active=True).count(),
                'public': dashboards.filter(is_public=True).count(),
                'by_type': self._get_dashboards_by_type(),
                'most_used': self._get_most_used_dashboards()
            },
            'kpis': {
                'total': kpis.count(),
                'active': kpis.filter(is_active=True).count(),
                'on_target': self._count_kpis_on_target(),
                'warning': self._count_kpis_warning(),
                'critical': self._count_kpis_critical(),
                'by_type': self._get_kpis_by_type(),
                'recent_calculations': self._get_recent_kpi_calculations()
            },
            'alerts': {
                'total': alerts.count(),
                'active': alerts.filter(status='active').count(),
                'triggered': alerts.filter(status='triggered').count(),
                'critical': alerts.filter(severity='critical').count(),
                'by_severity': self._get_alerts_by_severity(),
                'recent_triggers': self._get_recent_alert_triggers()
            },
            'analysis_jobs': {
                'total': analysis_jobs.count(),
                'pending': analysis_jobs.filter(status='pending').count(),
                'running': analysis_jobs.filter(status='running').count(),
                'completed': analysis_jobs.filter(status='completed').count(),
                'failed': analysis_jobs.filter(status='failed').count(),
                'by_type': self._get_analysis_jobs_by_type(),
                'average_execution_time': self._get_average_execution_time()
            },
            'user_activity': {
                'total_recent': recent_activities.count(),
                'unique_users': recent_activities.values('user').distinct().count(),
                'by_activity_type': self._get_activities_by_type(),
                'most_active_users': self._get_most_active_users(),
                'peak_hours': self._get_peak_usage_hours()
            },
            'performance': performance_metrics,
            'data_quality': data_quality_metrics,
            'usage': usage_metrics,
            'active_alerts': active_alerts,
            'system_health': self._get_system_health(),
            'recommendations': self._generate_bi_recommendations()
        }

    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calcule les métriques de performance"""
        # Temps de réponse moyen des rapports
        recent_jobs = AnalysisJob.objects.filter(
            tenant=self.tenant,
            job_type='report_generation',
            status='completed',
            completed_at__gte=timezone.now() - timedelta(days=7)
        )

        avg_response_time = recent_jobs.aggregate(
            avg=Avg('execution_time_seconds')
        )['avg'] or 0

        # Taux de succès des tâches
        total_jobs = AnalysisJob.objects.filter(
            tenant=self.tenant,
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()

        successful_jobs = AnalysisJob.objects.filter(
            tenant=self.tenant,
            status='completed',
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()

        success_rate = (successful_jobs / total_jobs * 100) if total_jobs > 0 else 0

        # Utilisation de la mémoire
        avg_memory_usage = recent_jobs.aggregate(
            avg=Avg('memory_usage_mb')
        )['avg'] or 0

        return {
            'average_response_time_seconds': float(avg_response_time),
            'success_rate_percentage': success_rate,
            'average_memory_usage_mb': float(avg_memory_usage),
            'total_jobs_last_week': total_jobs,
            'successful_jobs_last_week': successful_jobs
        }

    def _calculate_data_quality_metrics(self) -> Dict[str, Any]:
        """Calcule les métriques de qualité des données"""
        # Sources de données connectées
        total_sources = DataSource.objects.filter(tenant=self.tenant).count()
        connected_sources = DataSource.objects.filter(
            tenant=self.tenant,
            connection_status='connected'
        ).count()

        connection_rate = (connected_sources / total_sources * 100) if total_sources > 0 else 0

        # Datasets avec cache valide
        total_datasets = Dataset.objects.filter(tenant=self.tenant).count()
        cached_datasets = Dataset.objects.filter(
            tenant=self.tenant,
            cache_enabled=True,
            last_cached__gte=timezone.now() - timedelta(hours=24)
        ).count()

        cache_freshness = (cached_datasets / total_datasets * 100) if total_datasets > 0 else 0

        # Alertes de qualité des données
        data_quality_alerts = Alert.objects.filter(
            tenant=self.tenant,
            alert_type='data_quality',
            status='triggered'
        ).count()

        return {
            'connection_rate_percentage': connection_rate,
            'cache_freshness_percentage': cache_freshness,
            'data_quality_alerts': data_quality_alerts,
            'total_data_sources': total_sources,
            'connected_sources': connected_sources,
            'total_datasets': total_datasets,
            'cached_datasets': cached_datasets
        }

    def _calculate_usage_metrics(self) -> Dict[str, Any]:
        """Calcule les métriques d'utilisation"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)

        # Activités par jour
        daily_activities = UserActivity.objects.filter(
            tenant=self.tenant,
            created_at__gte=start_date
        ).extra(
            select={'day': 'date(created_at)'}
        ).values('day').annotate(
            count=Count('id')
        ).order_by('day')

        # Utilisateurs actifs
        active_users = UserActivity.objects.filter(
            tenant=self.tenant,
            created_at__gte=start_date
        ).values('user').distinct().count()

        # Rapports les plus consultés
        popular_reports = UserActivity.objects.filter(
            tenant=self.tenant,
            activity_type='report_view',
            created_at__gte=start_date
        ).values('object_name').annotate(
            views=Count('id')
        ).order_by('-views')[:5]

        return {
            'daily_activities': list(daily_activities),
            'active_users_last_30_days': active_users,
            'popular_reports': list(popular_reports),
            'total_activities_last_30_days': UserActivity.objects.filter(
                tenant=self.tenant,
                created_at__gte=start_date
            ).count()
        }

    def _get_active_alerts(self) -> List[Dict[str, Any]]:
        """Récupère les alertes actives"""
        active_alerts = Alert.objects.filter(
            tenant=self.tenant,
            status__in=['triggered', 'active']
        ).order_by('-last_triggered', '-created_at')[:10]

        alerts_data = []
        for alert in active_alerts:
            alerts_data.append({
                'id': str(alert.id),
                'name': alert.name,
                'description': alert.description,
                'alert_type': alert.alert_type,
                'severity': alert.severity,
                'status': alert.status,
                'last_triggered': alert.last_triggered.isoformat() if alert.last_triggered else None,
                'trigger_count': alert.trigger_count,
                'kpi_name': alert.kpi.name if alert.kpi else None
            })

        return alerts_data

    def _get_system_health(self) -> Dict[str, Any]:
        """Évalue la santé du système BI"""
        # Calcul d'un score de santé global
        health_score = 100

        # Pénalités pour les problèmes
        disconnected_sources = DataSource.objects.filter(
            tenant=self.tenant,
            connection_status__in=['disconnected', 'error']
        ).count()

        if disconnected_sources > 0:
            health_score -= min(disconnected_sources * 10, 30)

        # Alertes critiques
        critical_alerts = Alert.objects.filter(
            tenant=self.tenant,
            severity='critical',
            status='triggered'
        ).count()

        if critical_alerts > 0:
            health_score -= min(critical_alerts * 15, 40)

        # Tâches échouées récentes
        failed_jobs = AnalysisJob.objects.filter(
            tenant=self.tenant,
            status='failed',
            created_at__gte=timezone.now() - timedelta(hours=24)
        ).count()

        if failed_jobs > 0:
            health_score -= min(failed_jobs * 5, 20)

        health_score = max(health_score, 0)

        # Déterminer le statut
        if health_score >= 90:
            status = 'excellent'
        elif health_score >= 75:
            status = 'good'
        elif health_score >= 50:
            status = 'warning'
        else:
            status = 'critical'

        return {
            'score': health_score,
            'status': status,
            'disconnected_sources': disconnected_sources,
            'critical_alerts': critical_alerts,
            'failed_jobs_24h': failed_jobs
        }

    def _get_data_sources_by_type(self) -> Dict[str, int]:
        """Répartition des sources de données par type"""
        sources = DataSource.objects.filter(tenant=self.tenant).values('source_type').annotate(
            count=Count('id')
        ).order_by('-count')

        return {source['source_type']: source['count'] for source in sources}

    def _get_last_data_refresh(self) -> Optional[str]:
        """Dernière actualisation des données"""
        last_refresh = DataSource.objects.filter(
            tenant=self.tenant,
            last_refresh__isnull=False
        ).aggregate(max_refresh=Max('last_refresh'))['max_refresh']

        return last_refresh.isoformat() if last_refresh else None

    def _get_datasets_by_type(self) -> Dict[str, int]:
        """Répartition des datasets par type"""
        datasets = Dataset.objects.filter(tenant=self.tenant).values('dataset_type').annotate(
            count=Count('id')
        ).order_by('-count')

        return {dataset['dataset_type']: dataset['count'] for dataset in datasets}

    def _get_reports_by_type(self) -> Dict[str, int]:
        """Répartition des rapports par type"""
        reports = Report.objects.filter(tenant=self.tenant).values('report_type').annotate(
            count=Count('id')
        ).order_by('-count')

        return {report['report_type']: report['count'] for report in reports}

    def _get_most_viewed_reports(self) -> List[Dict[str, Any]]:
        """Rapports les plus consultés"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)

        viewed_reports = UserActivity.objects.filter(
            tenant=self.tenant,
            activity_type='report_view',
            created_at__gte=start_date
        ).values('object_name', 'object_id').annotate(
            views=Count('id')
        ).order_by('-views')[:5]

        return list(viewed_reports)

    def _get_dashboards_by_type(self) -> Dict[str, int]:
        """Répartition des tableaux de bord par type"""
        dashboards = Dashboard.objects.filter(tenant=self.tenant).values('dashboard_type').annotate(
            count=Count('id')
        ).order_by('-count')

        return {dashboard['dashboard_type']: dashboard['count'] for dashboard in dashboards}

    def _get_most_used_dashboards(self) -> List[Dict[str, Any]]:
        """Tableaux de bord les plus utilisés"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)

        used_dashboards = UserActivity.objects.filter(
            tenant=self.tenant,
            activity_type='dashboard_view',
            created_at__gte=start_date
        ).values('object_name', 'object_id').annotate(
            views=Count('id')
        ).order_by('-views')[:5]

        return list(used_dashboards)

    def _count_kpis_on_target(self) -> int:
        """Nombre de KPIs atteignant leur cible"""
        kpis = KPI.objects.filter(
            tenant=self.tenant,
            is_active=True,
            current_value__isnull=False,
            target_value__isnull=False
        )

        on_target_count = 0
        for kpi in kpis:
            if kpi.is_on_target:
                on_target_count += 1

        return on_target_count

    def _count_kpis_warning(self) -> int:
        """Nombre de KPIs en alerte"""
        return KPI.objects.filter(
            tenant=self.tenant,
            is_active=True,
            current_value__isnull=False,
            warning_threshold__isnull=False,
            current_value__gte=F('warning_threshold')
        ).count()

    def _count_kpis_critical(self) -> int:
        """Nombre de KPIs critiques"""
        return KPI.objects.filter(
            tenant=self.tenant,
            is_active=True,
            current_value__isnull=False,
            critical_threshold__isnull=False,
            current_value__gte=F('critical_threshold')
        ).count()

    def _get_kpis_by_type(self) -> Dict[str, int]:
        """Répartition des KPIs par type"""
        kpis = KPI.objects.filter(tenant=self.tenant).values('kpi_type').annotate(
            count=Count('id')
        ).order_by('-count')

        return {kpi['kpi_type']: kpi['count'] for kpi in kpis}

    def _get_recent_kpi_calculations(self) -> List[Dict[str, Any]]:
        """Calculs de KPIs récents"""
        recent_kpis = KPI.objects.filter(
            tenant=self.tenant,
            last_calculated__isnull=False
        ).order_by('-last_calculated')[:5]

        calculations = []
        for kpi in recent_kpis:
            calculations.append({
                'name': kpi.name,
                'value': float(kpi.current_value) if kpi.current_value else None,
                'last_calculated': kpi.last_calculated.isoformat(),
                'trend_direction': kpi.trend_direction
            })

        return calculations

    def _get_alerts_by_severity(self) -> Dict[str, int]:
        """Répartition des alertes par sévérité"""
        alerts = Alert.objects.filter(tenant=self.tenant).values('severity').annotate(
            count=Count('id')
        ).order_by('severity')

        return {alert['severity']: alert['count'] for alert in alerts}

    def _get_recent_alert_triggers(self) -> List[Dict[str, Any]]:
        """Déclenchements d'alertes récents"""
        recent_alerts = Alert.objects.filter(
            tenant=self.tenant,
            last_triggered__isnull=False
        ).order_by('-last_triggered')[:5]

        triggers = []
        for alert in recent_alerts:
            triggers.append({
                'name': alert.name,
                'severity': alert.severity,
                'last_triggered': alert.last_triggered.isoformat(),
                'trigger_count': alert.trigger_count
            })

        return triggers

    def _get_analysis_jobs_by_type(self) -> Dict[str, int]:
        """Répartition des tâches d'analyse par type"""
        jobs = AnalysisJob.objects.filter(tenant=self.tenant).values('job_type').annotate(
            count=Count('id')
        ).order_by('-count')

        return {job['job_type']: job['count'] for job in jobs}

    def _get_average_execution_time(self) -> float:
        """Temps d'exécution moyen des tâches"""
        avg_time = AnalysisJob.objects.filter(
            tenant=self.tenant,
            status='completed',
            execution_time_seconds__isnull=False
        ).aggregate(avg=Avg('execution_time_seconds'))['avg']

        return float(avg_time) if avg_time else 0

    def _get_activities_by_type(self) -> Dict[str, int]:
        """Répartition des activités par type"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)

        activities = UserActivity.objects.filter(
            tenant=self.tenant,
            created_at__gte=start_date
        ).values('activity_type').annotate(
            count=Count('id')
        ).order_by('-count')

        return {activity['activity_type']: activity['count'] for activity in activities}

    def _get_most_active_users(self) -> List[Dict[str, Any]]:
        """Utilisateurs les plus actifs"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)

        active_users = UserActivity.objects.filter(
            tenant=self.tenant,
            created_at__gte=start_date
        ).values('user__first_name', 'user__last_name').annotate(
            activity_count=Count('id')
        ).order_by('-activity_count')[:5]

        users = []
        for user in active_users:
            users.append({
                'name': f"{user['user__first_name']} {user['user__last_name']}",
                'activity_count': user['activity_count']
            })

        return users

    def _get_peak_usage_hours(self) -> List[Dict[str, Any]]:
        """Heures de pointe d'utilisation"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=7)

        # Utilisation par heure
        hourly_usage = UserActivity.objects.filter(
            tenant=self.tenant,
            created_at__gte=start_date
        ).extra(
            select={'hour': 'extract(hour from created_at)'}
        ).values('hour').annotate(
            count=Count('id')
        ).order_by('hour')

        return list(hourly_usage)

    def _generate_bi_recommendations(self) -> List[Dict[str, Any]]:
        """Génère des recommandations BI"""
        recommendations = []

        # Vérifier les sources de données déconnectées
        disconnected_sources = DataSource.objects.filter(
            tenant=self.tenant,
            connection_status__in=['disconnected', 'error']
        ).count()

        if disconnected_sources > 0:
            recommendations.append({
                'type': 'warning',
                'priority': 'high',
                'title': 'Sources de données déconnectées',
                'description': f"{disconnected_sources} source(s) de données ne sont pas connectées",
                'recommendation': 'Vérifier et reconnecter les sources de données pour assurer la fraîcheur des rapports',
                'action': 'check_data_sources'
            })

        # Vérifier les KPIs non calculés récemment
        stale_kpis = KPI.objects.filter(
            tenant=self.tenant,
            is_active=True,
            last_calculated__lt=timezone.now() - timedelta(hours=24)
        ).count()

        if stale_kpis > 0:
            recommendations.append({
                'type': 'info',
                'priority': 'medium',
                'title': 'KPIs non mis à jour',
                'description': f"{stale_kpis} KPI(s) n'ont pas été calculés dans les dernières 24h",
                'recommendation': 'Planifier des calculs automatiques pour maintenir les KPIs à jour',
                'action': 'schedule_kpi_calculations'
            })

        # Vérifier l'utilisation des tableaux de bord
        unused_dashboards = Dashboard.objects.filter(
            tenant=self.tenant,
            is_active=True
        ).exclude(
            id__in=UserActivity.objects.filter(
                tenant=self.tenant,
                activity_type='dashboard_view',
                created_at__gte=timezone.now() - timedelta(days=30)
            ).values('object_id')
        ).count()

        if unused_dashboards > 0:
            recommendations.append({
                'type': 'info',
                'priority': 'low',
                'title': 'Tableaux de bord inutilisés',
                'description': f"{unused_dashboards} tableau(x) de bord n'ont pas été consultés ce mois",
                'recommendation': 'Archiver ou promouvoir ces tableaux de bord selon leur pertinence',
                'action': 'review_dashboards'
            })

        return recommendations

    def generate_bi_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights BI avec IA"""
        insights = []

        try:
            # Analyser les performances du système
            performance_insights = self._analyze_performance_insights()
            insights.extend(performance_insights)

            # Analyser l'utilisation
            usage_insights = self._analyze_usage_insights()
            insights.extend(usage_insights)

            # Analyser la qualité des données
            quality_insights = self._analyze_data_quality_insights()
            insights.extend(quality_insights)

            # Générer des insights IA si disponible
            if ai_service.is_available():
                ai_insights = self._generate_ai_insights()
                insights.extend(ai_insights)

        except Exception as e:
            logger.error(f"Erreur lors de la génération d'insights BI: {str(e)}")
            insights.append({
                'type': 'error',
                'priority': 'high',
                'title': 'Erreur de génération d\'insights',
                'description': f'Impossible de générer les insights: {str(e)}',
                'recommendation': 'Vérifier la configuration du système BI',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _analyze_performance_insights(self) -> List[Dict[str, Any]]:
        """Analyse les insights de performance"""
        insights = []

        # Tâches échouées récentes
        failed_jobs = AnalysisJob.objects.filter(
            tenant=self.tenant,
            status='failed',
            created_at__gte=timezone.now() - timedelta(hours=24)
        ).count()

        if failed_jobs > 0:
            insights.append({
                'type': 'warning',
                'priority': 'high',
                'title': 'Tâches d\'analyse échouées',
                'description': f"{failed_jobs} tâche(s) d'analyse ont échoué dans les dernières 24h",
                'recommendation': 'Vérifier les logs et corriger les problèmes de configuration',
                'generated_at': timezone.now().isoformat()
            })

        # Temps d'exécution élevé
        slow_jobs = AnalysisJob.objects.filter(
            tenant=self.tenant,
            status='completed',
            execution_time_seconds__gt=300,  # Plus de 5 minutes
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()

        if slow_jobs > 0:
            insights.append({
                'type': 'info',
                'priority': 'medium',
                'title': 'Performances dégradées',
                'description': f"{slow_jobs} tâche(s) ont pris plus de 5 minutes à s'exécuter cette semaine",
                'recommendation': 'Optimiser les requêtes et considérer l\'indexation des données',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _analyze_usage_insights(self) -> List[Dict[str, Any]]:
        """Analyse les insights d'utilisation"""
        insights = []

        # Faible adoption
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)

        active_users = UserActivity.objects.filter(
            tenant=self.tenant,
            created_at__gte=start_date
        ).values('user').distinct().count()

        total_users = User.objects.filter(tenant=self.tenant, is_active=True).count()
        adoption_rate = (active_users / total_users * 100) if total_users > 0 else 0

        if adoption_rate < 50:
            insights.append({
                'type': 'opportunity',
                'priority': 'medium',
                'title': 'Faible adoption du BI',
                'description': f"Seulement {adoption_rate:.1f}% des utilisateurs ont utilisé le BI ce mois",
                'recommendation': 'Organiser des formations et promouvoir les tableaux de bord utiles',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _analyze_data_quality_insights(self) -> List[Dict[str, Any]]:
        """Analyse les insights de qualité des données"""
        insights = []

        # Sources de données obsolètes
        stale_sources = DataSource.objects.filter(
            tenant=self.tenant,
            last_refresh__lt=timezone.now() - timedelta(hours=24)
        ).count()

        if stale_sources > 0:
            insights.append({
                'type': 'warning',
                'priority': 'medium',
                'title': 'Données obsolètes',
                'description': f"{stale_sources} source(s) de données n'ont pas été actualisées depuis 24h",
                'recommendation': 'Configurer des rafraîchissements automatiques ou vérifier les connexions',
                'generated_at': timezone.now().isoformat()
            })

        return insights

    def _generate_ai_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights avec IA"""
        try:
            # Préparer le contexte BI
            dashboard_data = self.get_bi_dashboard()

            prompt = f"""
            En tant qu'expert en Business Intelligence, analyse cette situation et fournis 3-5 insights stratégiques:

            Données BI: {dashboard_data}

            Fournis des insights sur:
            1. Optimisation des performances
            2. Amélioration de l'adoption utilisateur
            3. Qualité et gouvernance des données
            4. Opportunités d'automatisation
            5. Recommandations stratégiques

            Pour chaque insight, fournis:
            - type: 'opportunity', 'warning', 'critical', 'info'
            - priority: 'high', 'medium', 'low'
            - title: titre court
            - description: description détaillée
            - recommendation: action recommandée

            Réponds au format JSON avec une liste d'insights.
            """

            ai_response = ai_service.generate_response(prompt, "bi", temperature=0.7)

            if ai_response.success:
                import json
                ai_insights_data = json.loads(ai_response.content)

                # Ajouter la date de génération
                for insight in ai_insights_data:
                    insight['generated_at'] = timezone.now().isoformat()

                return ai_insights_data

        except Exception as e:
            logger.error(f"Erreur lors de la génération d'insights IA: {str(e)}")

        return []

    def create_kpi(self, kpi_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée un nouveau KPI"""
        try:
            with transaction.atomic():
                # Récupérer le dataset
                dataset = Dataset.objects.get(
                    id=kpi_data['dataset_id'],
                    tenant=self.tenant
                )

                kpi = KPI.objects.create(
                    tenant=self.tenant,
                    dataset=dataset,
                    name=kpi_data['name'],
                    description=kpi_data.get('description', ''),
                    kpi_type=kpi_data['kpi_type'],
                    calculation_method=kpi_data['calculation_method'],
                    calculation_config=kpi_data.get('calculation_config', {}),
                    target_value=kpi_data.get('target_value'),
                    unit=kpi_data.get('unit', ''),
                    format_config=kpi_data.get('format_config', {}),
                    warning_threshold=kpi_data.get('warning_threshold'),
                    critical_threshold=kpi_data.get('critical_threshold'),
                    calculation_frequency=kpi_data.get('calculation_frequency', 60),
                    created_by=user
                )

                return {
                    'success': True,
                    'kpi': {
                        'id': str(kpi.id),
                        'name': kpi.name,
                        'kpi_type': kpi.kpi_type,
                        'calculation_method': kpi.calculation_method
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def calculate_kpi(self, kpi_id: str) -> Dict[str, Any]:
        """Calcule la valeur d'un KPI"""
        try:
            kpi = KPI.objects.get(id=kpi_id, tenant=self.tenant)

            # Simulation du calcul (à implémenter selon la méthode)
            if kpi.calculation_method == 'count':
                # Exemple de calcul simple
                kpi.current_value = Decimal('100')
            elif kpi.calculation_method == 'sum':
                kpi.current_value = Decimal('1500.50')
            elif kpi.calculation_method == 'average':
                kpi.current_value = Decimal('75.25')
            else:
                kpi.current_value = Decimal('50')

            # Calculer la tendance
            if kpi.previous_value:
                if kpi.current_value > kpi.previous_value:
                    kpi.trend_direction = 'up'
                    kpi.trend_percentage = ((kpi.current_value - kpi.previous_value) / kpi.previous_value) * 100
                elif kpi.current_value < kpi.previous_value:
                    kpi.trend_direction = 'down'
                    kpi.trend_percentage = ((kpi.previous_value - kpi.current_value) / kpi.previous_value) * 100
                else:
                    kpi.trend_direction = 'stable'
                    kpi.trend_percentage = Decimal('0')

            kpi.last_calculated = timezone.now()
            kpi.save()

            return {
                'success': True,
                'kpi': {
                    'id': str(kpi.id),
                    'name': kpi.name,
                    'current_value': float(kpi.current_value),
                    'trend_direction': kpi.trend_direction,
                    'trend_percentage': float(kpi.trend_percentage) if kpi.trend_percentage else None,
                    'last_calculated': kpi.last_calculated.isoformat()
                }
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }