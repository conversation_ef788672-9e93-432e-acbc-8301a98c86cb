import React, { Suspense, lazy, useEffect, useState } from 'react'
import { CircularProgress, Box, Alert } from '@mui/material'
import { useScalableStore } from '../../store/scalableStore'
import { usePerformanceMonitor } from '../../store/scalableStore'

// Cache des composants chargés
const componentCache = new Map<string, React.ComponentType<any>>()

// Fonction pour charger dynamiquement un agent
const loadAgentComponent = (agentName: string) => {
  const cacheKey = `agent-${agentName}`
  
  if (componentCache.has(cacheKey)) {
    return componentCache.get(cacheKey)!
  }
  
  const LazyComponent = lazy(() => 
    import(`../../pages/agents/${agentName}Page.tsx`)
      .then(module => ({ default: module.default }))
      .catch(error => {
        console.error(`Erreur lors du chargement de l'agent ${agentName}:`, error)
        // Composant de fallback en cas d'erreur
        return {
          default: () => (
            <Alert severity="error">
              Erreur lors du chargement de l'agent {agentName}
            </Alert>
          )
        }
      })
  )
  
  componentCache.set(cacheKey, LazyComponent)
  return LazyComponent
}

interface LazyAgentLoaderProps {
  agentName: string
  fallback?: React.ReactNode
  preload?: boolean
  onLoad?: () => void
  onError?: (error: Error) => void
}

export const LazyAgentLoader: React.FC<LazyAgentLoaderProps> = ({
  agentName,
  fallback,
  preload = false,
  onLoad,
  onError,
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const { trackRenderTime } = usePerformanceMonitor()
  const { loadAgent, agents } = useScalableStore()

  // Préchargement si demandé
  useEffect(() => {
    if (preload) {
      loadAgentComponent(agentName)
    }
  }, [agentName, preload])

  // Surveillance des performances
  useEffect(() => {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      trackRenderTime(`LazyAgent-${agentName}`, endTime - startTime)
    }
  }, [agentName, trackRenderTime])

  // Chargement de l'agent dans le store
  useEffect(() => {
    loadAgent(agentName)
  }, [agentName, loadAgent])

  const AgentComponent = loadAgentComponent(agentName)

  const defaultFallback = (
    <Box 
      display="flex" 
      justifyContent="center" 
      alignItems="center" 
      minHeight="400px"
      flexDirection="column"
      gap={2}
    >
      <CircularProgress size={40} />
      <Box>Chargement de l'agent {agentName}...</Box>
    </Box>
  )

  const handleLoad = () => {
    setIsLoading(false)
    onLoad?.()
  }

  const handleError = (error: Error) => {
    setError(error)
    setIsLoading(false)
    onError?.(error)
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Erreur lors du chargement de l'agent {agentName}: {error.message}
      </Alert>
    )
  }

  return (
    <Suspense fallback={fallback || defaultFallback}>
      <ErrorBoundary
        onError={handleError}
        fallback={
          <Alert severity="error" sx={{ m: 2 }}>
            Une erreur s'est produite lors du rendu de l'agent {agentName}
          </Alert>
        }
      >
        <AgentComponent onLoad={handleLoad} />
      </ErrorBoundary>
    </Suspense>
  )
}

// Composant Error Boundary pour capturer les erreurs de rendu
interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback: React.ReactNode
  onError?: (error: Error) => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Erreur capturée par ErrorBoundary:', error, errorInfo)
    this.props.onError?.(error)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback
    }

    return this.props.children
  }
}

// Hook pour précharger des agents
export const useAgentPreloader = () => {
  const preloadAgent = (agentName: string) => {
    // Précharger le composant sans l'afficher
    loadAgentComponent(agentName)
  }

  const preloadMultipleAgents = (agentNames: string[]) => {
    agentNames.forEach(preloadAgent)
  }

  return { preloadAgent, preloadMultipleAgents }
}

// Composant pour précharger les agents critiques
export const AgentPreloader: React.FC<{ agents: string[] }> = ({ agents }) => {
  const { preloadMultipleAgents } = useAgentPreloader()

  useEffect(() => {
    // Précharger après un délai pour ne pas bloquer le rendu initial
    const timer = setTimeout(() => {
      preloadMultipleAgents(agents)
    }, 1000)

    return () => clearTimeout(timer)
  }, [agents, preloadMultipleAgents])

  return null // Ce composant ne rend rien
}
