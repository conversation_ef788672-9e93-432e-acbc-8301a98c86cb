<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent HR - Ressources Humaines | ERP HUB PostgreSQL</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #10b981 30%, #059669 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #10b981;
            color: white;
        }
        
        .btn-primary:hover {
            background: #059669;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-size: 0.875rem; /* Réduction de la taille de police */
        }

        .data-table th,
        .data-table td {
            padding: 0.5rem; /* Réduction du padding de 1rem à 0.5rem */
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            line-height: 1.25; /* Réduction de la hauteur de ligne */
            vertical-align: middle;
        }

        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 0.8125rem; /* Police encore plus petite pour les en-têtes */
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .data-table tbody tr {
            height: 2.5rem; /* Hauteur fixe pour les lignes */
        }
        
        .badge {
            display: inline-block;
            padding: 0.125rem 0.5rem; /* Réduction du padding des badges */
            border-radius: 0.75rem;
            font-size: 0.6875rem; /* Police plus petite pour les badges */
            font-weight: 600;
            line-height: 1.2;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #10b981;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #fde68a;
            color: #92400e;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-connected {
            background: #10b981;
        }
        
        .status-disconnected {
            background: #ef4444;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">👥 Agent HR - ERP HUB</div>
        <div class="nav-buttons">
            <div class="connection-status">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Connexion...</span>
            </div>
            <button class="btn btn-primary" onclick="refreshData()">
                <span class="material-icons" style="font-size: 1rem;">refresh</span>
                Actualiser
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion des Ressources Humaines</h1>
            <p class="page-subtitle">Employés, congés et évaluations - Connecté à PostgreSQL</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalEmployees">0</div>
                <div class="stat-label">Total Employés</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeEmployees">0</div>
                <div class="stat-label">Employés Actifs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalDepartments">0</div>
                <div class="stat-label">Départements</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="pendingLeaves">0</div>
                <div class="stat-label">Congés en Attente</div>
            </div>
        </div>

        <!-- Alertes -->
        <div id="alertContainer"></div>

        <!-- Employés -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">👥 Employés</h2>
                <button class="btn btn-primary" onclick="loadEmployees()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Recharger
                </button>
            </div>
            <div class="section-content">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>N° Employé</th>
                                <th>Nom Complet</th>
                                <th>Email</th>
                                <th>Poste</th>
                                <th>Département</th>
                                <th>Salaire</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody id="employeesTableBody">
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 2rem;">
                                    <div class="loading"></div>
                                    Chargement des employés...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Congés -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">🏖️ Congés</h2>
                <button class="btn btn-primary" onclick="loadLeaves()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Recharger
                </button>
            </div>
            <div class="section-content">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Employé</th>
                                <th>Type de Congé</th>
                                <th>Date Début</th>
                                <th>Date Fin</th>
                                <th>Nombre de Jours</th>
                                <th>Statut</th>
                                <th>Motif</th>
                            </tr>
                        </thead>
                        <tbody id="leavesTableBody">
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 2rem;">
                                    <div class="loading"></div>
                                    Chargement des congés...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <script>
        let employees = [];
        let leaves = [];
        let isLoading = false;

        // Configuration API
        const API_BASE_URL = 'http://localhost:5000/api';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
        });

        // Charger toutes les données
        async function loadAllData() {
            await checkConnection();
            await loadEmployees();
            await loadLeaves();
            updateStats();
        }

        // Vérifier la connexion à l'API
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    updateConnectionStatus(true, data.database_status === 'connected');
                } else {
                    updateConnectionStatus(false, false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateConnectionStatus(false, false);
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(apiConnected, dbConnected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (apiConnected && dbConnected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'PostgreSQL connecté';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Déconnecté';
            }
        }

        // Charger les employés depuis PostgreSQL
        async function loadEmployees() {
            try {
                showAlert('Chargement des employés depuis PostgreSQL...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/employees`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        employees = data.data || [];
                        renderEmployeesTable();
                        showAlert(`${employees.length} employés chargés depuis PostgreSQL`, 'success');
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement employés:', error);
                showAlert('Erreur lors du chargement des employés: ' + error.message, 'error');
                
                // Fallback vers données de démonstration
                employees = [];
                renderEmployeesTable();
            }
        }

        // Charger les congés depuis PostgreSQL
        async function loadLeaves() {
            try {
                const response = await fetch(`${API_BASE_URL}/leaves`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        leaves = data.data || [];
                        renderLeavesTable();
                        console.log(`${leaves.length} congés chargés depuis PostgreSQL`);
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement congés:', error);
                showAlert('Erreur lors du chargement des congés: ' + error.message, 'error');
                
                // Fallback vers données vides
                leaves = [];
                renderLeavesTable();
            }
        }

        // Afficher le tableau des employés
        function renderEmployeesTable() {
            const tbody = document.getElementById('employeesTableBody');
            
            if (employees.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun employé trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = employees.map(employee => `
                <tr>
                    <td><strong>${employee.employeeNumber}</strong></td>
                    <td>${employee.firstName} ${employee.lastName}</td>
                    <td>${employee.email || 'N/A'}</td>
                    <td>${employee.position || 'N/A'}</td>
                    <td>${employee.department || 'N/A'}</td>
                    <td>${employee.salary ? employee.salary.toLocaleString() + '€' : 'N/A'}</td>
                    <td>${getStatusBadge(employee.status)}</td>
                </tr>
            `).join('');
        }

        // Afficher le tableau des congés
        function renderLeavesTable() {
            const tbody = document.getElementById('leavesTableBody');
            
            if (leaves.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun congé trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = leaves.map(leave => `
                <tr>
                    <td><strong>${leave.employeeName}</strong></td>
                    <td>${getLeaveTypeBadge(leave.leaveType)}</td>
                    <td>${formatDate(leave.startDate)}</td>
                    <td>${formatDate(leave.endDate)}</td>
                    <td>${leave.daysCount} jour(s)</td>
                    <td>${getLeaveStatusBadge(leave.status)}</td>
                    <td>${leave.reason || 'N/A'}</td>
                </tr>
            `).join('');
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const totalEmployees = employees.length;
            const activeEmployees = employees.filter(emp => emp.status === 'active').length;
            const departments = [...new Set(employees.map(emp => emp.department).filter(d => d))].length;
            const pendingLeaves = leaves.filter(leave => leave.status === 'pending').length;

            document.getElementById('totalEmployees').textContent = totalEmployees;
            document.getElementById('activeEmployees').textContent = activeEmployees;
            document.getElementById('totalDepartments').textContent = departments;
            document.getElementById('pendingLeaves').textContent = pendingLeaves;
        }

        // Fonctions utilitaires
        function getStatusBadge(status) {
            const badges = {
                'active': '<span class="badge badge-success">Actif</span>',
                'inactive': '<span class="badge badge-warning">Inactif</span>',
                'terminated': '<span class="badge badge-danger">Terminé</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getLeaveTypeBadge(type) {
            const types = {
                'vacation': '🏖️ Vacances',
                'sick': '🤒 Maladie',
                'personal': '👤 Personnel',
                'maternity': '👶 Maternité',
                'paternity': '👨‍👶 Paternité'
            };
            return types[type] || type;
        }

        function getLeaveStatusBadge(status) {
            const badges = {
                'pending': '<span class="badge badge-warning">En attente</span>',
                'approved': '<span class="badge badge-success">Approuvé</span>',
                'rejected': '<span class="badge badge-danger">Rejeté</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            container.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
            
            // Masquer l'alerte après 5 secondes
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function refreshData() {
            loadAllData();
        }
    </script>
</body>
</html>
