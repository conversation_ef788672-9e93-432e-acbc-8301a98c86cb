# 🌐 API REST SERVER POUR ERP HUB
# Serveur Flask pour connecter frontend à la base de données

from flask import Flask, request, jsonify
from flask_cors import CORS
from database_setup import ERPDatabase
import json
from datetime import datetime

app = Flask(__name__)
CORS(app)  # Permettre les requêtes depuis le frontend

# Initialiser la base de données
db = ERPDatabase()

# ===== ENDPOINTS BUDGETS =====

@app.route('/api/budgets', methods=['GET'])
def get_budgets():
    """Récupérer tous les budgets"""
    try:
        budgets = db.read_budgets()
        return jsonify({
            'success': True,
            'data': budgets,
            'count': len(budgets)
        }), 200
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets', methods=['POST'])
def create_budget():
    """Créer un nouveau budget"""
    try:
        budget_data = request.get_json()
        
        # Validation des champs obligatoires
        required_fields = ['id', 'categoryName', 'categoryType']
        for field in required_fields:
            if field not in budget_data:
                return jsonify({
                    'success': False,
                    'error': f'Champ obligatoire manquant : {field}'
                }), 400
        
        # Ajouter les dates
        budget_data['createdDate'] = datetime.now().isoformat()
        budget_data['modifiedDate'] = datetime.now().isoformat()
        
        success = db.create_budget(budget_data)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Budget créé avec succès',
                'data': budget_data
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': 'Erreur lors de la création du budget'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['PUT'])
def update_budget(budget_id):
    """Mettre à jour un budget"""
    try:
        budget_data = request.get_json()
        budget_data['modifiedDate'] = datetime.now().isoformat()
        
        success = db.update_budget(budget_id, budget_data)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Budget mis à jour avec succès'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': 'Budget non trouvé ou erreur de mise à jour'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['DELETE'])
def delete_budget(budget_id):
    """Supprimer un budget"""
    try:
        success = db.delete_budget(budget_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Budget supprimé avec succès'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': 'Budget non trouvé ou erreur de suppression'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS HISTORIQUE =====

@app.route('/api/history', methods=['POST'])
def save_action():
    """Sauvegarder une action dans l'historique"""
    try:
        action_data = request.get_json()
        
        success = db.save_action_history(
            action_data['action'],
            action_data['data'],
            action_data.get('user_id', 'default')
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Action sauvegardée dans l\'historique'
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': 'Erreur lors de la sauvegarde de l\'historique'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS UTILITAIRES =====

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """Récupérer les statistiques de la base de données"""
    try:
        stats = db.get_database_stats()
        return jsonify({
            'success': True,
            'data': stats
        }), 200
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Vérifier l'état du serveur"""
    return jsonify({
        'success': True,
        'message': 'Serveur ERP HUB opérationnel',
        'timestamp': datetime.now().isoformat()
    }), 200

# ===== ENDPOINTS IMPORT/EXPORT =====

@app.route('/api/budgets/import', methods=['POST'])
def import_budgets():
    """Importer des budgets en lot"""
    try:
        import_data = request.get_json()
        budgets = import_data.get('budgets', [])
        
        success_count = 0
        error_count = 0
        errors = []
        
        for budget in budgets:
            try:
                # Générer un ID unique si pas fourni
                if 'id' not in budget:
                    budget['id'] = f"budget_{datetime.now().timestamp()}_{success_count}"
                
                budget['createdDate'] = datetime.now().isoformat()
                budget['modifiedDate'] = datetime.now().isoformat()
                
                if db.create_budget(budget):
                    success_count += 1
                else:
                    error_count += 1
                    errors.append(f"Erreur création budget {budget.get('id', 'inconnu')}")
                    
            except Exception as e:
                error_count += 1
                errors.append(f"Erreur budget {budget.get('id', 'inconnu')}: {str(e)}")
        
        return jsonify({
            'success': True,
            'message': f'{success_count} budgets importés, {error_count} erreurs',
            'imported': success_count,
            'errors': error_count,
            'error_details': errors
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/export', methods=['GET'])
def export_budgets():
    """Exporter tous les budgets"""
    try:
        budgets = db.read_budgets()
        
        return jsonify({
            'success': True,
            'data': budgets,
            'count': len(budgets),
            'exported_at': datetime.now().isoformat()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== GESTION DES ERREURS =====

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint non trouvé'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'Erreur interne du serveur'
    }), 500

# ===== LANCEMENT DU SERVEUR =====

if __name__ == '__main__':
    print("🚀 Démarrage du serveur API ERP HUB...")
    print("📊 Base de données initialisée")
    
    # Afficher les statistiques au démarrage
    stats = db.get_database_stats()
    print(f"📈 Statistiques : {stats}")
    
    print("🌐 Serveur disponible sur : http://localhost:5000")
    print("📋 Endpoints disponibles :")
    print("   GET  /api/budgets          - Récupérer tous les budgets")
    print("   POST /api/budgets          - Créer un nouveau budget")
    print("   PUT  /api/budgets/<id>     - Mettre à jour un budget")
    print("   DELETE /api/budgets/<id>   - Supprimer un budget")
    print("   POST /api/history          - Sauvegarder action historique")
    print("   GET  /api/stats            - Statistiques base de données")
    print("   GET  /api/health           - État du serveur")
    print("   POST /api/budgets/import   - Import en lot")
    print("   GET  /api/budgets/export   - Export complet")
    
    # Lancer le serveur
    app.run(debug=True, host='0.0.0.0', port=5000)
