#!/usr/bin/env python3
"""
Script pour créer automatiquement les applications d'agents
"""
import os
import sys

# Ajouter le répertoire backend au path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Configuration des agents
AGENTS = [
    {'name': 'hr', 'verbose_name': 'Agent RH', 'description': 'Gestion des ressources humaines'},
    {'name': 'sales', 'verbose_name': 'Agent Ventes', 'description': 'Gestion des ventes et devis'},
    {'name': 'purchase', 'verbose_name': 'Agent Achats', 'description': 'Gestion des achats et fournisseurs'},
    {'name': 'logistics', 'verbose_name': 'Agent Logistique', 'description': 'Gestion des livraisons et transport'},
    {'name': 'stock', 'verbose_name': 'Agent Stock', 'description': 'Gestion des inventaires'},
    {'name': 'accounting', 'verbose_name': 'Agent Comptabilité', 'description': 'Gestion comptable'},
    {'name': 'finance', 'verbose_name': 'Agent Finance', 'description': 'Gestion financière'},
    {'name': 'crm', 'verbose_name': 'Agent CRM', 'description': 'Gestion de la relation client'},
    {'name': 'bi', 'verbose_name': 'Agent BI', 'description': 'Business Intelligence et analytics'},
]

def create_agent_files(agent):
    """Crée les fichiers pour un agent"""
    agent_dir = f"backend/agents/{agent['name']}"
    
    # Créer le répertoire s'il n'existe pas
    os.makedirs(agent_dir, exist_ok=True)
    
    # __init__.py
    with open(f"{agent_dir}/__init__.py", 'w', encoding='utf-8') as f:
        f.write(f"# {agent['verbose_name']} - {agent['description']}\n")
    
    # apps.py
    with open(f"{agent_dir}/apps.py", 'w', encoding='utf-8') as f:
        class_name = agent['name'].capitalize() + 'Config'
        f.write(f"""from django.apps import AppConfig


class {class_name}(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'agents.{agent['name']}'
    verbose_name = '{agent['verbose_name']}'
""")
    
    # urls.py
    with open(f"{agent_dir}/urls.py", 'w', encoding='utf-8') as f:
        f.write(f'''"""
URLs pour l'agent {agent['name']}
"""
from django.urls import path
from . import views

app_name = '{agent['name']}'

urlpatterns = [
    path('status/', views.{agent['name']}_status, name='status'),
    path('dashboard/', views.{agent['name']}_dashboard, name='dashboard'),
]
''')
    
    # views.py
    with open(f"{agent_dir}/views.py", 'w', encoding='utf-8') as f:
        f.write(f'''"""
Vues pour l'agent {agent['name']}
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema


@extend_schema(
    summary="Statut de l'agent {agent['name']}",
    description="Retourne le statut de l'agent {agent['name']}"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def {agent['name']}_status(request):
    """Retourne le statut de l'agent {agent['name']}"""
    return Response({{
        'status': 'active',
        'agent': '{agent['name']}',
        'message': '{agent['verbose_name']} opérationnel'
    }})


@extend_schema(
    summary="Dashboard de l'agent {agent['name']}",
    description="Retourne les données du dashboard pour l'agent {agent['name']}"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def {agent['name']}_dashboard(request):
    """Retourne les données du dashboard"""
    return Response({{
        'agent': '{agent['name']}',
        'tenant': request.user.tenant.name,
        'description': '{agent['description']}',
        'status': 'operational'
    }})
''')
    
    print(f"✅ Agent {agent['name']} créé avec succès")

def main():
    """Fonction principale"""
    print("🚀 Création des agents ERP HUB...")
    
    for agent in AGENTS:
        create_agent_files(agent)
    
    print(f"\n🎉 {len(AGENTS)} agents créés avec succès!")
    print("\nAgents créés:")
    for agent in AGENTS:
        print(f"  - {agent['verbose_name']} ({agent['name']})")

if __name__ == "__main__":
    main()
