"""
Modèles pour l'architecture d'agents ERP HUB
"""
import uuid
import json
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from core.models import TimeStampedModel, UUIDModel, Tenant, User


class Agent(UUIDModel, TimeStampedModel):
    """
    Modèle de base pour tous les agents du système
    """
    AGENT_TYPES = [
        ('manager', _('Agent Manager')),
        ('hr', _('Agent RH')),
        ('sales', _('Agent Ventes')),
        ('purchase', _('Agent Achats')),
        ('logistics', _('Agent Logistique')),
        ('stock', _('Agent Stock')),
        ('accounting', _('Agent Comptabilité')),
        ('finance', _('Agent Finance')),
        ('crm', _('Agent CRM')),
        ('bi', _('Agent BI')),
    ]
    
    STATUS_CHOICES = [
        ('active', _('Actif')),
        ('inactive', _('Inactif')),
        ('error', _('Erreur')),
        ('maintenance', _('Maintenance')),
    ]
    
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='agents',
        verbose_name=_("Tenant")
    )
    
    name = models.CharField(_("Nom"), max_length=100)
    agent_type = models.CharField(_("Type d'agent"), max_length=20, choices=AGENT_TYPES)
    description = models.TextField(_("Description"), blank=True)
    
    # Statut et configuration
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='active')
    is_enabled = models.BooleanField(_("Activé"), default=True)
    priority = models.PositiveIntegerField(_("Priorité"), default=1)
    
    # Configuration IA
    ai_enabled = models.BooleanField(_("IA activée"), default=False)
    ai_model = models.CharField(_("Modèle IA"), max_length=100, blank=True)
    ai_temperature = models.FloatField(_("Température IA"), default=0.7)
    ai_max_tokens = models.PositiveIntegerField(_("Tokens max"), default=1000)
    
    # Configuration et métadonnées
    config = models.JSONField(_("Configuration"), default=dict, blank=True)
    capabilities = models.JSONField(_("Capacités"), default=list, blank=True)
    dependencies = models.JSONField(_("Dépendances"), default=list, blank=True)
    
    # Métriques
    last_activity = models.DateTimeField(_("Dernière activité"), null=True, blank=True)
    total_tasks = models.PositiveIntegerField(_("Total tâches"), default=0)
    successful_tasks = models.PositiveIntegerField(_("Tâches réussies"), default=0)
    failed_tasks = models.PositiveIntegerField(_("Tâches échouées"), default=0)
    
    class Meta:
        verbose_name = _("Agent")
        verbose_name_plural = _("Agents")
        unique_together = ['tenant', 'agent_type']
        ordering = ['priority', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.tenant.name})"
    
    @property
    def success_rate(self):
        """Calcule le taux de réussite des tâches"""
        if self.total_tasks == 0:
            return 0
        return (self.successful_tasks / self.total_tasks) * 100
    
    def update_metrics(self, success=True):
        """Met à jour les métriques de l'agent"""
        self.total_tasks += 1
        if success:
            self.successful_tasks += 1
        else:
            self.failed_tasks += 1
        self.save(update_fields=['total_tasks', 'successful_tasks', 'failed_tasks'])


class AgentTask(UUIDModel, TimeStampedModel):
    """
    Modèle pour les tâches assignées aux agents
    """
    TASK_STATUS = [
        ('pending', _('En attente')),
        ('running', _('En cours')),
        ('completed', _('Terminée')),
        ('failed', _('Échouée')),
        ('cancelled', _('Annulée')),
    ]
    
    PRIORITY_LEVELS = [
        (1, _('Très basse')),
        (2, _('Basse')),
        (3, _('Normale')),
        (4, _('Haute')),
        (5, _('Critique')),
    ]
    
    agent = models.ForeignKey(
        Agent,
        on_delete=models.CASCADE,
        related_name='tasks',
        verbose_name=_("Agent")
    )
    
    title = models.CharField(_("Titre"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    task_type = models.CharField(_("Type de tâche"), max_length=100)
    
    # Statut et priorité
    status = models.CharField(_("Statut"), max_length=20, choices=TASK_STATUS, default='pending')
    priority = models.PositiveIntegerField(_("Priorité"), choices=PRIORITY_LEVELS, default=3)
    
    # Données de la tâche
    input_data = models.JSONField(_("Données d'entrée"), default=dict, blank=True)
    output_data = models.JSONField(_("Données de sortie"), default=dict, blank=True)
    error_message = models.TextField(_("Message d'erreur"), blank=True)
    
    # Timing
    scheduled_at = models.DateTimeField(_("Programmée pour"), null=True, blank=True)
    started_at = models.DateTimeField(_("Démarrée à"), null=True, blank=True)
    completed_at = models.DateTimeField(_("Terminée à"), null=True, blank=True)
    
    # Relations
    parent_task = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='subtasks',
        verbose_name=_("Tâche parente")
    )
    
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Assignée par")
    )
    
    class Meta:
        verbose_name = _("Tâche d'agent")
        verbose_name_plural = _("Tâches d'agents")
        ordering = ['-priority', '-created_at']
    
    def __str__(self):
        return f"{self.title} ({self.agent.name})"


class AgentCommunication(UUIDModel, TimeStampedModel):
    """
    Modèle pour la communication entre agents
    """
    MESSAGE_TYPES = [
        ('request', _('Requête')),
        ('response', _('Réponse')),
        ('notification', _('Notification')),
        ('broadcast', _('Diffusion')),
        ('error', _('Erreur')),
    ]
    
    from_agent = models.ForeignKey(
        Agent,
        on_delete=models.CASCADE,
        related_name='sent_messages',
        verbose_name=_("Agent expéditeur")
    )
    
    to_agent = models.ForeignKey(
        Agent,
        on_delete=models.CASCADE,
        related_name='received_messages',
        null=True,
        blank=True,
        verbose_name=_("Agent destinataire")
    )
    
    message_type = models.CharField(_("Type de message"), max_length=20, choices=MESSAGE_TYPES)
    subject = models.CharField(_("Sujet"), max_length=200)
    content = models.JSONField(_("Contenu"), default=dict)
    
    # Métadonnées
    correlation_id = models.UUIDField(_("ID de corrélation"), null=True, blank=True)
    is_read = models.BooleanField(_("Lu"), default=False)
    read_at = models.DateTimeField(_("Lu à"), null=True, blank=True)
    
    # Relation avec une tâche
    related_task = models.ForeignKey(
        AgentTask,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Tâche liée")
    )
    
    class Meta:
        verbose_name = _("Communication d'agent")
        verbose_name_plural = _("Communications d'agents")
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.subject} ({self.from_agent.name} → {self.to_agent.name if self.to_agent else 'Tous'})"


class Workflow(UUIDModel, TimeStampedModel):
    """
    Modèle pour les workflows automatisés
    """
    STATUS_CHOICES = [
        ('draft', _('Brouillon')),
        ('active', _('Actif')),
        ('paused', _('En pause')),
        ('completed', _('Terminé')),
        ('failed', _('Échoué')),
    ]
    
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='workflows',
        verbose_name=_("Tenant")
    )
    
    name = models.CharField(_("Nom"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    
    # Configuration du workflow
    trigger_type = models.CharField(_("Type de déclencheur"), max_length=100)
    trigger_config = models.JSONField(_("Configuration du déclencheur"), default=dict)
    
    # Étapes du workflow
    steps = models.JSONField(_("Étapes"), default=list)
    
    # Statut et métadonnées
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='draft')
    is_enabled = models.BooleanField(_("Activé"), default=True)
    
    # Métriques
    execution_count = models.PositiveIntegerField(_("Nombre d'exécutions"), default=0)
    success_count = models.PositiveIntegerField(_("Exécutions réussies"), default=0)
    failure_count = models.PositiveIntegerField(_("Exécutions échouées"), default=0)
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Créé par")
    )
    
    class Meta:
        verbose_name = _("Workflow")
        verbose_name_plural = _("Workflows")
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.tenant.name})"


class WorkflowExecution(UUIDModel, TimeStampedModel):
    """
    Modèle pour les exécutions de workflows
    """
    STATUS_CHOICES = [
        ('running', _('En cours')),
        ('completed', _('Terminée')),
        ('failed', _('Échouée')),
        ('cancelled', _('Annulée')),
    ]
    
    workflow = models.ForeignKey(
        Workflow,
        on_delete=models.CASCADE,
        related_name='executions',
        verbose_name=_("Workflow")
    )
    
    status = models.CharField(_("Statut"), max_length=20, choices=STATUS_CHOICES, default='running')
    
    # Données d'exécution
    input_data = models.JSONField(_("Données d'entrée"), default=dict)
    output_data = models.JSONField(_("Données de sortie"), default=dict)
    execution_log = models.JSONField(_("Journal d'exécution"), default=list)
    error_message = models.TextField(_("Message d'erreur"), blank=True)
    
    # Timing
    started_at = models.DateTimeField(_("Démarrée à"), auto_now_add=True)
    completed_at = models.DateTimeField(_("Terminée à"), null=True, blank=True)
    
    # Étape actuelle
    current_step = models.PositiveIntegerField(_("Étape actuelle"), default=0)
    
    triggered_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Déclenchée par")
    )
    
    class Meta:
        verbose_name = _("Exécution de workflow")
        verbose_name_plural = _("Exécutions de workflows")
        ordering = ['-started_at']
    
    def __str__(self):
        return f"{self.workflow.name} - {self.started_at.strftime('%Y-%m-%d %H:%M')}"
