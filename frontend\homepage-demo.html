<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP HUB - Système Multi-Agents</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: #f9fafb;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 100;
            padding: 1rem 0;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea 30%, #764ba2 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            text-decoration: none;
            color: #6b7280;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-link:hover {
            color: #667eea;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea 30%, #764ba2 90%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-outline {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .hero {
            text-align: center;
            padding: 6rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: linear-gradient(45deg, #667eea 30%, #764ba2 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.25rem;
            color: #6b7280;
            margin-bottom: 3rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 4rem;
        }

        .metrics-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 1px solid #e5e7eb;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 4rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: #6b7280;
            font-size: 0.875rem;
        }

        .agents-section {
            margin: 4rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 3rem;
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .agent-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 1rem;
            padding: 1.5rem;
            transition: all 0.3s;
            cursor: pointer;
        }

        .agent-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .agent-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .agent-icon {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .agent-info h3 {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            background: #10b981;
            color: white;
        }

        .agent-description {
            color: #6b7280;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .agent-metric {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
        }

        .metric-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }

        .metric-text {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .features-section {
            background: white;
            padding: 4rem 2rem;
            margin-top: 4rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .feature {
            text-align: center;
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .feature h3 {
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .footer {
            background: #1f2937;
            color: white;
            padding: 3rem 2rem 2rem;
            margin-top: 4rem;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .footer-section h4 {
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .footer-links {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: #9ca3af;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            margin-top: 2rem;
            padding-top: 2rem;
            text-align: center;
            color: #9ca3af;
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .nav-links {
                display: none;
            }

            .agents-grid {
                grid-template-columns: 1fr;
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-slide-up {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function App() {
            const [metrics, setMetrics] = useState({
                activeUsers: 45,
                totalTransactions: 2340,
                systemUptime: '99.9%',
                responseTime: 120
            });

            const agents = [
                {
                    id: 'manager',
                    name: 'Agent Manager',
                    description: 'Supervision générale du système ERP et coordination des agents',
                    icon: '📊',
                    color: '#6366f1',
                    metric: { value: 10, label: 'Agents actifs' }
                },
                {
                    id: 'hr',
                    name: 'Agent HR',
                    description: 'Gestion des ressources humaines et administration du personnel',
                    icon: '👥',
                    color: '#8b5cf6',
                    metric: { value: 150, label: 'Employés' }
                },
                {
                    id: 'sales',
                    name: 'Agent Sales',
                    description: 'Gestion commerciale, pipeline de ventes et suivi des prospects',
                    icon: '📈',
                    color: '#06b6d4',
                    metric: { value: 85, label: 'Leads actifs' }
                },
                {
                    id: 'purchase',
                    name: 'Agent Purchase',
                    description: 'Gestion des achats, fournisseurs et commandes d\'approvisionnement',
                    icon: '🛒',
                    color: '#10b981',
                    metric: { value: 45, label: 'Commandes' }
                },
                {
                    id: 'logistics',
                    name: 'Agent Logistics',
                    description: 'Transport, livraisons et gestion de la chaîne logistique',
                    icon: '🚚',
                    color: '#f59e0b',
                    metric: { value: 23, label: 'Expéditions' }
                },
                {
                    id: 'stock',
                    name: 'Agent Stock',
                    description: 'Gestion d\'inventaire, stocks et mouvements de marchandises',
                    icon: '📦',
                    color: '#84cc16',
                    metric: { value: 1250, label: 'Produits' }
                },
                {
                    id: 'accounting',
                    name: 'Agent Accounting',
                    description: 'Comptabilité générale, écritures et rapports financiers',
                    icon: '🏦',
                    color: '#ef4444',
                    metric: { value: 2340, label: 'Transactions' }
                },
                {
                    id: 'finance',
                    name: 'Agent Finance',
                    description: 'Gestion financière, trésorerie et analyse des performances',
                    icon: '💰',
                    color: '#f97316',
                    metric: { value: 95.2, label: '% Performance' }
                },
                {
                    id: 'crm',
                    name: 'Agent CRM',
                    description: 'Relation client avancée et gestion de la satisfaction',
                    icon: '🤝',
                    color: '#ec4899',
                    metric: { value: 340, label: 'Clients' }
                },
                {
                    id: 'bi',
                    name: 'Agent BI',
                    description: 'Business Intelligence, analytics et tableaux de bord',
                    icon: '📊',
                    color: '#3b82f6',
                    metric: { value: 45, label: 'Rapports' }
                }
            ];

            const handleAgentClick = (agentId) => {
                window.location.href = 'login-demo.html';
            };

            const handleLogin = () => {
                window.location.href = 'login-demo.html';
            };

            return (
                <div>
                    {/* Header */}
                    <header className="header">
                        <div className="header-content">
                            <div className="logo">🏢 ERP HUB</div>
                            <nav className="nav-links">
                                <a href="#" className="nav-link">Fonctionnalités</a>
                                <a href="#" className="nav-link">Documentation</a>
                                <a href="#" className="nav-link">Support</a>
                                <button className="btn btn-primary" onClick={handleLogin}>
                                    Se connecter
                                </button>
                            </nav>
                        </div>
                    </header>

                    {/* Hero Section */}
                    <section className="hero animate-fade-in">
                        <h1>Système ERP Multi-Agents</h1>
                        <p>
                            Une plateforme ERP intelligente avec 10 agents spécialisés pour optimiser
                            tous les aspects de votre entreprise. Architecture scalable et moderne.
                        </p>
                        <div className="hero-buttons">
                            <button className="btn btn-primary" onClick={handleLogin}>
                                Commencer maintenant
                            </button>
                            <button className="btn btn-outline">
                                Voir la démo
                            </button>
                        </div>

                        {/* Métriques */}
                        <div className="metrics-card animate-slide-up">
                            <h3 style={{textAlign: 'center', marginBottom: '1rem'}}>
                                📊 Métriques Système en Temps Réel
                            </h3>
                            <div className="metrics-grid">
                                <div className="metric">
                                    <div className="metric-value">{metrics.activeUsers}</div>
                                    <div className="metric-label">Utilisateurs actifs</div>
                                </div>
                                <div className="metric">
                                    <div className="metric-value">{metrics.totalTransactions.toLocaleString()}</div>
                                    <div className="metric-label">Transactions</div>
                                </div>
                                <div className="metric">
                                    <div className="metric-value">{metrics.systemUptime}</div>
                                    <div className="metric-label">Disponibilité</div>
                                </div>
                                <div className="metric">
                                    <div className="metric-value">{metrics.responseTime}ms</div>
                                    <div className="metric-label">Temps de réponse</div>
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Agents Section */}
                    <section className="agents-section">
                        <h2 className="section-title">🤖 Agents ERP Intelligents</h2>
                        <div className="agents-grid">
                            {agents.map((agent, index) => (
                                <div
                                    key={agent.id}
                                    className="agent-card animate-fade-in"
                                    style={{animationDelay: `${index * 0.1}s`}}
                                    onClick={() => handleAgentClick(agent.id)}
                                >
                                    <div className="agent-header">
                                        <div
                                            className="agent-icon"
                                            style={{backgroundColor: agent.color}}
                                        >
                                            {agent.icon}
                                        </div>
                                        <div className="agent-info">
                                            <h3>{agent.name}</h3>
                                            <span className="status-badge">Actif</span>
                                        </div>
                                    </div>
                                    <p className="agent-description">{agent.description}</p>
                                    <div className="agent-metric">
                                        <div className="metric-number">
                                            {agent.metric.value.toLocaleString()}
                                        </div>
                                        <div className="metric-text">{agent.metric.label}</div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </section>

                    {/* Features Section */}
                    <section className="features-section">
                        <div style={{maxWidth: '1200px', margin: '0 auto'}}>
                            <h2 className="section-title">🚀 Pourquoi Choisir ERP HUB ?</h2>
                            <div className="features-grid">
                                <div className="feature">
                                    <div className="feature-icon">⚡</div>
                                    <h3>Performance Optimale</h3>
                                    <p>Architecture moderne avec temps de réponse &lt;200ms et scalabilité horizontale</p>
                                </div>
                                <div className="feature">
                                    <div className="feature-icon">🛡️</div>
                                    <h3>Sécurité Enterprise</h3>
                                    <p>Authentification JWT, chiffrement des données et audit trail complet</p>
                                </div>
                                <div className="feature">
                                    <div className="feature-icon">☁️</div>
                                    <h3>Cloud Ready</h3>
                                    <p>Déploiement Docker, monitoring intégré et backup automatique</p>
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Footer */}
                    <footer className="footer">
                        <div className="footer-content">
                            <div className="footer-section">
                                <h4>🏢 ERP HUB</h4>
                                <p>Système ERP multi-agents pour optimiser votre entreprise avec une architecture moderne et scalable.</p>
                            </div>
                            <div className="footer-section">
                                <h4>Produit</h4>
                                <ul className="footer-links">
                                    <li><a href="#">Fonctionnalités</a></li>
                                    <li><a href="#">Tarifs</a></li>
                                    <li><a href="#">Démo</a></li>
                                </ul>
                            </div>
                            <div className="footer-section">
                                <h4>Support</h4>
                                <ul className="footer-links">
                                    <li><a href="#">Documentation</a></li>
                                    <li><a href="#">API</a></li>
                                    <li><a href="#">Contact</a></li>
                                </ul>
                            </div>
                            <div className="footer-section">
                                <h4>Contact</h4>
                                <p>
                                    📧 <EMAIL><br />
                                    📞 +33 1 23 45 67 89<br />
                                    📍 Paris, France
                                </p>
                            </div>
                        </div>
                        <div className="footer-bottom">
                            <p>© 2024 ERP HUB. Tous droits réservés. | Mentions légales | Politique de confidentialité</p>
                        </div>
                    </footer>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
