"""
Serializers pour l'Agent Finance
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
from .models import (
    Bank, BankAccount, BankTransaction, CashFlowForecast, CashFlowForecastLine,
    Investment, Loan, FinancialRatio, TreasuryReport
)

User = get_user_model()


class BankSerializer(serializers.ModelSerializer):
    """Serializer pour les banques"""
    
    class Meta:
        model = Bank
        fields = [
            'id', 'bank_code', 'name', 'swift_code',
            'address_line1', 'address_line2', 'city', 'postal_code', 'country',
            'phone', 'email', 'website',
            'contact_person', 'contact_phone', 'contact_email',
            'is_active', 'is_primary',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class BankAccountSerializer(serializers.ModelSerializer):
    """Serializer pour les comptes bancaires"""
    bank_name = serializers.CharField(source='bank.name', read_only=True)
    account_manager_name = serializers.CharField(source='account_manager.get_full_name', read_only=True)
    balance_with_overdraft = serializers.ReadOnlyField()
    is_overdrawn = serializers.ReadOnlyField()
    
    class Meta:
        model = BankAccount
        fields = [
            'id', 'bank', 'bank_name', 'account_number', 'iban', 'account_name', 'account_type',
            'currency', 'overdraft_limit', 'credit_limit',
            'opening_balance', 'current_balance', 'available_balance', 'balance_with_overdraft',
            'opening_date', 'closing_date', 'last_reconciliation_date',
            'status', 'is_main_account', 'auto_reconciliation', 'is_overdrawn',
            'account_manager', 'account_manager_name',
            'description', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'current_balance', 'available_balance', 'balance_with_overdraft', 'is_overdrawn', 'created_at', 'updated_at']


class BankTransactionSerializer(serializers.ModelSerializer):
    """Serializer pour les transactions bancaires"""
    bank_account_name = serializers.CharField(source='bank_account.account_name', read_only=True)
    reconciled_by_name = serializers.CharField(source='reconciled_by.get_full_name', read_only=True)
    
    class Meta:
        model = BankTransaction
        fields = [
            'id', 'bank_account', 'bank_account_name', 'transaction_id', 'reference',
            'transaction_type', 'status', 'amount', 'currency',
            'transaction_date', 'value_date', 'booking_date',
            'description', 'counterpart_name', 'counterpart_account', 'counterpart_bank',
            'category', 'subcategory',
            'balance_before', 'balance_after',
            'is_reconciled', 'reconciliation_date', 'reconciled_by', 'reconciled_by_name',
            'accounting_entry_id', 'import_batch', 'source',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'transaction_id', 'balance_before', 'balance_after', 'created_at', 'updated_at']


class BankTransactionCreateSerializer(serializers.Serializer):
    """Serializer pour la création de transactions bancaires"""
    bank_account_id = serializers.UUIDField()
    transaction_type = serializers.ChoiceField(choices=BankTransaction.TRANSACTION_TYPES)
    amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    currency = serializers.CharField(max_length=3, required=False)
    transaction_date = serializers.DateField()
    value_date = serializers.DateField(required=False, allow_null=True)
    description = serializers.CharField(max_length=500)
    counterpart_name = serializers.CharField(max_length=200, required=False, allow_blank=True)
    counterpart_account = serializers.CharField(max_length=50, required=False, allow_blank=True)
    category = serializers.CharField(max_length=100, required=False, allow_blank=True)
    reference = serializers.CharField(max_length=100, required=False, allow_blank=True)
    auto_process = serializers.BooleanField(default=False)


class CashFlowForecastLineSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes de prévision de trésorerie"""
    weighted_amount = serializers.ReadOnlyField()
    
    class Meta:
        model = CashFlowForecastLine
        fields = [
            'id', 'line_number', 'description', 'flow_type', 'category',
            'amount', 'weighted_amount', 'expected_date',
            'probability', 'confidence_level',
            'reference_document', 'customer_supplier',
            'is_recurring', 'recurrence_pattern',
            'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'weighted_amount', 'created_at', 'updated_at']


class CashFlowForecastSerializer(serializers.ModelSerializer):
    """Serializer pour les prévisions de trésorerie"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    lines = CashFlowForecastLineSerializer(many=True, read_only=True)
    net_cash_flow = serializers.ReadOnlyField()
    
    class Meta:
        model = CashFlowForecast
        fields = [
            'id', 'name', 'forecast_type', 'start_date', 'end_date', 'status',
            'opening_balance', 'total_inflows', 'total_outflows', 'closing_balance', 'net_cash_flow',
            'created_by', 'created_by_name',
            'description', 'assumptions', 'notes', 'lines',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'net_cash_flow', 'total_inflows', 'total_outflows', 'closing_balance', 'created_at', 'updated_at']


class CashFlowForecastCreateSerializer(serializers.Serializer):
    """Serializer pour la création de prévisions de trésorerie"""
    name = serializers.CharField(max_length=200)
    forecast_type = serializers.ChoiceField(choices=CashFlowForecast.FORECAST_TYPES)
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    opening_balance = serializers.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    description = serializers.CharField(required=False, allow_blank=True)
    assumptions = serializers.CharField(required=False, allow_blank=True)
    notes = serializers.CharField(required=False, allow_blank=True)
    
    # Lignes de prévision
    lines = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True
    )


class InvestmentSerializer(serializers.ModelSerializer):
    """Serializer pour les investissements"""
    portfolio_manager_name = serializers.CharField(source='portfolio_manager.get_full_name', read_only=True)
    gain_loss = serializers.ReadOnlyField()
    gain_loss_percentage = serializers.ReadOnlyField()
    is_profitable = serializers.ReadOnlyField()
    
    class Meta:
        model = Investment
        fields = [
            'id', 'investment_code', 'name', 'investment_type',
            'initial_amount', 'current_value', 'currency',
            'purchase_date', 'maturity_date', 'sale_date',
            'expected_return_rate', 'actual_return_rate',
            'risk_level', 'status',
            'portfolio_manager', 'portfolio_manager_name',
            'gain_loss', 'gain_loss_percentage', 'is_profitable',
            'description', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'investment_code', 'gain_loss', 'gain_loss_percentage', 'is_profitable', 'created_at', 'updated_at']


class InvestmentCreateSerializer(serializers.Serializer):
    """Serializer pour la création d'investissements"""
    name = serializers.CharField(max_length=200)
    investment_type = serializers.ChoiceField(choices=Investment.INVESTMENT_TYPES)
    initial_amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    current_value = serializers.DecimalField(max_digits=15, decimal_places=2, required=False)
    currency = serializers.CharField(max_length=3, default='EUR')
    purchase_date = serializers.DateField()
    maturity_date = serializers.DateField(required=False, allow_null=True)
    expected_return_rate = serializers.DecimalField(max_digits=5, decimal_places=2, required=False, allow_null=True)
    risk_level = serializers.IntegerField(default=3, min_value=1, max_value=5)
    description = serializers.CharField(required=False, allow_blank=True)
    notes = serializers.CharField(required=False, allow_blank=True)


class LoanSerializer(serializers.ModelSerializer):
    """Serializer pour les emprunts"""
    loan_officer_name = serializers.CharField(source='loan_officer.get_full_name', read_only=True)
    total_paid = serializers.ReadOnlyField()
    payment_progress_percentage = serializers.ReadOnlyField()
    remaining_payments = serializers.ReadOnlyField()
    
    class Meta:
        model = Loan
        fields = [
            'id', 'loan_number', 'loan_name', 'loan_type',
            'lender_name', 'lender_contact',
            'principal_amount', 'outstanding_balance', 'currency',
            'interest_rate', 'interest_type',
            'disbursement_date', 'maturity_date', 'first_payment_date',
            'repayment_frequency', 'monthly_payment',
            'collateral_description', 'collateral_value',
            'debt_to_equity_covenant', 'interest_coverage_covenant',
            'status', 'loan_officer', 'loan_officer_name',
            'total_paid', 'payment_progress_percentage', 'remaining_payments',
            'purpose', 'terms_conditions', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'loan_number', 'total_paid', 'payment_progress_percentage', 'remaining_payments', 'created_at', 'updated_at']


class LoanCreateSerializer(serializers.Serializer):
    """Serializer pour la création d'emprunts"""
    loan_name = serializers.CharField(max_length=200)
    loan_type = serializers.ChoiceField(choices=Loan.LOAN_TYPES)
    lender_name = serializers.CharField(max_length=200)
    lender_contact = serializers.CharField(max_length=200, required=False, allow_blank=True)
    principal_amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    currency = serializers.CharField(max_length=3, default='EUR')
    interest_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    interest_type = serializers.ChoiceField(choices=Loan.INTEREST_TYPES, default='fixed')
    disbursement_date = serializers.DateField()
    maturity_date = serializers.DateField()
    first_payment_date = serializers.DateField()
    repayment_frequency = serializers.ChoiceField(choices=Loan.REPAYMENT_FREQUENCIES)
    monthly_payment = serializers.DecimalField(max_digits=15, decimal_places=2, required=False, allow_null=True)
    collateral_description = serializers.CharField(required=False, allow_blank=True)
    collateral_value = serializers.DecimalField(max_digits=15, decimal_places=2, required=False, allow_null=True)
    purpose = serializers.CharField(required=False, allow_blank=True)
    terms_conditions = serializers.CharField(required=False, allow_blank=True)
    notes = serializers.CharField(required=False, allow_blank=True)


class FinancialRatioSerializer(serializers.ModelSerializer):
    """Serializer pour les ratios financiers"""
    calculated_by_name = serializers.CharField(source='calculated_by.get_full_name', read_only=True)
    variance_from_benchmark = serializers.ReadOnlyField()
    variance_from_target = serializers.ReadOnlyField()
    performance_vs_benchmark = serializers.ReadOnlyField()
    
    class Meta:
        model = FinancialRatio
        fields = [
            'id', 'ratio_name', 'ratio_code', 'category',
            'calculation_date', 'period_start', 'period_end',
            'ratio_value', 'benchmark_value', 'target_value',
            'variance_from_benchmark', 'variance_from_target', 'performance_vs_benchmark',
            'numerator', 'denominator',
            'calculation_method', 'interpretation', 'notes',
            'calculated_by', 'calculated_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'variance_from_benchmark', 'variance_from_target', 'performance_vs_benchmark', 'created_at', 'updated_at']


class TreasuryReportSerializer(serializers.ModelSerializer):
    """Serializer pour les rapports de trésorerie"""
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = TreasuryReport
        fields = [
            'id', 'report_name', 'report_type', 'report_date',
            'period_start', 'period_end', 'status', 'report_data',
            'include_forecasts', 'include_investments', 'include_loans', 'currency_filter',
            'generated_by', 'generated_by_name',
            'generation_time', 'error_message',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'status', 'report_data', 'generation_time', 'error_message', 'created_at', 'updated_at']


class TreasuryReportGenerateSerializer(serializers.Serializer):
    """Serializer pour la génération de rapports de trésorerie"""
    report_type = serializers.ChoiceField(choices=TreasuryReport.REPORT_TYPES)
    period_start = serializers.DateField()
    period_end = serializers.DateField()
    include_forecasts = serializers.BooleanField(default=False)
    include_investments = serializers.BooleanField(default=True)
    include_loans = serializers.BooleanField(default=True)
    currency_filter = serializers.CharField(max_length=3, required=False, allow_blank=True)


class FinancialRatiosCalculateSerializer(serializers.Serializer):
    """Serializer pour le calcul de ratios financiers"""
    period_start = serializers.DateField()
    period_end = serializers.DateField()


class FinanceDashboardSerializer(serializers.Serializer):
    """Serializer pour le dashboard Finance"""
    tenant = serializers.CharField()
    timestamp = serializers.CharField()
    treasury_position = serializers.DictField()
    bank_accounts = serializers.DictField()
    transactions = serializers.DictField()
    investments = serializers.DictField()
    loans = serializers.DictField()
    forecasts = serializers.DictField()
    liquidity = serializers.DictField()
    ratios = serializers.ListField()
    alerts = serializers.DictField()
    recent_activities = serializers.ListField()


class FinanceInsightSerializer(serializers.Serializer):
    """Serializer pour les insights financiers"""
    type = serializers.ChoiceField(choices=['critical', 'warning', 'opportunity', 'info'])
    priority = serializers.ChoiceField(choices=['high', 'medium', 'low'])
    title = serializers.CharField()
    description = serializers.CharField()
    recommendation = serializers.CharField()
    generated_at = serializers.CharField()


class TreasuryPositionSerializer(serializers.Serializer):
    """Serializer pour la position de trésorerie"""
    total_cash = serializers.DecimalField(max_digits=15, decimal_places=2)
    available_cash = serializers.DecimalField(max_digits=15, decimal_places=2)
    cash_by_currency = serializers.DictField()
    calculation_date = serializers.CharField()


class LiquidityAnalysisSerializer(serializers.Serializer):
    """Serializer pour l'analyse de liquidité"""
    current_cash = serializers.DecimalField(max_digits=15, decimal_places=2)
    monthly_inflows = serializers.DecimalField(max_digits=15, decimal_places=2)
    monthly_outflows = serializers.DecimalField(max_digits=15, decimal_places=2)
    net_cash_flow = serializers.DecimalField(max_digits=15, decimal_places=2)
    cash_runway_days = serializers.DecimalField(max_digits=10, decimal_places=2)
    liquidity_level = serializers.ChoiceField(choices=[
        ('excellent', 'Excellent'),
        ('good', 'Bon'),
        ('adequate', 'Adéquat'),
        ('low', 'Faible'),
        ('critical', 'Critique')
    ])
    analysis_period = serializers.DictField()


class CashFlowReportSerializer(serializers.Serializer):
    """Serializer pour les rapports de flux de trésorerie"""
    report_type = serializers.CharField()
    period = serializers.CharField()
    summary = serializers.DictField()
    inflows_by_category = serializers.DictField()
    outflows_by_category = serializers.DictField()
    generation_date = serializers.CharField()


class InvestmentPortfolioReportSerializer(serializers.Serializer):
    """Serializer pour les rapports de portefeuille d'investissements"""
    report_type = serializers.CharField()
    summary = serializers.DictField()
    investments = serializers.ListField()
    generation_date = serializers.CharField()


class LoanPortfolioReportSerializer(serializers.Serializer):
    """Serializer pour les rapports de portefeuille de crédits"""
    report_type = serializers.CharField()
    summary = serializers.DictField()
    loans = serializers.ListField()
    generation_date = serializers.CharField()
