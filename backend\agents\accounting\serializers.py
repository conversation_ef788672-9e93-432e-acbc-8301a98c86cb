"""
Serializers pour l'Agent Accounting
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
from .models import (
    ChartOfAccounts, FiscalYear, Journal, AccountingEntry, AccountingEntryLine,
    TaxCode, Budget, BudgetLine, FinancialReport, ReconciliationRule
)

User = get_user_model()


class ChartOfAccountsSerializer(serializers.ModelSerializer):
    """Serializer pour le plan comptable"""
    parent_account_name = serializers.CharField(source='parent_account.account_name', read_only=True)
    full_code = serializers.ReadOnlyField()
    
    class Meta:
        model = ChartOfAccounts
        fields = [
            'id', 'account_code', 'account_name', 'account_type', 'account_category',
            'parent_account', 'parent_account_name', 'level', 'full_code',
            'is_active', 'is_reconcilable', 'allow_manual_entries',
            'opening_balance', 'current_balance', 'description', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'full_code', 'current_balance', 'created_at', 'updated_at']


class FiscalYearSerializer(serializers.ModelSerializer):
    """Serializer pour les exercices comptables"""
    closed_by_name = serializers.CharField(source='closed_by.get_full_name', read_only=True)
    
    class Meta:
        model = FiscalYear
        fields = [
            'id', 'name', 'start_date', 'end_date', 'status', 'is_current',
            'closed_date', 'locked_date', 'closed_by', 'closed_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class JournalSerializer(serializers.ModelSerializer):
    """Serializer pour les journaux"""
    default_debit_account_name = serializers.CharField(source='default_debit_account.account_name', read_only=True)
    default_credit_account_name = serializers.CharField(source='default_credit_account.account_name', read_only=True)
    responsible_user_name = serializers.CharField(source='responsible_user.get_full_name', read_only=True)
    
    class Meta:
        model = Journal
        fields = [
            'id', 'code', 'name', 'journal_type',
            'default_debit_account', 'default_debit_account_name',
            'default_credit_account', 'default_credit_account_name',
            'sequence_prefix', 'sequence_number', 'is_active',
            'allow_cancellation', 'require_validation',
            'responsible_user', 'responsible_user_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class AccountingEntryLineSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes d'écriture"""
    account_code = serializers.CharField(source='account.account_code', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    amount = serializers.ReadOnlyField()
    is_debit = serializers.ReadOnlyField()
    
    class Meta:
        model = AccountingEntryLine
        fields = [
            'id', 'account', 'account_code', 'account_name',
            'debit_amount', 'credit_amount', 'amount', 'is_debit',
            'description', 'partner_type', 'partner_id', 'partner_name',
            'analytic_account', 'cost_center', 'project_code',
            'reconciliation_ref', 'is_reconciled', 'reconciled_date',
            'tax_code', 'tax_amount', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'amount', 'is_debit', 'created_at', 'updated_at']


class AccountingEntrySerializer(serializers.ModelSerializer):
    """Serializer pour les écritures comptables"""
    journal_name = serializers.CharField(source='journal.name', read_only=True)
    fiscal_year_name = serializers.CharField(source='fiscal_year.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    validated_by_name = serializers.CharField(source='validated_by.get_full_name', read_only=True)
    lines = AccountingEntryLineSerializer(many=True, read_only=True)
    is_balanced = serializers.ReadOnlyField()
    
    class Meta:
        model = AccountingEntry
        fields = [
            'id', 'entry_number', 'reference', 'journal', 'journal_name',
            'fiscal_year', 'fiscal_year_name', 'entry_date', 'value_date', 'due_date',
            'description', 'notes', 'status', 'entry_type',
            'total_debit', 'total_credit', 'is_balanced',
            'created_by', 'created_by_name', 'validated_by', 'validated_by_name',
            'validated_date', 'source_document_type', 'source_document_id',
            'reconciliation_ref', 'is_reconciled', 'lines',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'entry_number', 'is_balanced', 'total_debit', 'total_credit', 'created_at', 'updated_at']


class AccountingEntryCreateSerializer(serializers.Serializer):
    """Serializer pour la création d'écritures comptables"""
    journal_id = serializers.UUIDField()
    entry_date = serializers.DateField()
    value_date = serializers.DateField(required=False, allow_null=True)
    due_date = serializers.DateField(required=False, allow_null=True)
    description = serializers.CharField(max_length=500)
    notes = serializers.CharField(required=False, allow_blank=True)
    entry_type = serializers.ChoiceField(choices=AccountingEntry.ENTRY_TYPES, default='manual')
    reference = serializers.CharField(max_length=100, required=False, allow_blank=True)
    auto_post = serializers.BooleanField(default=False)
    
    # Lignes d'écriture
    lines = serializers.ListField(
        child=serializers.DictField(),
        min_length=2  # Au minimum 2 lignes pour une écriture équilibrée
    )


class TaxCodeSerializer(serializers.ModelSerializer):
    """Serializer pour les codes de taxe"""
    tax_account_name = serializers.CharField(source='tax_account.account_name', read_only=True)
    
    class Meta:
        model = TaxCode
        fields = [
            'id', 'code', 'name', 'tax_type', 'scope', 'rate',
            'tax_account', 'tax_account_name', 'is_active', 'is_default',
            'include_in_price', 'valid_from', 'valid_to',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class BudgetLineSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes de budget"""
    account_code = serializers.CharField(source='account.account_code', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    
    class Meta:
        model = BudgetLine
        fields = [
            'id', 'account', 'account_code', 'account_name',
            'budgeted_amount', 'cost_center', 'project_code',
            'description', 'notes',
            'january', 'february', 'march', 'april', 'may', 'june',
            'july', 'august', 'september', 'october', 'november', 'december',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class BudgetSerializer(serializers.ModelSerializer):
    """Serializer pour les budgets"""
    fiscal_year_name = serializers.CharField(source='fiscal_year.name', read_only=True)
    budget_manager_name = serializers.CharField(source='budget_manager.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    lines = BudgetLineSerializer(many=True, read_only=True)
    net_budget = serializers.ReadOnlyField()
    
    class Meta:
        model = Budget
        fields = [
            'id', 'name', 'code', 'budget_type', 'fiscal_year', 'fiscal_year_name',
            'start_date', 'end_date', 'status',
            'total_revenue_budget', 'total_expense_budget', 'net_budget',
            'budget_manager', 'budget_manager_name',
            'approved_by', 'approved_by_name', 'approved_date',
            'description', 'notes', 'lines',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'net_budget', 'total_revenue_budget', 'total_expense_budget', 'created_at', 'updated_at']


class BudgetCreateSerializer(serializers.Serializer):
    """Serializer pour la création de budgets"""
    name = serializers.CharField(max_length=200)
    code = serializers.CharField(max_length=50)
    budget_type = serializers.ChoiceField(choices=Budget.BUDGET_TYPES)
    fiscal_year_id = serializers.UUIDField()
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    description = serializers.CharField(required=False, allow_blank=True)
    notes = serializers.CharField(required=False, allow_blank=True)
    
    # Lignes de budget
    lines = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True
    )


class FinancialReportSerializer(serializers.ModelSerializer):
    """Serializer pour les rapports financiers"""
    fiscal_year_name = serializers.CharField(source='fiscal_year.name', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = FinancialReport
        fields = [
            'id', 'name', 'report_type', 'start_date', 'end_date',
            'fiscal_year', 'fiscal_year_name', 'status', 'report_data',
            'include_zero_balances', 'group_by_category', 'show_comparatives',
            'generated_by', 'generated_by_name', 'generation_time', 'error_message',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'status', 'report_data', 'generation_time', 'error_message', 'created_at', 'updated_at']


class ReconciliationRuleSerializer(serializers.ModelSerializer):
    """Serializer pour les règles de lettrage"""
    accounts_names = serializers.SerializerMethodField()
    
    class Meta:
        model = ReconciliationRule
        fields = [
            'id', 'name', 'rule_type', 'accounts', 'accounts_names',
            'amount_tolerance', 'date_tolerance_days', 'is_active',
            'auto_apply', 'priority', 'custom_conditions',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_accounts_names(self, obj):
        return [f"{account.account_code} - {account.account_name}" for account in obj.accounts.all()]


class AccountingDashboardSerializer(serializers.Serializer):
    """Serializer pour le dashboard Accounting"""
    tenant = serializers.CharField()
    timestamp = serializers.CharField()
    fiscal_year = serializers.DictField()
    accounts = serializers.DictField()
    entries = serializers.DictField()
    journals = serializers.DictField()
    budgets = serializers.DictField()
    financial_summary = serializers.DictField()
    alerts = serializers.DictField()
    recent_activities = serializers.ListField()


class AccountingInsightSerializer(serializers.Serializer):
    """Serializer pour les insights comptables"""
    type = serializers.ChoiceField(choices=['critical', 'warning', 'opportunity', 'info'])
    priority = serializers.ChoiceField(choices=['high', 'medium', 'low'])
    title = serializers.CharField()
    description = serializers.CharField()
    recommendation = serializers.CharField()
    generated_at = serializers.CharField()


class FinancialReportGenerateSerializer(serializers.Serializer):
    """Serializer pour la génération de rapports financiers"""
    report_type = serializers.ChoiceField(choices=FinancialReport.REPORT_TYPES)
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    include_zero_balances = serializers.BooleanField(default=False)
    group_by_category = serializers.BooleanField(default=True)
    show_comparatives = serializers.BooleanField(default=False)


class AccountReconciliationSerializer(serializers.Serializer):
    """Serializer pour le lettrage de comptes"""
    account_ids = serializers.ListField(
        child=serializers.UUIDField(),
        min_length=2
    )
    reconciliation_ref = serializers.CharField(max_length=50)


class TrialBalanceSerializer(serializers.Serializer):
    """Serializer pour la balance générale"""
    accounts = serializers.ListField()
    totals = serializers.DictField()
    period = serializers.DictField()


class IncomeStatementSerializer(serializers.Serializer):
    """Serializer pour le compte de résultat"""
    revenue = serializers.DictField()
    expenses = serializers.DictField()
    profitability = serializers.DictField()


class BalanceSheetSerializer(serializers.Serializer):
    """Serializer pour le bilan"""
    assets = serializers.DictField()
    liabilities = serializers.DictField()
    equity = serializers.DictField()
    total_liabilities_and_equity = serializers.DecimalField(max_digits=15, decimal_places=2)
    balanced = serializers.BooleanField()


class BudgetVsActualSerializer(serializers.Serializer):
    """Serializer pour le rapport budget vs réalisé"""
    comparisons = serializers.ListField()
    summary = serializers.DictField()
    period = serializers.DictField()


class AccountBalanceSerializer(serializers.Serializer):
    """Serializer pour les soldes de comptes"""
    account_id = serializers.UUIDField()
    account_code = serializers.CharField()
    account_name = serializers.CharField()
    account_type = serializers.CharField()
    balance = serializers.DecimalField(max_digits=15, decimal_places=2)
    balance_date = serializers.DateField()


class JournalEntryValidationSerializer(serializers.Serializer):
    """Serializer pour la validation d'écritures"""
    entry_ids = serializers.ListField(
        child=serializers.UUIDField(),
        min_length=1
    )
    validation_notes = serializers.CharField(required=False, allow_blank=True)


class FiscalYearCloseSerializer(serializers.Serializer):
    """Serializer pour la clôture d'exercice"""
    fiscal_year_id = serializers.UUIDField()
    generate_closing_entries = serializers.BooleanField(default=True)
    transfer_result_to_account = serializers.UUIDField(required=False, allow_null=True)
    closing_notes = serializers.CharField(required=False, allow_blank=True)


class TaxDeclarationSerializer(serializers.Serializer):
    """Serializer pour les déclarations fiscales"""
    tax_period_start = serializers.DateField()
    tax_period_end = serializers.DateField()
    tax_type = serializers.ChoiceField(choices=TaxCode.TAX_TYPES)
    include_draft_entries = serializers.BooleanField(default=False)


class AccountingExportSerializer(serializers.Serializer):
    """Serializer pour l'export comptable"""
    export_type = serializers.ChoiceField(choices=[
        ('fec', 'Fichier des Écritures Comptables (FEC)'),
        ('balance', 'Balance générale'),
        ('journal', 'Journal'),
        ('grand_livre', 'Grand livre'),
    ])
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    format = serializers.ChoiceField(choices=[
        ('csv', 'CSV'),
        ('xlsx', 'Excel'),
        ('xml', 'XML'),
        ('txt', 'Texte'),
    ], default='csv')
    include_validated_only = serializers.BooleanField(default=True)
