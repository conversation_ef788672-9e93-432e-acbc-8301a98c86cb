import api from './api';

class CRMService {
  // Dashboard et statut
  async getStatus() {
    const response = await api.get('/agents/crm/status/');
    return response.data;
  }

  async getDashboard() {
    const response = await api.get('/agents/crm/dashboard/');
    return response.data;
  }

  async getInsights() {
    const response = await api.get('/agents/crm/insights/');
    return response.data;
  }

  // Contacts
  async getContacts(params = {}) {
    const response = await api.get('/agents/crm/contacts/', { params });
    return response.data;
  }

  async getContact(id) {
    const response = await api.get(`/agents/crm/contacts/${id}/`);
    return response.data;
  }

  async createContact(contactData) {
    const response = await api.post('/agents/crm/contacts/create/', contactData);
    return response.data;
  }

  async updateContact(id, contactData) {
    const response = await api.put(`/agents/crm/contacts/${id}/`, contactData);
    return response.data;
  }

  async deleteContact(id) {
    const response = await api.delete(`/agents/crm/contacts/${id}/`);
    return response.data;
  }

  // Opportunités
  async getOpportunities(params = {}) {
    const response = await api.get('/agents/crm/opportunities/', { params });
    return response.data;
  }

  async getOpportunity(id) {
    const response = await api.get(`/agents/crm/opportunities/${id}/`);
    return response.data;
  }

  async createOpportunity(opportunityData) {
    const response = await api.post('/agents/crm/opportunities/create/', opportunityData);
    return response.data;
  }

  async updateOpportunity(id, opportunityData) {
    const response = await api.put(`/agents/crm/opportunities/${id}/`, opportunityData);
    return response.data;
  }

  async deleteOpportunity(id) {
    const response = await api.delete(`/agents/crm/opportunities/${id}/`);
    return response.data;
  }

  // Campagnes
  async getCampaigns(params = {}) {
    const response = await api.get('/agents/crm/campaigns/', { params });
    return response.data;
  }

  async getCampaign(id) {
    const response = await api.get(`/agents/crm/campaigns/${id}/`);
    return response.data;
  }

  async createCampaign(campaignData) {
    const response = await api.post('/agents/crm/campaigns/create/', campaignData);
    return response.data;
  }

  async updateCampaign(id, campaignData) {
    const response = await api.put(`/agents/crm/campaigns/${id}/`, campaignData);
    return response.data;
  }

  async deleteCampaign(id) {
    const response = await api.delete(`/agents/crm/campaigns/${id}/`);
    return response.data;
  }

  // Tickets de support
  async getTickets(params = {}) {
    const response = await api.get('/agents/crm/tickets/', { params });
    return response.data;
  }

  async getTicket(id) {
    const response = await api.get(`/agents/crm/tickets/${id}/`);
    return response.data;
  }

  async createTicket(ticketData) {
    const response = await api.post('/agents/crm/tickets/create/', ticketData);
    return response.data;
  }

  async updateTicket(id, ticketData) {
    const response = await api.put(`/agents/crm/tickets/${id}/`, ticketData);
    return response.data;
  }

  async deleteTicket(id) {
    const response = await api.delete(`/agents/crm/tickets/${id}/`);
    return response.data;
  }

  // Interactions
  async getInteractions(params = {}) {
    const response = await api.get('/agents/crm/interactions/', { params });
    return response.data;
  }

  async getInteraction(id) {
    const response = await api.get(`/agents/crm/interactions/${id}/`);
    return response.data;
  }

  async createInteraction(interactionData) {
    const response = await api.post('/agents/crm/interactions/create/', interactionData);
    return response.data;
  }

  async updateInteraction(id, interactionData) {
    const response = await api.put(`/agents/crm/interactions/${id}/`, interactionData);
    return response.data;
  }

  async deleteInteraction(id) {
    const response = await api.delete(`/agents/crm/interactions/${id}/`);
    return response.data;
  }

  // Fonctionnalités avancées
  async updateLeadScores() {
    const response = await api.post('/agents/crm/lead-scores/update/');
    return response.data;
  }

  // Utilitaires pour les choix
  getContactTypes() {
    return [
      { value: 'prospect', label: 'Prospect' },
      { value: 'customer', label: 'Client' },
      { value: 'partner', label: 'Partenaire' },
      { value: 'supplier', label: 'Fournisseur' },
      { value: 'lead', label: 'Lead' },
      { value: 'former_customer', label: 'Ancien client' }
    ];
  }

  getContactStatuses() {
    return [
      { value: 'active', label: 'Actif' },
      { value: 'inactive', label: 'Inactif' },
      { value: 'qualified', label: 'Qualifié' },
      { value: 'unqualified', label: 'Non qualifié' },
      { value: 'converted', label: 'Converti' },
      { value: 'lost', label: 'Perdu' },
      { value: 'blacklisted', label: 'Liste noire' }
    ];
  }

  getLeadSources() {
    return [
      { value: 'website', label: 'Site web' },
      { value: 'social_media', label: 'Réseaux sociaux' },
      { value: 'email_campaign', label: 'Campagne email' },
      { value: 'referral', label: 'Recommandation' },
      { value: 'cold_call', label: 'Appel à froid' },
      { value: 'trade_show', label: 'Salon professionnel' },
      { value: 'advertising', label: 'Publicité' },
      { value: 'partner', label: 'Partenaire' },
      { value: 'direct', label: 'Direct' },
      { value: 'other', label: 'Autre' }
    ];
  }

  getOpportunityStages() {
    return [
      { value: 'prospecting', label: 'Prospection' },
      { value: 'qualification', label: 'Qualification' },
      { value: 'needs_analysis', label: 'Analyse des besoins' },
      { value: 'proposal', label: 'Proposition' },
      { value: 'negotiation', label: 'Négociation' },
      { value: 'closed_won', label: 'Gagné' },
      { value: 'closed_lost', label: 'Perdu' },
      { value: 'on_hold', label: 'En attente' }
    ];
  }

  getOpportunityTypes() {
    return [
      { value: 'new_business', label: 'Nouvelle affaire' },
      { value: 'existing_business', label: 'Affaire existante' },
      { value: 'upsell', label: 'Montée en gamme' },
      { value: 'cross_sell', label: 'Vente croisée' },
      { value: 'renewal', label: 'Renouvellement' }
    ];
  }

  getCampaignTypes() {
    return [
      { value: 'email', label: 'Email' },
      { value: 'sms', label: 'SMS' },
      { value: 'social_media', label: 'Réseaux sociaux' },
      { value: 'advertising', label: 'Publicité' },
      { value: 'direct_mail', label: 'Courrier direct' },
      { value: 'webinar', label: 'Webinaire' },
      { value: 'event', label: 'Événement' },
      { value: 'content', label: 'Contenu' },
      { value: 'referral', label: 'Parrainage' },
      { value: 'other', label: 'Autre' }
    ];
  }

  getCampaignStatuses() {
    return [
      { value: 'draft', label: 'Brouillon' },
      { value: 'scheduled', label: 'Programmée' },
      { value: 'active', label: 'Active' },
      { value: 'paused', label: 'En pause' },
      { value: 'completed', label: 'Terminée' },
      { value: 'cancelled', label: 'Annulée' }
    ];
  }

  getTicketTypes() {
    return [
      { value: 'question', label: 'Question' },
      { value: 'bug_report', label: 'Rapport de bug' },
      { value: 'feature_request', label: 'Demande de fonctionnalité' },
      { value: 'complaint', label: 'Réclamation' },
      { value: 'refund_request', label: 'Demande de remboursement' },
      { value: 'technical_support', label: 'Support technique' },
      { value: 'billing_inquiry', label: 'Question de facturation' },
      { value: 'account_issue', label: 'Problème de compte' },
      { value: 'other', label: 'Autre' }
    ];
  }

  getTicketStatuses() {
    return [
      { value: 'new', label: 'Nouveau' },
      { value: 'open', label: 'Ouvert' },
      { value: 'in_progress', label: 'En cours' },
      { value: 'pending', label: 'En attente' },
      { value: 'resolved', label: 'Résolu' },
      { value: 'closed', label: 'Fermé' },
      { value: 'cancelled', label: 'Annulé' }
    ];
  }

  getPriorityLevels() {
    return [
      { value: 'low', label: 'Faible' },
      { value: 'medium', label: 'Moyenne' },
      { value: 'high', label: 'Élevée' },
      { value: 'urgent', label: 'Urgente' },
      { value: 'critical', label: 'Critique' }
    ];
  }

  getInteractionTypes() {
    return [
      { value: 'call', label: 'Appel téléphonique' },
      { value: 'email', label: 'Email' },
      { value: 'meeting', label: 'Réunion' },
      { value: 'demo', label: 'Démonstration' },
      { value: 'proposal', label: 'Proposition' },
      { value: 'follow_up', label: 'Suivi' },
      { value: 'support', label: 'Support' },
      { value: 'social_media', label: 'Réseaux sociaux' },
      { value: 'chat', label: 'Chat' },
      { value: 'sms', label: 'SMS' },
      { value: 'letter', label: 'Courrier' },
      { value: 'other', label: 'Autre' }
    ];
  }

  getInteractionDirections() {
    return [
      { value: 'inbound', label: 'Entrant' },
      { value: 'outbound', label: 'Sortant' }
    ];
  }

  getInteractionOutcomes() {
    return [
      { value: 'successful', label: 'Réussi' },
      { value: 'no_answer', label: 'Pas de réponse' },
      { value: 'busy', label: 'Occupé' },
      { value: 'voicemail', label: 'Messagerie' },
      { value: 'meeting_scheduled', label: 'Réunion programmée' },
      { value: 'follow_up_required', label: 'Suivi requis' },
      { value: 'not_interested', label: 'Pas intéressé' },
      { value: 'interested', label: 'Intéressé' },
      { value: 'proposal_requested', label: 'Proposition demandée' },
      { value: 'closed_won', label: 'Affaire gagnée' },
      { value: 'closed_lost', label: 'Affaire perdue' }
    ];
  }
}

export const crmService = new CRMService();
