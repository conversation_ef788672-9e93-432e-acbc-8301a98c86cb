import { createTheme, ThemeOptions } from '@mui/material/styles';

// Palette de couleurs ERP HUB
const colors = {
  primary: {
    main: '#1976d2',
    light: '#42a5f5',
    dark: '#1565c0',
    contrastText: '#ffffff'
  },
  secondary: {
    main: '#9c27b0',
    light: '#ba68c8',
    dark: '#7b1fa2',
    contrastText: '#ffffff'
  },
  success: {
    main: '#4caf50',
    light: '#81c784',
    dark: '#388e3c',
    contrastText: '#ffffff'
  },
  warning: {
    main: '#ff9800',
    light: '#ffb74d',
    dark: '#f57c00',
    contrastText: '#000000'
  },
  error: {
    main: '#f44336',
    light: '#e57373',
    dark: '#d32f2f',
    contrastText: '#ffffff'
  },
  info: {
    main: '#2196f3',
    light: '#64b5f6',
    dark: '#1976d2',
    contrastText: '#ffffff'
  },
  grey: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#eeeeee',
    300: '#e0e0e0',
    400: '#bdbdbd',
    500: '#9e9e9e',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121'
  }
};

// Configuration du thème
const themeOptions: ThemeOptions = {
  palette: {
    mode: 'light',
    ...colors,
    background: {
      default: '#f8f9fa',
      paper: '#ffffff'
    },
    text: {
      primary: '#212121',
      secondary: '#757575'
    }
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 700,
      lineHeight: 1.3
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.3
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.4
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 600,
      lineHeight: 1.4
    },
    subtitle1: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.5
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.5
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.6
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.6
    },
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: 1.4
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 600,
      textTransform: 'none'
    }
  },
  shape: {
    borderRadius: 12
  },
  spacing: 8,
  components: {
    // Card personnalisée
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          borderRadius: 16,
          border: '1px solid rgba(0, 0, 0, 0.05)',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12)',
            transform: 'translateY(-1px)'
          }
        }
      }
    },
    // Button personnalisé
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 10,
          padding: '10px 24px',
          fontSize: '0.875rem',
          fontWeight: 600,
          textTransform: 'none',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
          }
        },
        contained: {
          '&:hover': {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)'
          }
        }
      }
    },
    // Chip personnalisé
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          fontWeight: 500
        }
      }
    },
    // Paper personnalisé
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'
        }
      }
    },
    // Avatar personnalisé
    MuiAvatar: {
      styleOverrides: {
        root: {
          fontWeight: 600
        }
      }
    },
    // LinearProgress personnalisé
    MuiLinearProgress: {
      styleOverrides: {
        root: {
          borderRadius: 4,
          height: 8
        },
        bar: {
          borderRadius: 4
        }
      }
    },
    // Table personnalisée
    MuiTableCell: {
      styleOverrides: {
        head: {
          fontWeight: 600,
          backgroundColor: '#f8f9fa',
          borderBottom: '2px solid #e0e0e0'
        },
        root: {
          borderBottom: '1px solid #f0f0f0'
        }
      }
    },
    // Alert personnalisée
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          border: '1px solid',
          '&.MuiAlert-standardSuccess': {
            backgroundColor: '#f1f8e9',
            borderColor: '#4caf50'
          },
          '&.MuiAlert-standardError': {
            backgroundColor: '#ffebee',
            borderColor: '#f44336'
          },
          '&.MuiAlert-standardWarning': {
            backgroundColor: '#fff3e0',
            borderColor: '#ff9800'
          },
          '&.MuiAlert-standardInfo': {
            backgroundColor: '#e3f2fd',
            borderColor: '#2196f3'
          }
        }
      }
    },
    // IconButton personnalisé
    MuiIconButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)'
          }
        }
      }
    },
    // Tooltip personnalisé
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: '#424242',
          fontSize: '0.75rem',
          borderRadius: 8,
          padding: '8px 12px'
        }
      }
    }
  }
};

// Créer le thème
export const erpTheme = createTheme(themeOptions);

// Thème sombre (optionnel)
export const erpDarkTheme = createTheme({
  ...themeOptions,
  palette: {
    mode: 'dark',
    ...colors,
    background: {
      default: '#121212',
      paper: '#1e1e1e'
    },
    text: {
      primary: '#ffffff',
      secondary: '#b0b0b0'
    }
  }
});

// Couleurs spécifiques aux agents
export const agentColors = {
  manager: '#1976d2',
  hr: '#9c27b0',
  sales: '#2196f3',
  purchase: '#ff9800',
  logistics: '#4caf50',
  stock: '#00bcd4',
  accounting: '#795548',
  finance: '#8bc34a',
  crm: '#e91e63',
  bi: '#673ab7'
};

// Gradients pour les cartes
export const cardGradients = {
  primary: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
  secondary: 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
  success: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
  warning: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
  error: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
  info: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)'
};

export default erpTheme;
