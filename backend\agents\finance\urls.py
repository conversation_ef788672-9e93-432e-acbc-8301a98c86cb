"""
URLs pour l'agent Finance
"""
from django.urls import path
from . import views

app_name = 'finance'

urlpatterns = [
    # Statut et dashboard
    path('status/', views.finance_status, name='status'),
    path('dashboard/', views.finance_dashboard, name='dashboard'),

    # Gestion des banques
    path('banks/', views.BankListCreateView.as_view(), name='bank-list'),
    path('banks/<uuid:pk>/', views.BankDetailView.as_view(), name='bank-detail'),

    # Gestion des comptes bancaires
    path('accounts/', views.BankAccountListCreateView.as_view(), name='account-list'),
    path('accounts/<uuid:pk>/', views.BankAccountDetailView.as_view(), name='account-detail'),

    # Gestion des transactions bancaires
    path('transactions/', views.BankTransactionListView.as_view(), name='transaction-list'),
    path('transactions/create/', views.BankTransactionCreateView.as_view(), name='transaction-create'),

    # Gestion des prévisions de trésorerie
    path('forecasts/', views.CashFlowForecastListView.as_view(), name='forecast-list'),
    path('forecasts/create/', views.CashFlowForecastCreateView.as_view(), name='forecast-create'),

    # Gestion des investissements
    path('investments/', views.InvestmentListView.as_view(), name='investment-list'),
    path('investments/create/', views.InvestmentCreateView.as_view(), name='investment-create'),

    # Gestion des emprunts
    path('loans/', views.LoanListView.as_view(), name='loan-list'),
    path('loans/create/', views.LoanCreateView.as_view(), name='loan-create'),

    # Gestion des rapports de trésorerie
    path('reports/', views.TreasuryReportListView.as_view(), name='report-list'),
    path('reports/generate/', views.TreasuryReportGenerateView.as_view(), name='report-generate'),

    # Calcul des ratios financiers
    path('ratios/calculate/', views.calculate_financial_ratios, name='ratios-calculate'),

    # Analyses et insights
    path('insights/', views.finance_insights, name='insights'),
    path('treasury-position/', views.treasury_position, name='treasury-position'),
    path('liquidity-analysis/', views.liquidity_analysis, name='liquidity-analysis'),
]
