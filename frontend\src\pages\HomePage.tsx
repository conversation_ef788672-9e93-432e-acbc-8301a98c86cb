import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  AppBar,
  Toolbar,
  IconButton,
  Chip,
  Avatar,
  Fade,
  Slide,
  useTheme,
  useMediaQuery,
  LinearProgress,
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  TrendingUp as SalesIcon,
  ShoppingCart as PurchaseIcon,
  LocalShipping as LogisticsIcon,
  Inventory as StockIcon,
  AccountBalance as AccountingIcon,
  AttachMoney as FinanceIcon,
  Handshake as CRMIcon,
  Analytics as BIIcon,
  Login as LoginIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  Cloud as CloudIcon,
  Menu as MenuIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { useAuthStore } from '../store/authStore'
import { realtimeService } from '../services/api'

// Types pour les données
interface AgentData {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  status: 'active' | 'inactive' | 'maintenance'
  color: string
  metrics?: {
    value: number
    label: string
  }
}

interface SystemMetrics {
  activeUsers: number
  totalTransactions: number
  systemUptime: string
  responseTime: number
}

export const HomePage: React.FC = () => {
  const navigate = useNavigate()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const { isAuthenticated, user } = useAuthStore()
  
  const [metrics, setMetrics] = useState<SystemMetrics>({
    activeUsers: 0,
    totalTransactions: 0,
    systemUptime: '99.9%',
    responseTime: 0
  })
  const [loading, setLoading] = useState(true)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  // Configuration des agents
  const agents: AgentData[] = [
    {
      id: 'manager',
      name: 'Agent Manager',
      description: 'Supervision générale du système ERP et coordination des agents',
      icon: <DashboardIcon sx={{ fontSize: 40 }} />,
      status: 'active',
      color: '#6366f1',
      metrics: { value: 10, label: 'Agents actifs' }
    },
    {
      id: 'hr',
      name: 'Agent HR',
      description: 'Gestion des ressources humaines et administration du personnel',
      icon: <PeopleIcon sx={{ fontSize: 40 }} />,
      status: 'active',
      color: '#8b5cf6',
      metrics: { value: 150, label: 'Employés' }
    },
    {
      id: 'sales',
      name: 'Agent Sales',
      description: 'Gestion commerciale, pipeline de ventes et suivi des prospects',
      icon: <SalesIcon sx={{ fontSize: 40 }} />,
      status: 'active',
      color: '#06b6d4',
      metrics: { value: 85, label: 'Leads actifs' }
    },
    {
      id: 'purchase',
      name: 'Agent Purchase',
      description: 'Gestion des achats, fournisseurs et commandes d\'approvisionnement',
      icon: <PurchaseIcon sx={{ fontSize: 40 }} />,
      status: 'active',
      color: '#10b981',
      metrics: { value: 45, label: 'Commandes' }
    },
    {
      id: 'logistics',
      name: 'Agent Logistics',
      description: 'Transport, livraisons et gestion de la chaîne logistique',
      icon: <LogisticsIcon sx={{ fontSize: 40 }} />,
      status: 'active',
      color: '#f59e0b',
      metrics: { value: 23, label: 'Expéditions' }
    },
    {
      id: 'stock',
      name: 'Agent Stock',
      description: 'Gestion d\'inventaire, stocks et mouvements de marchandises',
      icon: <StockIcon sx={{ fontSize: 40 }} />,
      status: 'active',
      color: '#84cc16',
      metrics: { value: 1250, label: 'Produits' }
    },
    {
      id: 'accounting',
      name: 'Agent Accounting',
      description: 'Comptabilité générale, écritures et rapports financiers',
      icon: <AccountingIcon sx={{ fontSize: 40 }} />,
      status: 'active',
      color: '#ef4444',
      metrics: { value: 2340, label: 'Transactions' }
    },
    {
      id: 'finance',
      name: 'Agent Finance',
      description: 'Gestion financière, trésorerie et analyse des performances',
      icon: <FinanceIcon sx={{ fontSize: 40 }} />,
      status: 'active',
      color: '#f97316',
      metrics: { value: 95.2, label: '% Performance' }
    },
    {
      id: 'crm',
      name: 'Agent CRM',
      description: 'Relation client avancée et gestion de la satisfaction',
      icon: <CRMIcon sx={{ fontSize: 40 }} />,
      status: 'active',
      color: '#ec4899',
      metrics: { value: 340, label: 'Clients' }
    },
    {
      id: 'bi',
      name: 'Agent BI',
      description: 'Business Intelligence, analytics et tableaux de bord',
      icon: <BIIcon sx={{ fontSize: 40 }} />,
      status: 'active',
      color: '#3b82f6',
      metrics: { value: 45, label: 'Rapports' }
    }
  ]

  // Charger les métriques en temps réel
  useEffect(() => {
    const loadMetrics = async () => {
      try {
        const response = await realtimeService.getMetrics()
        if (response.status === 'success') {
          setMetrics({
            activeUsers: response.metrics.active_users || 45,
            totalTransactions: response.metrics.network_traffic || 2340,
            systemUptime: '99.9%',
            responseTime: response.metrics.system_load || 120
          })
        }
      } catch (error) {
        console.error('Erreur lors du chargement des métriques:', error)
        // Valeurs par défaut
        setMetrics({
          activeUsers: 45,
          totalTransactions: 2340,
          systemUptime: '99.9%',
          responseTime: 120
        })
      } finally {
        setLoading(false)
      }
    }

    loadMetrics()
    
    // Actualiser toutes les 30 secondes
    const interval = setInterval(loadMetrics, 30000)
    return () => clearInterval(interval)
  }, [])

  const handleAgentClick = (agentId: string) => {
    if (isAuthenticated) {
      navigate(`/agents/${agentId}`)
    } else {
      navigate('/login')
    }
  }

  const handleLogin = () => {
    navigate('/login')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#10b981'
      case 'inactive': return '#ef4444'
      case 'maintenance': return '#f59e0b'
      default: return '#6b7280'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Actif'
      case 'inactive': return 'Inactif'
      case 'maintenance': return 'Maintenance'
      default: return 'Inconnu'
    }
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Header */}
      <AppBar 
        position="sticky" 
        elevation={0}
        sx={{ 
          bgcolor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Toolbar>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography
                variant="h5"
                component="div"
                sx={{
                  fontWeight: 700,
                  background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontFamily: 'Inter, sans-serif'
                }}
              >
                🏢 ERP HUB
              </Typography>
            </motion.div>
          </Box>

          {!isMobile && (
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button color="inherit" sx={{ color: 'text.primary' }}>
                Fonctionnalités
              </Button>
              <Button color="inherit" sx={{ color: 'text.primary' }}>
                Documentation
              </Button>
              <Button color="inherit" sx={{ color: 'text.primary' }}>
                Support
              </Button>
            </Box>
          )}

          <Box sx={{ ml: 2 }}>
            {isAuthenticated ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                  {user?.first_name?.[0] || 'U'}
                </Avatar>
                <Typography variant="body2" sx={{ color: 'text.primary' }}>
                  {user?.first_name} {user?.last_name}
                </Typography>
              </Box>
            ) : (
              <Button
                variant="contained"
                startIcon={<LoginIcon />}
                onClick={handleLogin}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                Se connecter
              </Button>
            )}
          </Box>

          {isMobile && (
            <IconButton
              edge="end"
              color="inherit"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              sx={{ color: 'text.primary' }}
            >
              <MenuIcon />
            </IconButton>
          )}
        </Toolbar>
      </AppBar>

      {/* Section Hero */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Box textAlign="center" mb={8}>
            <Typography
              variant="h2"
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 800,
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 3
              }}
            >
              Système ERP Multi-Agents
            </Typography>
            
            <Typography
              variant="h5"
              color="text.secondary"
              sx={{ 
                mb: 4, 
                maxWidth: 800, 
                mx: 'auto',
                lineHeight: 1.6,
                fontWeight: 400
              }}
            >
              Une plateforme ERP intelligente avec 10 agents spécialisés pour optimiser 
              tous les aspects de votre entreprise. Architecture scalable et moderne.
            </Typography>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="large"
                onClick={() => isAuthenticated ? navigate('/dashboard') : handleLogin()}
                sx={{
                  borderRadius: 3,
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  textTransform: 'none'
                }}
              >
                {isAuthenticated ? 'Accéder au Dashboard' : 'Commencer maintenant'}
              </Button>
              
              <Button
                variant="outlined"
                size="large"
                sx={{
                  borderRadius: 3,
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  textTransform: 'none'
                }}
              >
                Voir la démo
              </Button>
            </Box>
          </Box>
        </motion.div>

        {/* Métriques en temps réel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <Card 
            sx={{ 
              mb: 8, 
              background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
              border: '1px solid',
              borderColor: 'divider'
            }}
          >
            <CardContent sx={{ py: 4 }}>
              <Typography variant="h6" gutterBottom textAlign="center" sx={{ mb: 3 }}>
                📊 Métriques Système en Temps Réel
              </Typography>
              
              {loading ? (
                <LinearProgress sx={{ mb: 2 }} />
              ) : (
                <Grid container spacing={4}>
                  <Grid item xs={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="primary" sx={{ fontWeight: 700 }}>
                        {metrics.activeUsers}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Utilisateurs actifs
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="success.main" sx={{ fontWeight: 700 }}>
                        {metrics.totalTransactions.toLocaleString()}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Transactions
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="info.main" sx={{ fontWeight: 700 }}>
                        {metrics.systemUptime}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Disponibilité
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="warning.main" sx={{ fontWeight: 700 }}>
                        {metrics.responseTime}ms
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Temps de réponse
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Grille des Agents */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <Typography 
            variant="h4" 
            component="h2" 
            textAlign="center" 
            gutterBottom 
            sx={{ mb: 6, fontWeight: 700 }}
          >
            🤖 Agents ERP Intelligents
          </Typography>

          <Grid container spacing={3}>
            {agents.map((agent, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={agent.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <Card
                    sx={{
                      height: '100%',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        boxShadow: theme.shadows[8],
                        transform: 'translateY(-4px)'
                      },
                      border: '1px solid',
                      borderColor: 'divider'
                    }}
                    onClick={() => handleAgentClick(agent.id)}
                  >
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Avatar
                          sx={{
                            bgcolor: agent.color,
                            width: 56,
                            height: 56,
                            mr: 2
                          }}
                        >
                          {agent.icon}
                        </Avatar>
                        
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                            {agent.name}
                          </Typography>
                          <Chip
                            label={getStatusLabel(agent.status)}
                            size="small"
                            sx={{
                              bgcolor: getStatusColor(agent.status),
                              color: 'white',
                              fontSize: '0.75rem'
                            }}
                          />
                        </Box>
                      </Box>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ mb: 2, lineHeight: 1.5 }}
                      >
                        {agent.description}
                      </Typography>

                      {agent.metrics && (
                        <Box
                          sx={{
                            bgcolor: 'background.paper',
                            borderRadius: 1,
                            p: 1.5,
                            border: '1px solid',
                            borderColor: 'divider'
                          }}
                        >
                          <Typography variant="h6" color="primary" sx={{ fontWeight: 700 }}>
                            {agent.metrics.value.toLocaleString()}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {agent.metrics.label}
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* Section Avantages */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <Box sx={{ mt: 12, mb: 8 }}>
            <Typography 
              variant="h4" 
              component="h2" 
              textAlign="center" 
              gutterBottom 
              sx={{ mb: 6, fontWeight: 700 }}
            >
              🚀 Pourquoi Choisir ERP HUB ?
            </Typography>

            <Grid container spacing={4}>
              <Grid item xs={12} md={4}>
                <Box textAlign="center">
                  <SpeedIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                    Performance Optimale
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Architecture moderne avec temps de réponse &lt;200ms et scalabilité horizontale
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box textAlign="center">
                  <SecurityIcon sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                    Sécurité Enterprise
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Authentification JWT, chiffrement des données et audit trail complet
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box textAlign="center">
                  <CloudIcon sx={{ fontSize: 60, color: 'info.main', mb: 2 }} />
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                    Cloud Ready
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Déploiement Docker, monitoring intégré et backup automatique
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </motion.div>
      </Container>

      {/* Footer */}
      <Box
        component="footer"
        sx={{
          bgcolor: 'background.paper',
          borderTop: '1px solid',
          borderColor: 'divider',
          py: 6,
          mt: 8
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 700 }}>
                🏢 ERP HUB
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Système ERP multi-agents pour optimiser votre entreprise avec une architecture moderne et scalable.
              </Typography>
            </Grid>

            <Grid item xs={12} md={2}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Produit
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button size="small" sx={{ justifyContent: 'flex-start', textTransform: 'none' }}>
                  Fonctionnalités
                </Button>
                <Button size="small" sx={{ justifyContent: 'flex-start', textTransform: 'none' }}>
                  Tarifs
                </Button>
                <Button size="small" sx={{ justifyContent: 'flex-start', textTransform: 'none' }}>
                  Démo
                </Button>
              </Box>
            </Grid>

            <Grid item xs={12} md={2}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Support
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button size="small" sx={{ justifyContent: 'flex-start', textTransform: 'none' }}>
                  Documentation
                </Button>
                <Button size="small" sx={{ justifyContent: 'flex-start', textTransform: 'none' }}>
                  API
                </Button>
                <Button size="small" sx={{ justifyContent: 'flex-start', textTransform: 'none' }}>
                  Contact
                </Button>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Contact
              </Typography>
              <Typography variant="body2" color="text.secondary">
                📧 <EMAIL><br />
                📞 +33 1 23 45 67 89<br />
                📍 Paris, France
              </Typography>
            </Grid>
          </Grid>

          <Box
            sx={{
              borderTop: '1px solid',
              borderColor: 'divider',
              mt: 4,
              pt: 4,
              textAlign: 'center'
            }}
          >
            <Typography variant="body2" color="text.secondary">
              © 2024 ERP HUB. Tous droits réservés. | Mentions légales | Politique de confidentialité
            </Typography>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}
