import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Calculator,
  FileText,
  TrendingUp,
  DollarSign,
  PieChart,
  AlertTriangle,
  CheckCircle,
  Clock,
  BookOpen,
  Receipt,
  BarChart3,
  Target,
  Lightbulb,
  RefreshCw,
  Calendar
} from 'lucide-react';

interface AccountingDashboard {
  tenant: string;
  timestamp: string;
  fiscal_year: {
    name: string;
    start_date: string;
    end_date: string;
    status: string;
    is_current: boolean;
  };
  accounts: {
    total: number;
    active: number;
    by_type: {
      asset: number;
      liability: number;
      equity: number;
      revenue: number;
      expense: number;
    };
  };
  entries: {
    total: number;
    current_year: number;
    posted: number;
    validated: number;
    pending: number;
  };
  journals: {
    total: number;
    by_type: Record<string, number>;
  };
  budgets: {
    total: number;
    current_year: number;
    approved: number;
    active: number;
  };
  financial_summary: {
    revenue: number;
    expenses: number;
    net_income: number;
    assets: number;
    liabilities: number;
    equity: number;
    ratios: {
      debt_to_equity: number;
      profit_margin: number;
    };
  };
  alerts: {
    pending_entries: number;
    unreconciled_accounts: number;
    budget_variances: number;
    tax_deadlines: number;
  };
  recent_activities: Array<{
    type: string;
    description: string;
    date: string;
    amount: number;
    journal: string;
    user: string;
  }>;
}

interface AccountingEntry {
  id: string;
  entry_number: string;
  description: string;
  entry_date: string;
  status: string;
  total_debit: number;
  total_credit: number;
  journal_name: string;
  created_by_name: string;
}

interface FinancialReport {
  id: string;
  name: string;
  report_type: string;
  start_date: string;
  end_date: string;
  status: string;
  created_at: string;
}

interface Budget {
  id: string;
  name: string;
  code: string;
  budget_type: string;
  status: string;
  total_revenue_budget: number;
  total_expense_budget: number;
  net_budget: number;
  fiscal_year_name: string;
}

const AccountingPage: React.FC = () => {
  const [dashboard, setDashboard] = useState<AccountingDashboard | null>(null);
  const [entries, setEntries] = useState<AccountingEntry[]>([]);
  const [reports, setReports] = useState<FinancialReport[]>([]);
  const [budgets, setBudgets] = useState<Budget[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchDashboard();
    if (activeTab === 'entries') fetchEntries();
    if (activeTab === 'reports') fetchReports();
    if (activeTab === 'budgets') fetchBudgets();
  }, [activeTab]);

  const fetchDashboard = async () => {
    try {
      const response = await fetch('/api/agents/accounting/dashboard/');
      if (response.ok) {
        const data = await response.json();
        setDashboard(data);
      }
    } catch (error) {
      console.error('Erreur lors du chargement du dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchEntries = async () => {
    try {
      const response = await fetch('/api/agents/accounting/entries/');
      if (response.ok) {
        const data = await response.json();
        setEntries(data.results || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des écritures:', error);
    }
  };

  const fetchReports = async () => {
    try {
      const response = await fetch('/api/agents/accounting/reports/');
      if (response.ok) {
        const data = await response.json();
        setReports(data.results || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des rapports:', error);
    }
  };

  const fetchBudgets = async () => {
    try {
      const response = await fetch('/api/agents/accounting/budgets/');
      if (response.ok) {
        const data = await response.json();
        setBudgets(data.results || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des budgets:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: 'Brouillon', variant: 'secondary' as const },
      posted: { label: 'Comptabilisée', variant: 'default' as const },
      validated: { label: 'Validée', variant: 'default' as const },
      cancelled: { label: 'Annulée', variant: 'destructive' as const },
      open: { label: 'Ouvert', variant: 'default' as const },
      closed: { label: 'Clôturé', variant: 'secondary' as const },
      approved: { label: 'Approuvé', variant: 'default' as const },
      active: { label: 'Actif', variant: 'default' as const },
      completed: { label: 'Terminé', variant: 'default' as const },
      generating: { label: 'En cours', variant: 'secondary' as const },
      error: { label: 'Erreur', variant: 'destructive' as const }
    };

    const config = statusConfig[status as keyof typeof statusConfig] ||
                  { label: status, variant: 'secondary' as const };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Agent Accounting</h1>
          <p className="text-muted-foreground">
            Comptabilité générale et analytique
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Opérationnel
          </Badge>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Vue d'ensemble
          </TabsTrigger>
          <TabsTrigger value="entries" className="flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            Écritures
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Rapports
          </TabsTrigger>
          <TabsTrigger value="budgets" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Budgets
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {dashboard && (
            <>
              {/* Exercice comptable en cours */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Exercice Comptable
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">{dashboard.fiscal_year.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        Du {new Date(dashboard.fiscal_year.start_date).toLocaleDateString('fr-FR')}
                        au {new Date(dashboard.fiscal_year.end_date).toLocaleDateString('fr-FR')}
                      </p>
                    </div>
                    {getStatusBadge(dashboard.fiscal_year.status)}
                  </div>
                </CardContent>
              </Card>

              {/* Métriques principales */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Comptes Actifs</CardTitle>
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{dashboard.accounts.active}</div>
                    <p className="text-xs text-muted-foreground">
                      sur {dashboard.accounts.total} comptes
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Écritures</CardTitle>
                    <Receipt className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{dashboard.entries.current_year}</div>
                    <p className="text-xs text-muted-foreground">
                      {dashboard.entries.pending} en attente
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Résultat Net</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(dashboard.financial_summary.net_income)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Marge: {dashboard.financial_summary.ratios.profit_margin.toFixed(1)}%
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Actif</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(dashboard.financial_summary.assets)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Ratio D/E: {dashboard.financial_summary.ratios.debt_to_equity.toFixed(2)}
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Alertes */}
              {(dashboard.alerts.pending_entries > 0 ||
                dashboard.alerts.unreconciled_accounts > 0 ||
                dashboard.alerts.budget_variances > 0 ||
                dashboard.alerts.tax_deadlines > 0) && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-orange-800">
                      <AlertTriangle className="h-5 w-5" />
                      Alertes
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {dashboard.alerts.pending_entries > 0 && (
                      <div className="flex items-center justify-between text-sm">
                        <span>Écritures en attente de validation</span>
                        <Badge variant="outline">{dashboard.alerts.pending_entries}</Badge>
                      </div>
                    )}
                    {dashboard.alerts.unreconciled_accounts > 0 && (
                      <div className="flex items-center justify-between text-sm">
                        <span>Comptes non lettrés</span>
                        <Badge variant="outline">{dashboard.alerts.unreconciled_accounts}</Badge>
                      </div>
                    )}
                    {dashboard.alerts.budget_variances > 0 && (
                      <div className="flex items-center justify-between text-sm">
                        <span>Écarts budgétaires significatifs</span>
                        <Badge variant="outline">{dashboard.alerts.budget_variances}</Badge>
                      </div>
                    )}
                    {dashboard.alerts.tax_deadlines > 0 && (
                      <div className="flex items-center justify-between text-sm">
                        <span>Échéances fiscales à venir</span>
                        <Badge variant="outline">{dashboard.alerts.tax_deadlines}</Badge>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Activités récentes */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Activités Récentes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {dashboard.recent_activities.slice(0, 5).map((activity, index) => (
                      <div key={index} className="flex items-center justify-between border-b pb-2 last:border-b-0">
                        <div className="flex-1">
                          <p className="text-sm font-medium">{activity.description}</p>
                          <p className="text-xs text-muted-foreground">
                            {activity.journal} • {activity.user} •
                            {new Date(activity.date).toLocaleDateString('fr-FR')}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">
                            {formatCurrency(activity.amount)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="entries" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Écritures Comptables</h2>
            <Button>
              <Receipt className="h-4 w-4 mr-2" />
              Nouvelle Écriture
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="border-b bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Numéro</th>
                      <th className="text-left p-4 font-medium">Description</th>
                      <th className="text-left p-4 font-medium">Date</th>
                      <th className="text-left p-4 font-medium">Journal</th>
                      <th className="text-left p-4 font-medium">Montant</th>
                      <th className="text-left p-4 font-medium">Statut</th>
                      <th className="text-left p-4 font-medium">Créé par</th>
                    </tr>
                  </thead>
                  <tbody>
                    {entries.map((entry) => (
                      <tr key={entry.id} className="border-b hover:bg-muted/50">
                        <td className="p-4 font-mono text-sm">{entry.entry_number}</td>
                        <td className="p-4">{entry.description}</td>
                        <td className="p-4 text-sm">
                          {new Date(entry.entry_date).toLocaleDateString('fr-FR')}
                        </td>
                        <td className="p-4 text-sm">{entry.journal_name}</td>
                        <td className="p-4 text-sm font-medium">
                          {formatCurrency(entry.total_debit)}
                        </td>
                        <td className="p-4">{getStatusBadge(entry.status)}</td>
                        <td className="p-4 text-sm">{entry.created_by_name}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Rapports Financiers</h2>
            <Button>
              <FileText className="h-4 w-4 mr-2" />
              Générer Rapport
            </Button>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {reports.map((report) => (
              <Card key={report.id}>
                <CardHeader>
                  <CardTitle className="text-lg">{report.name}</CardTitle>
                  <CardDescription>
                    {report.report_type.replace('_', ' ')} •
                    {new Date(report.start_date).toLocaleDateString('fr-FR')} -
                    {new Date(report.end_date).toLocaleDateString('fr-FR')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    {getStatusBadge(report.status)}
                    <p className="text-xs text-muted-foreground">
                      {new Date(report.created_at).toLocaleDateString('fr-FR')}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="budgets" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Budgets</h2>
            <Button>
              <Target className="h-4 w-4 mr-2" />
              Nouveau Budget
            </Button>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {budgets.map((budget) => (
              <Card key={budget.id}>
                <CardHeader>
                  <CardTitle className="text-lg">{budget.name}</CardTitle>
                  <CardDescription>
                    {budget.budget_type} • {budget.fiscal_year_name}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Produits budgétés</span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(budget.total_revenue_budget)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Charges budgétées</span>
                    <span className="font-medium text-red-600">
                      {formatCurrency(budget.total_expense_budget)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm border-t pt-2">
                    <span className="font-medium">Résultat budgété</span>
                    <span className={`font-bold ${budget.net_budget >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(budget.net_budget)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    {getStatusBadge(budget.status)}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AccountingPage;
