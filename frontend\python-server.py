#!/usr/bin/env python3
import http.server
import socketserver
import os
import webbrowser
from urllib.parse import urlparse

PORT = 3000
DJANGO_API_URL = "http://localhost:8000"

class ERPHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def do_GET(self):
        # Route de santé
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "status": "OK",
                "service": "ERP HUB Python Server",
                "frontend_port": PORT,
                "django_backend": DJANGO_API_URL
            }
            self.wfile.write(str(response).encode())
            return
        
        # Proxy vers Django pour les routes API
        if self.path.startswith('/api/'):
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "message": "API Proxy vers Django",
                "django_url": f"{DJANGO_API_URL}{self.path}",
                "note": "Démarrez Django sur le port 8000"
            }
            self.wfile.write(str(response).encode())
            return
        
        # Servir la page principale pour toutes les autres routes
        if self.path == '/' or self.path.startswith('/agents') or self.path.startswith('/dashboard'):
            self.path = '/test-simple.html'
        
        return super().do_GET()

def start_server():
    with socketserver.TCPServer(("", PORT), ERPHandler) as httpd:
        print("=" * 50)
        print("🚀 ERP HUB SERVER DÉMARRÉ AVEC SUCCÈS !")
        print("=" * 50)
        print(f"📡 Serveur Python : http://localhost:{PORT}")
        print(f"🔗 Backend Django : {DJANGO_API_URL}")
        print(f"💚 Health Check : http://localhost:{PORT}/health")
        print("=" * 50)
        print("✅ Votre ERP HUB est maintenant accessible !")
        print("🌐 Ouverture automatique du navigateur...")
        print("=" * 50)
        
        # Ouvrir automatiquement le navigateur
        webbrowser.open(f'http://localhost:{PORT}')
        
        print(f"🔄 Serveur en écoute sur le port {PORT}...")
        print("Appuyez sur Ctrl+C pour arrêter le serveur")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Arrêt du serveur ERP HUB")
            httpd.shutdown()

if __name__ == "__main__":
    start_server()
