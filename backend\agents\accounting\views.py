"""
Vues pour l'agent Accounting
"""
import logging
from django.utils import timezone
from django.db import models
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema

from core.permissions import AccountingReadPermission, AccountingWritePermission
from .services import AccountingService
from .models import (
    ChartOfAccounts, FiscalYear, Journal, AccountingEntry, TaxCode,
    Budget, FinancialReport, ReconciliationRule
)
from .serializers import (
    ChartOfAccountsSerializer, FiscalYearSerializer, JournalSerializer,
    AccountingEntrySerializer, AccountingEntryCreateSerializer, TaxCodeSerializer,
    BudgetSerializer, BudgetCreateSerializer, FinancialReportSerializer,
    ReconciliationRuleSerializer, AccountingDashboardSerializer,
    AccountingInsightSerializer, FinancialReportGenerateSerializer
)

logger = logging.getLogger('agents.accounting')


@extend_schema(
    summary="Statut de l'agent Accounting",
    description="Retourne le statut de l'agent Accounting"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def accounting_status(request):
    """Retourne le statut de l'agent Accounting"""
    try:
        accounting_service = AccountingService(request.user.tenant)

        return Response({
            'status': 'active',
            'agent': 'accounting',
            'message': 'Agent Accounting opérationnel',
            'capabilities': [
                'chart_of_accounts_management',
                'journal_entries',
                'financial_reporting',
                'budget_management',
                'tax_management',
                'reconciliation',
                'analytics',
                'compliance_monitoring'
            ]
        })
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut Accounting: {str(e)}")
        return Response({
            'status': 'error',
            'agent': 'accounting',
            'message': f'Erreur: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Dashboard de l'agent Accounting",
    description="Retourne les données complètes du dashboard Accounting"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, AccountingReadPermission])
def accounting_dashboard(request):
    """Retourne les données du dashboard Accounting"""
    try:
        accounting_service = AccountingService(request.user.tenant)
        dashboard_data = accounting_service.get_accounting_dashboard()

        return Response(dashboard_data)
    except Exception as e:
        logger.error(f"Erreur lors de la génération du dashboard Accounting: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Gestion des écritures comptables
class AccountingEntryCreateView(APIView):
    """Vue pour créer des écritures comptables avec logique métier"""
    permission_classes = [IsAuthenticated, AccountingWritePermission]

    @extend_schema(
        summary="Créer une écriture comptable",
        description="Crée une nouvelle écriture comptable avec validation automatique",
        request=AccountingEntryCreateSerializer
    )
    def post(self, request):
        """Crée une nouvelle écriture comptable"""
        try:
            serializer = AccountingEntryCreateSerializer(data=request.data)
            if serializer.is_valid():
                accounting_service = AccountingService(request.user.tenant)

                result = accounting_service.create_journal_entry(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'écriture: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AccountingEntryListView(generics.ListAPIView):
    """Vue pour lister les écritures comptables"""
    serializer_class = AccountingEntrySerializer
    permission_classes = [IsAuthenticated, AccountingReadPermission]

    def get_queryset(self):
        queryset = AccountingEntry.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        journal_id = self.request.query_params.get('journal')
        if journal_id:
            queryset = queryset.filter(journal_id=journal_id)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        entry_type = self.request.query_params.get('entry_type')
        if entry_type:
            queryset = queryset.filter(entry_type=entry_type)

        # Filtres par date
        date_from = self.request.query_params.get('date_from')
        if date_from:
            queryset = queryset.filter(entry_date__gte=date_from)

        date_to = self.request.query_params.get('date_to')
        if date_to:
            queryset = queryset.filter(entry_date__lte=date_to)

        # Recherche
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(entry_number__icontains=search) |
                models.Q(description__icontains=search) |
                models.Q(reference__icontains=search)
            )

        return queryset.order_by('-entry_date', '-entry_number')


# Gestion des rapports financiers
class FinancialReportGenerateView(APIView):
    """Vue pour générer des rapports financiers"""
    permission_classes = [IsAuthenticated, AccountingReadPermission]

    @extend_schema(
        summary="Générer un rapport financier",
        description="Génère un rapport financier selon le type spécifié",
        request=FinancialReportGenerateSerializer
    )
    def post(self, request):
        """Génère un rapport financier"""
        try:
            serializer = FinancialReportGenerateSerializer(data=request.data)
            if serializer.is_valid():
                accounting_service = AccountingService(request.user.tenant)

                result = accounting_service.generate_financial_report(
                    serializer.validated_data['report_type'],
                    serializer.validated_data['start_date'].isoformat(),
                    serializer.validated_data['end_date'].isoformat(),
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la génération du rapport: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FinancialReportListView(generics.ListAPIView):
    """Vue pour lister les rapports financiers"""
    serializer_class = FinancialReportSerializer
    permission_classes = [IsAuthenticated, AccountingReadPermission]

    def get_queryset(self):
        queryset = FinancialReport.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        report_type = self.request.query_params.get('type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-created_at')


# Gestion des budgets
class BudgetCreateView(APIView):
    """Vue pour créer des budgets avec logique métier"""
    permission_classes = [IsAuthenticated, AccountingWritePermission]

    @extend_schema(
        summary="Créer un budget",
        description="Crée un nouveau budget avec calcul automatique des totaux",
        request=BudgetCreateSerializer
    )
    def post(self, request):
        """Crée un nouveau budget"""
        try:
            serializer = BudgetCreateSerializer(data=request.data)
            if serializer.is_valid():
                accounting_service = AccountingService(request.user.tenant)

                result = accounting_service.create_budget(
                    serializer.validated_data,
                    request.user
                )

                if result['success']:
                    return Response(result, status=status.HTTP_201_CREATED)
                else:
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Erreur lors de la création du budget: {str(e)}")
            return Response(
                {'error': f'Erreur interne: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BudgetListView(generics.ListAPIView):
    """Vue pour lister les budgets"""
    serializer_class = BudgetSerializer
    permission_classes = [IsAuthenticated, AccountingReadPermission]

    def get_queryset(self):
        queryset = Budget.objects.filter(tenant=self.request.user.tenant)

        # Filtres
        budget_type = self.request.query_params.get('type')
        if budget_type:
            queryset = queryset.filter(budget_type=budget_type)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        fiscal_year_id = self.request.query_params.get('fiscal_year')
        if fiscal_year_id:
            queryset = queryset.filter(fiscal_year_id=fiscal_year_id)

        return queryset.order_by('-start_date', 'name')


# Analyses et insights
@extend_schema(
    summary="Insights comptables IA",
    description="Génère des insights et recommandations basés sur l'IA"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, AccountingReadPermission])
def accounting_insights(request):
    """Génère des insights comptables avec IA"""
    try:
        accounting_service = AccountingService(request.user.tenant)
        insights = accounting_service.generate_accounting_insights()

        return Response({'insights': insights})
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'insights: {str(e)}")
        return Response({
            'error': f'Erreur lors de la génération d\'insights: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Lettrage de comptes
@extend_schema(
    summary="Lettrage de comptes",
    description="Effectue le lettrage automatique de lignes d'écriture"
)
@api_view(['POST'])
@permission_classes([IsAuthenticated, AccountingWritePermission])
def reconcile_accounts(request):
    """Effectue le lettrage de comptes"""
    try:
        account_ids = request.data.get('account_ids', [])
        reconciliation_ref = request.data.get('reconciliation_ref', '')

        if not account_ids or not reconciliation_ref:
            return Response({
                'error': 'account_ids et reconciliation_ref sont requis'
            }, status=status.HTTP_400_BAD_REQUEST)

        accounting_service = AccountingService(request.user.tenant)
        result = accounting_service.reconcile_accounts(
            account_ids,
            reconciliation_ref,
            request.user
        )

        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Erreur lors du lettrage: {str(e)}")
        return Response({
            'error': f'Erreur interne: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
