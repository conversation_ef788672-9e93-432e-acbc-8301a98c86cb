"""
Modèles pour l'Agent Logistics - Gestion du Transport et de la Logistique
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid

from core.models import TimeStampedModel, UUIDModel, Tenant, User


class Carrier(UUIDModel, TimeStampedModel):
    """Modèle pour les transporteurs"""

    CARRIER_TYPES = [
        ('road', _('Transport routier')),
        ('rail', _('Transport ferroviaire')),
        ('air', _('Transport aérien')),
        ('sea', _('Transport maritime')),
        ('multimodal', _('Transport multimodal')),
        ('express', _('Messagerie express')),
        ('postal', _('Services postaux')),
    ]

    SERVICE_LEVELS = [
        ('standard', _('Standard')),
        ('express', _('Express')),
        ('overnight', _('<PERSON><PERSON><PERSON> le lendemain')),
        ('same_day', _('Livraison le jour même')),
        ('economy', _('Économique')),
        ('premium', _('Premium')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='carriers',
        verbose_name=_("Tenant")
    )

    # Informations de base
    carrier_code = models.CharField(_("Code transporteur"), max_length=20)
    name = models.CharField(_("Nom"), max_length=200)
    carrier_type = models.CharField(_("Type de transport"), max_length=20, choices=CARRIER_TYPES)
    service_level = models.CharField(_("Niveau de service"), max_length=20, choices=SERVICE_LEVELS, default='standard')

    # Contact
    contact_person = models.CharField(_("Personne de contact"), max_length=200, blank=True)
    email = models.EmailField(_("Email"), blank=True)
    phone = models.CharField(_("Téléphone"), max_length=50, blank=True)
    website = models.URLField(_("Site web"), blank=True)

    # Adresse
    address_line1 = models.CharField(_("Adresse ligne 1"), max_length=200, blank=True)
    address_line2 = models.CharField(_("Adresse ligne 2"), max_length=200, blank=True)
    city = models.CharField(_("Ville"), max_length=100, blank=True)
    postal_code = models.CharField(_("Code postal"), max_length=20, blank=True)
    country = models.CharField(_("Pays"), max_length=100, blank=True)

    # Capacités
    max_weight = models.DecimalField(
        _("Poids maximum (kg)"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    max_volume = models.DecimalField(
        _("Volume maximum (m³)"),
        max_digits=10,
        decimal_places=3,
        null=True,
        blank=True
    )

    # Zones de service
    service_zones = models.TextField(_("Zones de service"), blank=True, help_text="Zones géographiques couvertes")
    international_service = models.BooleanField(_("Service international"), default=False)

    # Évaluations
    reliability_rating = models.DecimalField(
        _("Note fiabilité"),
        max_digits=3,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.00')), MaxValueValidator(Decimal('5.00'))]
    )
    speed_rating = models.DecimalField(
        _("Note rapidité"),
        max_digits=3,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.00')), MaxValueValidator(Decimal('5.00'))]
    )
    cost_rating = models.DecimalField(
        _("Note coût"),
        max_digits=3,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.00')), MaxValueValidator(Decimal('5.00'))]
    )

    # Responsable
    account_manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_carriers',
        verbose_name=_("Responsable compte")
    )

    # Conditions commerciales
    payment_terms = models.PositiveIntegerField(_("Délai de paiement (jours)"), default=30)
    currency = models.CharField(_("Devise"), max_length=3, default='EUR')

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)
    is_preferred = models.BooleanField(_("Transporteur préféré"), default=False)

    class Meta:
        verbose_name = _("Transporteur")
        verbose_name_plural = _("Transporteurs")
        unique_together = ['tenant', 'carrier_code']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.carrier_code})"

    @property
    def overall_rating(self):
        """Calcule la note globale"""
        ratings = [r for r in [self.reliability_rating, self.speed_rating, self.cost_rating] if r is not None]
        return sum(ratings) / len(ratings) if ratings else None


class CarrierRate(UUIDModel, TimeStampedModel):
    """Modèle pour les tarifs des transporteurs"""

    RATE_TYPES = [
        ('weight', _('Par poids')),
        ('volume', _('Par volume')),
        ('distance', _('Par distance')),
        ('zone', _('Par zone')),
        ('flat', _('Forfaitaire')),
        ('percentage', _('Pourcentage de la valeur')),
    ]

    carrier = models.ForeignKey(
        Carrier,
        on_delete=models.CASCADE,
        related_name='rates',
        verbose_name=_("Transporteur")
    )

    # Définition du tarif
    rate_name = models.CharField(_("Nom du tarif"), max_length=200)
    rate_type = models.CharField(_("Type de tarif"), max_length=20, choices=RATE_TYPES)
    service_level = models.CharField(_("Niveau de service"), max_length=20, choices=Carrier.SERVICE_LEVELS)

    # Zones géographiques
    origin_zone = models.CharField(_("Zone d'origine"), max_length=100, blank=True)
    destination_zone = models.CharField(_("Zone de destination"), max_length=100, blank=True)

    # Critères d'application
    min_weight = models.DecimalField(
        _("Poids minimum (kg)"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    max_weight = models.DecimalField(
        _("Poids maximum (kg)"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    min_volume = models.DecimalField(
        _("Volume minimum (m³)"),
        max_digits=10,
        decimal_places=3,
        null=True,
        blank=True
    )
    max_volume = models.DecimalField(
        _("Volume maximum (m³)"),
        max_digits=10,
        decimal_places=3,
        null=True,
        blank=True
    )
    min_distance = models.DecimalField(
        _("Distance minimum (km)"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    max_distance = models.DecimalField(
        _("Distance maximum (km)"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Tarification
    base_rate = models.DecimalField(
        _("Tarif de base"),
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    rate_per_unit = models.DecimalField(
        _("Tarif par unité"),
        max_digits=10,
        decimal_places=4,
        default=Decimal('0.0000')
    )
    fuel_surcharge_percentage = models.DecimalField(
        _("Surcharge carburant (%)"),
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00')
    )

    # Délais
    transit_time_days = models.PositiveIntegerField(_("Délai de transit (jours)"), default=1)

    # Validité
    valid_from = models.DateField(_("Valide à partir du"))
    valid_to = models.DateField(_("Valide jusqu'au"), null=True, blank=True)

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)

    class Meta:
        verbose_name = _("Tarif transporteur")
        verbose_name_plural = _("Tarifs transporteurs")
        ordering = ['carrier__name', 'rate_name']

    def __str__(self):
        return f"{self.carrier.name} - {self.rate_name}"


class Shipment(UUIDModel, TimeStampedModel):
    """Modèle pour les expéditions"""

    SHIPMENT_TYPES = [
        ('outbound', _('Expédition sortante')),
        ('inbound', _('Réception entrante')),
        ('internal', _('Transfert interne')),
        ('return', _('Retour')),
    ]

    SHIPMENT_STATUS = [
        ('draft', _('Brouillon')),
        ('planned', _('Planifiée')),
        ('confirmed', _('Confirmée')),
        ('picked_up', _('Collectée')),
        ('in_transit', _('En transit')),
        ('out_for_delivery', _('En cours de livraison')),
        ('delivered', _('Livrée')),
        ('failed', _('Échec de livraison')),
        ('returned', _('Retournée')),
        ('cancelled', _('Annulée')),
    ]

    PRIORITY_LEVELS = [
        (1, _('Très faible')),
        (2, _('Faible')),
        (3, _('Normale')),
        (4, _('Élevée')),
        (5, _('Critique')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='shipments',
        verbose_name=_("Tenant")
    )

    # Numérotation
    shipment_number = models.CharField(_("Numéro d'expédition"), max_length=50, unique=True)
    tracking_number = models.CharField(_("Numéro de suivi"), max_length=100, blank=True)

    # Type et statut
    shipment_type = models.CharField(_("Type d'expédition"), max_length=20, choices=SHIPMENT_TYPES)
    status = models.CharField(_("Statut"), max_length=20, choices=SHIPMENT_STATUS, default='draft')
    priority = models.PositiveIntegerField(_("Priorité"), choices=PRIORITY_LEVELS, default=3)

    # Transporteur
    carrier = models.ForeignKey(
        Carrier,
        on_delete=models.PROTECT,
        related_name='shipments',
        verbose_name=_("Transporteur")
    )
    service_level = models.CharField(_("Niveau de service"), max_length=20, choices=Carrier.SERVICE_LEVELS)

    # Adresses
    sender_name = models.CharField(_("Nom expéditeur"), max_length=200)
    sender_address = models.TextField(_("Adresse expéditeur"))
    sender_city = models.CharField(_("Ville expéditeur"), max_length=100)
    sender_postal_code = models.CharField(_("Code postal expéditeur"), max_length=20)
    sender_country = models.CharField(_("Pays expéditeur"), max_length=100)
    sender_contact = models.CharField(_("Contact expéditeur"), max_length=200, blank=True)
    sender_phone = models.CharField(_("Téléphone expéditeur"), max_length=50, blank=True)
    sender_email = models.EmailField(_("Email expéditeur"), blank=True)

    recipient_name = models.CharField(_("Nom destinataire"), max_length=200)
    recipient_address = models.TextField(_("Adresse destinataire"))
    recipient_city = models.CharField(_("Ville destinataire"), max_length=100)
    recipient_postal_code = models.CharField(_("Code postal destinataire"), max_length=20)
    recipient_country = models.CharField(_("Pays destinataire"), max_length=100)
    recipient_contact = models.CharField(_("Contact destinataire"), max_length=200, blank=True)
    recipient_phone = models.CharField(_("Téléphone destinataire"), max_length=50, blank=True)
    recipient_email = models.EmailField(_("Email destinataire"), blank=True)

    # Caractéristiques physiques
    total_weight = models.DecimalField(
        _("Poids total (kg)"),
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_volume = models.DecimalField(
        _("Volume total (m³)"),
        max_digits=10,
        decimal_places=3,
        default=Decimal('0.000')
    )
    package_count = models.PositiveIntegerField(_("Nombre de colis"), default=1)

    # Dates
    planned_pickup_date = models.DateTimeField(_("Date de collecte prévue"), null=True, blank=True)
    actual_pickup_date = models.DateTimeField(_("Date de collecte réelle"), null=True, blank=True)
    planned_delivery_date = models.DateTimeField(_("Date de livraison prévue"), null=True, blank=True)
    actual_delivery_date = models.DateTimeField(_("Date de livraison réelle"), null=True, blank=True)

    # Coûts
    estimated_cost = models.DecimalField(
        _("Coût estimé"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    actual_cost = models.DecimalField(
        _("Coût réel"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    currency = models.CharField(_("Devise"), max_length=3, default='EUR')

    # Instructions spéciales
    special_instructions = models.TextField(_("Instructions spéciales"), blank=True)
    delivery_instructions = models.TextField(_("Instructions de livraison"), blank=True)

    # Références
    customer_reference = models.CharField(_("Référence client"), max_length=100, blank=True)
    order_reference = models.CharField(_("Référence commande"), max_length=100, blank=True)

    # Responsable
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_shipments',
        verbose_name=_("Créé par")
    )

    # Assurance et valeur
    declared_value = models.DecimalField(
        _("Valeur déclarée"),
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True
    )
    insurance_required = models.BooleanField(_("Assurance requise"), default=False)
    insurance_cost = models.DecimalField(
        _("Coût assurance"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Signature et preuve de livraison
    signature_required = models.BooleanField(_("Signature requise"), default=False)
    proof_of_delivery = models.TextField(_("Preuve de livraison"), blank=True)
    delivery_photo_url = models.URLField(_("Photo de livraison"), blank=True)

    class Meta:
        verbose_name = _("Expédition")
        verbose_name_plural = _("Expéditions")
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.shipment_number} - {self.recipient_name}"

    @property
    def is_delayed(self):
        """Vérifie si l'expédition est en retard"""
        if self.planned_delivery_date and self.status not in ['delivered', 'cancelled']:
            from django.utils import timezone
            return timezone.now() > self.planned_delivery_date
        return False

    @property
    def delivery_delay_days(self):
        """Calcule le retard de livraison en jours"""
        if self.actual_delivery_date and self.planned_delivery_date:
            delay = self.actual_delivery_date - self.planned_delivery_date
            return delay.days
        return None


class ShipmentItem(UUIDModel, TimeStampedModel):
    """Modèle pour les articles d'une expédition"""

    shipment = models.ForeignKey(
        Shipment,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_("Expédition")
    )

    # Description de l'article
    item_name = models.CharField(_("Nom de l'article"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    sku = models.CharField(_("SKU"), max_length=100, blank=True)

    # Quantités
    quantity = models.DecimalField(
        _("Quantité"),
        max_digits=10,
        decimal_places=3,
        default=Decimal('1.000')
    )
    unit_of_measure = models.CharField(_("Unité de mesure"), max_length=20, default='pcs')

    # Caractéristiques physiques
    unit_weight = models.DecimalField(
        _("Poids unitaire (kg)"),
        max_digits=8,
        decimal_places=3,
        null=True,
        blank=True
    )
    unit_volume = models.DecimalField(
        _("Volume unitaire (m³)"),
        max_digits=8,
        decimal_places=6,
        null=True,
        blank=True
    )

    # Valeur
    unit_value = models.DecimalField(
        _("Valeur unitaire"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    total_value = models.DecimalField(
        _("Valeur totale"),
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Classification douanière
    hs_code = models.CharField(_("Code HS"), max_length=20, blank=True)
    country_of_origin = models.CharField(_("Pays d'origine"), max_length=100, blank=True)

    # Conditions spéciales
    fragile = models.BooleanField(_("Fragile"), default=False)
    hazardous = models.BooleanField(_("Matière dangereuse"), default=False)
    temperature_controlled = models.BooleanField(_("Température contrôlée"), default=False)

    class Meta:
        verbose_name = _("Article d'expédition")
        verbose_name_plural = _("Articles d'expédition")
        ordering = ['item_name']

    def save(self, *args, **kwargs):
        """Calcule automatiquement la valeur totale"""
        if self.unit_value and self.quantity:
            self.total_value = self.unit_value * self.quantity
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item_name} ({self.quantity} {self.unit_of_measure})"


class ShipmentPackage(UUIDModel, TimeStampedModel):
    """Modèle pour les colis d'une expédition"""

    PACKAGE_TYPES = [
        ('box', _('Carton')),
        ('envelope', _('Enveloppe')),
        ('tube', _('Tube')),
        ('pallet', _('Palette')),
        ('bag', _('Sac')),
        ('crate', _('Caisse')),
        ('drum', _('Fût')),
        ('other', _('Autre')),
    ]

    shipment = models.ForeignKey(
        Shipment,
        on_delete=models.CASCADE,
        related_name='packages',
        verbose_name=_("Expédition")
    )

    # Identification
    package_number = models.CharField(_("Numéro de colis"), max_length=50)
    tracking_number = models.CharField(_("Numéro de suivi"), max_length=100, blank=True)
    package_type = models.CharField(_("Type de colis"), max_length=20, choices=PACKAGE_TYPES, default='box')

    # Dimensions
    length = models.DecimalField(
        _("Longueur (cm)"),
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True
    )
    width = models.DecimalField(
        _("Largeur (cm)"),
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True
    )
    height = models.DecimalField(
        _("Hauteur (cm)"),
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True
    )
    weight = models.DecimalField(
        _("Poids (kg)"),
        max_digits=8,
        decimal_places=3,
        default=Decimal('0.000')
    )

    # Contenu
    description = models.TextField(_("Description du contenu"), blank=True)

    # Étiquetage
    barcode = models.CharField(_("Code-barres"), max_length=100, blank=True)
    label_printed = models.BooleanField(_("Étiquette imprimée"), default=False)

    class Meta:
        verbose_name = _("Colis")
        verbose_name_plural = _("Colis")
        unique_together = ['shipment', 'package_number']
        ordering = ['package_number']

    @property
    def volume(self):
        """Calcule le volume du colis en m³"""
        if self.length and self.width and self.height:
            return (self.length * self.width * self.height) / 1000000  # cm³ to m³
        return None

    def __str__(self):
        return f"{self.shipment.shipment_number} - Colis {self.package_number}"


class ShipmentTracking(UUIDModel, TimeStampedModel):
    """Modèle pour le suivi des expéditions"""

    EVENT_TYPES = [
        ('created', _('Expédition créée')),
        ('confirmed', _('Expédition confirmée')),
        ('picked_up', _('Collecte effectuée')),
        ('in_transit', _('En transit')),
        ('customs_clearance', _('Dédouanement')),
        ('out_for_delivery', _('En cours de livraison')),
        ('delivery_attempt', _('Tentative de livraison')),
        ('delivered', _('Livré')),
        ('exception', _('Exception')),
        ('returned', _('Retourné')),
        ('cancelled', _('Annulé')),
    ]

    shipment = models.ForeignKey(
        Shipment,
        on_delete=models.CASCADE,
        related_name='tracking_events',
        verbose_name=_("Expédition")
    )

    # Événement
    event_type = models.CharField(_("Type d'événement"), max_length=20, choices=EVENT_TYPES)
    event_date = models.DateTimeField(_("Date de l'événement"))
    event_description = models.TextField(_("Description de l'événement"))

    # Localisation
    location = models.CharField(_("Lieu"), max_length=200, blank=True)
    city = models.CharField(_("Ville"), max_length=100, blank=True)
    country = models.CharField(_("Pays"), max_length=100, blank=True)

    # Détails
    carrier_event_code = models.CharField(_("Code événement transporteur"), max_length=50, blank=True)
    next_expected_event = models.CharField(_("Prochaine étape attendue"), max_length=200, blank=True)
    estimated_delivery = models.DateTimeField(_("Livraison estimée"), null=True, blank=True)

    # Source de l'information
    source = models.CharField(
        _("Source"),
        max_length=20,
        choices=[
            ('manual', _('Manuel')),
            ('carrier_api', _('API transporteur')),
            ('webhook', _('Webhook')),
            ('email', _('Email')),
            ('scan', _('Scan')),
        ],
        default='manual'
    )

    # Utilisateur (si manuel)
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='tracking_events',
        verbose_name=_("Utilisateur")
    )

    class Meta:
        verbose_name = _("Événement de suivi")
        verbose_name_plural = _("Événements de suivi")
        ordering = ['-event_date']

    def __str__(self):
        return f"{self.shipment.shipment_number} - {self.event_type} ({self.event_date})"


class Route(UUIDModel, TimeStampedModel):
    """Modèle pour les itinéraires de livraison"""

    ROUTE_STATUS = [
        ('planned', _('Planifié')),
        ('in_progress', _('En cours')),
        ('completed', _('Terminé')),
        ('cancelled', _('Annulé')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='routes',
        verbose_name=_("Tenant")
    )

    # Identification
    route_number = models.CharField(_("Numéro d'itinéraire"), max_length=50, unique=True)
    route_name = models.CharField(_("Nom de l'itinéraire"), max_length=200)

    # Statut et dates
    status = models.CharField(_("Statut"), max_length=20, choices=ROUTE_STATUS, default='planned')
    planned_date = models.DateField(_("Date planifiée"))
    start_time = models.TimeField(_("Heure de début"), null=True, blank=True)
    end_time = models.TimeField(_("Heure de fin"), null=True, blank=True)
    actual_start_time = models.DateTimeField(_("Heure de début réelle"), null=True, blank=True)
    actual_end_time = models.DateTimeField(_("Heure de fin réelle"), null=True, blank=True)

    # Transporteur et véhicule
    carrier = models.ForeignKey(
        Carrier,
        on_delete=models.PROTECT,
        related_name='routes',
        verbose_name=_("Transporteur")
    )
    vehicle_info = models.CharField(_("Informations véhicule"), max_length=200, blank=True)
    driver_name = models.CharField(_("Nom du chauffeur"), max_length=200, blank=True)
    driver_phone = models.CharField(_("Téléphone chauffeur"), max_length=50, blank=True)

    # Capacités
    max_weight = models.DecimalField(
        _("Poids maximum (kg)"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    max_volume = models.DecimalField(
        _("Volume maximum (m³)"),
        max_digits=10,
        decimal_places=3,
        null=True,
        blank=True
    )

    # Statistiques
    total_distance = models.DecimalField(
        _("Distance totale (km)"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    estimated_duration = models.DurationField(_("Durée estimée"), null=True, blank=True)
    actual_duration = models.DurationField(_("Durée réelle"), null=True, blank=True)

    # Coûts
    estimated_cost = models.DecimalField(
        _("Coût estimé"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    actual_cost = models.DecimalField(
        _("Coût réel"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Responsable
    planner = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='planned_routes',
        verbose_name=_("Planificateur")
    )

    # Notes
    notes = models.TextField(_("Notes"), blank=True)

    class Meta:
        verbose_name = _("Itinéraire")
        verbose_name_plural = _("Itinéraires")
        ordering = ['-planned_date', 'route_number']

    def __str__(self):
        return f"{self.route_number} - {self.route_name}"


class RouteStop(UUIDModel, TimeStampedModel):
    """Modèle pour les arrêts d'un itinéraire"""

    STOP_TYPES = [
        ('pickup', _('Collecte')),
        ('delivery', _('Livraison')),
        ('both', _('Collecte et livraison')),
        ('break', _('Pause')),
        ('fuel', _('Carburant')),
        ('customs', _('Douane')),
    ]

    STOP_STATUS = [
        ('planned', _('Planifié')),
        ('arrived', _('Arrivé')),
        ('in_progress', _('En cours')),
        ('completed', _('Terminé')),
        ('failed', _('Échec')),
        ('skipped', _('Ignoré')),
    ]

    route = models.ForeignKey(
        Route,
        on_delete=models.CASCADE,
        related_name='stops',
        verbose_name=_("Itinéraire")
    )

    # Ordre et type
    stop_sequence = models.PositiveIntegerField(_("Séquence d'arrêt"))
    stop_type = models.CharField(_("Type d'arrêt"), max_length=20, choices=STOP_TYPES)
    status = models.CharField(_("Statut"), max_length=20, choices=STOP_STATUS, default='planned')

    # Expéditions associées
    shipments = models.ManyToManyField(
        Shipment,
        related_name='route_stops',
        verbose_name=_("Expéditions"),
        blank=True
    )

    # Adresse
    stop_name = models.CharField(_("Nom de l'arrêt"), max_length=200)
    address = models.TextField(_("Adresse"))
    city = models.CharField(_("Ville"), max_length=100)
    postal_code = models.CharField(_("Code postal"), max_length=20)
    country = models.CharField(_("Pays"), max_length=100)

    # Contact
    contact_name = models.CharField(_("Nom du contact"), max_length=200, blank=True)
    contact_phone = models.CharField(_("Téléphone contact"), max_length=50, blank=True)
    contact_email = models.EmailField(_("Email contact"), blank=True)

    # Horaires
    planned_arrival_time = models.DateTimeField(_("Heure d'arrivée prévue"), null=True, blank=True)
    planned_departure_time = models.DateTimeField(_("Heure de départ prévue"), null=True, blank=True)
    actual_arrival_time = models.DateTimeField(_("Heure d'arrivée réelle"), null=True, blank=True)
    actual_departure_time = models.DateTimeField(_("Heure de départ réelle"), null=True, blank=True)

    # Durée estimée de l'arrêt
    estimated_service_time = models.DurationField(_("Durée de service estimée"), null=True, blank=True)
    actual_service_time = models.DurationField(_("Durée de service réelle"), null=True, blank=True)

    # Instructions
    special_instructions = models.TextField(_("Instructions spéciales"), blank=True)

    # Géolocalisation
    latitude = models.DecimalField(
        _("Latitude"),
        max_digits=10,
        decimal_places=7,
        null=True,
        blank=True
    )
    longitude = models.DecimalField(
        _("Longitude"),
        max_digits=10,
        decimal_places=7,
        null=True,
        blank=True
    )

    # Résultats
    delivery_success = models.BooleanField(_("Livraison réussie"), null=True, blank=True)
    failure_reason = models.TextField(_("Raison de l'échec"), blank=True)
    signature_obtained = models.BooleanField(_("Signature obtenue"), default=False)
    proof_of_delivery = models.TextField(_("Preuve de livraison"), blank=True)

    class Meta:
        verbose_name = _("Arrêt d'itinéraire")
        verbose_name_plural = _("Arrêts d'itinéraire")
        unique_together = ['route', 'stop_sequence']
        ordering = ['route', 'stop_sequence']

    def __str__(self):
        return f"{self.route.route_number} - Arrêt {self.stop_sequence}: {self.stop_name}"


class DeliveryAttempt(UUIDModel, TimeStampedModel):
    """Modèle pour les tentatives de livraison"""

    ATTEMPT_RESULTS = [
        ('successful', _('Réussie')),
        ('failed_no_one_home', _('Personne au domicile')),
        ('failed_refused', _('Refusé')),
        ('failed_damaged', _('Colis endommagé')),
        ('failed_address_issue', _('Problème d\'adresse')),
        ('failed_access_issue', _('Problème d\'accès')),
        ('failed_other', _('Autre raison')),
        ('rescheduled', _('Reprogrammée')),
    ]

    shipment = models.ForeignKey(
        Shipment,
        on_delete=models.CASCADE,
        related_name='delivery_attempts',
        verbose_name=_("Expédition")
    )

    route_stop = models.ForeignKey(
        RouteStop,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='delivery_attempts',
        verbose_name=_("Arrêt d'itinéraire")
    )

    # Détails de la tentative
    attempt_number = models.PositiveIntegerField(_("Numéro de tentative"))
    attempt_date = models.DateTimeField(_("Date de tentative"))
    result = models.CharField(_("Résultat"), max_length=30, choices=ATTEMPT_RESULTS)

    # Détails du résultat
    delivered_to = models.CharField(_("Livré à"), max_length=200, blank=True)
    signature_name = models.CharField(_("Nom du signataire"), max_length=200, blank=True)
    failure_reason = models.TextField(_("Raison de l'échec"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)

    # Prochaine tentative
    next_attempt_date = models.DateTimeField(_("Prochaine tentative"), null=True, blank=True)

    # Preuve
    photo_url = models.URLField(_("Photo de livraison"), blank=True)
    signature_url = models.URLField(_("Signature"), blank=True)

    # Responsable
    driver_name = models.CharField(_("Nom du chauffeur"), max_length=200, blank=True)

    class Meta:
        verbose_name = _("Tentative de livraison")
        verbose_name_plural = _("Tentatives de livraison")
        unique_together = ['shipment', 'attempt_number']
        ordering = ['-attempt_date']

    def __str__(self):
        return f"{self.shipment.shipment_number} - Tentative {self.attempt_number}"
