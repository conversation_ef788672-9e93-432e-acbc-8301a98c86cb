import express from 'express';
import path from 'path';
import cors from 'cors';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;
const DJANGO_API_URL = process.env.DJANGO_API_URL || 'http://localhost:8000';

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'dist')));

// Proxy vers Django API
app.use('/api', createProxyMiddleware({
  target: DJANGO_API_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api': '/api'
  },
  onError: (err, req, res) => {
    console.error('Proxy Error:', err);
    res.status(500).json({
      error: 'Erreur de connexion au backend Django',
      message: 'Vérifiez que le serveur Django fonctionne sur ' + DJANGO_API_URL
    });
  }
}));

// Route de santé
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'ERP HUB Frontend Server',
    timestamp: new Date().toISOString(),
    django_backend: DJANGO_API_URL
  });
});

// Servir l'application React pour toutes les autres routes
app.get('*', (req, res) => {
  // Si dist n'existe pas, servir la page de test
  const distPath = path.join(__dirname, 'dist', 'index.html');
  const testPath = path.join(__dirname, 'test-simple.html');

  // Vérifier si le build existe
  try {
    res.sendFile(distPath);
  } catch (error) {
    console.log('Build non trouvé, utilisation de la page de test');
    res.sendFile(testPath);
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('Erreur serveur:', err);
  res.status(500).json({
    error: 'Erreur interne du serveur',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Une erreur est survenue'
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 ERP HUB Frontend Server démarré sur http://localhost:${PORT}`);
  console.log(`📡 Proxy Django API: ${DJANGO_API_URL}`);
  console.log(`🌍 Environnement: ${process.env.NODE_ENV || 'development'}`);
});
