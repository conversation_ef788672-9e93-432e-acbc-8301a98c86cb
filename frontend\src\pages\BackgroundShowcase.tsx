import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useBackground, getDefaultBackgrounds, BackgroundConfig } from '../hooks/useBackground';
import { BackgroundSelector } from '../components/ui/BackgroundSelector';
import { SimpleMetricCard } from '../components/ui/SimpleMetricCard';
import { SimpleChart } from '../components/ui/SimpleChart';

export const BackgroundShowcase: React.FC = () => {
  const { currentBackground, setBackground, isAnimated, toggleAnimation } = useBackground();
  const [previewBackground, setPreviewBackground] = useState<BackgroundConfig | null>(null);
  
  const demoData = [
    { name: 'Jan', value: 4000, value2: 2400 },
    { name: 'Fév', value: 3000, value2: 1398 },
    { name: 'Mar', value: 2000, value2: 9800 },
    { name: 'Avr', value: 2780, value2: 3908 },
    { name: '<PERSON>', value: 1890, value2: 4800 },
    { name: '<PERSON>', value: 2390, value2: 3800 }
  ];

  const backgroundCategories = [
    {
      title: '🎯 Couleurs Solides',
      description: 'Arrière-plans unis pour une interface épurée',
      backgrounds: getDefaultBackgrounds().filter(bg => bg.type === 'solid')
    },
    {
      title: '🌈 Dégradés',
      description: 'Transitions colorées élégantes et modernes',
      backgrounds: getDefaultBackgrounds().filter(bg => bg.type === 'gradient')
    },
    {
      title: '🔷 Motifs',
      description: 'Patterns subtils pour ajouter de la texture',
      backgrounds: getDefaultBackgrounds().filter(bg => bg.type === 'pattern')
    },
    {
      title: '✨ Animations',
      description: 'Arrière-plans dynamiques et interactifs',
      backgrounds: getDefaultBackgrounds().filter(bg => bg.type === 'animated')
    }
  ];

  const applyPreview = (background: BackgroundConfig) => {
    setPreviewBackground(background);
    setBackground(background);
  };

  const resetToDefault = () => {
    const defaultBg = getDefaultBackgrounds()[0];
    setBackground(defaultBg);
    setPreviewBackground(null);
  };

  return (
    <div className="min-h-screen transition-all duration-500">
      {/* Header fixe */}
      <div className="fixed top-0 left-0 right-0 z-40 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <span className="text-2xl">🎨</span>
              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  Galerie d'Arrière-plans
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Personnalisez votre interface ERP HUB
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <BackgroundSelector />
              <button
                onClick={resetToDefault}
                className="px-3 py-2 text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                🔄 Reset
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="pt-20 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Aperçu actuel */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Aperçu Actuel: {currentBackground.name}
              </h2>
              
              {/* Widgets de démonstration */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <SimpleMetricCard
                  title="Chiffre d'Affaires"
                  value={2847500}
                  icon="💰"
                  color="green"
                  trend="up"
                  trendValue={15.2}
                />
                <SimpleMetricCard
                  title="Utilisateurs Actifs"
                  value={342}
                  icon="👥"
                  color="blue"
                  trend="up"
                  trendValue={8.7}
                />
                <SimpleMetricCard
                  title="Performance"
                  value={98.5}
                  unit="%"
                  icon="📈"
                  color="purple"
                  trend="up"
                  trendValue={2.1}
                />
              </div>
              
              <div className="mt-6">
                <SimpleChart
                  title="Démonstration avec l'arrière-plan actuel"
                  data={demoData}
                  type="line"
                  height={200}
                  color="#1976d2"
                />
              </div>
            </div>
          </motion.div>

          {/* Contrôle des animations */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mb-8"
          >
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Contrôle des Animations
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Activez ou désactivez les arrière-plans animés pour optimiser les performances
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    {isAnimated ? 'Activées' : 'Désactivées'}
                  </span>
                  <button
                    onClick={toggleAnimation}
                    className={`
                      relative w-14 h-7 rounded-full transition-colors
                      ${isAnimated ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'}
                    `}
                  >
                    <motion.div
                      className="absolute top-1 w-5 h-5 bg-white rounded-full shadow-sm"
                      animate={{ x: isAnimated ? 30 : 2 }}
                      transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                    />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Catégories d'arrière-plans */}
          {backgroundCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 + categoryIndex * 0.1 }}
              className="mb-8"
            >
              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                <div className="mb-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    {category.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {category.description}
                  </p>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {category.backgrounds.map((background, index) => (
                    <motion.div
                      key={background.id}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.05 }}
                      className={`
                        relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all group
                        ${currentBackground.id === background.id
                          ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800'
                          : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'
                        }
                      `}
                      onClick={() => applyPreview(background)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {/* Aperçu miniature */}
                      <div className="w-full h-24 relative overflow-hidden">
                        {background.type === 'solid' && (
                          <div 
                            className="w-full h-full"
                            style={{ backgroundColor: background.config.color }}
                          />
                        )}
                        
                        {background.type === 'gradient' && background.config.gradient && (
                          <div 
                            className="w-full h-full"
                            style={{
                              background: background.config.gradient.type === 'linear'
                                ? `linear-gradient(${background.config.gradient.direction || '135deg'}, ${background.config.gradient.colors.join(', ')})`
                                : `radial-gradient(circle, ${background.config.gradient.colors.join(', ')})`
                            }}
                          />
                        )}
                        
                        {background.type === 'pattern' && (
                          <div 
                            className="w-full h-full"
                            style={{
                              backgroundColor: background.config.pattern?.backgroundColor || '#f8f9fa',
                              backgroundImage: background.preview,
                              backgroundSize: '20px 20px'
                            }}
                          />
                        )}
                        
                        {background.type === 'animated' && (
                          <div 
                            className="w-full h-full flex items-center justify-center"
                            style={{ backgroundColor: background.preview }}
                          >
                            <motion.div
                              animate={{ 
                                rotate: 360,
                                scale: [1, 1.2, 1]
                              }}
                              transition={{ 
                                duration: 2, 
                                repeat: Infinity, 
                                ease: "linear" 
                              }}
                              className="text-white text-xl opacity-70"
                            >
                              ✨
                            </motion.div>
                          </div>
                        )}

                        {/* Overlay de sélection */}
                        {currentBackground.id === background.id && (
                          <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-sm">✓</span>
                            </div>
                          </div>
                        )}

                        {/* Overlay au survol */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all flex items-center justify-center">
                          <span className="text-white opacity-0 group-hover:opacity-100 transition-opacity text-sm font-medium">
                            Appliquer
                          </span>
                        </div>
                      </div>

                      {/* Informations */}
                      <div className="p-3 bg-white dark:bg-gray-800">
                        <h4 className="font-medium text-gray-900 dark:text-white text-sm truncate">
                          {background.name}
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                          {background.type}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}

          {/* Conseils d'utilisation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              💡 Conseils d'Utilisation
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700 dark:text-gray-300">
              <div>
                <h4 className="font-medium mb-2">🎯 Couleurs Solides</h4>
                <p>Idéales pour un environnement de travail sobre et professionnel. Réduisent la fatigue oculaire.</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">🌈 Dégradés</h4>
                <p>Apportent une touche moderne et élégante. Parfaits pour les présentations et démonstrations.</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">🔷 Motifs</h4>
                <p>Ajoutent de la texture sans distraire. Utilisez-les avec parcimonie pour éviter la surcharge visuelle.</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">✨ Animations</h4>
                <p>Créent une expérience immersive. Désactivez-les sur les appareils moins performants.</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};
