"""
URLs pour l'application core
"""
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'core'

urlpatterns = [
    # Authentification
    path('login/', views.CustomTokenObtainPairView.as_view(), name='login'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # Profil utilisateur
    path('profile/', views.UserProfileView.as_view(), name='user_profile'),
    path('change-password/', views.ChangePasswordView.as_view(), name='change_password'),
    
    # Gestion des utilisateurs
    path('users/', views.UserListCreateView.as_view(), name='user_list_create'),
    path('users/<uuid:pk>/', views.UserDetailView.as_view(), name='user_detail'),
    
    # Gestion des rôles
    path('roles/', views.RoleListCreateView.as_view(), name='role_list_create'),
    path('users/<uuid:user_id>/roles/', views.UserRoleManagementView.as_view(), name='user_role_assign'),
    path('users/<uuid:user_id>/roles/<uuid:role_id>/', views.UserRoleManagementView.as_view(), name='user_role_revoke'),
    
    # Tenant
    path('tenant/', views.TenantDetailView.as_view(), name='tenant_detail'),
    
    # Health check
    path('health/', views.health_check, name='health_check'),
]
