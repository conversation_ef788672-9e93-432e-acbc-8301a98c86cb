"""
Configuration des URLs pour ERP HUB
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)

urlpatterns = [
    # Administration Django
    path('admin/', admin.site.urls),
    
    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    
    # API Core (authentification, utilisateurs)
    path('api/auth/', include('core.urls')),
    
    # API Agents
    path('api/agents/manager/', include('agents.manager.urls')),
    path('api/agents/hr/', include('agents.hr.urls')),
    path('api/agents/sales/', include('agents.sales.urls')),
    path('api/agents/purchase/', include('agents.purchase.urls')),
    path('api/agents/logistics/', include('agents.logistics.urls')),
    path('api/agents/stock/', include('agents.stock.urls')),
    path('api/agents/accounting/', include('agents.accounting.urls')),
    path('api/agents/finance/', include('agents.finance.urls')),
    path('api/agents/crm/', include('agents.crm.urls')),
    path('api/agents/bi/', include('agents.bi.urls')),
]

# Configuration pour servir les fichiers statiques et media en développement
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Configuration du titre de l'admin
admin.site.site_header = "ERP HUB Administration"
admin.site.site_title = "ERP HUB Admin"
admin.site.index_title = "Bienvenue dans l'administration ERP HUB"
