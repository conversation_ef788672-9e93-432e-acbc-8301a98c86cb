@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Styles de base pour ERP HUB */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }

  /* Scrollbar personnalisée */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer components {
  /* Boutons */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }

  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }

  .btn-outline {
    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }

  /* Cartes */
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Formulaires */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-sm text-danger-600 mt-1;
  }

  /* Navigation */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }

  .nav-link-active {
    @apply nav-link bg-primary-100 text-primary-700;
  }

  .nav-link-inactive {
    @apply nav-link text-gray-600 hover:bg-gray-50 hover:text-gray-900;
  }

  /* Badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }

  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }

  /* Agents colors */
  .agent-manager {
    @apply text-agent-manager;
  }

  .agent-hr {
    @apply text-agent-hr;
  }

  .agent-sales {
    @apply text-agent-sales;
  }

  .agent-purchase {
    @apply text-agent-purchase;
  }

  .agent-logistics {
    @apply text-agent-logistics;
  }

  .agent-stock {
    @apply text-agent-stock;
  }

  .agent-accounting {
    @apply text-agent-accounting;
  }

  .agent-finance {
    @apply text-agent-finance;
  }

  .agent-crm {
    @apply text-agent-crm;
  }

  .agent-bi {
    @apply text-agent-bi;
  }
}

@layer utilities {
  /* Animations personnalisées */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  /* Utilitaires de layout */
  .container-fluid {
    @apply w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-sm {
    @apply w-full max-w-screen-sm mx-auto px-4;
  }

  .container-md {
    @apply w-full max-w-screen-md mx-auto px-4;
  }

  .container-lg {
    @apply w-full max-w-screen-lg mx-auto px-4;
  }

  .container-xl {
    @apply w-full max-w-screen-xl mx-auto px-4;
  }
}
