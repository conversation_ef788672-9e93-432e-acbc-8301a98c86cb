<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Manager - Pilotage Exécutif | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #374151 30%, #1f2937 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #374151;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1f2937;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-info {
            background: #3b82f6;
            color: white;
        }
        
        .btn-info:hover {
            background: #2563eb;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn-warning:hover {
            background: #d97706;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
            position: relative;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .stat-trend {
            font-size: 0.75rem;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }
        
        .trend-up {
            color: #10b981;
        }
        
        .trend-down {
            color: #ef4444;
        }
        
        .trend-neutral {
            color: #6b7280;
        }
        
        .status-indicator {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
        }
        
        .status-excellent {
            background: #10b981;
        }
        
        .status-good {
            background: #f59e0b;
        }
        
        .status-warning {
            background: #ef4444;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .dashboard-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .tab-button {
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            color: #374151;
            border-bottom-color: #374151;
        }
        
        .tab-button:hover {
            color: #1f2937;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .department-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .department-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            position: relative;
        }
        
        .department-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .department-name {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .department-status {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            margin-left: auto;
        }
        
        .department-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .metric {
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
        }
        
        .metric-label {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }
        
        .objectives-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .objective-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
        }
        
        .objective-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .progress-bar {
            width: 100%;
            height: 0.5rem;
            background: #f3f4f6;
            border-radius: 0.25rem;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            transition: width 0.3s;
        }
        
        .progress-text {
            font-size: 0.875rem;
            color: #6b7280;
            display: flex;
            justify-content: space-between;
        }
        
        .alert-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-critical {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .alert-warning {
            background: #fffbeb;
            border: 1px solid #fed7aa;
        }
        
        .alert-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
        }
        
        .alert-icon {
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .alert-icon.critical {
            background: #ef4444;
            color: white;
        }
        
        .alert-icon.warning {
            background: #f59e0b;
            color: white;
        }
        
        .alert-icon.info {
            background: #3b82f6;
            color: white;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .dashboard-tabs {
                flex-wrap: wrap;
            }
            
            .tab-button {
                padding: 0.75rem 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">👨‍💼 Agent Manager - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="generateExecutiveReport()">
                <span class="material-icons" style="font-size: 1rem;">assessment</span>
                Rapport Exécutif
            </button>
            <button class="btn btn-info" onclick="openModal('objectivesModal')">
                <span class="material-icons" style="font-size: 1rem;">flag</span>
                Configurer Objectifs
            </button>
            <button class="btn btn-warning" onclick="openModal('alertsModal')">
                <span class="material-icons" style="font-size: 1rem;">notifications</span>
                Paramètres Alertes
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Pilotage Exécutif</h1>
            <p class="page-subtitle">Vue d'ensemble stratégique et contrôle de gestion</p>
        </div>

        <!-- Statistiques exécutives -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="status-indicator status-excellent"></div>
                <div class="stat-value" id="globalPerformance">0/100</div>
                <div class="stat-label">Performance Globale</div>
                <div class="stat-trend trend-up" id="performanceTrend">
                    <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                    Excellent
                </div>
            </div>
            <div class="stat-card">
                <div class="status-indicator status-excellent"></div>
                <div class="stat-value" id="globalGrowth">0%</div>
                <div class="stat-label">Croissance</div>
                <div class="stat-trend trend-up" id="growthTrend">
                    <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                    +0% vs objectif
                </div>
            </div>
            <div class="stat-card">
                <div class="status-indicator status-good"></div>
                <div class="stat-value" id="globalProfitability">0%</div>
                <div class="stat-label">Rentabilité</div>
                <div class="stat-trend trend-up" id="profitabilityTrend">
                    <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                    +0% vs objectif
                </div>
            </div>
            <div class="stat-card">
                <div class="status-indicator status-good"></div>
                <div class="stat-value" id="riskLevel">Faible</div>
                <div class="stat-label">Niveau de Risque</div>
                <div class="stat-trend trend-neutral" id="riskTrend">
                    <span class="material-icons" style="font-size: 1rem;">shield</span>
                    Contrôlé
                </div>
            </div>
        </div>

        <!-- Onglets de pilotage -->
        <div class="dashboard-tabs">
            <button class="tab-button active" onclick="switchTab('overview')">
                <span class="material-icons" style="font-size: 1rem; margin-right: 0.5rem;">dashboard</span>
                Vue d'Ensemble
            </button>
            <button class="tab-button" onclick="switchTab('departments')">
                <span class="material-icons" style="font-size: 1rem; margin-right: 0.5rem;">business</span>
                Départements
            </button>
            <button class="tab-button" onclick="switchTab('objectives')">
                <span class="material-icons" style="font-size: 1rem; margin-right: 0.5rem;">flag</span>
                Objectifs
            </button>
            <button class="tab-button" onclick="switchTab('alerts')">
                <span class="material-icons" style="font-size: 1rem; margin-right: 0.5rem;">notifications</span>
                Alertes
            </button>
            <button class="tab-button" onclick="switchTab('reports')">
                <span class="material-icons" style="font-size: 1rem; margin-right: 0.5rem;">assessment</span>
                Rapports
            </button>
        </div>

        <!-- Contenu des onglets -->
        <div id="overviewTab" class="tab-content active">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">Indicateurs Clés de Performance</h2>
                    <button class="btn btn-primary" onclick="refreshOverview()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Actualiser
                    </button>
                </div>
                <div class="section-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="totalRevenue">0€</div>
                            <div class="stat-label">Chiffre d'Affaires</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="totalTreasury">0€</div>
                            <div class="stat-label">Trésorerie</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="totalEmployees">0</div>
                            <div class="stat-label">Effectif Total</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="clientSatisfaction">0★</div>
                            <div class="stat-label">Satisfaction Client</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="stockRotation">0x</div>
                            <div class="stat-label">Rotation Stock</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="deliveryTime">0j</div>
                            <div class="stat-label">Délai Moyen</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="departmentsTab" class="tab-content">
            <div class="department-grid" id="departmentGrid">
                <!-- Les cartes départements seront générées dynamiquement -->
            </div>
        </div>

        <div id="objectivesTab" class="tab-content">
            <div class="objectives-grid" id="objectivesGrid">
                <!-- Les objectifs seront générés dynamiquement -->
            </div>
        </div>

        <div id="alertsTab" class="tab-content">
            <div id="alertsContainer">
                <!-- Les alertes seront générées dynamiquement -->
            </div>
        </div>

        <!-- Nouveau: Rapports de Direction -->
        <div id="reportsTab" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">Rapports de Direction</h2>
                    <button class="btn btn-primary" onclick="generateExecutiveReport()">
                        <span class="material-icons" style="font-size: 1rem;">assessment</span>
                        Générer Rapport
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Nom du Rapport</th>
                                    <th>Type</th>
                                    <th>Date de Génération</th>
                                    <th>Période Analysée</th>
                                    <th>Dernière Révision</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="reportsTableBody">
                                <!-- Les données seront chargées dynamiquement -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Configuration Objectifs -->
    <div id="objectivesModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Configuration des Objectifs Stratégiques</h3>
                <button class="close-btn" onclick="closeModal('objectivesModal')">&times;</button>
            </div>
            <div id="objectivesModalAlertContainer"></div>
            <div style="margin-bottom: 1.5rem;">
                <h4 style="margin-bottom: 1rem; color: #374151;">Objectifs Annuels 2024</h4>
                <div style="display: grid; gap: 1rem;">
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Chiffre d'Affaires Cible (€)</label>
                        <input type="number" id="revenueTarget" value="1000000" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Croissance Cible (%)</label>
                        <input type="number" id="growthTarget" value="20" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Marge Cible (%)</label>
                        <input type="number" id="marginTarget" value="45" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Effectif Cible</label>
                        <input type="number" id="staffTarget" value="15" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Satisfaction Client Cible (%)</label>
                        <input type="number" id="satisfactionTarget" value="90" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                </div>
            </div>
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button class="btn btn-secondary" onclick="closeModal('objectivesModal')">
                    Annuler
                </button>
                <button class="btn btn-success" onclick="saveObjectives()">
                    <span class="material-icons" style="font-size: 1rem;">save</span>
                    Enregistrer
                </button>
            </div>
        </div>
    </div>

    <!-- Modal Configuration Alertes -->
    <div id="alertsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Configuration des Alertes</h3>
                <button class="close-btn" onclick="closeModal('alertsModal')">&times;</button>
            </div>
            <div id="alertsModalAlertContainer"></div>
            <div style="margin-bottom: 1.5rem;">
                <h4 style="margin-bottom: 1rem; color: #374151;">Seuils d'Alerte</h4>
                <div style="display: grid; gap: 1rem;">
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Trésorerie Critique (€)</label>
                        <input type="number" id="treasuryAlert" value="50000" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Marge Minimale (%)</label>
                        <input type="number" id="marginAlert" value="30" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Délai Livraison Max (jours)</label>
                        <input type="number" id="deliveryAlert" value="10" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Satisfaction Min (%)</label>
                        <input type="number" id="satisfactionAlert" value="80" style="width: 100%; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 0.5rem;">
                    </div>
                </div>
            </div>
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button class="btn btn-secondary" onclick="closeModal('alertsModal')">
                    Annuler
                </button>
                <button class="btn btn-success" onclick="saveAlerts()">
                    <span class="material-icons" style="font-size: 1rem;">save</span>
                    Enregistrer
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentTab = 'overview';
        let isLoading = false;
        let executiveReports = [];

        // Fonction utilitaire pour formater les dates
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        }

        // Données consolidées depuis tous les agents ERP
        const consolidatedData = {
            // Données agrégées depuis les 9 agents
            global: {
                totalRevenue: 63600, // Sales
                totalTreasury: 240000, // Finance
                totalEmployees: 10, // HR
                clientSatisfaction: 4.0, // CRM
                stockRotation: 4.2, // Stock
                deliveryTime: 5.8, // Logistics
                grossMargin: 42.5, // Finance
                monthlyGrowth: 15.2, // Sales
                performanceScore: 84 // BI
            },

            // Données par département
            departments: {
                hr: {
                    name: 'Ressources Humaines',
                    icon: '👥',
                    status: 'excellent',
                    employees: 10,
                    satisfaction: 87,
                    productivity: 92,
                    budget: 80000,
                    spent: 25000
                },
                sales: {
                    name: 'Commercial',
                    icon: '📈',
                    status: 'excellent',
                    revenue: 63600,
                    conversion: 30,
                    prospects: 10,
                    growth: 15.2
                },
                purchase: {
                    name: 'Achats',
                    icon: '🛒',
                    status: 'good',
                    orders: 10,
                    totalValue: 89500,
                    suppliers: 8,
                    avgDelay: 12
                },
                logistics: {
                    name: 'Logistique',
                    icon: '🚚',
                    status: 'warning',
                    deliveries: 10,
                    onTime: 80,
                    avgDelay: 5.8,
                    efficiency: 89
                },
                stock: {
                    name: 'Stock',
                    icon: '📦',
                    status: 'excellent',
                    products: 10,
                    value: 89500,
                    rotation: 4.2,
                    availability: 95
                },
                accounting: {
                    name: 'Comptabilité',
                    icon: '🏦',
                    status: 'excellent',
                    entries: 10,
                    balance: 0,
                    compliance: 100,
                    automation: 85
                },
                finance: {
                    name: 'Finance',
                    icon: '💰',
                    status: 'excellent',
                    treasury: 240000,
                    roi: 5.6,
                    margin: 42.5,
                    budget: 185000
                },
                crm: {
                    name: 'CRM',
                    icon: '🤝',
                    status: 'good',
                    clients: 8,
                    active: 75,
                    satisfaction: 4.0,
                    interactions: 12
                },
                bi: {
                    name: 'Business Intelligence',
                    icon: '📊',
                    status: 'excellent',
                    reports: 15,
                    accuracy: 98,
                    automation: 90,
                    insights: 25
                }
            },

            // Objectifs stratégiques
            objectives: {
                revenue: { target: 1000000, current: 63600, progress: 6.4 },
                growth: { target: 20, current: 15.2, progress: 76 },
                margin: { target: 45, current: 42.5, progress: 94 },
                staff: { target: 15, current: 10, progress: 67 },
                satisfaction: { target: 90, current: 80, progress: 89 }
            },

            // Alertes actives
            alerts: [
                {
                    id: 'ALERT-001',
                    type: 'warning',
                    title: 'Délais de livraison',
                    message: 'Délai moyen de 5.8 jours dépasse la cible de 5 jours',
                    department: 'logistics',
                    priority: 'medium',
                    createdDate: '2024-01-30T08:15:00'
                },
                {
                    id: 'ALERT-002',
                    type: 'info',
                    title: 'Objectif effectif',
                    message: 'Recruter 5 employés supplémentaires pour atteindre l\'objectif',
                    department: 'hr',
                    priority: 'low',
                    createdDate: '2024-01-29T14:30:00'
                },
                {
                    id: 'ALERT-003',
                    type: 'info',
                    title: 'Performance commerciale',
                    message: 'Excellente progression du CA, maintenir la dynamique',
                    department: 'sales',
                    priority: 'low',
                    createdDate: '2024-01-30T10:45:00'
                }
            ]
        };

        // Données de démonstration - Rapports de Direction
        const demoExecutiveReports = [
            {
                id: 'RPT-EXEC-001',
                name: 'Rapport Mensuel de Direction',
                type: 'Synthèse Exécutive',
                generatedDate: '2024-01-30T09:00:00',
                analysisPeriod: 'Janvier 2024',
                lastRevisionDate: '2024-01-30T09:00:00',
                status: 'completed',
                description: 'Vue d\'ensemble complète de tous les départements'
            },
            {
                id: 'RPT-EXEC-002',
                name: 'Analyse de Performance Trimestrielle',
                type: 'Performance',
                generatedDate: '2024-01-28T16:30:00',
                analysisPeriod: 'Q1 2024',
                lastRevisionDate: '2024-01-29T11:15:00',
                status: 'completed',
                description: 'Évaluation des objectifs et KPIs stratégiques'
            },
            {
                id: 'RPT-EXEC-003',
                name: 'Rapport de Risques Opérationnels',
                type: 'Risques',
                generatedDate: '2024-01-27T14:00:00',
                analysisPeriod: 'Janvier 2024',
                lastRevisionDate: '2024-01-28T09:30:00',
                status: 'completed',
                description: 'Identification et mitigation des risques'
            },
            {
                id: 'RPT-EXEC-004',
                name: 'Tableau de Bord Financier',
                type: 'Finance',
                generatedDate: '2024-01-26T11:45:00',
                analysisPeriod: 'Janvier 2024',
                lastRevisionDate: '2024-01-29T16:20:00',
                status: 'completed',
                description: 'Situation financière et trésorerie'
            },
            {
                id: 'RPT-EXEC-005',
                name: 'Rapport Stratégique Annuel',
                type: 'Stratégie',
                generatedDate: '2024-01-25T10:00:00',
                analysisPeriod: '2024',
                lastRevisionDate: '2024-01-30T08:45:00',
                status: 'in_progress',
                description: 'Plan stratégique et objectifs annuels'
            }
        ];

        // Fonctions utilitaires
        function showAlert(message, type = 'error', container = 'alertsModalAlertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function switchTab(tabName) {
            // Désactiver tous les onglets
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Activer l'onglet sélectionné
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');

            currentTab = tabName;
            updateTabContent();
        }

        function updateTabContent() {
            switch(currentTab) {
                case 'overview':
                    updateOverviewTab();
                    break;
                case 'departments':
                    updateDepartmentsTab();
                    break;
                case 'objectives':
                    updateObjectivesTab();
                    break;
                case 'alerts':
                    updateAlertsTab();
                    break;
                case 'reports':
                    updateReportsTab();
                    break;
            }
        }

        function updateGlobalStats() {
            const data = consolidatedData.global;

            // Performance globale (score BI)
            document.getElementById('globalPerformance').textContent = data.performanceScore + '/100';

            // Croissance
            document.getElementById('globalGrowth').textContent = data.monthlyGrowth.toFixed(1) + '%';
            document.getElementById('growthTrend').innerHTML = `
                <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                +${(data.monthlyGrowth - 10).toFixed(1)}% vs objectif
            `;

            // Rentabilité
            document.getElementById('globalProfitability').textContent = data.grossMargin.toFixed(1) + '%';
            document.getElementById('profitabilityTrend').innerHTML = `
                <span class="material-icons" style="font-size: 1rem;">trending_up</span>
                +${(data.grossMargin - 40).toFixed(1)}% vs objectif
            `;

            // Niveau de risque (calculé)
            const riskLevel = calculateRiskLevel();
            document.getElementById('riskLevel').textContent = riskLevel.label;
            document.getElementById('riskTrend').innerHTML = `
                <span class="material-icons" style="font-size: 1rem;">shield</span>
                ${riskLevel.status}
            `;
        }

        function calculateRiskLevel() {
            const data = consolidatedData.global;
            let riskScore = 0;

            // Facteurs de risque
            if (data.totalTreasury < 100000) riskScore += 30;
            if (data.grossMargin < 35) riskScore += 25;
            if (data.deliveryTime > 7) riskScore += 20;
            if (data.clientSatisfaction < 3.5) riskScore += 25;

            if (riskScore <= 20) return { label: 'Faible', status: 'Contrôlé' };
            if (riskScore <= 50) return { label: 'Modéré', status: 'Surveillé' };
            return { label: 'Élevé', status: 'Action requise' };
        }

        function updateOverviewTab() {
            const data = consolidatedData.global;

            document.getElementById('totalRevenue').textContent = data.totalRevenue.toLocaleString() + '€';
            document.getElementById('totalTreasury').textContent = data.totalTreasury.toLocaleString() + '€';
            document.getElementById('totalEmployees').textContent = data.totalEmployees;
            document.getElementById('clientSatisfaction').textContent = data.clientSatisfaction.toFixed(1) + '★';
            document.getElementById('stockRotation').textContent = data.stockRotation.toFixed(1) + 'x';
            document.getElementById('deliveryTime').textContent = data.deliveryTime.toFixed(1) + 'j';
        }

        function updateDepartmentsTab() {
            const container = document.getElementById('departmentGrid');
            const departments = consolidatedData.departments;

            container.innerHTML = Object.keys(departments).map(key => {
                const dept = departments[key];
                const statusClass = `status-${dept.status}`;

                return `
                    <div class="department-card">
                        <div class="department-header">
                            <div class="department-name">${dept.icon} ${dept.name}</div>
                            <div class="department-status ${statusClass}"></div>
                        </div>
                        <div class="department-metrics">
                            ${getDepartmentMetrics(key, dept)}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getDepartmentMetrics(key, dept) {
            switch(key) {
                case 'hr':
                    return `
                        <div class="metric">
                            <div class="metric-value">${dept.employees}</div>
                            <div class="metric-label">Employés</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.satisfaction}%</div>
                            <div class="metric-label">Satisfaction</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.productivity}%</div>
                            <div class="metric-label">Productivité</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${((dept.budget - dept.spent) / 1000).toFixed(0)}k€</div>
                            <div class="metric-label">Budget Restant</div>
                        </div>
                    `;
                case 'sales':
                    return `
                        <div class="metric">
                            <div class="metric-value">${(dept.revenue / 1000).toFixed(0)}k€</div>
                            <div class="metric-label">CA</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.conversion}%</div>
                            <div class="metric-label">Conversion</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.prospects}</div>
                            <div class="metric-label">Prospects</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">+${dept.growth.toFixed(1)}%</div>
                            <div class="metric-label">Croissance</div>
                        </div>
                    `;
                case 'finance':
                    return `
                        <div class="metric">
                            <div class="metric-value">${(dept.treasury / 1000).toFixed(0)}k€</div>
                            <div class="metric-label">Trésorerie</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.roi.toFixed(1)}%</div>
                            <div class="metric-label">ROI</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.margin.toFixed(1)}%</div>
                            <div class="metric-label">Marge</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${(dept.budget / 1000).toFixed(0)}k€</div>
                            <div class="metric-label">Budget</div>
                        </div>
                    `;
                case 'logistics':
                    return `
                        <div class="metric">
                            <div class="metric-value">${dept.deliveries}</div>
                            <div class="metric-label">Livraisons</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.onTime}%</div>
                            <div class="metric-label">À l'heure</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.avgDelay.toFixed(1)}j</div>
                            <div class="metric-label">Délai Moyen</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.efficiency}%</div>
                            <div class="metric-label">Efficacité</div>
                        </div>
                    `;
                case 'crm':
                    return `
                        <div class="metric">
                            <div class="metric-value">${dept.clients}</div>
                            <div class="metric-label">Clients</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.active}%</div>
                            <div class="metric-label">Actifs</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.satisfaction.toFixed(1)}★</div>
                            <div class="metric-label">Satisfaction</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${dept.interactions}</div>
                            <div class="metric-label">Interactions</div>
                        </div>
                    `;
                default:
                    return `
                        <div class="metric">
                            <div class="metric-value">-</div>
                            <div class="metric-label">Métrique 1</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">-</div>
                            <div class="metric-label">Métrique 2</div>
                        </div>
                    `;
            }
        }

        function updateObjectivesTab() {
            const container = document.getElementById('objectivesGrid');
            const objectives = consolidatedData.objectives;

            container.innerHTML = Object.keys(objectives).map(key => {
                const obj = objectives[key];
                const progressColor = obj.progress >= 80 ? '#10b981' : obj.progress >= 60 ? '#f59e0b' : '#ef4444';

                return `
                    <div class="objective-card">
                        <div class="objective-title">${getObjectiveTitle(key)}</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${obj.progress}%; background: ${progressColor};"></div>
                        </div>
                        <div class="progress-text">
                            <span>${obj.current.toLocaleString()} / ${obj.target.toLocaleString()}</span>
                            <span>${obj.progress.toFixed(1)}%</span>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getObjectiveTitle(key) {
            const titles = {
                'revenue': 'Chiffre d\'Affaires Annuel',
                'growth': 'Croissance (%)',
                'margin': 'Marge Brute (%)',
                'staff': 'Effectif Cible',
                'satisfaction': 'Satisfaction Client (%)'
            };
            return titles[key] || key;
        }

        function updateAlertsTab() {
            const container = document.getElementById('alertsContainer');
            const alerts = consolidatedData.alerts;

            if (alerts.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <span class="material-icons" style="font-size: 3rem; margin-bottom: 1rem; color: #10b981;">check_circle</span>
                        <h3>Aucune alerte active</h3>
                        <p>Tous les indicateurs sont dans les seuils normaux</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = alerts.map(alert => {
                const iconClass = alert.type === 'critical' ? 'critical' : alert.type === 'warning' ? 'warning' : 'info';
                const icon = alert.type === 'critical' ? 'error' : alert.type === 'warning' ? 'warning' : 'info';

                return `
                    <div class="alert-item alert-${alert.type}">
                        <div class="alert-icon ${iconClass}">
                            <span class="material-icons" style="font-size: 1rem;">${icon}</span>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; margin-bottom: 0.25rem;">${alert.title}</div>
                            <div style="color: #6b7280; font-size: 0.875rem;">${alert.message}</div>
                            <div style="color: #6b7280; font-size: 0.75rem; margin-top: 0.25rem;">
                                Département: ${getDepartmentName(alert.department)} • Priorité: ${getPriorityLabel(alert.priority)}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getDepartmentName(key) {
            return consolidatedData.departments[key]?.name || key;
        }

        function getPriorityLabel(priority) {
            const labels = {
                'high': 'Haute',
                'medium': 'Moyenne',
                'low': 'Basse'
            };
            return labels[priority] || priority;
        }

        function refreshOverview() {
            updateGlobalStats();
            updateTabContent();
            showAlert('Vue d\'ensemble actualisée avec succès', 'success', 'alertsModalAlertContainer');
        }

        // Génération du rapport exécutif
        function generateExecutiveReport() {
            const data = consolidatedData.global;
            const departments = consolidatedData.departments;
            const objectives = consolidatedData.objectives;

            let reportContent = `
                <h2>Rapport Exécutif - ${new Date().toLocaleDateString()}</h2>
                <p style="color: #6b7280; margin-bottom: 2rem;">Synthèse stratégique et pilotage de performance</p>

                <h3>🎯 Performance Globale</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: left;">Indicateur</th>
                        <th style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">Valeur Actuelle</th>
                        <th style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">Objectif</th>
                        <th style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">Performance</th>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb;">Score Performance Global</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${data.performanceScore}/100</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">80/100</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">✓ Excellent</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb;">Chiffre d'Affaires</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${data.totalRevenue.toLocaleString()}€</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${objectives.revenue.target.toLocaleString()}€</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right; color: #f59e0b;">⚠ ${objectives.revenue.progress.toFixed(1)}%</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb;">Croissance Mensuelle</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${data.monthlyGrowth.toFixed(1)}%</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${objectives.growth.target}%</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">✓ ${objectives.growth.progress.toFixed(1)}%</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb;">Marge Brute</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${data.grossMargin.toFixed(1)}%</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${objectives.margin.target}%</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">✓ ${objectives.margin.progress.toFixed(1)}%</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb;">Trésorerie</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">${data.totalTreasury.toLocaleString()}€</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right;">200 000€</td>
                        <td style="padding: 0.75rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">✓ +20%</td>
                    </tr>
                </table>

                <h3>📊 Performance par Département</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Département</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: center;">Statut</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">KPI Principal</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Performance</th>
                    </tr>
            `;

            Object.keys(departments).forEach(key => {
                const dept = departments[key];
                const statusIcon = dept.status === 'excellent' ? '🟢' : dept.status === 'good' ? '🟡' : '🔴';
                const statusLabel = dept.status === 'excellent' ? 'Excellent' : dept.status === 'good' ? 'Bon' : 'À améliorer';

                let kpi = '';
                let performance = '';

                switch(key) {
                    case 'hr':
                        kpi = `${dept.employees} employés`;
                        performance = `${dept.satisfaction}% satisfaction`;
                        break;
                    case 'sales':
                        kpi = `${(dept.revenue / 1000).toFixed(0)}k€ CA`;
                        performance = `+${dept.growth.toFixed(1)}% croissance`;
                        break;
                    case 'finance':
                        kpi = `${(dept.treasury / 1000).toFixed(0)}k€ trésorerie`;
                        performance = `${dept.margin.toFixed(1)}% marge`;
                        break;
                    case 'logistics':
                        kpi = `${dept.deliveries} livraisons`;
                        performance = `${dept.onTime}% à l'heure`;
                        break;
                    case 'crm':
                        kpi = `${dept.clients} clients`;
                        performance = `${dept.satisfaction.toFixed(1)}★ satisfaction`;
                        break;
                    default:
                        kpi = 'N/A';
                        performance = 'N/A';
                }

                reportContent += `
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">${dept.icon} ${dept.name}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: center;">${statusIcon} ${statusLabel}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${kpi}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${performance}</td>
                    </tr>
                `;
            });

            reportContent += `
                </table>

                <h3>🚨 Alertes et Recommandations</h3>
                <ul style="margin: 1rem 0; padding-left: 2rem;">
            `;

            consolidatedData.alerts.forEach(alert => {
                const priorityIcon = alert.priority === 'high' ? '🔴' : alert.priority === 'medium' ? '🟡' : '🔵';
                reportContent += `
                    <li style="margin-bottom: 0.5rem;">${priorityIcon} <strong>${alert.title}:</strong> ${alert.message}</li>
                `;
            });

            reportContent += `
                </ul>

                <h3>📈 Recommandations Stratégiques</h3>
                <ul style="margin: 1rem 0; padding-left: 2rem;">
                    <li style="margin-bottom: 0.5rem;">✅ <strong>Commercial :</strong> Excellente performance, accélérer le développement commercial pour atteindre l'objectif annuel</li>
                    <li style="margin-bottom: 0.5rem;">✅ <strong>Finance :</strong> Trésorerie solide, envisager des investissements stratégiques</li>
                    <li style="margin-bottom: 0.5rem;">⚠️ <strong>RH :</strong> Recruter 5 employés supplémentaires pour soutenir la croissance</li>
                    <li style="margin-bottom: 0.5rem;">⚠️ <strong>Logistique :</strong> Optimiser les délais de livraison pour améliorer la satisfaction client</li>
                    <li style="margin-bottom: 0.5rem;">📊 <strong>Global :</strong> Performance excellente, maintenir la dynamique et surveiller les indicateurs de risque</li>
                </ul>

                <h3>🎯 Prochaines Étapes</h3>
                <ol style="margin: 1rem 0; padding-left: 2rem;">
                    <li style="margin-bottom: 0.5rem;">Lancer le recrutement de 5 nouveaux employés (RH)</li>
                    <li style="margin-bottom: 0.5rem;">Optimiser les processus logistiques pour réduire les délais</li>
                    <li style="margin-bottom: 0.5rem;">Intensifier les efforts commerciaux pour atteindre l'objectif CA</li>
                    <li style="margin-bottom: 0.5rem;">Évaluer les opportunités d'investissement avec l'excédent de trésorerie</li>
                    <li style="margin-bottom: 0.5rem;">Révision trimestrielle des objectifs et ajustements stratégiques</li>
                </ol>
            `;

            // Ouvrir le rapport dans une nouvelle fenêtre
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Rapport Exécutif - ERP HUB</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 2rem; line-height: 1.6; }
                        h2 { color: #374151; margin-bottom: 1rem; }
                        h3 { color: #4b5563; margin: 2rem 0 1rem 0; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem; }
                        table { border-collapse: collapse; width: 100%; margin: 1rem 0; }
                        th, td { border: 1px solid #e5e7eb; padding: 0.5rem; }
                        th { background: #f9fafb; font-weight: 600; }
                        ul, ol { margin: 1rem 0; }
                        li { margin-bottom: 0.5rem; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${reportContent}
                    <div style="margin-top: 3rem; text-align: center; border-top: 1px solid #e5e7eb; padding-top: 2rem;">
                        <button onclick="window.print()" style="padding: 0.75rem 1.5rem; background: #374151; color: white; border: none; border-radius: 0.5rem; cursor: pointer; font-size: 1rem;">
                            📄 Imprimer le Rapport
                        </button>
                    </div>
                </body>
                </html>
            `);
            reportWindow.document.close();
        }

        // Sauvegarde des objectifs
        function saveObjectives() {
            const objectives = {
                revenue: document.getElementById('revenueTarget').value,
                growth: document.getElementById('growthTarget').value,
                margin: document.getElementById('marginTarget').value,
                staff: document.getElementById('staffTarget').value,
                satisfaction: document.getElementById('satisfactionTarget').value
            };

            localStorage.setItem('managerObjectives', JSON.stringify(objectives));
            showAlert('Objectifs sauvegardés avec succès', 'success', 'objectivesModalAlertContainer');
            closeModal('objectivesModal');

            // Recalculer les progrès
            updateObjectiveProgress();
        }

        function updateObjectiveProgress() {
            const savedObjectives = localStorage.getItem('managerObjectives');
            if (savedObjectives) {
                const objectives = JSON.parse(savedObjectives);
                const data = consolidatedData.global;

                // Mettre à jour les progrès
                consolidatedData.objectives.revenue.target = parseInt(objectives.revenue);
                consolidatedData.objectives.revenue.progress = (data.totalRevenue / objectives.revenue) * 100;

                consolidatedData.objectives.growth.target = parseInt(objectives.growth);
                consolidatedData.objectives.growth.progress = (data.monthlyGrowth / objectives.growth) * 100;

                consolidatedData.objectives.margin.target = parseInt(objectives.margin);
                consolidatedData.objectives.margin.progress = (data.grossMargin / objectives.margin) * 100;

                consolidatedData.objectives.staff.target = parseInt(objectives.staff);
                consolidatedData.objectives.staff.progress = (data.totalEmployees / objectives.staff) * 100;

                consolidatedData.objectives.satisfaction.target = parseInt(objectives.satisfaction);
                consolidatedData.objectives.satisfaction.progress = ((data.clientSatisfaction * 20) / objectives.satisfaction) * 100;
            }
        }

        // Sauvegarde des alertes
        function saveAlerts() {
            const thresholds = {
                treasury: document.getElementById('treasuryAlert').value,
                margin: document.getElementById('marginAlert').value,
                delivery: document.getElementById('deliveryAlert').value,
                satisfaction: document.getElementById('satisfactionAlert').value
            };

            localStorage.setItem('managerAlertThresholds', JSON.stringify(thresholds));
            showAlert('Seuils d\'alerte sauvegardés avec succès', 'success', 'alertsModalAlertContainer');
            closeModal('alertsModal');
        }

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Gestion des rapports de direction
        function updateReportsTab() {
            loadExecutiveReports();
        }

        function renderReportsTable() {
            const tbody = document.getElementById('reportsTableBody');

            if (executiveReports.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun rapport trouvé. Cliquez sur "Générer Rapport" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = executiveReports.map(report => {
                const statusBadge = getReportStatusBadge(report.status);
                return `
                    <tr>
                        <td>${report.name}</td>
                        <td>${report.type}</td>
                        <td>${formatDateTime(report.generatedDate)}</td>
                        <td>${report.analysisPeriod}</td>
                        <td>${formatDateTime(report.lastRevisionDate)}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="viewExecutiveReport('${report.id}')" title="Voir">
                                <span class="material-icons" style="font-size: 1rem;">visibility</span>
                            </button>
                            <button class="btn btn-info" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="reviseReport('${report.id}')" title="Réviser">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-warning" style="padding: 0.25rem 0.5rem;" onclick="exportExecutiveReport('${report.id}')" title="Exporter">
                                <span class="material-icons" style="font-size: 1rem;">download</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getReportStatusBadge(status) {
            const badges = {
                'completed': '<span class="badge badge-success">Terminé</span>',
                'in_progress': '<span class="badge badge-warning">En cours</span>',
                'draft': '<span class="badge badge-info">Brouillon</span>',
                'reviewed': '<span class="badge badge-primary">Révisé</span>'
            };
            return badges[status] || '<span class="badge badge-secondary">Inconnu</span>';
        }

        function loadExecutiveReports() {
            try {
                executiveReports = [...demoExecutiveReports];
            } catch (error) {
                console.log('Chargement des données de démonstration');
                executiveReports = [...demoExecutiveReports];
            }
            renderReportsTable();
        }

        function viewExecutiveReport(id) {
            const report = executiveReports.find(r => r.id === id);
            if (report) {
                alert(`Affichage du rapport: ${report.name}\nType: ${report.type}\nPériode: ${report.analysisPeriod}\nDescription: ${report.description}`);
            }
        }

        function reviseReport(id) {
            const report = executiveReports.find(r => r.id === id);
            if (report) {
                report.lastRevisionDate = new Date().toISOString();
                report.status = 'reviewed';
                renderReportsTable();
                showAlert(`Rapport "${report.name}" révisé avec succès`, 'success', 'alertsModalAlertContainer');
            }
        }

        function exportExecutiveReport(id) {
            const report = executiveReports.find(r => r.id === id);
            if (report) {
                alert(`Export du rapport: ${report.name}\nFormats disponibles: PDF, Excel, PowerPoint`);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                updateObjectiveProgress();
                updateGlobalStats();
                updateTabContent();

                // Charger les objectifs sauvegardés
                const savedObjectives = localStorage.getItem('managerObjectives');
                if (savedObjectives) {
                    const objectives = JSON.parse(savedObjectives);
                    document.getElementById('revenueTarget').value = objectives.revenue || 1000000;
                    document.getElementById('growthTarget').value = objectives.growth || 20;
                    document.getElementById('marginTarget').value = objectives.margin || 45;
                    document.getElementById('staffTarget').value = objectives.staff || 15;
                    document.getElementById('satisfactionTarget').value = objectives.satisfaction || 90;
                }

                // Charger les seuils d'alerte sauvegardés
                const savedThresholds = localStorage.getItem('managerAlertThresholds');
                if (savedThresholds) {
                    const thresholds = JSON.parse(savedThresholds);
                    document.getElementById('treasuryAlert').value = thresholds.treasury || 50000;
                    document.getElementById('marginAlert').value = thresholds.margin || 30;
                    document.getElementById('deliveryAlert').value = thresholds.delivery || 10;
                    document.getElementById('satisfactionAlert').value = thresholds.satisfaction || 80;
                }
            }
        });
    </script>
</body>
</html>
