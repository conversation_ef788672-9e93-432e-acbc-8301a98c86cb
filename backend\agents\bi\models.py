"""
Modèles pour l'Agent BI - Business Intelligence et Reporting
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import json

from core.models import TimeStampedModel, UUIDModel, Tenant, User


class DataSource(UUIDModel, TimeStampedModel):
    """Modèle pour les sources de données"""

    SOURCE_TYPES = [
        ('database', _('Base de données')),
        ('api', _('API REST')),
        ('file', _('Fichier')),
        ('agent', _('Agent ERP')),
        ('external', _('Source externe')),
        ('manual', _('Saisie manuelle')),
    ]

    CONNECTION_STATUS = [
        ('connected', _('Connecté')),
        ('disconnected', _('Déconnecté')),
        ('error', _('Erreur')),
        ('testing', _('Test en cours')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='data_sources',
        verbose_name=_("Tenant")
    )

    # Informations de base
    name = models.CharField(_("Nom de la source"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    source_type = models.CharField(_("Type de source"), max_length=20, choices=SOURCE_TYPES)

    # Configuration de connexion
    connection_config = models.JSONField(_("Configuration de connexion"), default=dict)
    connection_status = models.CharField(_("Statut de connexion"), max_length=15, choices=CONNECTION_STATUS, default='disconnected')
    last_connection_test = models.DateTimeField(_("Dernier test de connexion"), null=True, blank=True)

    # Métadonnées
    schema_info = models.JSONField(_("Informations de schéma"), default=dict)
    refresh_frequency = models.PositiveIntegerField(_("Fréquence de rafraîchissement (minutes)"), default=60)
    last_refresh = models.DateTimeField(_("Dernier rafraîchissement"), null=True, blank=True)

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)
    auto_refresh = models.BooleanField(_("Rafraîchissement automatique"), default=True)

    # Responsable
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_data_sources',
        verbose_name=_("Créé par")
    )

    class Meta:
        verbose_name = _("Source de données")
        verbose_name_plural = _("Sources de données")
        unique_together = ['tenant', 'name']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.source_type})"


class Dataset(UUIDModel, TimeStampedModel):
    """Modèle pour les jeux de données"""

    DATASET_TYPES = [
        ('table', _('Table')),
        ('view', _('Vue')),
        ('query', _('Requête')),
        ('aggregation', _('Agrégation')),
        ('calculation', _('Calcul')),
        ('external', _('Externe')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='datasets',
        verbose_name=_("Tenant")
    )

    data_source = models.ForeignKey(
        DataSource,
        on_delete=models.CASCADE,
        related_name='datasets',
        verbose_name=_("Source de données")
    )

    # Informations de base
    name = models.CharField(_("Nom du dataset"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    dataset_type = models.CharField(_("Type de dataset"), max_length=20, choices=DATASET_TYPES)

    # Configuration
    query_config = models.JSONField(_("Configuration de requête"), default=dict)
    columns_config = models.JSONField(_("Configuration des colonnes"), default=list)
    filters_config = models.JSONField(_("Configuration des filtres"), default=list)

    # Métadonnées
    row_count = models.PositiveIntegerField(_("Nombre de lignes"), default=0)
    column_count = models.PositiveIntegerField(_("Nombre de colonnes"), default=0)
    data_size_mb = models.DecimalField(_("Taille des données (MB)"), max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # Cache et performance
    cache_enabled = models.BooleanField(_("Cache activé"), default=True)
    cache_duration_minutes = models.PositiveIntegerField(_("Durée du cache (minutes)"), default=30)
    last_cached = models.DateTimeField(_("Dernière mise en cache"), null=True, blank=True)

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)

    # Responsable
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_datasets',
        verbose_name=_("Créé par")
    )

    class Meta:
        verbose_name = _("Dataset")
        verbose_name_plural = _("Datasets")
        unique_together = ['tenant', 'name']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.data_source.name})"


class Report(UUIDModel, TimeStampedModel):
    """Modèle pour les rapports"""

    REPORT_TYPES = [
        ('table', _('Tableau')),
        ('chart', _('Graphique')),
        ('dashboard', _('Tableau de bord')),
        ('kpi', _('Indicateur KPI')),
        ('pivot', _('Tableau croisé')),
        ('export', _('Export')),
    ]

    CHART_TYPES = [
        ('line', _('Ligne')),
        ('bar', _('Barres')),
        ('column', _('Colonnes')),
        ('pie', _('Camembert')),
        ('doughnut', _('Anneau')),
        ('area', _('Aires')),
        ('scatter', _('Nuage de points')),
        ('gauge', _('Jauge')),
        ('funnel', _('Entonnoir')),
        ('heatmap', _('Carte de chaleur')),
    ]

    REPORT_STATUS = [
        ('draft', _('Brouillon')),
        ('published', _('Publié')),
        ('archived', _('Archivé')),
        ('error', _('Erreur')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='reports',
        verbose_name=_("Tenant")
    )

    dataset = models.ForeignKey(
        Dataset,
        on_delete=models.CASCADE,
        related_name='reports',
        verbose_name=_("Dataset")
    )

    # Informations de base
    name = models.CharField(_("Nom du rapport"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    report_type = models.CharField(_("Type de rapport"), max_length=20, choices=REPORT_TYPES)
    chart_type = models.CharField(_("Type de graphique"), max_length=20, choices=CHART_TYPES, blank=True)

    # Configuration du rapport
    config = models.JSONField(_("Configuration"), default=dict)
    layout_config = models.JSONField(_("Configuration de mise en page"), default=dict)
    style_config = models.JSONField(_("Configuration de style"), default=dict)

    # Filtres et paramètres
    filters = models.JSONField(_("Filtres"), default=list)
    parameters = models.JSONField(_("Paramètres"), default=dict)

    # Planification
    is_scheduled = models.BooleanField(_("Planifié"), default=False)
    schedule_config = models.JSONField(_("Configuration de planification"), default=dict)
    last_execution = models.DateTimeField(_("Dernière exécution"), null=True, blank=True)
    next_execution = models.DateTimeField(_("Prochaine exécution"), null=True, blank=True)

    # Partage et permissions
    is_public = models.BooleanField(_("Public"), default=False)
    shared_with = models.JSONField(_("Partagé avec"), default=list)

    # Statut
    status = models.CharField(_("Statut"), max_length=15, choices=REPORT_STATUS, default='draft')

    # Responsable
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_reports',
        verbose_name=_("Créé par")
    )

    class Meta:
        verbose_name = _("Rapport")
        verbose_name_plural = _("Rapports")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['report_type', 'status']),
            models.Index(fields=['created_by', 'status']),
        ]

    def __str__(self):
        return f"{self.name} ({self.report_type})"


class Dashboard(UUIDModel, TimeStampedModel):
    """Modèle pour les tableaux de bord"""

    DASHBOARD_TYPES = [
        ('executive', _('Exécutif')),
        ('operational', _('Opérationnel')),
        ('analytical', _('Analytique')),
        ('financial', _('Financier')),
        ('sales', _('Commercial')),
        ('hr', _('RH')),
        ('custom', _('Personnalisé')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='dashboards',
        verbose_name=_("Tenant")
    )

    # Informations de base
    name = models.CharField(_("Nom du tableau de bord"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    dashboard_type = models.CharField(_("Type de tableau de bord"), max_length=20, choices=DASHBOARD_TYPES)

    # Configuration
    layout_config = models.JSONField(_("Configuration de mise en page"), default=dict)
    theme_config = models.JSONField(_("Configuration du thème"), default=dict)
    refresh_interval = models.PositiveIntegerField(_("Intervalle de rafraîchissement (secondes)"), default=300)

    # Widgets et rapports
    widgets = models.JSONField(_("Widgets"), default=list)
    reports = models.ManyToManyField(Report, through='DashboardReport', related_name='dashboards')

    # Partage et permissions
    is_public = models.BooleanField(_("Public"), default=False)
    shared_with = models.JSONField(_("Partagé avec"), default=list)

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)
    is_default = models.BooleanField(_("Par défaut"), default=False)

    # Responsable
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_dashboards',
        verbose_name=_("Créé par")
    )

    class Meta:
        verbose_name = _("Tableau de bord")
        verbose_name_plural = _("Tableaux de bord")
        unique_together = ['tenant', 'name']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.dashboard_type})"


class DashboardReport(UUIDModel, TimeStampedModel):
    """Modèle de liaison entre tableaux de bord et rapports"""

    dashboard = models.ForeignKey(Dashboard, on_delete=models.CASCADE)
    report = models.ForeignKey(Report, on_delete=models.CASCADE)

    # Position et taille
    position_x = models.PositiveIntegerField(_("Position X"), default=0)
    position_y = models.PositiveIntegerField(_("Position Y"), default=0)
    width = models.PositiveIntegerField(_("Largeur"), default=6)
    height = models.PositiveIntegerField(_("Hauteur"), default=4)

    # Configuration spécifique
    widget_config = models.JSONField(_("Configuration du widget"), default=dict)

    # Ordre d'affichage
    order = models.PositiveIntegerField(_("Ordre"), default=0)

    class Meta:
        verbose_name = _("Rapport de tableau de bord")
        verbose_name_plural = _("Rapports de tableau de bord")
        unique_together = ['dashboard', 'report']
        ordering = ['order']


class KPI(UUIDModel, TimeStampedModel):
    """Modèle pour les indicateurs de performance clés"""

    KPI_TYPES = [
        ('financial', _('Financier')),
        ('sales', _('Commercial')),
        ('operational', _('Opérationnel')),
        ('customer', _('Client')),
        ('hr', _('Ressources humaines')),
        ('quality', _('Qualité')),
        ('efficiency', _('Efficacité')),
        ('growth', _('Croissance')),
    ]

    CALCULATION_METHODS = [
        ('sum', _('Somme')),
        ('average', _('Moyenne')),
        ('count', _('Comptage')),
        ('min', _('Minimum')),
        ('max', _('Maximum')),
        ('percentage', _('Pourcentage')),
        ('ratio', _('Ratio')),
        ('custom', _('Personnalisé')),
    ]

    TREND_DIRECTIONS = [
        ('up', _('Hausse')),
        ('down', _('Baisse')),
        ('stable', _('Stable')),
        ('unknown', _('Inconnu')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='kpis',
        verbose_name=_("Tenant")
    )

    dataset = models.ForeignKey(
        Dataset,
        on_delete=models.CASCADE,
        related_name='kpis',
        verbose_name=_("Dataset")
    )

    # Informations de base
    name = models.CharField(_("Nom du KPI"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    kpi_type = models.CharField(_("Type de KPI"), max_length=20, choices=KPI_TYPES)

    # Configuration du calcul
    calculation_method = models.CharField(_("Méthode de calcul"), max_length=20, choices=CALCULATION_METHODS)
    calculation_config = models.JSONField(_("Configuration de calcul"), default=dict)

    # Valeurs
    current_value = models.DecimalField(_("Valeur actuelle"), max_digits=15, decimal_places=4, null=True, blank=True)
    target_value = models.DecimalField(_("Valeur cible"), max_digits=15, decimal_places=4, null=True, blank=True)
    previous_value = models.DecimalField(_("Valeur précédente"), max_digits=15, decimal_places=4, null=True, blank=True)

    # Unité et format
    unit = models.CharField(_("Unité"), max_length=50, blank=True)
    format_config = models.JSONField(_("Configuration de format"), default=dict)

    # Seuils et alertes
    warning_threshold = models.DecimalField(_("Seuil d'alerte"), max_digits=15, decimal_places=4, null=True, blank=True)
    critical_threshold = models.DecimalField(_("Seuil critique"), max_digits=15, decimal_places=4, null=True, blank=True)

    # Tendance
    trend_direction = models.CharField(_("Direction de la tendance"), max_length=10, choices=TREND_DIRECTIONS, default='unknown')
    trend_percentage = models.DecimalField(_("Pourcentage de tendance"), max_digits=10, decimal_places=2, null=True, blank=True)

    # Mise à jour
    last_calculated = models.DateTimeField(_("Dernière calculation"), null=True, blank=True)
    calculation_frequency = models.PositiveIntegerField(_("Fréquence de calcul (minutes)"), default=60)

    # Statut
    is_active = models.BooleanField(_("Actif"), default=True)

    # Responsable
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_kpis',
        verbose_name=_("Créé par")
    )

    class Meta:
        verbose_name = _("KPI")
        verbose_name_plural = _("KPIs")
        unique_together = ['tenant', 'name']
        ordering = ['name']
        indexes = [
            models.Index(fields=['kpi_type', 'is_active']),
            models.Index(fields=['last_calculated']),
        ]

    def __str__(self):
        return f"{self.name} ({self.kpi_type})"

    @property
    def performance_vs_target(self):
        """Performance par rapport à la cible (%)"""
        if self.target_value and self.target_value != 0 and self.current_value is not None:
            return ((self.current_value - self.target_value) / self.target_value) * 100
        return None

    @property
    def is_on_target(self):
        """Indique si le KPI atteint sa cible"""
        if self.target_value and self.current_value is not None:
            return abs(self.current_value - self.target_value) <= (self.target_value * 0.05)  # 5% de tolérance
        return None


class Alert(UUIDModel, TimeStampedModel):
    """Modèle pour les alertes BI"""

    ALERT_TYPES = [
        ('threshold', _('Seuil')),
        ('anomaly', _('Anomalie')),
        ('trend', _('Tendance')),
        ('data_quality', _('Qualité des données')),
        ('performance', _('Performance')),
        ('custom', _('Personnalisé')),
    ]

    ALERT_SEVERITY = [
        ('info', _('Information')),
        ('warning', _('Avertissement')),
        ('critical', _('Critique')),
        ('emergency', _('Urgence')),
    ]

    ALERT_STATUS = [
        ('active', _('Actif')),
        ('triggered', _('Déclenché')),
        ('acknowledged', _('Acquitté')),
        ('resolved', _('Résolu')),
        ('disabled', _('Désactivé')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='bi_alerts',
        verbose_name=_("Tenant")
    )

    kpi = models.ForeignKey(
        KPI,
        on_delete=models.CASCADE,
        related_name='alerts',
        verbose_name=_("KPI"),
        null=True,
        blank=True
    )

    # Informations de base
    name = models.CharField(_("Nom de l'alerte"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    alert_type = models.CharField(_("Type d'alerte"), max_length=20, choices=ALERT_TYPES)
    severity = models.CharField(_("Sévérité"), max_length=15, choices=ALERT_SEVERITY)

    # Configuration de l'alerte
    condition_config = models.JSONField(_("Configuration de condition"), default=dict)
    notification_config = models.JSONField(_("Configuration de notification"), default=dict)

    # Statut et déclenchement
    status = models.CharField(_("Statut"), max_length=15, choices=ALERT_STATUS, default='active')
    last_triggered = models.DateTimeField(_("Dernier déclenchement"), null=True, blank=True)
    trigger_count = models.PositiveIntegerField(_("Nombre de déclenchements"), default=0)

    # Acquittement
    acknowledged_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='acknowledged_alerts',
        verbose_name=_("Acquitté par")
    )
    acknowledged_at = models.DateTimeField(_("Acquitté le"), null=True, blank=True)

    # Responsable
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_alerts',
        verbose_name=_("Créé par")
    )

    class Meta:
        verbose_name = _("Alerte")
        verbose_name_plural = _("Alertes")
        ordering = ['-last_triggered', '-created_at']
        indexes = [
            models.Index(fields=['status', 'severity']),
            models.Index(fields=['alert_type', 'status']),
        ]

    def __str__(self):
        return f"{self.name} ({self.severity})"


class AnalysisJob(UUIDModel, TimeStampedModel):
    """Modèle pour les tâches d'analyse"""

    JOB_TYPES = [
        ('data_refresh', _('Rafraîchissement des données')),
        ('kpi_calculation', _('Calcul des KPIs')),
        ('report_generation', _('Génération de rapport')),
        ('alert_check', _('Vérification des alertes')),
        ('data_export', _('Export de données')),
        ('analysis', _('Analyse')),
        ('ml_training', _('Entraînement ML')),
        ('prediction', _('Prédiction')),
    ]

    JOB_STATUS = [
        ('pending', _('En attente')),
        ('running', _('En cours')),
        ('completed', _('Terminé')),
        ('failed', _('Échec')),
        ('cancelled', _('Annulé')),
    ]

    PRIORITY_LEVELS = [
        ('low', _('Faible')),
        ('normal', _('Normale')),
        ('high', _('Élevée')),
        ('urgent', _('Urgente')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='analysis_jobs',
        verbose_name=_("Tenant")
    )

    # Informations de base
    name = models.CharField(_("Nom de la tâche"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    job_type = models.CharField(_("Type de tâche"), max_length=20, choices=JOB_TYPES)
    priority = models.CharField(_("Priorité"), max_length=10, choices=PRIORITY_LEVELS, default='normal')

    # Configuration
    config = models.JSONField(_("Configuration"), default=dict)
    parameters = models.JSONField(_("Paramètres"), default=dict)

    # Planification
    scheduled_at = models.DateTimeField(_("Planifié pour"), null=True, blank=True)
    started_at = models.DateTimeField(_("Démarré à"), null=True, blank=True)
    completed_at = models.DateTimeField(_("Terminé à"), null=True, blank=True)

    # Statut et résultats
    status = models.CharField(_("Statut"), max_length=15, choices=JOB_STATUS, default='pending')
    progress_percentage = models.PositiveIntegerField(_("Pourcentage de progression"), default=0)
    result_data = models.JSONField(_("Données de résultat"), default=dict)
    error_message = models.TextField(_("Message d'erreur"), blank=True)

    # Métriques
    execution_time_seconds = models.PositiveIntegerField(_("Temps d'exécution (secondes)"), null=True, blank=True)
    memory_usage_mb = models.DecimalField(_("Utilisation mémoire (MB)"), max_digits=10, decimal_places=2, null=True, blank=True)

    # Responsable
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_analysis_jobs',
        verbose_name=_("Créé par")
    )

    class Meta:
        verbose_name = _("Tâche d'analyse")
        verbose_name_plural = _("Tâches d'analyse")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['job_type', 'status']),
            models.Index(fields=['scheduled_at']),
        ]

    def __str__(self):
        return f"{self.name} ({self.job_type})"

    @property
    def duration(self):
        """Durée d'exécution"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None


class BIAnalytics(UUIDModel, TimeStampedModel):
    """Modèle pour les analytics BI"""

    METRIC_TYPES = [
        ('usage', _('Utilisation')),
        ('performance', _('Performance')),
        ('data_quality', _('Qualité des données')),
        ('user_engagement', _('Engagement utilisateur')),
        ('system_health', _('Santé du système')),
        ('business_impact', _('Impact business')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='bi_analytics',
        verbose_name=_("Tenant")
    )

    # Informations de base
    metric_type = models.CharField(_("Type de métrique"), max_length=20, choices=METRIC_TYPES)
    metric_name = models.CharField(_("Nom de la métrique"), max_length=200)

    # Période
    period_start = models.DateTimeField(_("Début de période"))
    period_end = models.DateTimeField(_("Fin de période"))

    # Valeurs
    metric_value = models.DecimalField(_("Valeur de la métrique"), max_digits=15, decimal_places=4)
    target_value = models.DecimalField(_("Valeur cible"), max_digits=15, decimal_places=4, null=True, blank=True)
    previous_value = models.DecimalField(_("Valeur précédente"), max_digits=15, decimal_places=4, null=True, blank=True)

    # Métadonnées
    dimensions = models.JSONField(_("Dimensions"), default=dict)
    attributes = models.JSONField(_("Attributs"), default=dict)
    calculation_method = models.TextField(_("Méthode de calcul"), blank=True)

    # Responsable
    calculated_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='calculated_bi_analytics',
        verbose_name=_("Calculé par")
    )

    class Meta:
        verbose_name = _("Analytics BI")
        verbose_name_plural = _("Analytics BI")
        unique_together = ['tenant', 'metric_type', 'metric_name', 'period_start', 'period_end']
        ordering = ['-period_end', 'metric_type']
        indexes = [
            models.Index(fields=['metric_type', 'period_start']),
            models.Index(fields=['period_start', 'period_end']),
        ]

    def __str__(self):
        return f"{self.metric_name} ({self.period_start.strftime('%Y-%m-%d')} - {self.period_end.strftime('%Y-%m-%d')}): {self.metric_value}"

    @property
    def variance_from_target(self):
        """Écart par rapport à la cible"""
        if self.target_value:
            return self.metric_value - self.target_value
        return None

    @property
    def variance_from_previous(self):
        """Écart par rapport à la période précédente"""
        if self.previous_value:
            return self.metric_value - self.previous_value
        return None

    @property
    def growth_rate(self):
        """Taux de croissance par rapport à la période précédente (%)"""
        if self.previous_value and self.previous_value != 0:
            return ((self.metric_value - self.previous_value) / self.previous_value) * 100
        return None


class UserActivity(UUIDModel, TimeStampedModel):
    """Modèle pour l'activité utilisateur BI"""

    ACTIVITY_TYPES = [
        ('dashboard_view', _('Vue tableau de bord')),
        ('report_view', _('Vue rapport')),
        ('report_export', _('Export rapport')),
        ('kpi_check', _('Consultation KPI')),
        ('alert_acknowledge', _('Acquittement alerte')),
        ('data_query', _('Requête données')),
        ('filter_apply', _('Application filtre')),
        ('share', _('Partage')),
    ]

    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='bi_user_activities',
        verbose_name=_("Tenant")
    )

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='bi_activities',
        verbose_name=_("Utilisateur")
    )

    # Informations de l'activité
    activity_type = models.CharField(_("Type d'activité"), max_length=20, choices=ACTIVITY_TYPES)
    object_type = models.CharField(_("Type d'objet"), max_length=50, blank=True)
    object_id = models.CharField(_("ID de l'objet"), max_length=100, blank=True)
    object_name = models.CharField(_("Nom de l'objet"), max_length=200, blank=True)

    # Contexte
    session_id = models.CharField(_("ID de session"), max_length=100, blank=True)
    ip_address = models.GenericIPAddressField(_("Adresse IP"), null=True, blank=True)
    user_agent = models.TextField(_("User Agent"), blank=True)

    # Métadonnées
    metadata = models.JSONField(_("Métadonnées"), default=dict)
    duration_seconds = models.PositiveIntegerField(_("Durée (secondes)"), null=True, blank=True)

    class Meta:
        verbose_name = _("Activité utilisateur")
        verbose_name_plural = _("Activités utilisateur")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'activity_type']),
            models.Index(fields=['activity_type', 'created_at']),
            models.Index(fields=['object_type', 'object_id']),
        ]

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.activity_type} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"