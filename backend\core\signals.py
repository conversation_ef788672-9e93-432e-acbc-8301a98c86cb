"""
Signaux pour l'application core
"""
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .models import Tenant, Role

User = get_user_model()


@receiver(post_save, sender=Tenant)
def create_default_roles(sender, instance, created, **kwargs):
    """
    Crée les rôles par défaut lors de la création d'un nouveau tenant
    """
    if created:
        default_roles = [
            {
                'name': 'Administrateur',
                'description': 'Accès complet à tous les modules',
                'is_system_role': True,
                'permissions': {
                    'hr': ['create', 'read', 'update', 'delete'],
                    'sales': ['create', 'read', 'update', 'delete'],
                    'purchase': ['create', 'read', 'update', 'delete'],
                    'logistics': ['create', 'read', 'update', 'delete'],
                    'stock': ['create', 'read', 'update', 'delete'],
                    'accounting': ['create', 'read', 'update', 'delete'],
                    'finance': ['create', 'read', 'update', 'delete'],
                    'crm': ['create', 'read', 'update', 'delete'],
                    'bi': ['create', 'read', 'update', 'delete'],
                }
            },
            {
                'name': 'Manager',
                'description': 'Accès en lecture/écriture aux modules principaux',
                'is_system_role': True,
                'permissions': {
                    'hr': ['read', 'update'],
                    'sales': ['create', 'read', 'update'],
                    'purchase': ['create', 'read', 'update'],
                    'logistics': ['read', 'update'],
                    'stock': ['read', 'update'],
                    'accounting': ['read'],
                    'finance': ['read'],
                    'crm': ['create', 'read', 'update'],
                    'bi': ['read'],
                }
            },
            {
                'name': 'Employé',
                'description': 'Accès en lecture aux modules de base',
                'is_system_role': True,
                'permissions': {
                    'hr': ['read'],
                    'sales': ['read'],
                    'purchase': ['read'],
                    'logistics': ['read'],
                    'stock': ['read'],
                    'crm': ['read'],
                }
            },
            {
                'name': 'Comptable',
                'description': 'Accès complet aux modules financiers',
                'is_system_role': True,
                'permissions': {
                    'accounting': ['create', 'read', 'update', 'delete'],
                    'finance': ['create', 'read', 'update', 'delete'],
                    'sales': ['read'],
                    'purchase': ['read'],
                    'bi': ['read'],
                }
            },
            {
                'name': 'Commercial',
                'description': 'Accès aux modules de vente et CRM',
                'is_system_role': True,
                'permissions': {
                    'sales': ['create', 'read', 'update'],
                    'crm': ['create', 'read', 'update'],
                    'stock': ['read'],
                    'bi': ['read'],
                }
            }
        ]
        
        for role_data in default_roles:
            Role.objects.create(
                tenant=instance,
                **role_data
            )


@receiver(post_save, sender=User)
def assign_default_role(sender, instance, created, **kwargs):
    """
    Assigne un rôle par défaut aux nouveaux utilisateurs
    """
    if created and not instance.is_superuser:
        # Assigner le rôle "Employé" par défaut
        try:
            default_role = Role.objects.get(
                tenant=instance.tenant,
                name='Employé',
                is_system_role=True
            )
            from .models import UserRole
            UserRole.objects.create(
                user=instance,
                role=default_role
            )
        except Role.DoesNotExist:
            pass  # Le rôle par défaut n'existe pas encore


@receiver(pre_save, sender=User)
def update_username_with_tenant(sender, instance, **kwargs):
    """
    S'assure que le username est unique par tenant
    """
    if instance.tenant_id and not instance.username.endswith(f"@{instance.tenant.slug}"):
        # Ajouter le slug du tenant au username pour garantir l'unicité globale
        base_username = instance.username.split('@')[0]  # Enlever le tenant existant s'il y en a un
        instance.username = f"{base_username}@{instance.tenant.slug}"
