# 🧪 TEST DE CONNEXION POSTGRESQL
# Script simple pour tester la connexion à PostgreSQL

import sys

try:
    import psycopg2
    print("✅ psycopg2 importé avec succès")
except ImportError as e:
    print(f"❌ Erreur import psycopg2: {e}")
    print("💡 Essayez: pip install psycopg2-binary")
    sys.exit(1)

# Configuration de connexion (utiliser la config du conteneur)
config = {
    'host': 'localhost',
    'port': '5432',
    'database': 'erp_hub',     # Base créée par le conteneur
    'user': 'erp_admin',       # Utilisateur créé par le conteneur
    'password': 'erp_secure_2024'
}

print("🔌 Test de connexion PostgreSQL...")
print(f"📍 Host: {config['host']}:{config['port']}")
print(f"🗄️ Database: {config['database']}")
print(f"👤 User: {config['user']}")

try:
    # Tentative de connexion
    conn = psycopg2.connect(
        host=config['host'],
        port=config['port'],
        database=config['database'],
        user=config['user'],
        password=config['password']
    )

    print("✅ Connexion PostgreSQL réussie !")

    # Test simple
    cursor = conn.cursor()
    cursor.execute("SELECT 1;")
    result = cursor.fetchone()
    print(f"📊 Test simple: {result[0]}")

    # Créer la base erp_hub si elle n'existe pas
    cursor.execute("SELECT 1 FROM pg_database WHERE datname = 'erp_hub';")
    if not cursor.fetchone():
        print("🗄️ Création de la base erp_hub...")
        conn.autocommit = True
        cursor.execute("CREATE DATABASE erp_hub;")
        print("✅ Base erp_hub créée")
    else:
        print("✅ Base erp_hub existe déjà")

    # Créer l'utilisateur erp_admin si il n'existe pas
    cursor.execute("SELECT 1 FROM pg_user WHERE usename = 'erp_admin';")
    if not cursor.fetchone():
        print("👤 Création de l'utilisateur erp_admin...")
        cursor.execute("CREATE USER erp_admin WITH PASSWORD 'erp_secure_2024';")
        cursor.execute("GRANT ALL PRIVILEGES ON DATABASE erp_hub TO erp_admin;")
        print("✅ Utilisateur erp_admin créé")
    else:
        print("✅ Utilisateur erp_admin existe déjà")

    cursor.close()
    conn.close()

    print("🎉 Configuration PostgreSQL terminée avec succès !")

except psycopg2.Error as e:
    print(f"❌ Erreur de connexion PostgreSQL: {e}")
    print("💡 Vérifiez que le conteneur PostgreSQL est démarré")
    print("💡 Commande: docker ps")
    sys.exit(1)

except Exception as e:
    print(f"❌ Erreur inattendue: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
