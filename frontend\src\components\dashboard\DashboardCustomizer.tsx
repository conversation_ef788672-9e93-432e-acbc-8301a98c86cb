import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Widget {
  id: string;
  name: string;
  type: 'metric' | 'chart' | 'table' | 'list';
  size: 'small' | 'medium' | 'large';
  position: { x: number; y: number };
  visible: boolean;
  config: any;
}

interface DashboardLayout {
  id: string;
  name: string;
  widgets: Widget[];
  columns: number;
  theme: string;
}

export const DashboardCustomizer: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentLayout, setCurrentLayout] = useState<DashboardLayout | null>(null);
  const [availableWidgets] = useState<Omit<Widget, 'position' | 'visible'>[]>([
    { id: 'revenue', name: '<PERSON><PERSON>re d\'Affaires', type: 'metric', size: 'small', config: { color: 'blue' } },
    { id: 'sales-chart', name: '<PERSON><PERSON><PERSON><PERSON>', type: 'chart', size: 'large', config: { chartType: 'line' } },
    { id: 'top-customers', name: 'Top Clients', type: 'list', size: 'medium', config: { limit: 5 } },
    { id: 'recent-orders', name: 'Commandes Récentes', type: 'table', size: 'large', config: { limit: 10 } },
    { id: 'kpi-summary', name: 'Résumé KPI', type: 'metric', size: 'medium', config: { layout: 'grid' } },
    { id: 'performance', name: 'Performance Équipe', type: 'chart', size: 'medium', config: { chartType: 'bar' } }
  ]);

  const [savedLayouts, setSavedLayouts] = useState<DashboardLayout[]>([
    {
      id: 'default',
      name: 'Layout par Défaut',
      columns: 4,
      theme: 'light',
      widgets: [
        { id: 'revenue', name: 'Chiffre d\'Affaires', type: 'metric', size: 'small', position: { x: 0, y: 0 }, visible: true, config: { color: 'blue' } },
        { id: 'sales-chart', name: 'Graphique Ventes', type: 'chart', size: 'large', position: { x: 1, y: 0 }, visible: true, config: { chartType: 'line' } }
      ]
    }
  ]);

  useEffect(() => {
    // Charger les layouts sauvegardés depuis localStorage
    const saved = localStorage.getItem('dashboard-layouts');
    if (saved) {
      setSavedLayouts(JSON.parse(saved));
    }
  }, []);

  const saveLayout = (layout: DashboardLayout) => {
    const updated = savedLayouts.map(l => l.id === layout.id ? layout : l);
    if (!updated.find(l => l.id === layout.id)) {
      updated.push(layout);
    }
    setSavedLayouts(updated);
    localStorage.setItem('dashboard-layouts', JSON.stringify(updated));
  };

  const createNewLayout = () => {
    const newLayout: DashboardLayout = {
      id: `layout-${Date.now()}`,
      name: `Nouveau Layout ${savedLayouts.length + 1}`,
      columns: 4,
      theme: 'light',
      widgets: []
    };
    setCurrentLayout(newLayout);
  };

  const addWidget = (widgetTemplate: Omit<Widget, 'position' | 'visible'>) => {
    if (!currentLayout) return;

    const newWidget: Widget = {
      ...widgetTemplate,
      position: { x: 0, y: currentLayout.widgets.length },
      visible: true
    };

    setCurrentLayout({
      ...currentLayout,
      widgets: [...currentLayout.widgets, newWidget]
    });
  };

  const removeWidget = (widgetId: string) => {
    if (!currentLayout) return;

    setCurrentLayout({
      ...currentLayout,
      widgets: currentLayout.widgets.filter(w => w.id !== widgetId)
    });
  };

  const toggleWidgetVisibility = (widgetId: string) => {
    if (!currentLayout) return;

    setCurrentLayout({
      ...currentLayout,
      widgets: currentLayout.widgets.map(w =>
        w.id === widgetId ? { ...w, visible: !w.visible } : w
      )
    });
  };

  const updateWidgetSize = (widgetId: string, size: Widget['size']) => {
    if (!currentLayout) return;

    setCurrentLayout({
      ...currentLayout,
      widgets: currentLayout.widgets.map(w =>
        w.id === widgetId ? { ...w, size } : w
      )
    });
  };

  const getWidgetIcon = (type: Widget['type']) => {
    switch (type) {
      case 'metric': return '📊';
      case 'chart': return '📈';
      case 'table': return '📋';
      case 'list': return '📝';
      default: return '📊';
    }
  };

  const getSizeLabel = (size: Widget['size']) => {
    switch (size) {
      case 'small': return 'Petit';
      case 'medium': return 'Moyen';
      case 'large': return 'Grand';
      default: return 'Moyen';
    }
  };

  return (
    <>
      {/* Bouton d'ouverture */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 w-14 h-14 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors z-40"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        🎛️
      </motion.button>

      {/* Panel de customisation */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-50"
              onClick={() => setIsOpen(false)}
            />

            {/* Panel */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed right-0 top-0 h-full w-96 bg-white dark:bg-gray-800 shadow-xl z-50 overflow-y-auto"
            >
              <div className="p-6">
                {/* En-tête */}
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    🎨 Personnalisation
                  </h2>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    ✕
                  </button>
                </div>

                {/* Layouts sauvegardés */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                    Layouts Sauvegardés
                  </h3>
                  <div className="space-y-2">
                    {savedLayouts.map(layout => (
                      <motion.div
                        key={layout.id}
                        className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                        onClick={() => setCurrentLayout(layout)}
                        whileHover={{ scale: 1.02 }}
                      >
                        <div className="font-medium text-gray-900 dark:text-white">
                          {layout.name}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">
                          {layout.widgets.length} widgets • {layout.columns} colonnes
                        </div>
                      </motion.div>
                    ))}
                  </div>
                  
                  <button
                    onClick={createNewLayout}
                    className="w-full mt-3 p-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-300 hover:border-blue-500 hover:text-blue-500 transition-colors"
                  >
                    + Nouveau Layout
                  </button>
                </div>

                {/* Configuration du layout actuel */}
                {currentLayout && (
                  <>
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                        Configuration
                      </h3>
                      
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Nom du Layout
                          </label>
                          <input
                            type="text"
                            value={currentLayout.name}
                            onChange={(e) => setCurrentLayout({ ...currentLayout, name: e.target.value })}
                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Colonnes
                          </label>
                          <select
                            value={currentLayout.columns}
                            onChange={(e) => setCurrentLayout({ ...currentLayout, columns: parseInt(e.target.value) })}
                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          >
                            <option value={2}>2 colonnes</option>
                            <option value={3}>3 colonnes</option>
                            <option value={4}>4 colonnes</option>
                            <option value={6}>6 colonnes</option>
                          </select>
                        </div>
                      </div>

                      <button
                        onClick={() => saveLayout(currentLayout)}
                        className="w-full mt-3 p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        💾 Sauvegarder
                      </button>
                    </div>

                    {/* Widgets disponibles */}
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                        Ajouter des Widgets
                      </h3>
                      <div className="space-y-2">
                        {availableWidgets.map(widget => (
                          <motion.div
                            key={widget.id}
                            className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                          >
                            <div className="flex items-center gap-2">
                              <span className="text-lg">{getWidgetIcon(widget.type)}</span>
                              <div>
                                <div className="font-medium text-gray-900 dark:text-white">
                                  {widget.name}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-300">
                                  {getSizeLabel(widget.size)}
                                </div>
                              </div>
                            </div>
                            <button
                              onClick={() => addWidget(widget)}
                              className="p-1 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900 rounded transition-colors"
                            >
                              ➕
                            </button>
                          </motion.div>
                        ))}
                      </div>
                    </div>

                    {/* Widgets actuels */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                        Widgets Actuels
                      </h3>
                      <div className="space-y-2">
                        {currentLayout.widgets.map(widget => (
                          <motion.div
                            key={widget.id}
                            className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                            layout
                          >
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <span className="text-lg">{getWidgetIcon(widget.type)}</span>
                                <span className="font-medium text-gray-900 dark:text-white">
                                  {widget.name}
                                </span>
                              </div>
                              <div className="flex gap-1">
                                <button
                                  onClick={() => toggleWidgetVisibility(widget.id)}
                                  className={`p-1 rounded transition-colors ${
                                    widget.visible 
                                      ? 'text-green-600 hover:bg-green-100 dark:hover:bg-green-900' 
                                      : 'text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600'
                                  }`}
                                >
                                  {widget.visible ? '👁️' : '🙈'}
                                </button>
                                <button
                                  onClick={() => removeWidget(widget.id)}
                                  className="p-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900 rounded transition-colors"
                                >
                                  🗑️
                                </button>
                              </div>
                            </div>
                            
                            <div className="flex gap-1">
                              {(['small', 'medium', 'large'] as const).map(size => (
                                <button
                                  key={size}
                                  onClick={() => updateWidgetSize(widget.id, size)}
                                  className={`px-2 py-1 text-xs rounded transition-colors ${
                                    widget.size === size
                                      ? 'bg-blue-600 text-white'
                                      : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                                  }`}
                                >
                                  {getSizeLabel(size)}
                                </button>
                              ))}
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};
