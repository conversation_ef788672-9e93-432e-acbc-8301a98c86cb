import { api } from './client'
import type {
  LoginCredentials,
  LoginResponse,
  RefreshTokenResponse,
  User,
  ChangePasswordData,
  UserCreateData,
  UserUpdateData,
} from '@/types/auth'

export const authApi = {
  // Connexion
  login: (credentials: LoginCredentials): Promise<LoginResponse> =>
    api.post('/auth/login/', credentials),

  // Rafraîchissement du token
  refreshToken: (refreshToken: string): Promise<RefreshTokenResponse> =>
    api.post('/auth/token/refresh/', { refresh: refreshToken }),

  // Profil utilisateur
  getProfile: (): Promise<User> =>
    api.get('/auth/profile/'),

  updateProfile: (data: UserUpdateData): Promise<User> =>
    api.patch('/auth/profile/', data),

  // Changement de mot de passe
  changePassword: (data: ChangePasswordData): Promise<{ message: string }> =>
    api.post('/auth/change-password/', data),

  // Gestion des utilisateurs (admin)
  getUsers: (): Promise<User[]> =>
    api.get('/auth/users/'),

  createUser: (data: UserCreateData): Promise<User> =>
    api.post('/auth/users/', data),

  getUser: (userId: string): Promise<User> =>
    api.get(`/auth/users/${userId}/`),

  updateUser: (userId: string, data: UserUpdateData): Promise<User> =>
    api.patch(`/auth/users/${userId}/`, data),

  deleteUser: (userId: string): Promise<void> =>
    api.delete(`/auth/users/${userId}/`),

  // Gestion des rôles
  getRoles: () =>
    api.get('/auth/roles/'),

  createRole: (data: any) =>
    api.post('/auth/roles/', data),

  assignRole: (userId: string, roleId: string) =>
    api.post(`/auth/users/${userId}/roles/`, { role: roleId }),

  revokeRole: (userId: string, roleId: string) =>
    api.delete(`/auth/users/${userId}/roles/${roleId}/`),

  // Tenant
  getTenant: () =>
    api.get('/auth/tenant/'),

  updateTenant: (data: any) =>
    api.patch('/auth/tenant/', data),

  // Health check
  healthCheck: () =>
    api.get('/auth/health/'),
}
