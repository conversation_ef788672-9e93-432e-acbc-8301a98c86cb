"""
Services pour l'Agent Stock
Logique métier pour la gestion des stocks et inventaires
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg, F
from django.db import transaction
from decimal import Decimal

from core.models import Tenant, User
from agents.models import Agent
from agents.ai_service import ai_service
from .models import (
    Warehouse, Location, StockItem, StockLevel, StockMovement,
    StockReservation, StockInventory, StockInventoryLine
)

logger = logging.getLogger('agents.stock')


class StockService:
    """
    Service principal pour l'Agent Stock
    Gère toutes les opérations de stock et inventaires
    """

    def __init__(self, tenant: Tenant):
        self.tenant = tenant
        self.stock_agent = self._get_or_create_stock_agent()

    def _get_or_create_stock_agent(self) -> Agent:
        """Récupère ou crée l'agent Stock pour le tenant"""
        agent, created = Agent.objects.get_or_create(
            tenant=self.tenant,
            agent_type='stock',
            defaults={
                'name': 'Agent Stock',
                'description': 'Gestion des stocks et inventaires',
                'ai_enabled': True,
                'ai_model': 'gpt-4',
                'capabilities': [
                    'inventory_management',
                    'stock_movement_tracking',
                    'warehouse_optimization',
                    'stock_level_monitoring',
                    'abc_analysis',
                    'demand_forecasting',
                    'reorder_point_calculation'
                ]
            }
        )
        if created:
            logger.info(f"Agent Stock créé pour le tenant {self.tenant.name}")
        return agent

    def get_stock_dashboard(self) -> Dict[str, Any]:
        """Retourne les données du dashboard Stock"""

        # Articles en stock
        stock_items = StockItem.objects.filter(tenant=self.tenant, is_active=True)

        # Niveaux de stock
        stock_levels = StockLevel.objects.filter(
            item__tenant=self.tenant,
            quantity_on_hand__gt=0
        )

        # Entrepôts
        warehouses = Warehouse.objects.filter(tenant=self.tenant, is_active=True)

        # Mouvements récents
        recent_movements = StockMovement.objects.filter(
            tenant=self.tenant,
            movement_date__gte=timezone.now() - timedelta(days=30)
        )

        # Alertes de stock
        low_stock_items = self._get_low_stock_items()
        out_of_stock_items = self._get_out_of_stock_items()

        # Valeur totale du stock
        total_stock_value = stock_levels.aggregate(
            total=Sum('total_value')
        )['total'] or Decimal('0.00')

        # Rotation des stocks
        stock_turnover = self._calculate_stock_turnover()

        # Inventaires en cours
        active_inventories = StockInventory.objects.filter(
            tenant=self.tenant,
            status__in=['planned', 'in_progress']
        )

        return {
            'tenant': self.tenant.name,
            'timestamp': timezone.now().isoformat(),
            'items': {
                'total': stock_items.count(),
                'in_stock': stock_levels.values('item').distinct().count(),
                'low_stock': len(low_stock_items),
                'out_of_stock': len(out_of_stock_items)
            },
            'warehouses': {
                'total': warehouses.count(),
                'active': warehouses.filter(is_active=True).count()
            },
            'value': {
                'total_stock_value': float(total_stock_value),
                'average_turnover': stock_turnover.get('average', 0)
            },
            'movements': {
                'last_30_days': recent_movements.count(),
                'receipts': recent_movements.filter(movement_type='receipt').count(),
                'issues': recent_movements.filter(movement_type='issue').count()
            },
            'inventories': {
                'active': active_inventories.count(),
                'planned': active_inventories.filter(status='planned').count()
            },
            'alerts': {
                'low_stock': len(low_stock_items),
                'out_of_stock': len(out_of_stock_items),
                'expired_reservations': self._get_expired_reservations_count()
            },
            'recent_activities': self._get_recent_activities()
        }

    def _get_low_stock_items(self) -> List[Dict[str, Any]]:
        """Identifie les articles avec un stock faible"""
        low_stock_items = []

        stock_levels = StockLevel.objects.filter(
            item__tenant=self.tenant
        ).select_related('item', 'location')

        for level in stock_levels:
            if (level.item.minimum_stock > 0 and
                level.quantity_available <= level.item.minimum_stock):
                low_stock_items.append({
                    'item_id': str(level.item.id),
                    'sku': level.item.sku,
                    'name': level.item.name,
                    'location': level.location.code,
                    'current_stock': float(level.quantity_available),
                    'minimum_stock': float(level.item.minimum_stock),
                    'shortage': float(level.item.minimum_stock - level.quantity_available)
                })

        return low_stock_items

    def _get_out_of_stock_items(self) -> List[Dict[str, Any]]:
        """Identifie les articles en rupture de stock"""
        out_of_stock_items = []

        # Articles avec stock zéro mais qui ont eu des mouvements récents
        items_with_zero_stock = StockItem.objects.filter(
            tenant=self.tenant,
            is_active=True
        ).exclude(
            stock_levels__quantity_on_hand__gt=0
        ).filter(
            movements__movement_date__gte=timezone.now() - timedelta(days=90)
        ).distinct()

        for item in items_with_zero_stock:
            out_of_stock_items.append({
                'item_id': str(item.id),
                'sku': item.sku,
                'name': item.name,
                'last_movement': item.movements.order_by('-movement_date').first().movement_date.isoformat()
            })

        return out_of_stock_items

    def _calculate_stock_turnover(self) -> Dict[str, float]:
        """Calcule la rotation des stocks"""
        # Période d'analyse (12 mois)
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=365)

        # Mouvements de sortie sur la période
        issues = StockMovement.objects.filter(
            tenant=self.tenant,
            movement_type='issue',
            movement_date__date__gte=start_date,
            movement_date__date__lte=end_date
        )

        # Calcul par article
        turnover_rates = []

        for item in StockItem.objects.filter(tenant=self.tenant, is_active=True):
            # Quantité sortie sur la période
            total_issued = issues.filter(item=item).aggregate(
                total=Sum('quantity')
            )['total'] or Decimal('0.00')

            # Stock moyen
            current_stock = StockLevel.objects.filter(item=item).aggregate(
                total=Sum('quantity_on_hand')
            )['total'] or Decimal('0.00')

            if current_stock > 0:
                turnover = float(total_issued) / float(current_stock)
                turnover_rates.append(turnover)

        average_turnover = sum(turnover_rates) / len(turnover_rates) if turnover_rates else 0

        return {
            'average': round(average_turnover, 2),
            'items_analyzed': len(turnover_rates)
        }

    def _get_expired_reservations_count(self) -> int:
        """Compte les réservations expirées"""
        return StockReservation.objects.filter(
            tenant=self.tenant,
            status='active',
            expiry_date__lt=timezone.now()
        ).count()

    def _get_recent_activities(self) -> List[Dict[str, Any]]:
        """Récupère les activités récentes"""
        activities = []

        # Mouvements récents
        recent_movements = StockMovement.objects.filter(
            tenant=self.tenant,
            movement_date__gte=timezone.now() - timedelta(days=7)
        ).order_by('-movement_date')[:10]

        for movement in recent_movements:
            activities.append({
                'type': f'movement_{movement.movement_type}',
                'description': f"{movement.movement_type.title()}: {movement.item.sku} ({movement.quantity})",
                'date': movement.movement_date.isoformat(),
                'location': movement.location_to.code if movement.location_to else movement.location_from.code if movement.location_from else 'N/A'
            })

        return activities

    def create_stock_movement(self, movement_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée un mouvement de stock"""
        try:
            with transaction.atomic():
                # Générer le numéro de mouvement
                movement_number = self._generate_movement_number(movement_data['movement_type'])

                movement = StockMovement.objects.create(
                    tenant=self.tenant,
                    movement_number=movement_number,
                    item_id=movement_data['item_id'],
                    location_from_id=movement_data.get('location_from_id'),
                    location_to_id=movement_data.get('location_to_id'),
                    movement_type=movement_data['movement_type'],
                    movement_reason=movement_data['movement_reason'],
                    quantity=movement_data['quantity'],
                    unit_cost=movement_data.get('unit_cost'),
                    lot_number=movement_data.get('lot_number', ''),
                    serial_number=movement_data.get('serial_number', ''),
                    expiry_date=movement_data.get('expiry_date'),
                    reference_document=movement_data.get('reference_document', ''),
                    reference_type=movement_data.get('reference_type', ''),
                    user=user,
                    movement_date=movement_data.get('movement_date', timezone.now()),
                    comments=movement_data.get('comments', '')
                )

                # Mettre à jour les niveaux de stock
                self._update_stock_levels(movement)

                # Générer des recommandations IA si disponible
                if ai_service.is_available():
                    recommendations = self._generate_stock_recommendations(movement)
                    if recommendations:
                        self._apply_stock_recommendations(recommendations)

                return {
                    'success': True,
                    'movement': {
                        'id': str(movement.id),
                        'movement_number': movement.movement_number,
                        'movement_type': movement.movement_type,
                        'quantity': float(movement.quantity)
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_movement_number(self, movement_type: str) -> str:
        """Génère un numéro de mouvement unique"""
        current_year = timezone.now().year
        prefix_map = {
            'receipt': 'REC',
            'issue': 'OUT',
            'transfer': 'TRF',
            'adjustment': 'ADJ',
            'return': 'RET',
            'scrap': 'SCR',
            'production': 'PRD',
            'consumption': 'CON'
        }

        prefix = prefix_map.get(movement_type, 'MOV')

        last_movement = StockMovement.objects.filter(
            tenant=self.tenant,
            movement_number__startswith=f"{prefix}{current_year}"
        ).order_by('-movement_number').first()

        if last_movement:
            last_number = int(last_movement.movement_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"{prefix}{current_year}{new_number:04d}"

    def _update_stock_levels(self, movement: StockMovement):
        """Met à jour les niveaux de stock suite à un mouvement"""

        if movement.movement_type in ['receipt', 'return', 'production']:
            # Entrée de stock
            if movement.location_to:
                stock_level, created = StockLevel.objects.get_or_create(
                    item=movement.item,
                    location=movement.location_to,
                    defaults={
                        'quantity_on_hand': Decimal('0.000'),
                        'average_cost': movement.unit_cost or Decimal('0.00')
                    }
                )

                # Mise à jour du coût moyen (méthode FIFO simplifiée)
                if movement.unit_cost and stock_level.quantity_on_hand > 0:
                    total_value = (stock_level.quantity_on_hand * stock_level.average_cost) + (movement.quantity * movement.unit_cost)
                    total_quantity = stock_level.quantity_on_hand + movement.quantity
                    stock_level.average_cost = total_value / total_quantity
                elif movement.unit_cost:
                    stock_level.average_cost = movement.unit_cost

                stock_level.quantity_on_hand += movement.quantity
                stock_level.last_movement_date = movement.movement_date
                stock_level.save()

        elif movement.movement_type in ['issue', 'consumption', 'scrap']:
            # Sortie de stock
            if movement.location_from:
                try:
                    stock_level = StockLevel.objects.get(
                        item=movement.item,
                        location=movement.location_from
                    )
                    stock_level.quantity_on_hand -= movement.quantity
                    stock_level.last_movement_date = movement.movement_date
                    stock_level.save()
                except StockLevel.DoesNotExist:
                    logger.warning(f"Niveau de stock non trouvé pour {movement.item.sku} @ {movement.location_from.code}")

        elif movement.movement_type == 'transfer':
            # Transfert entre emplacements
            if movement.location_from and movement.location_to:
                # Sortie de l'emplacement source
                try:
                    source_level = StockLevel.objects.get(
                        item=movement.item,
                        location=movement.location_from
                    )
                    source_level.quantity_on_hand -= movement.quantity
                    source_level.last_movement_date = movement.movement_date
                    source_level.save()
                except StockLevel.DoesNotExist:
                    logger.warning(f"Niveau de stock source non trouvé pour {movement.item.sku}")

                # Entrée dans l'emplacement destination
                dest_level, created = StockLevel.objects.get_or_create(
                    item=movement.item,
                    location=movement.location_to,
                    defaults={
                        'quantity_on_hand': Decimal('0.000'),
                        'average_cost': movement.unit_cost or source_level.average_cost if 'source_level' in locals() else Decimal('0.00')
                    }
                )
                dest_level.quantity_on_hand += movement.quantity
                dest_level.last_movement_date = movement.movement_date
                dest_level.save()

    def _generate_stock_recommendations(self, movement: StockMovement) -> Dict[str, Any]:
        """Génère des recommandations IA basées sur un mouvement de stock"""
        try:
            # Analyser le contexte du mouvement
            item = movement.item
            current_stock = StockLevel.objects.filter(item=item).aggregate(
                total=Sum('quantity_on_hand')
            )['total'] or Decimal('0.00')

            context = {
                'movement': {
                    'type': movement.movement_type,
                    'quantity': float(movement.quantity),
                    'item_sku': item.sku,
                    'item_name': item.name
                },
                'stock_status': {
                    'current_stock': float(current_stock),
                    'minimum_stock': float(item.minimum_stock),
                    'reorder_point': float(item.reorder_point) if item.reorder_point else None
                }
            }

            prompt = f"""
            En tant qu'expert en gestion de stock, analyse ce mouvement et fournis des recommandations:

            Contexte: {context}

            Fournis des recommandations pour:
            1. Alertes de stock (rupture, stock faible)
            2. Actions de réapprovisionnement
            3. Optimisations d'emplacement
            4. Prévisions de demande

            Réponds au format JSON avec les clés: alerts, reorder_suggestions, optimization_tips, demand_forecast
            """

            ai_response = ai_service.generate_response(prompt, "stock", temperature=0.6)

            if ai_response.success:
                import json
                return json.loads(ai_response.content)

        except Exception as e:
            logger.error(f"Erreur lors de la génération de recommandations stock: {str(e)}")

        return {}

    def _apply_stock_recommendations(self, recommendations: Dict[str, Any]):
        """Applique les recommandations IA"""
        try:
            # Log des recommandations pour suivi
            logger.info(f"Recommandations stock générées: {recommendations}")

            # Dans un vrai système, on pourrait:
            # - Créer automatiquement des demandes d'achat
            # - Envoyer des notifications aux responsables
            # - Ajuster les seuils de réapprovisionnement

        except Exception as e:
            logger.error(f"Erreur lors de l'application des recommandations: {str(e)}")

    def create_stock_reservation(self, reservation_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée une réservation de stock"""
        try:
            with transaction.atomic():
                # Vérifier la disponibilité
                stock_level = StockLevel.objects.get(
                    item_id=reservation_data['item_id'],
                    location_id=reservation_data['location_id']
                )

                if stock_level.quantity_available < reservation_data['quantity_reserved']:
                    return {
                        'success': False,
                        'error': 'Stock insuffisant pour la réservation'
                    }

                # Générer le numéro de réservation
                reservation_number = self._generate_reservation_number()

                reservation = StockReservation.objects.create(
                    tenant=self.tenant,
                    reservation_number=reservation_number,
                    item_id=reservation_data['item_id'],
                    location_id=reservation_data['location_id'],
                    reservation_type=reservation_data['reservation_type'],
                    quantity_reserved=reservation_data['quantity_reserved'],
                    reservation_date=reservation_data.get('reservation_date', timezone.now()),
                    required_date=reservation_data['required_date'],
                    expiry_date=reservation_data.get('expiry_date'),
                    reference_document=reservation_data.get('reference_document', ''),
                    user=user,
                    comments=reservation_data.get('comments', '')
                )

                # Mettre à jour le niveau de stock
                stock_level.quantity_reserved += reservation.quantity_reserved
                stock_level.save()

                return {
                    'success': True,
                    'reservation': {
                        'id': str(reservation.id),
                        'reservation_number': reservation.reservation_number,
                        'quantity_reserved': float(reservation.quantity_reserved)
                    }
                }

        except StockLevel.DoesNotExist:
            return {
                'success': False,
                'error': 'Niveau de stock non trouvé'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_reservation_number(self) -> str:
        """Génère un numéro de réservation unique"""
        current_year = timezone.now().year
        last_reservation = StockReservation.objects.filter(
            tenant=self.tenant,
            reservation_number__startswith=f"RES{current_year}"
        ).order_by('-reservation_number').first()

        if last_reservation:
            last_number = int(last_reservation.reservation_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"RES{current_year}{new_number:04d}"

    def create_inventory(self, inventory_data: Dict[str, Any], user: User) -> Dict[str, Any]:
        """Crée un inventaire physique"""
        try:
            with transaction.atomic():
                # Générer le numéro d'inventaire
                inventory_number = self._generate_inventory_number()

                inventory = StockInventory.objects.create(
                    tenant=self.tenant,
                    inventory_number=inventory_number,
                    inventory_type=inventory_data['inventory_type'],
                    warehouse_id=inventory_data['warehouse_id'],
                    planned_date=inventory_data['planned_date'],
                    supervisor=user,
                    description=inventory_data.get('description', ''),
                    comments=inventory_data.get('comments', '')
                )

                # Ajouter les emplacements et articles si spécifiés
                if 'location_ids' in inventory_data:
                    inventory.locations.set(inventory_data['location_ids'])

                if 'item_ids' in inventory_data:
                    inventory.items.set(inventory_data['item_ids'])

                # Générer les lignes d'inventaire
                self._generate_inventory_lines(inventory)

                return {
                    'success': True,
                    'inventory': {
                        'id': str(inventory.id),
                        'inventory_number': inventory.inventory_number,
                        'status': inventory.status,
                        'lines_count': inventory.lines.count()
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_inventory_number(self) -> str:
        """Génère un numéro d'inventaire unique"""
        current_year = timezone.now().year
        last_inventory = StockInventory.objects.filter(
            tenant=self.tenant,
            inventory_number__startswith=f"INV{current_year}"
        ).order_by('-inventory_number').first()

        if last_inventory:
            last_number = int(last_inventory.inventory_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"INV{current_year}{new_number:04d}"

    def _generate_inventory_lines(self, inventory: StockInventory):
        """Génère les lignes d'inventaire basées sur les stocks actuels"""

        # Déterminer les niveaux de stock à inclure
        stock_levels_query = StockLevel.objects.filter(
            location__warehouse=inventory.warehouse,
            quantity_on_hand__gt=0
        )

        # Filtrer par emplacements si spécifiés
        if inventory.locations.exists():
            stock_levels_query = stock_levels_query.filter(
                location__in=inventory.locations.all()
            )

        # Filtrer par articles si spécifiés
        if inventory.items.exists():
            stock_levels_query = stock_levels_query.filter(
                item__in=inventory.items.all()
            )

        # Créer les lignes d'inventaire
        for stock_level in stock_levels_query:
            StockInventoryLine.objects.create(
                inventory=inventory,
                item=stock_level.item,
                location=stock_level.location,
                system_quantity=stock_level.quantity_on_hand,
                unit_cost=stock_level.average_cost
            )

    def process_inventory_count(self, inventory_line_id: str, counted_quantity: Decimal, user: User) -> Dict[str, Any]:
        """Traite un comptage d'inventaire"""
        try:
            with transaction.atomic():
                line = StockInventoryLine.objects.get(id=inventory_line_id)

                line.counted_quantity = counted_quantity
                line.counter = user
                line.count_date = timezone.now()
                line.save()  # Les calculs d'ajustement se font automatiquement

                # Mettre à jour les statistiques de l'inventaire
                inventory = line.inventory
                inventory.total_items_counted = inventory.lines.filter(is_counted=True).count()
                inventory.total_discrepancies = inventory.lines.filter(
                    adjustment_quantity__ne=Decimal('0.000')
                ).count()
                inventory.total_value_adjustment = inventory.lines.aggregate(
                    total=Sum('adjustment_value')
                )['total'] or Decimal('0.00')
                inventory.save()

                return {
                    'success': True,
                    'line': {
                        'id': str(line.id),
                        'adjustment_quantity': float(line.adjustment_quantity),
                        'adjustment_value': float(line.adjustment_value)
                    }
                }

        except StockInventoryLine.DoesNotExist:
            return {
                'success': False,
                'error': 'Ligne d\'inventaire non trouvée'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def validate_inventory(self, inventory_id: str, user: User) -> Dict[str, Any]:
        """Valide un inventaire et applique les ajustements"""
        try:
            with transaction.atomic():
                inventory = StockInventory.objects.get(id=inventory_id, tenant=self.tenant)

                if inventory.status != 'completed':
                    return {
                        'success': False,
                        'error': 'L\'inventaire doit être terminé avant validation'
                    }

                # Appliquer les ajustements
                adjustments_applied = 0

                for line in inventory.lines.filter(adjustment_quantity__ne=Decimal('0.000')):
                    # Créer un mouvement d'ajustement
                    movement_number = self._generate_movement_number('adjustment')

                    movement = StockMovement.objects.create(
                        tenant=self.tenant,
                        movement_number=movement_number,
                        item=line.item,
                        location_to=line.location if line.adjustment_quantity > 0 else None,
                        location_from=line.location if line.adjustment_quantity < 0 else None,
                        movement_type='adjustment',
                        movement_reason='adjustment',
                        quantity=abs(line.adjustment_quantity),
                        unit_cost=line.unit_cost,
                        reference_document=inventory.inventory_number,
                        reference_type='inventory',
                        user=user,
                        movement_date=timezone.now(),
                        comments=f'Ajustement inventaire {inventory.inventory_number}'
                    )

                    # Mettre à jour le niveau de stock
                    self._update_stock_levels(movement)

                    # Marquer la ligne comme ajustée
                    line.is_adjusted = True
                    line.save()

                    adjustments_applied += 1

                # Marquer l'inventaire comme validé
                inventory.status = 'validated'
                inventory.save()

                return {
                    'success': True,
                    'inventory': {
                        'id': str(inventory.id),
                        'status': inventory.status,
                        'adjustments_applied': adjustments_applied,
                        'total_value_adjustment': float(inventory.total_value_adjustment)
                    }
                }

        except StockInventory.DoesNotExist:
            return {
                'success': False,
                'error': 'Inventaire non trouvé'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def analyze_stock_performance(self) -> Dict[str, Any]:
        """Analyse les performances du stock"""

        # Analyse ABC
        abc_analysis = self._perform_abc_analysis()

        # Rotation des stocks
        turnover_analysis = self._analyze_stock_turnover()

        # Articles à rotation lente
        slow_moving_items = self._identify_slow_moving_items()

        # Articles obsolètes
        obsolete_items = self._identify_obsolete_items()

        # Analyse de la couverture de stock
        coverage_analysis = self._analyze_stock_coverage()

        return {
            'abc_analysis': abc_analysis,
            'turnover_analysis': turnover_analysis,
            'slow_moving_items': slow_moving_items,
            'obsolete_items': obsolete_items,
            'coverage_analysis': coverage_analysis,
            'timestamp': timezone.now().isoformat()
        }

    def _perform_abc_analysis(self) -> Dict[str, Any]:
        """Effectue une analyse ABC des articles"""

        # Calculer la valeur de consommation annuelle pour chaque article
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=365)

        items_value = []

        for item in StockItem.objects.filter(tenant=self.tenant, is_active=True):
            # Consommation annuelle
            annual_consumption = StockMovement.objects.filter(
                item=item,
                movement_type='issue',
                movement_date__date__gte=start_date,
                movement_date__date__lte=end_date
            ).aggregate(total=Sum('quantity'))['total'] or Decimal('0.00')

            # Coût unitaire moyen
            avg_cost = StockLevel.objects.filter(item=item).aggregate(
                avg=Avg('average_cost')
            )['avg'] or Decimal('0.00')

            annual_value = float(annual_consumption * avg_cost)

            if annual_value > 0:
                items_value.append({
                    'item_id': str(item.id),
                    'sku': item.sku,
                    'name': item.name,
                    'annual_value': annual_value
                })

        # Trier par valeur décroissante
        items_value.sort(key=lambda x: x['annual_value'], reverse=True)

        # Calculer les pourcentages cumulés
        total_value = sum(item['annual_value'] for item in items_value)
        cumulative_percentage = 0

        abc_classification = {'A': [], 'B': [], 'C': []}

        for item in items_value:
            percentage = (item['annual_value'] / total_value * 100) if total_value > 0 else 0
            cumulative_percentage += percentage

            if cumulative_percentage <= 80:
                classification = 'A'
            elif cumulative_percentage <= 95:
                classification = 'B'
            else:
                classification = 'C'

            item['classification'] = classification
            item['cumulative_percentage'] = round(cumulative_percentage, 2)
            abc_classification[classification].append(item)

        return {
            'total_items': len(items_value),
            'total_value': total_value,
            'classification': {
                'A': {'count': len(abc_classification['A']), 'items': abc_classification['A'][:10]},  # Top 10
                'B': {'count': len(abc_classification['B']), 'items': abc_classification['B'][:5]},   # Top 5
                'C': {'count': len(abc_classification['C']), 'items': abc_classification['C'][:5]}    # Top 5
            }
        }

    def _analyze_stock_turnover(self) -> Dict[str, Any]:
        """Analyse détaillée de la rotation des stocks"""

        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=365)

        turnover_data = []

        for item in StockItem.objects.filter(tenant=self.tenant, is_active=True):
            # Stock moyen
            current_stock = StockLevel.objects.filter(item=item).aggregate(
                total=Sum('quantity_on_hand')
            )['total'] or Decimal('0.00')

            # Consommation annuelle
            annual_consumption = StockMovement.objects.filter(
                item=item,
                movement_type='issue',
                movement_date__date__gte=start_date,
                movement_date__date__lte=end_date
            ).aggregate(total=Sum('quantity'))['total'] or Decimal('0.00')

            if current_stock > 0:
                turnover_rate = float(annual_consumption) / float(current_stock)
                days_of_stock = 365 / turnover_rate if turnover_rate > 0 else 999

                turnover_data.append({
                    'item_id': str(item.id),
                    'sku': item.sku,
                    'name': item.name,
                    'current_stock': float(current_stock),
                    'annual_consumption': float(annual_consumption),
                    'turnover_rate': round(turnover_rate, 2),
                    'days_of_stock': round(days_of_stock, 1)
                })

        # Statistiques globales
        if turnover_data:
            avg_turnover = sum(item['turnover_rate'] for item in turnover_data) / len(turnover_data)
            avg_days_stock = sum(item['days_of_stock'] for item in turnover_data) / len(turnover_data)
        else:
            avg_turnover = 0
            avg_days_stock = 0

        # Trier par taux de rotation
        turnover_data.sort(key=lambda x: x['turnover_rate'], reverse=True)

        return {
            'average_turnover_rate': round(avg_turnover, 2),
            'average_days_of_stock': round(avg_days_stock, 1),
            'total_items': len(turnover_data),
            'high_turnover': turnover_data[:10],  # Top 10 rotation rapide
            'low_turnover': turnover_data[-10:]   # Top 10 rotation lente
        }

    def _identify_slow_moving_items(self) -> List[Dict[str, Any]]:
        """Identifie les articles à rotation lente"""

        # Articles sans mouvement depuis 90 jours
        cutoff_date = timezone.now() - timedelta(days=90)

        slow_moving = []

        for item in StockItem.objects.filter(tenant=self.tenant, is_active=True):
            last_movement = StockMovement.objects.filter(
                item=item,
                movement_type='issue'
            ).order_by('-movement_date').first()

            current_stock = StockLevel.objects.filter(item=item).aggregate(
                total=Sum('quantity_on_hand')
            )['total'] or Decimal('0.00')

            if current_stock > 0 and (not last_movement or last_movement.movement_date < cutoff_date):
                days_since_movement = (timezone.now() - last_movement.movement_date).days if last_movement else 999

                slow_moving.append({
                    'item_id': str(item.id),
                    'sku': item.sku,
                    'name': item.name,
                    'current_stock': float(current_stock),
                    'days_since_last_movement': days_since_movement,
                    'last_movement_date': last_movement.movement_date.isoformat() if last_movement else None
                })

        # Trier par nombre de jours sans mouvement
        slow_moving.sort(key=lambda x: x['days_since_last_movement'], reverse=True)

        return slow_moving

    def _identify_obsolete_items(self) -> List[Dict[str, Any]]:
        """Identifie les articles obsolètes"""

        # Articles sans mouvement depuis 180 jours avec stock
        cutoff_date = timezone.now() - timedelta(days=180)

        obsolete = []

        for item in StockItem.objects.filter(tenant=self.tenant, is_active=True):
            last_movement = StockMovement.objects.filter(item=item).order_by('-movement_date').first()

            current_stock = StockLevel.objects.filter(item=item).aggregate(
                total=Sum('quantity_on_hand')
            )['total'] or Decimal('0.00')

            stock_value = StockLevel.objects.filter(item=item).aggregate(
                total=Sum('total_value')
            )['total'] or Decimal('0.00')

            if (current_stock > 0 and
                (not last_movement or last_movement.movement_date < cutoff_date)):

                obsolete.append({
                    'item_id': str(item.id),
                    'sku': item.sku,
                    'name': item.name,
                    'current_stock': float(current_stock),
                    'stock_value': float(stock_value),
                    'days_since_last_movement': (timezone.now() - last_movement.movement_date).days if last_movement else 999
                })

        # Trier par valeur de stock décroissante
        obsolete.sort(key=lambda x: x['stock_value'], reverse=True)

        return obsolete

    def _analyze_stock_coverage(self) -> Dict[str, Any]:
        """Analyse la couverture de stock"""

        coverage_data = []

        for item in StockItem.objects.filter(tenant=self.tenant, is_active=True):
            # Consommation moyenne mensuelle
            monthly_consumption = StockMovement.objects.filter(
                item=item,
                movement_type='issue',
                movement_date__gte=timezone.now() - timedelta(days=90)
            ).aggregate(total=Sum('quantity'))['total'] or Decimal('0.00')

            monthly_avg = float(monthly_consumption) / 3  # Moyenne sur 3 mois

            current_stock = StockLevel.objects.filter(item=item).aggregate(
                total=Sum('quantity_on_hand')
            )['total'] or Decimal('0.00')

            if monthly_avg > 0:
                months_of_coverage = float(current_stock) / monthly_avg
            else:
                months_of_coverage = 999 if current_stock > 0 else 0

            coverage_data.append({
                'item_id': str(item.id),
                'sku': item.sku,
                'name': item.name,
                'current_stock': float(current_stock),
                'monthly_consumption': round(monthly_avg, 2),
                'months_of_coverage': round(months_of_coverage, 1)
            })

        # Statistiques
        if coverage_data:
            avg_coverage = sum(item['months_of_coverage'] for item in coverage_data if item['months_of_coverage'] < 999) / len([item for item in coverage_data if item['months_of_coverage'] < 999])
        else:
            avg_coverage = 0

        # Catégoriser par couverture
        low_coverage = [item for item in coverage_data if item['months_of_coverage'] < 1]
        high_coverage = [item for item in coverage_data if item['months_of_coverage'] > 6 and item['months_of_coverage'] < 999]

        return {
            'average_coverage_months': round(avg_coverage, 1),
            'total_items': len(coverage_data),
            'low_coverage': {
                'count': len(low_coverage),
                'items': sorted(low_coverage, key=lambda x: x['months_of_coverage'])[:10]
            },
            'high_coverage': {
                'count': len(high_coverage),
                'items': sorted(high_coverage, key=lambda x: x['months_of_coverage'], reverse=True)[:10]
            }
        }

    def generate_stock_insights(self) -> List[Dict[str, Any]]:
        """Génère des insights IA sur la gestion des stocks"""
        insights = []

        try:
            if not ai_service.is_available():
                return insights

            # Récupérer les données d'analyse
            performance_data = self.analyze_stock_performance()
            dashboard_data = self.get_stock_dashboard()

            context = {
                'stock_summary': dashboard_data,
                'performance': performance_data
            }

            prompt = f"""
            En tant qu'expert en gestion de stock, analyse ces données et fournis des insights:

            Données: {context}

            Identifie:
            1. Les problèmes critiques de stock
            2. Les opportunités d'optimisation
            3. Les recommandations d'actions
            4. Les prévisions de tendances

            Pour chaque insight, fournis:
            - type: "critical", "warning", "opportunity", "info"
            - priority: "high", "medium", "low"
            - title: titre court
            - description: description détaillée
            - recommendation: action recommandée

            Réponds au format JSON avec une liste d'insights.
            """

            ai_response = ai_service.generate_response(prompt, "stock", temperature=0.7)

            if ai_response.success:
                import json
                ai_insights = json.loads(ai_response.content)

                for insight in ai_insights:
                    insights.append({
                        'type': insight.get('type', 'info'),
                        'priority': insight.get('priority', 'medium'),
                        'title': insight.get('title', ''),
                        'description': insight.get('description', ''),
                        'recommendation': insight.get('recommendation', ''),
                        'generated_at': timezone.now().isoformat()
                    })

        except Exception as e:
            logger.error(f"Erreur lors de la génération d'insights stock: {str(e)}")

        return insights
