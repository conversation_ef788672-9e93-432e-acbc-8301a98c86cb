#!/usr/bin/env python3
"""
Serveur de test simple pour démontrer l'ERP HUB
"""
from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse

class ERPHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """Gérer les requêtes GET"""
        path = urllib.parse.urlparse(self.path).path
        
        # Headers CORS
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
        
        # Routes de l'API
        if path == '/api/health/':
            response = {
                'status': 'healthy',
                'message': 'ERP HUB API opérationnel',
                'version': '1.0.0',
                'agents': [
                    'manager', 'hr', 'sales', 'purchase', 'logistics',
                    'stock', 'accounting', 'finance', 'crm', 'bi'
                ]
            }
        elif path == '/api/status/':
            response = {
                'system': 'ERP HUB',
                'status': 'running',
                'agents_count': 10,
                'features': [
                    'Multi-tenant architecture',
                    'JWT Authentication',
                    'Role-based access control',
                    'AI-powered insights',
                    'Real-time analytics',
                    'Automated workflows'
                ]
            }
        elif path.startswith('/api/agents/'):
            agent_name = path.split('/')[-2] if path.endswith('/') else path.split('/')[-1]
            response = {
                'agent': agent_name,
                'status': 'active',
                'description': f'Agent {agent_name.upper()} opérationnel',
                'capabilities': [
                    'Data processing',
                    'AI insights',
                    'Automated workflows',
                    'Real-time monitoring'
                ],
                'endpoints': [
                    f'/api/agents/{agent_name}/status/',
                    f'/api/agents/{agent_name}/dashboard/',
                    f'/api/agents/{agent_name}/analytics/'
                ]
            }
        elif path == '/api/dashboard/':
            response = {
                'dashboard': 'ERP HUB Main Dashboard',
                'metrics': {
                    'total_agents': 10,
                    'active_users': 25,
                    'processed_transactions': 1247,
                    'system_health': '98%'
                },
                'agents_status': {
                    'manager': 'active',
                    'hr': 'active',
                    'sales': 'active',
                    'purchase': 'active',
                    'logistics': 'active',
                    'stock': 'active',
                    'accounting': 'active',
                    'finance': 'active',
                    'crm': 'active',
                    'bi': 'active'
                }
            }
        elif path == '/':
            response = {
                'message': 'Bienvenue sur ERP HUB API',
                'version': '1.0.0',
                'documentation': '/docs/',
                'health_check': '/api/health/',
                'dashboard': '/api/dashboard/',
                'agents': '/api/agents/'
            }
        else:
            response = {
                'error': 'Endpoint non trouvé',
                'available_endpoints': [
                    '/api/health/',
                    '/api/status/',
                    '/api/dashboard/',
                    '/api/agents/{agent_name}/'
                ]
            }
        
        # Envoyer la réponse JSON
        self.wfile.write(json.dumps(response, indent=2, ensure_ascii=False).encode('utf-8'))
    
    def do_OPTIONS(self):
        """Gérer les requêtes OPTIONS pour CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def log_message(self, format, *args):
        """Log personnalisé"""
        print(f"🌐 {self.address_string()} - {format % args}")

def run_server():
    """Démarrer le serveur"""
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, ERPHandler)
    
    print("🚀 ERP HUB - Serveur de Démonstration")
    print("=" * 50)
    print(f"🌐 Serveur démarré sur http://localhost:8000")
    print(f"📚 API Health: http://localhost:8000/api/health/")
    print(f"📊 Dashboard: http://localhost:8000/api/dashboard/")
    print(f"🤖 Agents: http://localhost:8000/api/agents/manager/")
    print(f"📖 Documentation: Endpoints disponibles via l'API")
    print("\n🎯 Fonctionnalités démontrées:")
    print("   ✅ 10 agents spécialisés")
    print("   ✅ API REST complète")
    print("   ✅ Architecture multi-tenant")
    print("   ✅ Intelligence artificielle")
    print("   ✅ Monitoring en temps réel")
    print("\n🛑 Appuyez sur Ctrl+C pour arrêter")
    print("-" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Serveur arrêté")
        httpd.server_close()

if __name__ == "__main__":
    run_server()
