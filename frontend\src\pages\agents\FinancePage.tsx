import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Banknote,
  TrendingUp,
  TrendingDown,
  DollarSign,
  PieChart,
  AlertTriangle,
  CheckCircle,
  Clock,
  CreditCard,
  Building2,
  BarChart3,
  Target,
  Lightbulb,
  RefreshCw,
  Wallet,
  LineChart,
  Calculator
} from 'lucide-react';

interface FinanceDashboard {
  tenant: string;
  timestamp: string;
  treasury_position: {
    total_cash: number;
    available_cash: number;
    cash_by_currency: Record<string, {
      current_balance: number;
      available_balance: number;
      accounts_count: number;
    }>;
    calculation_date: string;
  };
  bank_accounts: {
    total: number;
    active: number;
    total_balance: number;
    available_balance: number;
    overdrawn_accounts: number;
  };
  transactions: {
    total_recent: number;
    pending: number;
    processed: number;
    total_inflows: number;
    total_outflows: number;
  };
  investments: {
    total: number;
    total_value: number;
    total_gain_loss: number;
    profitable_count: number;
    by_type: Record<string, {
      count: number;
      total_value: number;
      total_gain_loss: number;
    }>;
  };
  loans: {
    total: number;
    total_principal: number;
    total_outstanding: number;
    total_paid: number;
    by_type: Record<string, {
      count: number;
      total_principal: number;
      total_outstanding: number;
    }>;
  };
  forecasts: {
    total: number;
    active: number;
    latest_forecast: {
      name: string;
      period: string;
      opening_balance: number;
      total_inflows: number;
      total_outflows: number;
      closing_balance: number;
      net_cash_flow: number;
    } | null;
  };
  liquidity: {
    current_cash: number;
    monthly_inflows: number;
    monthly_outflows: number;
    net_cash_flow: number;
    cash_runway_days: number;
    liquidity_level: string;
    analysis_period: {
      start_date: string;
      end_date: string;
    };
  };
  ratios: Array<{
    name: string;
    category: string;
    value: number;
    calculation_date: string;
    variance_from_target: number | null;
  }>;
  alerts: {
    low_cash_accounts: number;
    overdue_loans: number;
    underperforming_investments: number;
    covenant_breaches: number;
    pending_transactions: number;
  };
  recent_activities: Array<{
    type: string;
    description: string;
    date: string;
    amount: number;
    currency: string;
    account: string;
    status: string;
  }>;
}

interface BankAccount {
  id: string;
  account_name: string;
  account_number: string;
  bank_name: string;
  account_type: string;
  currency: string;
  current_balance: number;
  available_balance: number;
  status: string;
}

interface Investment {
  id: string;
  name: string;
  investment_code: string;
  investment_type: string;
  initial_amount: number;
  current_value: number;
  gain_loss: number;
  gain_loss_percentage: number;
  risk_level: number;
  status: string;
}

interface Loan {
  id: string;
  loan_name: string;
  loan_number: string;
  loan_type: string;
  lender_name: string;
  principal_amount: number;
  outstanding_balance: number;
  interest_rate: number;
  maturity_date: string;
  status: string;
}

const FinancePage: React.FC = () => {
  const [dashboard, setDashboard] = useState<FinanceDashboard | null>(null);
  const [accounts, setAccounts] = useState<BankAccount[]>([]);
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [loans, setLoans] = useState<Loan[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchDashboard();
    if (activeTab === 'accounts') fetchAccounts();
    if (activeTab === 'investments') fetchInvestments();
    if (activeTab === 'loans') fetchLoans();
  }, [activeTab]);

  const fetchDashboard = async () => {
    try {
      const response = await fetch('/api/agents/finance/dashboard/');
      if (response.ok) {
        const data = await response.json();
        setDashboard(data);
      }
    } catch (error) {
      console.error('Erreur lors du chargement du dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAccounts = async () => {
    try {
      const response = await fetch('/api/agents/finance/accounts/');
      if (response.ok) {
        const data = await response.json();
        setAccounts(data.results || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des comptes:', error);
    }
  };

  const fetchInvestments = async () => {
    try {
      const response = await fetch('/api/agents/finance/investments/');
      if (response.ok) {
        const data = await response.json();
        setInvestments(data.results || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des investissements:', error);
    }
  };

  const fetchLoans = async () => {
    try {
      const response = await fetch('/api/agents/finance/loans/');
      if (response.ok) {
        const data = await response.json();
        setLoans(data.results || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des emprunts:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: 'Actif', variant: 'default' as const },
      inactive: { label: 'Inactif', variant: 'secondary' as const },
      closed: { label: 'Fermé', variant: 'secondary' as const },
      frozen: { label: 'Gelé', variant: 'destructive' as const },
      pending: { label: 'En attente', variant: 'secondary' as const },
      processed: { label: 'Traité', variant: 'default' as const },
      sold: { label: 'Vendu', variant: 'secondary' as const },
      matured: { label: 'Échu', variant: 'secondary' as const },
      fully_paid: { label: 'Remboursé', variant: 'default' as const },
      defaulted: { label: 'En défaut', variant: 'destructive' as const }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || 
                  { label: status, variant: 'secondary' as const };
    
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getLiquidityBadge = (level: string) => {
    const levelConfig = {
      excellent: { label: 'Excellent', variant: 'default' as const, color: 'text-green-600' },
      good: { label: 'Bon', variant: 'default' as const, color: 'text-green-600' },
      adequate: { label: 'Adéquat', variant: 'secondary' as const, color: 'text-yellow-600' },
      low: { label: 'Faible', variant: 'secondary' as const, color: 'text-orange-600' },
      critical: { label: 'Critique', variant: 'destructive' as const, color: 'text-red-600' }
    };

    const config = levelConfig[level as keyof typeof levelConfig] || 
                  { label: level, variant: 'secondary' as const, color: 'text-gray-600' };
    
    return <Badge variant={config.variant} className={config.color}>{config.label}</Badge>;
  };

  const getRiskLevelBadge = (level: number) => {
    const riskConfig = {
      1: { label: 'Très faible', color: 'text-green-600' },
      2: { label: 'Faible', color: 'text-green-500' },
      3: { label: 'Modéré', color: 'text-yellow-600' },
      4: { label: 'Élevé', color: 'text-orange-600' },
      5: { label: 'Très élevé', color: 'text-red-600' }
    };

    const config = riskConfig[level as keyof typeof riskConfig] || 
                  { label: `Niveau ${level}`, color: 'text-gray-600' };
    
    return <span className={`text-sm font-medium ${config.color}`}>{config.label}</span>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Agent Finance</h1>
          <p className="text-muted-foreground">
            Trésorerie et analyses financières
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Opérationnel
          </Badge>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Vue d'ensemble
          </TabsTrigger>
          <TabsTrigger value="accounts" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Comptes
          </TabsTrigger>
          <TabsTrigger value="investments" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Investissements
          </TabsTrigger>
          <TabsTrigger value="loans" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Emprunts
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {dashboard && (
            <>
              {/* Position de trésorerie */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wallet className="h-5 w-5" />
                    Position de Trésorerie
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Trésorerie totale</p>
                      <p className="text-2xl font-bold">{formatCurrency(dashboard.treasury_position.total_cash)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Disponible</p>
                      <p className="text-2xl font-bold text-green-600">{formatCurrency(dashboard.treasury_position.available_cash)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Métriques principales */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Comptes Bancaires</CardTitle>
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{dashboard.bank_accounts.active}</div>
                    <p className="text-xs text-muted-foreground">
                      {dashboard.bank_accounts.overdrawn_accounts > 0 && 
                        `${dashboard.bank_accounts.overdrawn_accounts} à découvert`
                      }
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Investissements</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(dashboard.investments.total_value)}</div>
                    <p className={`text-xs ${dashboard.investments.total_gain_loss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {dashboard.investments.total_gain_loss >= 0 ? '+' : ''}{formatCurrency(dashboard.investments.total_gain_loss)}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Emprunts</CardTitle>
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(dashboard.loans.total_outstanding)}</div>
                    <p className="text-xs text-muted-foreground">
                      sur {formatCurrency(dashboard.loans.total_principal)}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Liquidité</CardTitle>
                    <LineChart className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{dashboard.liquidity.cash_runway_days.toFixed(0)} jours</div>
                    <div className="mt-1">
                      {getLiquidityBadge(dashboard.liquidity.liquidity_level)}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Alertes */}
              {(dashboard.alerts.low_cash_accounts > 0 || 
                dashboard.alerts.overdue_loans > 0 || 
                dashboard.alerts.underperforming_investments > 0 || 
                dashboard.alerts.pending_transactions > 0) && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-orange-800">
                      <AlertTriangle className="h-5 w-5" />
                      Alertes Financières
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {dashboard.alerts.low_cash_accounts > 0 && (
                      <div className="flex items-center justify-between text-sm">
                        <span>Comptes avec trésorerie faible</span>
                        <Badge variant="outline">{dashboard.alerts.low_cash_accounts}</Badge>
                      </div>
                    )}
                    {dashboard.alerts.overdue_loans > 0 && (
                      <div className="flex items-center justify-between text-sm">
                        <span>Emprunts en retard</span>
                        <Badge variant="outline">{dashboard.alerts.overdue_loans}</Badge>
                      </div>
                    )}
                    {dashboard.alerts.underperforming_investments > 0 && (
                      <div className="flex items-center justify-between text-sm">
                        <span>Investissements sous-performants</span>
                        <Badge variant="outline">{dashboard.alerts.underperforming_investments}</Badge>
                      </div>
                    )}
                    {dashboard.alerts.pending_transactions > 0 && (
                      <div className="flex items-center justify-between text-sm">
                        <span>Transactions en attente</span>
                        <Badge variant="outline">{dashboard.alerts.pending_transactions}</Badge>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Flux de trésorerie récent */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <LineChart className="h-5 w-5" />
                    Flux de Trésorerie (30 derniers jours)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">Entrées</p>
                      <p className="text-xl font-bold text-green-600">
                        {formatCurrency(dashboard.liquidity.monthly_inflows)}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">Sorties</p>
                      <p className="text-xl font-bold text-red-600">
                        {formatCurrency(dashboard.liquidity.monthly_outflows)}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">Net</p>
                      <p className={`text-xl font-bold ${dashboard.liquidity.net_cash_flow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {dashboard.liquidity.net_cash_flow >= 0 ? '+' : ''}{formatCurrency(dashboard.liquidity.net_cash_flow)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Activités récentes */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Activités Récentes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {dashboard.recent_activities.slice(0, 5).map((activity, index) => (
                      <div key={index} className="flex items-center justify-between border-b pb-2 last:border-b-0">
                        <div className="flex-1">
                          <p className="text-sm font-medium">{activity.description}</p>
                          <p className="text-xs text-muted-foreground">
                            {activity.account} • {new Date(activity.date).toLocaleDateString('fr-FR')}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">
                            {formatCurrency(activity.amount)}
                          </p>
                          {getStatusBadge(activity.status)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="accounts" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Comptes Bancaires</h2>
            <Button>
              <Building2 className="h-4 w-4 mr-2" />
              Nouveau Compte
            </Button>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {accounts.map((account) => (
              <Card key={account.id}>
                <CardHeader>
                  <CardTitle className="text-lg">{account.account_name}</CardTitle>
                  <CardDescription>
                    {account.bank_name} • {account.account_type}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Numéro</span>
                    <span className="font-mono">{account.account_number}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Solde actuel</span>
                    <span className={`font-bold ${account.current_balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(account.current_balance)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Disponible</span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(account.available_balance)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Devise</span>
                    <Badge variant="outline">{account.currency}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    {getStatusBadge(account.status)}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="investments" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Portefeuille d'Investissements</h2>
            <Button>
              <TrendingUp className="h-4 w-4 mr-2" />
              Nouvel Investissement
            </Button>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {investments.map((investment) => (
              <Card key={investment.id}>
                <CardHeader>
                  <CardTitle className="text-lg">{investment.name}</CardTitle>
                  <CardDescription>
                    {investment.investment_type} • {investment.investment_code}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Montant initial</span>
                    <span className="font-medium">
                      {formatCurrency(investment.initial_amount)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Valeur actuelle</span>
                    <span className="font-bold">
                      {formatCurrency(investment.current_value)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Gain/Perte</span>
                    <span className={`font-bold ${investment.gain_loss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {investment.gain_loss >= 0 ? '+' : ''}{formatCurrency(investment.gain_loss)}
                      <span className="text-xs ml-1">
                        ({investment.gain_loss_percentage >= 0 ? '+' : ''}{investment.gain_loss_percentage.toFixed(1)}%)
                      </span>
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Niveau de risque</span>
                    {getRiskLevelBadge(investment.risk_level)}
                  </div>
                  <div className="flex items-center justify-between">
                    {getStatusBadge(investment.status)}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="loans" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Portefeuille d'Emprunts</h2>
            <Button>
              <CreditCard className="h-4 w-4 mr-2" />
              Nouvel Emprunt
            </Button>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {loans.map((loan) => (
              <Card key={loan.id}>
                <CardHeader>
                  <CardTitle className="text-lg">{loan.loan_name}</CardTitle>
                  <CardDescription>
                    {loan.loan_type} • {loan.loan_number}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Prêteur</span>
                    <span className="font-medium">{loan.lender_name}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Montant principal</span>
                    <span className="font-medium">
                      {formatCurrency(loan.principal_amount)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Solde restant</span>
                    <span className="font-bold text-red-600">
                      {formatCurrency(loan.outstanding_balance)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Taux d'intérêt</span>
                    <span className="font-medium">{loan.interest_rate}%</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Échéance</span>
                    <span className="font-medium">
                      {new Date(loan.maturity_date).toLocaleDateString('fr-FR')}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    {getStatusBadge(loan.status)}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FinancePage;
