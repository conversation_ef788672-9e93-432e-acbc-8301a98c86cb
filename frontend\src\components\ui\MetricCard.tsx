import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, Typography, Box, IconButton, Chip } from '@mui/material';
import { TrendingUp, TrendingDown, MoreVert } from '@mui/icons-material';
import CountUp from 'react-countup';

interface MetricCardProps {
  title: string;
  value: number | string;
  previousValue?: number;
  unit?: string;
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: number;
  isLoading?: boolean;
  onClick?: () => void;
  subtitle?: string;
  format?: 'number' | 'currency' | 'percentage';
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  previousValue,
  unit = '',
  icon,
  color = 'primary',
  trend,
  trendValue,
  isLoading = false,
  onClick,
  subtitle,
  format = 'number'
}) => {
  const formatValue = (val: number | string) => {
    if (typeof val === 'string') return val;
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('fr-FR', {
          style: 'currency',
          currency: 'EUR'
        }).format(val);
      case 'percentage':
        return `${val.toFixed(2)}%`;
      default:
        return val.toLocaleString('fr-FR');
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up': return 'success';
      case 'down': return 'error';
      default: return 'default';
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return <TrendingUp fontSize="small" />;
      case 'down': return <TrendingDown fontSize="small" />;
      default: return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.02 }}
      onClick={onClick}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <Card
        sx={{
          height: '100%',
          background: `linear-gradient(135deg, ${color === 'primary' ? '#1976d2' : '#f5f5f5'} 0%, ${color === 'primary' ? '#1565c0' : '#e0e0e0'} 100%)`,
          color: color === 'primary' ? 'white' : 'inherit',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: `linear-gradient(90deg, ${color === 'primary' ? '#64b5f6' : '#2196f3'}, ${color === 'primary' ? '#42a5f5' : '#1976d2'})`,
          }
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Box display="flex" alignItems="center" gap={1}>
              {icon && (
                <Box
                  sx={{
                    p: 1,
                    borderRadius: '50%',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {icon}
                </Box>
              )}
              <Typography variant="h6" component="h3" fontWeight={600}>
                {title}
              </Typography>
            </Box>
            <IconButton size="small" sx={{ color: 'inherit' }}>
              <MoreVert />
            </IconButton>
          </Box>

          <Box mb={1}>
            <Typography variant="h3" component="div" fontWeight={700}>
              {isLoading ? (
                <motion.div
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  ---
                </motion.div>
              ) : typeof value === 'number' ? (
                <CountUp
                  end={value}
                  duration={2}
                  separator=" "
                  decimals={format === 'percentage' ? 2 : 0}
                  suffix={unit}
                  formattingFn={formatValue}
                />
              ) : (
                formatValue(value)
              )}
            </Typography>
          </Box>

          {subtitle && (
            <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
              {subtitle}
            </Typography>
          )}

          {trend && trendValue !== undefined && (
            <Box display="flex" alignItems="center" gap={1}>
              <Chip
                icon={getTrendIcon()}
                label={`${trendValue > 0 ? '+' : ''}${trendValue.toFixed(1)}%`}
                size="small"
                color={getTrendColor() as any}
                variant="outlined"
                sx={{
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  color: 'inherit',
                  borderColor: 'rgba(255, 255, 255, 0.3)'
                }}
              />
              <Typography variant="caption" sx={{ opacity: 0.7 }}>
                vs période précédente
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};
