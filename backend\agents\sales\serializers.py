"""
Serializers pour l'Agent Sales
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
from .models import Customer, Product, Opportunity, Quote, QuoteItem

User = get_user_model()


class CustomerSerializer(serializers.ModelSerializer):
    """Serializer pour les clients"""
    sales_rep_name = serializers.CharField(source='sales_rep.get_full_name', read_only=True)
    
    class Meta:
        model = Customer
        fields = [
            'id', 'customer_code', 'customer_type', 'name', 'email', 'phone', 'website',
            'address_line1', 'address_line2', 'city', 'postal_code', 'country',
            'credit_limit', 'payment_terms', 'sales_rep', 'sales_rep_name',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ProductSerializer(serializers.ModelSerializer):
    """Serializer pour les produits"""
    margin_percentage = serializers.ReadOnlyField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'product_code', 'name', 'description', 'product_type', 'category',
            'unit_price', 'cost_price', 'unit_of_measure', 'is_active', 'is_sellable',
            'margin_percentage', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class OpportunitySerializer(serializers.ModelSerializer):
    """Serializer pour les opportunités"""
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    sales_rep_name = serializers.CharField(source='sales_rep.get_full_name', read_only=True)
    weighted_value = serializers.ReadOnlyField()
    
    class Meta:
        model = Opportunity
        fields = [
            'id', 'title', 'description', 'customer', 'customer_name',
            'sales_rep', 'sales_rep_name', 'estimated_value', 'probability',
            'stage', 'priority', 'expected_close_date', 'actual_close_date',
            'last_contact_date', 'next_action', 'weighted_value',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class OpportunityCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création d'opportunités"""
    
    class Meta:
        model = Opportunity
        fields = [
            'title', 'description', 'customer', 'estimated_value', 'probability',
            'stage', 'priority', 'expected_close_date', 'next_action'
        ]
    
    def validate_probability(self, value):
        """Valide que la probabilité est entre 0 et 100"""
        if not 0 <= value <= 100:
            raise serializers.ValidationError("La probabilité doit être entre 0 et 100")
        return value


class QuoteItemSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes de devis"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.product_code', read_only=True)
    
    class Meta:
        model = QuoteItem
        fields = [
            'id', 'product', 'product_name', 'product_code', 'description',
            'quantity', 'unit_price', 'discount_percentage', 'line_total',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'line_total', 'created_at', 'updated_at']


class QuoteSerializer(serializers.ModelSerializer):
    """Serializer pour les devis"""
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    sales_rep_name = serializers.CharField(source='sales_rep.get_full_name', read_only=True)
    opportunity_title = serializers.CharField(source='opportunity.title', read_only=True)
    items = QuoteItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = Quote
        fields = [
            'id', 'quote_number', 'customer', 'customer_name', 'opportunity',
            'opportunity_title', 'sales_rep', 'sales_rep_name', 'quote_date',
            'valid_until', 'status', 'subtotal', 'tax_amount', 'total_amount',
            'notes', 'terms_conditions', 'items', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'quote_number', 'subtotal', 'tax_amount', 'total_amount', 'created_at', 'updated_at']


class QuoteCreateSerializer(serializers.Serializer):
    """Serializer pour la création de devis"""
    customer_id = serializers.UUIDField()
    opportunity_id = serializers.UUIDField(required=False)
    quote_date = serializers.DateField(required=False)
    valid_until = serializers.DateField()
    notes = serializers.CharField(required=False, allow_blank=True)
    terms_conditions = serializers.CharField(required=False, allow_blank=True)
    items = serializers.ListField(
        child=serializers.DictField(),
        min_length=1,
        help_text="Liste des lignes de devis"
    )
    
    def validate_items(self, value):
        """Valide les lignes de devis"""
        for item in value:
            required_fields = ['product_id', 'quantity', 'unit_price']
            for field in required_fields:
                if field not in item:
                    raise serializers.ValidationError(f"Le champ '{field}' est requis pour chaque ligne")
            
            # Validation des types
            try:
                float(item['quantity'])
                float(item['unit_price'])
                if 'discount_percentage' in item:
                    discount = float(item['discount_percentage'])
                    if not 0 <= discount <= 100:
                        raise serializers.ValidationError("La remise doit être entre 0 et 100%")
            except (ValueError, TypeError):
                raise serializers.ValidationError("Les valeurs numériques doivent être valides")
        
        return value


class SalesDashboardSerializer(serializers.Serializer):
    """Serializer pour le dashboard Sales"""
    tenant = serializers.CharField()
    timestamp = serializers.CharField()
    pipeline = serializers.DictField()
    quotes = serializers.DictField()
    customers = serializers.DictField()
    revenue = serializers.DictField()
    recent_activities = serializers.ListField()


class OpportunityStageUpdateSerializer(serializers.Serializer):
    """Serializer pour la mise à jour d'étape d'opportunité"""
    stage = serializers.ChoiceField(choices=Opportunity.STAGES)
    notes = serializers.CharField(required=False, allow_blank=True)


class SalesPerformanceSerializer(serializers.Serializer):
    """Serializer pour les performances de vente"""
    period = serializers.DictField()
    growth = serializers.DictField()
    sales_rep_performance = serializers.ListField()


class SalesInsightSerializer(serializers.Serializer):
    """Serializer pour les insights de vente"""
    type = serializers.CharField()
    priority = serializers.ChoiceField(choices=['high', 'medium', 'low'])
    title = serializers.CharField()
    description = serializers.CharField()
    recommendation = serializers.CharField()


class CustomerCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de clients"""
    
    class Meta:
        model = Customer
        fields = [
            'customer_type', 'name', 'email', 'phone', 'website',
            'address_line1', 'address_line2', 'city', 'postal_code', 'country',
            'credit_limit', 'payment_terms', 'sales_rep'
        ]
    
    def validate_email(self, value):
        """Valide l'unicité de l'email"""
        if value and Customer.objects.filter(email=value, tenant=self.context['tenant']).exists():
            raise serializers.ValidationError("Un client avec cet email existe déjà")
        return value


class ProductCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de produits"""
    
    class Meta:
        model = Product
        fields = [
            'name', 'description', 'product_type', 'category',
            'unit_price', 'cost_price', 'unit_of_measure', 'is_sellable'
        ]
    
    def validate_unit_price(self, value):
        """Valide que le prix est positif"""
        if value <= 0:
            raise serializers.ValidationError("Le prix unitaire doit être positif")
        return value


class QuoteStatusUpdateSerializer(serializers.Serializer):
    """Serializer pour la mise à jour du statut de devis"""
    status = serializers.ChoiceField(choices=Quote.STATUS_CHOICES)
    comments = serializers.CharField(required=False, allow_blank=True)


class PipelineAnalysisSerializer(serializers.Serializer):
    """Serializer pour l'analyse du pipeline"""
    stage_analysis = serializers.DictField()
    conversion_rates = serializers.DictField()
    average_deal_size = serializers.FloatField()
    sales_cycle_length = serializers.FloatField()
    top_opportunities = serializers.ListField()
    bottlenecks = serializers.ListField()


class SalesForecastSerializer(serializers.Serializer):
    """Serializer pour les prévisions de vente"""
    period = serializers.CharField()
    forecast_value = serializers.FloatField()
    confidence_level = serializers.FloatField()
    contributing_opportunities = serializers.ListField()
    risk_factors = serializers.ListField()
    recommendations = serializers.ListField()
