"""
Tâches Celery pour l'Agent Manager
Automatisation des processus de monitoring et d'optimisation
"""
import logging
from datetime import timedelta
from django.utils import timezone
from celery import shared_task
from celery.exceptions import Retry

from core.models import Tenant
from agents.models import Agent, AgentTask
from agents.ai_service import ai_service
from .services import AgentManagerService
from .models import SystemMetrics

logger = logging.getLogger('agents.manager.tasks')


@shared_task(bind=True, max_retries=3)
def collect_system_metrics_task(self, tenant_id):
    """
    Tâche périodique pour collecter les métriques système
    """
    try:
        tenant = Tenant.objects.get(id=tenant_id)
        manager_service = AgentManagerService(tenant)
        
        # Collecte des métriques
        metrics = manager_service.collect_system_metrics()
        
        logger.info(f"Métriques collectées pour {tenant.name}: {metrics.success_rate}% de réussite")
        
        # Vérification des alertes critiques
        if metrics.alerts:
            critical_alerts = [alert for alert in metrics.alerts if alert.get('severity') == 'high']
            if critical_alerts:
                # Déclencher des actions correctives
                trigger_corrective_actions.delay(tenant_id, critical_alerts)
        
        return {
            'tenant': tenant.name,
            'metrics_id': str(metrics.id),
            'success_rate': metrics.success_rate,
            'alerts_count': len(metrics.alerts)
        }
        
    except Tenant.DoesNotExist:
        logger.error(f"Tenant {tenant_id} non trouvé")
        return {'error': 'Tenant non trouvé'}
    except Exception as e:
        logger.error(f"Erreur lors de la collecte des métriques pour {tenant_id}: {str(e)}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        return {'error': str(e)}


@shared_task(bind=True, max_retries=2)
def optimize_workflows_task(self, tenant_id):
    """
    Tâche périodique pour optimiser les workflows
    """
    try:
        tenant = Tenant.objects.get(id=tenant_id)
        manager_service = AgentManagerService(tenant)
        
        # Analyse des workflows
        optimizations = manager_service.optimize_workflows()
        
        if optimizations:
            logger.info(f"Optimisations trouvées pour {tenant.name}: {len(optimizations)} workflows")
            
            # Appliquer les optimisations automatiques si l'IA est disponible
            if ai_service.is_available():
                apply_workflow_optimizations.delay(tenant_id, optimizations)
        
        return {
            'tenant': tenant.name,
            'optimizations_count': len(optimizations),
            'optimizations': optimizations
        }
        
    except Tenant.DoesNotExist:
        logger.error(f"Tenant {tenant_id} non trouvé")
        return {'error': 'Tenant non trouvé'}
    except Exception as e:
        logger.error(f"Erreur lors de l'optimisation des workflows pour {tenant_id}: {str(e)}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=120, exc=e)
        return {'error': str(e)}


@shared_task(bind=True)
def trigger_corrective_actions(self, tenant_id, alerts):
    """
    Déclenche des actions correctives basées sur les alertes
    """
    try:
        tenant = Tenant.objects.get(id=tenant_id)
        manager_service = AgentManagerService(tenant)
        
        actions_taken = []
        
        for alert in alerts:
            alert_type = alert.get('type')
            
            if alert_type == 'performance':
                # Redémarrer les agents avec de mauvaises performances
                action = restart_underperforming_agents.delay(tenant_id)
                actions_taken.append(f"Redémarrage des agents sous-performants: {action.id}")
                
            elif alert_type == 'load':
                # Redistribuer les tâches
                action = redistribute_tasks.delay(tenant_id)
                actions_taken.append(f"Redistribution des tâches: {action.id}")
                
            elif alert_type == 'error':
                # Analyser et résoudre les erreurs
                action = analyze_and_resolve_errors.delay(tenant_id)
                actions_taken.append(f"Analyse des erreurs: {action.id}")
        
        # Envoyer une notification aux administrateurs
        if actions_taken:
            manager_service.broadcast_message(
                'notification',
                'Actions correctives déclenchées',
                {
                    'alerts': alerts,
                    'actions': actions_taken,
                    'timestamp': timezone.now().isoformat()
                }
            )
        
        logger.info(f"Actions correctives déclenchées pour {tenant.name}: {len(actions_taken)}")
        return {'actions_taken': actions_taken}
        
    except Exception as e:
        logger.error(f"Erreur lors des actions correctives pour {tenant_id}: {str(e)}")
        return {'error': str(e)}


@shared_task(bind=True)
def restart_underperforming_agents(self, tenant_id):
    """
    Redémarre les agents avec de mauvaises performances
    """
    try:
        tenant = Tenant.objects.get(id=tenant_id)
        
        # Identifier les agents sous-performants
        underperforming_agents = Agent.objects.filter(
            tenant=tenant,
            status='active'
        ).exclude(agent_type='manager')
        
        restarted_agents = []
        
        for agent in underperforming_agents:
            if agent.success_rate < 70:  # Moins de 70% de réussite
                # Simuler un redémarrage (dans un vrai système, cela pourrait redémarrer le service)
                agent.status = 'maintenance'
                agent.save()
                
                # Remettre en service après quelques secondes
                restart_agent_service.apply_async(args=[agent.id], countdown=30)
                restarted_agents.append(agent.name)
        
        logger.info(f"Agents redémarrés pour {tenant.name}: {restarted_agents}")
        return {'restarted_agents': restarted_agents}
        
    except Exception as e:
        logger.error(f"Erreur lors du redémarrage des agents pour {tenant_id}: {str(e)}")
        return {'error': str(e)}


@shared_task
def restart_agent_service(agent_id):
    """
    Remet un agent en service après maintenance
    """
    try:
        agent = Agent.objects.get(id=agent_id)
        agent.status = 'active'
        agent.save()
        
        logger.info(f"Agent {agent.name} remis en service")
        return {'agent': agent.name, 'status': 'active'}
        
    except Agent.DoesNotExist:
        logger.error(f"Agent {agent_id} non trouvé")
        return {'error': 'Agent non trouvé'}


@shared_task(bind=True)
def redistribute_tasks(self, tenant_id):
    """
    Redistribue les tâches en attente pour équilibrer la charge
    """
    try:
        tenant = Tenant.objects.get(id=tenant_id)
        manager_service = AgentManagerService(tenant)
        
        # Récupérer les tâches en attente
        pending_tasks = AgentTask.objects.filter(
            agent__tenant=tenant,
            status='pending'
        ).order_by('priority', 'created_at')
        
        redistributed_count = 0
        
        for task in pending_tasks:
            # Trouver un agent moins chargé
            current_agent = task.agent
            current_load = current_agent.tasks.filter(status__in=['pending', 'running']).count()
            
            # Chercher un agent du même type avec moins de charge
            alternative_agents = Agent.objects.filter(
                tenant=tenant,
                agent_type=current_agent.agent_type,
                status='active',
                is_enabled=True
            ).exclude(id=current_agent.id)
            
            for alt_agent in alternative_agents:
                alt_load = alt_agent.tasks.filter(status__in=['pending', 'running']).count()
                if alt_load < current_load - 1:  # Au moins 2 tâches de différence
                    task.agent = alt_agent
                    task.save()
                    redistributed_count += 1
                    break
        
        logger.info(f"Tâches redistribuées pour {tenant.name}: {redistributed_count}")
        return {'redistributed_tasks': redistributed_count}
        
    except Exception as e:
        logger.error(f"Erreur lors de la redistribution pour {tenant_id}: {str(e)}")
        return {'error': str(e)}


@shared_task(bind=True)
def analyze_and_resolve_errors(self, tenant_id):
    """
    Analyse les erreurs récentes et propose des solutions
    """
    try:
        tenant = Tenant.objects.get(id=tenant_id)
        
        # Récupérer les tâches échouées récentes
        failed_tasks = AgentTask.objects.filter(
            agent__tenant=tenant,
            status='failed',
            completed_at__gte=timezone.now() - timedelta(hours=24)
        )
        
        error_analysis = {}
        
        for task in failed_tasks:
            error_type = task.error_message[:100] if task.error_message else 'Unknown error'
            if error_type not in error_analysis:
                error_analysis[error_type] = {
                    'count': 0,
                    'agents': set(),
                    'task_types': set()
                }
            
            error_analysis[error_type]['count'] += 1
            error_analysis[error_type]['agents'].add(task.agent.agent_type)
            error_analysis[error_type]['task_types'].add(task.task_type)
        
        # Convertir les sets en listes pour la sérialisation
        for error_type in error_analysis:
            error_analysis[error_type]['agents'] = list(error_analysis[error_type]['agents'])
            error_analysis[error_type]['task_types'] = list(error_analysis[error_type]['task_types'])
        
        # Générer des recommandations avec l'IA si disponible
        recommendations = []
        if ai_service.is_available() and error_analysis:
            ai_response = ai_service.generate_response(
                f"Analyse ces erreurs et propose des solutions: {error_analysis}",
                "manager",
                temperature=0.3
            )
            if ai_response.success:
                recommendations.append({
                    'type': 'ai_analysis',
                    'content': ai_response.content
                })
        
        logger.info(f"Analyse d'erreurs pour {tenant.name}: {len(error_analysis)} types d'erreurs")
        return {
            'error_analysis': error_analysis,
            'recommendations': recommendations,
            'total_failed_tasks': failed_tasks.count()
        }
        
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse d'erreurs pour {tenant_id}: {str(e)}")
        return {'error': str(e)}


@shared_task(bind=True)
def apply_workflow_optimizations(self, tenant_id, optimizations):
    """
    Applique automatiquement les optimisations de workflow recommandées
    """
    try:
        tenant = Tenant.objects.get(id=tenant_id)
        manager_service = AgentManagerService(tenant)
        
        applied_optimizations = []
        
        for optimization in optimizations:
            if optimization.get('issue') == 'low_success_rate':
                # Marquer le workflow pour révision manuelle
                applied_optimizations.append({
                    'workflow_id': optimization['workflow_id'],
                    'action': 'marked_for_review',
                    'reason': 'Low success rate requires manual intervention'
                })
            
            elif optimization.get('issue') == 'long_duration':
                # Suggérer une parallélisation automatique
                applied_optimizations.append({
                    'workflow_id': optimization['workflow_id'],
                    'action': 'parallelization_suggested',
                    'reason': 'Long duration detected'
                })
        
        # Enregistrer les optimisations appliquées
        if applied_optimizations:
            manager_service._record_decision(
                'workflow_optimization',
                {
                    'optimizations_applied': applied_optimizations,
                    'total_optimizations': len(optimizations)
                },
                f"Optimisations automatiques appliquées: {len(applied_optimizations)} sur {len(optimizations)}",
                []
            )
        
        logger.info(f"Optimisations appliquées pour {tenant.name}: {len(applied_optimizations)}")
        return {'applied_optimizations': applied_optimizations}
        
    except Exception as e:
        logger.error(f"Erreur lors de l'application d'optimisations pour {tenant_id}: {str(e)}")
        return {'error': str(e)}


@shared_task
def cleanup_old_metrics(days_to_keep=30):
    """
    Nettoie les anciennes métriques système
    """
    try:
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)
        
        deleted_count = SystemMetrics.objects.filter(
            created_at__lt=cutoff_date
        ).delete()[0]
        
        logger.info(f"Nettoyage des métriques: {deleted_count} entrées supprimées")
        return {'deleted_metrics': deleted_count}
        
    except Exception as e:
        logger.error(f"Erreur lors du nettoyage des métriques: {str(e)}")
        return {'error': str(e)}
