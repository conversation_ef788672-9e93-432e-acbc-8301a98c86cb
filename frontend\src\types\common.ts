export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: number
}

export interface PaginatedResponse<T = any> {
  count: number
  next: string | null
  previous: string | null
  results: T[]
}

export interface ApiError {
  message: string
  status: number
  details?: Record<string, string[]>
}

export interface Agent {
  name: string
  status: 'active' | 'inactive' | 'error'
  description: string
  color: string
  icon: string
  lastActivity?: string
}

export interface AgentStatus {
  status: 'active' | 'inactive' | 'error'
  agent: string
  message: string
}

export interface AgentDashboard {
  agent: string
  tenant: string
  description?: string
  status: string
  metrics?: Record<string, any>
  activities?: Activity[]
}

export interface Activity {
  id: string
  type: string
  description: string
  timestamp: string
  user?: string
  metadata?: Record<string, any>
}

export interface Module {
  name: string
  displayName: string
  description: string
  icon: string
  color: string
  isEnabled: boolean
  permissions: string[]
}

export interface DashboardStats {
  totalUsers: number
  activeAgents: number
  totalTransactions: number
  systemHealth: 'good' | 'warning' | 'error'
}

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
  actions?: NotificationAction[]
}

export interface NotificationAction {
  label: string
  action: string
  variant?: 'primary' | 'secondary' | 'danger'
}
