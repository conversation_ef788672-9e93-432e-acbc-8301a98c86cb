# ERP HUB - Système ERP Modulaire avec Architecture d'Agents

## Vue d'ensemble

ERP HUB est un système ERP modulaire moderne conçu avec une architecture d'agents hiérarchique, combinant :
- **Frontend React** avec Vite pour une interface utilisateur moderne
- **Backend Django REST Framework** pour une API robuste
- **PostgreSQL** pour la persistance des données
- **Architecture multi-agents** avec Agent Manager central
- **Docker** pour la conteneurisation et le déploiement

## Architecture des Agents

### Agent Manager (Orchestrateur Central)
- Coordonne tous les agents spécialisés
- Gère les flux de travail inter-modules
- Supervise les décisions stratégiques

### Agents Spécialisés
- **Agent RH** : Gestion des ressources humaines
- **Agent Ventes** : Gestion des ventes et devis
- **Agent Achats** : Gestion des achats et fournisseurs
- **Agent Logistique** : Gestion des livraisons et transport
- **Agent Stock** : Gestion des inventaires
- **Agent Comptabilité** : Gestion comptable
- **Agent Finance** : Gestion financière
- **Agent CRM** : Gestion de la relation client
- **Agent BI** : Business Intelligence et analytics

## Structure du Projet

```
ERP_HUB/
├── docker-compose.yml
├── docker-compose.dev.yml
├── .env.example
├── backend/                 # Django REST API
│   ├── Dockerfile
│   ├── requirements.txt
│   ├── manage.py
│   ├── config/             # Configuration Django
│   ├── core/               # Applications core (auth, users)
│   ├── agents/             # Architecture des agents
│   │   ├── manager/        # Agent Manager
│   │   ├── hr/            # Agent RH
│   │   ├── sales/         # Agent Ventes
│   │   ├── purchase/      # Agent Achats
│   │   ├── logistics/     # Agent Logistique
│   │   ├── stock/         # Agent Stock
│   │   ├── accounting/    # Agent Comptabilité
│   │   ├── finance/       # Agent Finance
│   │   ├── crm/           # Agent CRM
│   │   └── bi/            # Agent BI
│   └── modules/            # Modules métier
├── frontend/               # React Frontend
│   ├── Dockerfile
│   ├── package.json
│   ├── vite.config.js
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── agents/        # Interfaces agents
│   │   ├── modules/       # Modules ERP
│   │   └── services/      # Services API
├── database/              # Scripts PostgreSQL
│   ├── init/
│   └── migrations/
└── docs/                  # Documentation
```

## Démarrage Rapide

```bash
# Cloner et configurer
cp .env.example .env

# Démarrer l'environnement de développement
docker-compose -f docker-compose.dev.yml up --build

# Accéder à l'application
Frontend: http://localhost:3000
Backend API: http://localhost:8000
Admin Django: http://localhost:8000/admin
```

## Technologies Utilisées

- **Frontend** : React 18, Vite, TypeScript, Tailwind CSS
- **Backend** : Django 4.2, Django REST Framework, JWT Authentication
- **Base de données** : PostgreSQL 15
- **Conteneurisation** : Docker, Docker Compose
- **Architecture** : Microservices, Multi-agents, RBAC
