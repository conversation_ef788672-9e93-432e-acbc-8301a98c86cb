@echo off
echo.
echo ========================================
echo    🚀 ERP HUB - LANCEMENT AUTOMATIQUE
echo ========================================
echo.

:: Couleurs pour les messages
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

:: Vérification de Node.js
echo %BLUE%🔍 Vérification de Node.js...%RESET%
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Node.js n'est pas installé ou non accessible%RESET%
    echo %YELLOW%📥 Veuillez installer Node.js depuis https://nodejs.org%RESET%
    pause
    exit /b 1
)

:: Affichage de la version Node.js
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo %GREEN%✅ Node.js détecté: %NODE_VERSION%%RESET%

:: Vérification de npm
echo %BLUE%🔍 Vérification de npm...%RESET%
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ npm n'est pas accessible%RESET%
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo %GREEN%✅ npm détecté: %NPM_VERSION%%RESET%

:: Navigation vers le dossier frontend
echo %BLUE%📁 Navigation vers le dossier frontend...%RESET%
if not exist "frontend" (
    echo %RED%❌ Dossier frontend introuvable%RESET%
    echo %YELLOW%📍 Assurez-vous d'être dans le dossier racine du projet%RESET%
    pause
    exit /b 1
)

cd frontend
echo %GREEN%✅ Dossier frontend trouvé%RESET%

:: Vérification du package.json
if not exist "package.json" (
    echo %RED%❌ package.json introuvable dans le dossier frontend%RESET%
    pause
    exit /b 1
)

echo %GREEN%✅ package.json trouvé%RESET%

:: Résolution des problèmes PowerShell
echo %BLUE%🔧 Configuration PowerShell...%RESET%
powershell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force" >nul 2>&1
echo %GREEN%✅ Politique PowerShell configurée%RESET%

:: Nettoyage du cache npm (optionnel)
echo %BLUE%🧹 Nettoyage du cache npm...%RESET%
npm cache clean --force >nul 2>&1
echo %GREEN%✅ Cache npm nettoyé%RESET%

:: Installation des dépendances
echo %BLUE%📦 Installation des dépendances...%RESET%
echo %YELLOW%⏳ Cela peut prendre quelques minutes...%RESET%
npm install
if %errorlevel% neq 0 (
    echo %RED%❌ Erreur lors de l'installation des dépendances%RESET%
    echo %YELLOW%🔄 Tentative avec --legacy-peer-deps...%RESET%
    npm install --legacy-peer-deps
    if %errorlevel% neq 0 (
        echo %RED%❌ Installation échouée même avec --legacy-peer-deps%RESET%
        pause
        exit /b 1
    )
)
echo %GREEN%✅ Dépendances installées avec succès%RESET%

:: Vérification des ports disponibles
echo %BLUE%🌐 Vérification des ports...%RESET%
netstat -an | find "5173" >nul
if %errorlevel% equ 0 (
    echo %YELLOW%⚠️  Port 5173 déjà utilisé, utilisation du port 3000%RESET%
    set PORT_FLAG=--port 3000
    set APP_URL=http://localhost:3000
) else (
    set PORT_FLAG=
    set APP_URL=http://localhost:5173
)

:: Démarrage du serveur de développement
echo.
echo %GREEN%========================================%RESET%
echo %GREEN%    🎉 LANCEMENT DE ERP HUB%RESET%
echo %GREEN%========================================%RESET%
echo %BLUE%🚀 Démarrage du serveur Vite...%RESET%
echo %YELLOW%📍 URL: %APP_URL%%RESET%
echo %YELLOW%⏹️  Appuyez sur Ctrl+C pour arrêter%RESET%
echo.

:: Attendre 3 secondes puis ouvrir le navigateur
start "" cmd /c "timeout /t 5 /nobreak >nul && start %APP_URL%"

:: Lancement du serveur
npm run dev %PORT_FLAG% -- --host 0.0.0.0

:: Si on arrive ici, le serveur s'est arrêté
echo.
echo %YELLOW%👋 Serveur ERP HUB arrêté%RESET%
pause
