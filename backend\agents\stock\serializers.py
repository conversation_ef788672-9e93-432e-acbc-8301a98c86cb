"""
Serializers pour l'Agent Stock
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
from .models import (
    Warehouse, Location, StockItem, StockLevel, StockMovement,
    StockReservation, StockInventory, StockInventoryLine
)

User = get_user_model()


class WarehouseSerializer(serializers.ModelSerializer):
    """Serializer pour les entrepôts"""
    manager_name = serializers.CharField(source='manager.get_full_name', read_only=True)
    
    class Meta:
        model = Warehouse
        fields = [
            'id', 'code', 'name', 'warehouse_type', 'description',
            'address_line1', 'address_line2', 'city', 'postal_code', 'country',
            'manager', 'manager_name', 'total_capacity', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class LocationSerializer(serializers.ModelSerializer):
    """Serializer pour les emplacements"""
    warehouse_name = serializers.Char<PERSON><PERSON>(source='warehouse.name', read_only=True)
    parent_location_code = serializers.CharField(source='parent_location.code', read_only=True)
    
    class Meta:
        model = Location
        fields = [
            'id', 'warehouse', 'warehouse_name', 'code', 'name', 'location_type',
            'parent_location', 'parent_location_code', 'capacity', 'max_weight',
            'temperature_controlled', 'hazardous_materials', 'is_active', 'is_blocked',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class StockItemSerializer(serializers.ModelSerializer):
    """Serializer pour les articles en stock"""
    current_stock = serializers.SerializerMethodField()
    stock_value = serializers.SerializerMethodField()
    
    class Meta:
        model = StockItem
        fields = [
            'id', 'sku', 'name', 'description', 'item_type', 'barcode',
            'internal_reference', 'supplier_reference', 'unit_of_measure',
            'weight', 'volume', 'valuation_method', 'minimum_stock', 'maximum_stock',
            'reorder_point', 'reorder_quantity', 'standard_cost', 'last_purchase_price',
            'category', 'abc_classification', 'temperature_min', 'temperature_max',
            'humidity_max', 'expiry_tracking', 'shelf_life_days', 'is_active',
            'is_serialized', 'is_lot_tracked', 'current_stock', 'stock_value',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'current_stock', 'stock_value', 'created_at', 'updated_at']
    
    def get_current_stock(self, obj):
        """Retourne le stock total actuel"""
        total_stock = sum(level.quantity_on_hand for level in obj.stock_levels.all())
        return float(total_stock)
    
    def get_stock_value(self, obj):
        """Retourne la valeur totale du stock"""
        total_value = sum(level.total_value for level in obj.stock_levels.all())
        return float(total_value)


class StockLevelSerializer(serializers.ModelSerializer):
    """Serializer pour les niveaux de stock"""
    item_sku = serializers.CharField(source='item.sku', read_only=True)
    item_name = serializers.CharField(source='item.name', read_only=True)
    location_code = serializers.CharField(source='location.code', read_only=True)
    warehouse_name = serializers.CharField(source='location.warehouse.name', read_only=True)
    
    class Meta:
        model = StockLevel
        fields = [
            'id', 'item', 'item_sku', 'item_name', 'location', 'location_code',
            'warehouse_name', 'quantity_on_hand', 'quantity_reserved', 'quantity_available',
            'average_cost', 'total_value', 'last_movement_date', 'last_count_date',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'quantity_available', 'total_value', 'created_at', 'updated_at']


class StockMovementSerializer(serializers.ModelSerializer):
    """Serializer pour les mouvements de stock"""
    item_sku = serializers.CharField(source='item.sku', read_only=True)
    item_name = serializers.CharField(source='item.name', read_only=True)
    location_from_code = serializers.CharField(source='location_from.code', read_only=True)
    location_to_code = serializers.CharField(source='location_to.code', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = StockMovement
        fields = [
            'id', 'movement_number', 'item', 'item_sku', 'item_name',
            'location_from', 'location_from_code', 'location_to', 'location_to_code',
            'movement_type', 'movement_reason', 'quantity', 'unit_cost', 'total_cost',
            'lot_number', 'serial_number', 'expiry_date', 'reference_document',
            'reference_type', 'user', 'user_name', 'movement_date', 'comments',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'movement_number', 'total_cost', 'created_at', 'updated_at']


class StockMovementCreateSerializer(serializers.Serializer):
    """Serializer pour la création de mouvements de stock"""
    item_id = serializers.UUIDField()
    location_from_id = serializers.UUIDField(required=False, allow_null=True)
    location_to_id = serializers.UUIDField(required=False, allow_null=True)
    movement_type = serializers.ChoiceField(choices=StockMovement.MOVEMENT_TYPES)
    movement_reason = serializers.ChoiceField(choices=StockMovement.MOVEMENT_REASONS)
    quantity = serializers.DecimalField(max_digits=12, decimal_places=3, min_value=Decimal('0.001'))
    unit_cost = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True)
    lot_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    serial_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    expiry_date = serializers.DateField(required=False, allow_null=True)
    reference_document = serializers.CharField(max_length=100, required=False, allow_blank=True)
    reference_type = serializers.CharField(max_length=20, required=False, allow_blank=True)
    movement_date = serializers.DateTimeField(required=False)
    comments = serializers.CharField(required=False, allow_blank=True)
    
    def validate(self, data):
        """Validation des données de mouvement"""
        movement_type = data['movement_type']
        
        # Validation des emplacements selon le type de mouvement
        if movement_type in ['receipt', 'return', 'production']:
            if not data.get('location_to_id'):
                raise serializers.ValidationError("L'emplacement de destination est requis pour ce type de mouvement")
        
        elif movement_type in ['issue', 'consumption', 'scrap']:
            if not data.get('location_from_id'):
                raise serializers.ValidationError("L'emplacement source est requis pour ce type de mouvement")
        
        elif movement_type == 'transfer':
            if not data.get('location_from_id') or not data.get('location_to_id'):
                raise serializers.ValidationError("Les emplacements source et destination sont requis pour un transfert")
            if data.get('location_from_id') == data.get('location_to_id'):
                raise serializers.ValidationError("Les emplacements source et destination doivent être différents")
        
        return data


class StockReservationSerializer(serializers.ModelSerializer):
    """Serializer pour les réservations de stock"""
    item_sku = serializers.CharField(source='item.sku', read_only=True)
    item_name = serializers.CharField(source='item.name', read_only=True)
    location_code = serializers.CharField(source='location.code', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    quantity_remaining = serializers.ReadOnlyField()
    
    class Meta:
        model = StockReservation
        fields = [
            'id', 'reservation_number', 'item', 'item_sku', 'item_name',
            'location', 'location_code', 'reservation_type', 'status',
            'quantity_reserved', 'quantity_fulfilled', 'quantity_remaining',
            'reservation_date', 'required_date', 'expiry_date',
            'reference_document', 'user', 'user_name', 'comments',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'reservation_number', 'quantity_remaining', 'created_at', 'updated_at']


class StockReservationCreateSerializer(serializers.Serializer):
    """Serializer pour la création de réservations"""
    item_id = serializers.UUIDField()
    location_id = serializers.UUIDField()
    reservation_type = serializers.ChoiceField(choices=StockReservation.RESERVATION_TYPES)
    quantity_reserved = serializers.DecimalField(max_digits=12, decimal_places=3, min_value=Decimal('0.001'))
    reservation_date = serializers.DateTimeField(required=False)
    required_date = serializers.DateTimeField()
    expiry_date = serializers.DateTimeField(required=False, allow_null=True)
    reference_document = serializers.CharField(max_length=100, required=False, allow_blank=True)
    comments = serializers.CharField(required=False, allow_blank=True)


class StockInventoryLineSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes d'inventaire"""
    item_sku = serializers.CharField(source='item.sku', read_only=True)
    item_name = serializers.CharField(source='item.name', read_only=True)
    location_code = serializers.CharField(source='location.code', read_only=True)
    counter_name = serializers.CharField(source='counter.get_full_name', read_only=True)
    
    class Meta:
        model = StockInventoryLine
        fields = [
            'id', 'inventory', 'item', 'item_sku', 'item_name', 'location', 'location_code',
            'system_quantity', 'counted_quantity', 'adjustment_quantity',
            'unit_cost', 'adjustment_value', 'lot_number', 'serial_number',
            'counter', 'counter_name', 'count_date', 'is_counted', 'is_adjusted',
            'comments', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'adjustment_quantity', 'adjustment_value', 'is_counted', 'is_adjusted', 'created_at', 'updated_at']


class StockInventorySerializer(serializers.ModelSerializer):
    """Serializer pour les inventaires"""
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    supervisor_name = serializers.CharField(source='supervisor.get_full_name', read_only=True)
    lines = StockInventoryLineSerializer(many=True, read_only=True)
    
    class Meta:
        model = StockInventory
        fields = [
            'id', 'inventory_number', 'inventory_type', 'status',
            'warehouse', 'warehouse_name', 'planned_date', 'start_date', 'end_date',
            'supervisor', 'supervisor_name', 'total_items_counted', 'total_discrepancies',
            'total_value_adjustment', 'description', 'comments', 'lines',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'inventory_number', 'total_items_counted', 'total_discrepancies', 'total_value_adjustment', 'created_at', 'updated_at']


class StockInventoryCreateSerializer(serializers.Serializer):
    """Serializer pour la création d'inventaires"""
    inventory_type = serializers.ChoiceField(choices=StockInventory.INVENTORY_TYPES)
    warehouse_id = serializers.UUIDField()
    planned_date = serializers.DateField()
    location_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        allow_empty=True
    )
    item_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        allow_empty=True
    )
    description = serializers.CharField(required=False, allow_blank=True)
    comments = serializers.CharField(required=False, allow_blank=True)


class StockDashboardSerializer(serializers.Serializer):
    """Serializer pour le dashboard Stock"""
    tenant = serializers.CharField()
    timestamp = serializers.CharField()
    items = serializers.DictField()
    warehouses = serializers.DictField()
    value = serializers.DictField()
    movements = serializers.DictField()
    inventories = serializers.DictField()
    alerts = serializers.DictField()
    recent_activities = serializers.ListField()


class StockPerformanceSerializer(serializers.Serializer):
    """Serializer pour l'analyse des performances stock"""
    abc_analysis = serializers.DictField()
    turnover_analysis = serializers.DictField()
    slow_moving_items = serializers.ListField()
    obsolete_items = serializers.ListField()
    coverage_analysis = serializers.DictField()
    timestamp = serializers.CharField()


class StockInsightSerializer(serializers.Serializer):
    """Serializer pour les insights stock"""
    type = serializers.ChoiceField(choices=['critical', 'warning', 'opportunity', 'info'])
    priority = serializers.ChoiceField(choices=['high', 'medium', 'low'])
    title = serializers.CharField()
    description = serializers.CharField()
    recommendation = serializers.CharField()
    generated_at = serializers.CharField()


class InventoryCountSerializer(serializers.Serializer):
    """Serializer pour le comptage d'inventaire"""
    inventory_line_id = serializers.UUIDField()
    counted_quantity = serializers.DecimalField(max_digits=12, decimal_places=3, min_value=Decimal('0.000'))
    comments = serializers.CharField(required=False, allow_blank=True)


class StockAlertSerializer(serializers.Serializer):
    """Serializer pour les alertes de stock"""
    alert_type = serializers.ChoiceField(choices=['low_stock', 'out_of_stock', 'expired_reservation', 'slow_moving'])
    priority = serializers.ChoiceField(choices=['high', 'medium', 'low'])
    item_id = serializers.UUIDField()
    item_sku = serializers.CharField()
    item_name = serializers.CharField()
    message = serializers.CharField()
    current_stock = serializers.DecimalField(max_digits=12, decimal_places=3, required=False)
    minimum_stock = serializers.DecimalField(max_digits=12, decimal_places=3, required=False)
    location_code = serializers.CharField(required=False)
    generated_at = serializers.CharField()


class StockTransferSerializer(serializers.Serializer):
    """Serializer pour les transferts de stock"""
    item_id = serializers.UUIDField()
    location_from_id = serializers.UUIDField()
    location_to_id = serializers.UUIDField()
    quantity = serializers.DecimalField(max_digits=12, decimal_places=3, min_value=Decimal('0.001'))
    lot_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    serial_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    reference_document = serializers.CharField(max_length=100, required=False, allow_blank=True)
    comments = serializers.CharField(required=False, allow_blank=True)
    
    def validate(self, data):
        """Validation des données de transfert"""
        if data['location_from_id'] == data['location_to_id']:
            raise serializers.ValidationError("Les emplacements source et destination doivent être différents")
        return data


class StockAdjustmentSerializer(serializers.Serializer):
    """Serializer pour les ajustements de stock"""
    item_id = serializers.UUIDField()
    location_id = serializers.UUIDField()
    adjustment_quantity = serializers.DecimalField(max_digits=12, decimal_places=3)
    adjustment_reason = serializers.ChoiceField(choices=[
        ('damage', 'Dommage'),
        ('expiry', 'Expiration'),
        ('theft', 'Vol'),
        ('count_error', 'Erreur de comptage'),
        ('system_error', 'Erreur système'),
        ('other', 'Autre')
    ])
    unit_cost = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True)
    lot_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    serial_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    comments = serializers.CharField(required=False, allow_blank=True)


class StockValuationSerializer(serializers.Serializer):
    """Serializer pour la valorisation des stocks"""
    item_id = serializers.UUIDField()
    item_sku = serializers.CharField()
    item_name = serializers.CharField()
    quantity_on_hand = serializers.DecimalField(max_digits=12, decimal_places=3)
    average_cost = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    valuation_method = serializers.CharField()
    last_movement_date = serializers.DateTimeField()


class StockForecastSerializer(serializers.Serializer):
    """Serializer pour les prévisions de stock"""
    item_id = serializers.UUIDField()
    item_sku = serializers.CharField()
    item_name = serializers.CharField()
    current_stock = serializers.DecimalField(max_digits=12, decimal_places=3)
    predicted_consumption = serializers.DecimalField(max_digits=12, decimal_places=3)
    predicted_stock_out_date = serializers.DateField(allow_null=True)
    recommended_reorder_quantity = serializers.DecimalField(max_digits=12, decimal_places=3)
    confidence_level = serializers.FloatField()
    forecast_period_days = serializers.IntegerField()


class LocationUtilizationSerializer(serializers.Serializer):
    """Serializer pour l'utilisation des emplacements"""
    location_id = serializers.UUIDField()
    location_code = serializers.CharField()
    warehouse_name = serializers.CharField()
    capacity = serializers.DecimalField(max_digits=8, decimal_places=2, allow_null=True)
    used_capacity = serializers.DecimalField(max_digits=8, decimal_places=2)
    utilization_percentage = serializers.FloatField()
    items_count = serializers.IntegerField()
    total_value = serializers.DecimalField(max_digits=15, decimal_places=2)
